DROP KEYSPACE IF EXISTS chat;
CREATE KEYSPACE IF NOT EXISTS chat WITH replication = {'class':'SimpleStrategy', 'replication_factor' : 1};
CREATE TYPE IF NOT EXISTS chat.reply_message (
		id BIGINT,
		body TEXT,
		user_id TEXT,
		delete_level INT,
		deleted_by set<TEXT>,
		type TEXT,
		);

CREATE TYPE IF NOT EXISTS chat.policies (
		id INT,
		body TEXT,
		user_id TEXT,
		);

CREATE TABLE chat.messages (
		thread_id bigint,
		bucket bigint,
		id bigint,
		next_id bigint,
		prev_id bigint,
		sub_thread_id bigint,
		body text,
		created_at bigint,
		will_deleted_at bigint,
		delete_level int,
		deleted_by set<text>,
		deliver_status int,
		react_binary tinyint,
		reply_to frozen<reply_message>,
		type text,
		user_id text,
		uuid uuid,
		version text,
		PRIMARY KEY ((thread_id, bucket), id)
		) WITH CLUSTERING ORDER BY (id DESC)
	AND bloom_filter_fp_chance = 0.01
	AND caching = {'keys': 'ALL', 'rows_per_partition': 'NONE'}
	AND comment = ''
	AND compaction = {'class': 'org.apache.cassandra.db.compaction.SizeTieredCompactionStrategy', 'max_threshold': '32', 'min_threshold': '4'}
	AND compression = {'chunk_length_in_kb': '64', 'class': 'org.apache.cassandra.io.compress.LZ4Compressor'}
	AND crc_check_chance = 1.0
	AND default_time_to_live = 0
	AND gc_grace_seconds = 864000
	AND max_index_interval = 2048
	AND memtable_flush_period_in_ms = 0
	AND min_index_interval = 128
	AND speculative_retry = '99PERCENTILE';
