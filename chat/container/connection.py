from logging import Logger

from dependency_injector import providers
from dependency_injector.containers import Declar<PERSON><PERSON>ontainer

from chat.app.connection import AppConnections
from chat.config import Settings
from chat.connections.cassandra_c import CassandraConnection
from chat.connections.mqtt_c import MQTTConnection
from chat.connections.mysql_c import MySQLConnection
from chat.connections.rabbitmq_c import (
    RabbitMQConnection,
    RabbitMQConnectionV2,
)
from chat.connections.redis_c import RedisConnection
from chat.metrics.model import AppMetrics
from chat.publishers.mqtt import MqttPublisher
from chat.publishers.rabbitmq import AmqpClient, AmqpProducer


def init_cassandra_conn(config: Settings, log: Logger):
    cas_cfg = config.cassandra
    cas_c = CassandraConnection(
        "Chat DB",
        cas_cfg.host,
        cas_cfg.username,
        cas_cfg.password,
        cas_cfg.keyspace,
        log,
    )
    return cas_c


def init_mqtt_conn(config: Settings, log: Logger):
    mqtt_cfg = config.mqtt
    mqtt_c = MQTTConnection(
        "Mqtt publisher",
        mqtt_cfg.host,
        int(mqtt_cfg.port),
        mqtt_cfg.username,
        mqtt_cfg.password,
        log,
    )
    return mqtt_c


def init_mqtt_publisher(config: Settings, mqtt_c: MQTTConnection, log: Logger):
    mqtt_cfg = config.mqtt
    mqtt_pub = MqttPublisher(
        mqtt_c.client,
        mqtt_cfg.prefix_version,
        mqtt_cfg.postfix_status_channel,
        log,
    )
    return mqtt_pub


def init_redis_persistent_conn(config: Settings, log: Logger):
    r_cfg = config.redis_pcc
    redis_conn = RedisConnection(
        "Redis Persistent DB",
        r_cfg.host,
        r_cfg.port,
        r_cfg.db,
        r_cfg.password,
        log,
    )
    return redis_conn


def init_redis_thread_conn(config: Settings, log: Logger):
    r_cfg = config.redis_tcc
    redis_conn = RedisConnection(
        "Redis Thread DB",
        r_cfg.host,
        r_cfg.port,
        r_cfg.db,
        r_cfg.password,
        log,
    )
    return redis_conn


def init_redis_message_conn(config: Settings, log: Logger):
    r_cfg = config.redis_mcc
    redis_conn = RedisConnection(
        "Redis Message DB",
        r_cfg.host,
        r_cfg.port,
        r_cfg.db,
        r_cfg.password,
        log,
    )
    return redis_conn


def init_cache_conn(config: Settings, log: Logger):
    r_cache_cfg = config.redis_cache
    redis_cache_c = RedisConnection(
        "Redis Cache DB",
        r_cache_cfg.host,
        r_cache_cfg.port,
        r_cache_cfg.db,
        r_cache_cfg.password,
        log,
    )
    return redis_cache_c


def init_mysql_conn(config: Settings, metrics: AppMetrics, log: Logger):
    mysql_cfg = config.mysql
    mysql_conn = MySQLConnection("Mysql Chat DB", mysql_cfg.url, metrics, log)
    return mysql_conn


def init_rabbitmq_conn(config: Settings, log: Logger):
    rb_cfg = config.rabbitmq
    rb_c = RabbitMQConnection(
        "Chat Publisher Connection", rb_cfg.urls, rb_cfg.event_route, log
    )
    return rb_c


def init_rabbitmq_conn_v2(config: Settings, log: Logger):
    rb_cfg = config.rabbitmq
    rb_c = RabbitMQConnectionV2(
        "Chat Publisher Connection", rb_cfg.urls, rb_cfg.event_route, log
    )
    return rb_c


def init_rabbitmq_producer(
    conf, conn: RabbitMQConnection, log, metrics: AppMetrics
):
    rb_producer = AmqpProducer(
        "Api-producer", AmqpClient(conn, log), log, metrics
    )
    return rb_producer


class AppConnContainer(DeclarativeContainer):
    conf: providers.Dependency[Settings] = providers.Dependency()
    log = providers.Dependency(Logger)
    metrics = providers.Dependency(AppMetrics)

    cas_c = providers.Resource(init_cassandra_conn, conf, log)
    mqtt_c = providers.Resource(init_mqtt_conn, conf, log)
    mqtt_pub = providers.Resource(init_mqtt_publisher, conf, mqtt_c, log)
    mysql_c = providers.Resource(init_mysql_conn, conf, metrics, log)
    redis_persist_c = providers.Resource(init_redis_persistent_conn, conf, log)
    redis_thread_c = providers.Resource(init_redis_thread_conn, conf, log)
    redis_msg_c = providers.Resource(init_redis_message_conn, conf, log)
    redis_cache_c = providers.Resource(init_cache_conn, conf, log)

    rabbitmq_c = providers.Resource(init_rabbitmq_conn, conf, log)
    rabbitmq_c_v2 = providers.Resource(init_rabbitmq_conn_v2, conf, log)

    rabbitmq_pub = providers.Resource(
        init_rabbitmq_producer, conf, rabbitmq_c, log, metrics
    )

    app_conns = providers.Singleton(
        AppConnections,
        mqtt_c,
        mqtt_pub,
        mysql_c,
        cas_c,
        rabbitmq_c,
        rabbitmq_c_v2,
        rabbitmq_pub,
        redis_persist_c,
        redis_thread_c,
        redis_msg_c,
        redis_cache_c,
    )
