import os

from dependency_injector import providers
from dependency_injector.containers import Declar<PERSON><PERSON>ontainer

from chat.app import <PERSON>A<PERSON>
from chat.app.broker import AppBrokers
from chat.app.connection import AppConnections
from chat.app.repository import AppRepositories
from chat.block_users.container import BlockUsersContainer
from chat.config import Settings, load_config
from chat.container.log import init_logger
from chat.dist_lock import DistLockManager
from chat.folders.container import FolderContainer
from chat.health_check.container import HealthCheckContainer
from chat.messages.container import MessageContainer
from chat.metrics.container import MetricsContainer
from chat.metrics.model import AppMetrics
from chat.participants.container import ParticipantContainer
from chat.publishers.rabbitmq import AmqpProducer
from chat.quick_message.container import QuickMessageContainer
from chat.threads.container import ThreadContainer
from chat.tracking.container import TrackingContainer
from chat.users.container import UserContainer

from .connection import AppConnContainer
from .repository import AppRepoContainer


def init_conf():
    # try to pass custom env_file
    env_file = os.environ.get("ENV_FILE", ".env.prod")
    return load_config(env_file)


def get_app_conns(container: AppConnContainer) -> AppConnections:
    return container.app_conns()


def get_amqp_publisher(container: AppConnContainer) -> AmqpProducer:
    return container.rabbitmq_pub()  # type: ignore


def get_app_repos(container: AppRepoContainer) -> AppRepositories:
    return container.app_repos()


METRICS = AppMetrics()


def init_app_metrics():
    print("INIT app metrics ...")
    return METRICS


class HealthContainer(DeclarativeContainer):
    conf: providers.Resource[Settings] = providers.Resource(init_conf)
    log = providers.Resource(init_logger, conf=conf)
    app_metrics = providers.Resource(init_app_metrics)
    conn_container = providers.Container(
        AppConnContainer, conf=conf, log=log, metrics=app_metrics
    )
    lock_manager = providers.Singleton(
        DistLockManager, redis_c=conn_container.redis_persist_c
    )
    conns = providers.Resource(get_app_conns, container=conn_container)
    brokers = providers.Singleton(
        AppBrokers,
        conf,
        log,
        conn_container.redis_persist_c,
        conn_container.rabbitmq_pub,
        conn_container.mqtt_pub,
        lock_manager,
    )

    repo_container = providers.Container(
        AppRepoContainer,
        conf=conf,
        log=log,
        conn_container=conn_container,
        metrics=app_metrics,
    )
    repos = providers.Resource(get_app_repos, container=repo_container)
    app = providers.Singleton(
        MyApp,
        conf=conf,
        log=log,
        app_conns=conns,
        app_repos=repos,
        app_brokers=brokers,
        app_metrics=app_metrics,
        lock_manager=lock_manager,
    )

    health = providers.Container(HealthCheckContainer, app=app)


class AppContainer(DeclarativeContainer):

    conf: providers.Resource[Settings] = providers.Resource(init_conf)
    log = providers.Resource(init_logger, conf=conf)
    app_metrics = providers.Resource(init_app_metrics)

    conn_container = providers.Container(
        AppConnContainer, conf=conf, log=log, metrics=app_metrics
    )
    # conns: Container[AppConnections] = conn_container.app_conns
    conns = providers.Resource(get_app_conns, container=conn_container)

    lock_manager = providers.Singleton(
        DistLockManager, redis_c=conn_container.redis_persist_c
    )

    repo_container = providers.Container(
        AppRepoContainer,
        conf=conf,
        log=log,
        conn_container=conn_container,
        metrics=app_metrics,
    )
    repos = providers.Resource(get_app_repos, container=repo_container)
    # amqp = providers.Resource(get_amqp_publisher, container=repo_container)

    brokers = providers.Singleton(
        AppBrokers,
        conf,
        log,
        conn_container.redis_persist_c,
        conn_container.rabbitmq_pub,
        conn_container.mqtt_pub,
        lock_manager,
    )

    app = providers.Singleton(
        MyApp,
        conf=conf,
        log=log,
        app_conns=conns,
        app_repos=repos,
        app_brokers=brokers,
        app_metrics=app_metrics,
        lock_manager=lock_manager,
    )

    # containers
    thread = providers.Container(ThreadContainer, app=app)

    # TODO: refactor other modules
    block_users = providers.Container(BlockUsersContainer, app=app)
    participant = providers.Container(
        ParticipantContainer, app=app, thread_usc=thread.usecases
    )
    user = providers.Container(UserContainer, app=app)

    message = providers.Container(
        MessageContainer,
        app=app,
        thread_usc=thread.usecases,
        participant_usc=participant.usecases,
    )

    folder = providers.Container(
        FolderContainer,
        app=app,
        thread_usc=thread.usecases,
        pt_usc=participant.usecases,
    )
    health = providers.Container(HealthCheckContainer, app=app)

    metrics = providers.Container(MetricsContainer, app=app)

    tracking = providers.Container(TrackingContainer, app=app)

    quick_message = providers.Container(QuickMessageContainer, app=app)