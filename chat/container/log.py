import logging
import sys
import traceback
from io import String<PERSON>
from typing import Any, Dict, List

import structlog
from structlog.contextvars import merge_contextvars
from structlog.tracebacks import extract
from structlog.types import EventDict, ExcInfo
from structlog_sentry import SentryProcessor

from chat.config import Settings
from chat.utils.json import json_dumps

SHOW_LOCALS = True
LOCALS_MAX_STRING = 200
MAX_FRAMES = 5

IGNORE_VARIABLES = [
    "conf",
    "log",
    "config",
    "password",
    "passwd",
    "secret",
    "key",
    "auth",
]
IGNORE_KEYWORDS = [
    "password",
    "secret",
    "key",
    "private_key",
    "api_key",
    "passwd",
    "auth",
]


def filter_local_variables(locals):
    """Only keeps local variables ."""
    filtered_vars = {}
    for name, value in locals.items():
        if name.startswith("__"):
            continue

        if name.lower() in IGNORE_VARIABLES:
            continue

        if (
            value.startswith("<function")
            or value.startswith("<class")
            or value.startswith("'typing.")
            or value.startswith("typing.")
            or value.startswith("<module")
            or value.startswith('"<module')
        ):
            continue
        filtered_vars[name] = value
    return filtered_vars


def _figure_out_exc_info(v: Any) -> ExcInfo:
    """
    Depending on the Python version will try to do the smartest thing possible
    to transform *v* into an ``exc_info`` tuple.
    """
    if isinstance(v, BaseException):
        return (v.__class__, v, v.__traceback__)
    elif isinstance(v, tuple):
        return v  # type: ignore
    elif v:
        return sys.exc_info()  # type: ignore

    return v  # type: ignore


def _format_exception(exc_info: Any, local_vars: Dict) -> str:
    sio = StringIO()

    traceback.print_exception(exc_info[0], exc_info[1], exc_info[2], None, sio)
    s = sio.getvalue()
    sio.close()
    if s[-1:] == "\n":
        s = s[:-1]

    if local_vars:
        var_logs = "\n".join(
            line
            for line in [
                "\n\n[DEBUG] LOCAL VARS:",
                *[
                    "- {} = {}".format(name, val)
                    for name, val in local_vars.items()
                ],
            ]
            if not _contain_sensitive_keywords(line)
        )
        s += "\n" + var_logs

    return s


def _contain_sensitive_keywords(text: str):
    text = text.lower()
    for keyword in IGNORE_KEYWORDS:
        if keyword in text:
            return True
    return False


class CustomExceptionRenderer:
    def __init__(
        self,
    ) -> None:
        pass

    def __call__(self, logger, name: str, event_dict: EventDict) -> EventDict:
        exc_info = event_dict.pop("exc_info", None)
        if exc_info:
            exc_info = _figure_out_exc_info(exc_info)

            if exc_info is None or exc_info[0] is None:
                # exc_info can be (None, None, None)
                event_dict["exception"] = "NoneType: None"
                return event_dict

            trace = extract(
                *exc_info,
                show_locals=True,
                locals_max_string=LOCALS_MAX_STRING,
            )

            frame_local_vars = []
            for stack in trace.stacks:
                for frame in stack.frames:
                    frame_local_vars.append(
                        filter_local_variables(frame.locals)
                    )

            last_frame_local_vars = (
                frame_local_vars[-1] if len(frame_local_vars) > 0 else None
            )

            # Enable this to show all local variables in different frames
            # event_dict['local_vars'] = frame_local_vars
            event_dict["exception"] = _format_exception(
                exc_info, last_frame_local_vars
            )

        return event_dict


def init_logger(conf: Settings):
    """Setup structlog and return a logging.Logger instance."""
    # logzero.loglevel(logging.INFO)
    # LOG_FORMAT = "%(levelname)s %(asctime)s - %(message)s"
    # logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
    # logger = logging.getLogger()

    api_logger = logging.getLogger("api")
    api_logger.addHandler(logging.StreamHandler())
    api_logger.setLevel(logging.INFO)

    def logger_factory(*args, **kwargs):
        return api_logger

    use_json_format = conf.use_json_log
    timestamper = structlog.processors.TimeStamper(
        fmt="%Y-%m-%d %H:%M:%S.%f"
    )

    def rename_event_key(_, __, ed):
        ed["message"] = ed.pop("event")
        return ed

    shared_processors: List[Any] = [
        # structlog.threadlocal.merge_threadlocal,
        # structlog.stdlib.add_logger_name,
        merge_contextvars,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.stdlib.add_log_level,
        timestamper,
        SentryProcessor(event_level=logging.ERROR),
        # rename_event_key,
    ]

    def print_exception(_, __, event_dict):
        if "exception" in event_dict:
            print(event_dict["exception"])

        return event_dict

    if use_json_format:
        formatter = structlog.stdlib.ProcessorFormatter(
            # These run ONLY on `logging` entries that do NOT originate within
            # structlog.
            foreign_pre_chain=shared_processors,
            # These run on ALL entries after the pre_chain is done.
            processors=[
                CustomExceptionRenderer(),
                # print_exception,
                # Remove _record & _from_structlog.
                structlog.processors.CallsiteParameterAdder(
                    {
                        structlog.processors.CallsiteParameter.FILENAME,
                        structlog.processors.CallsiteParameter.FUNC_NAME,
                        structlog.processors.CallsiteParameter.LINENO,
                    }
                ),
                structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                structlog.processors.JSONRenderer(serializer=json_dumps),
            ],
        )

        structlog.configure(
            processors=shared_processors
            + [
                CustomExceptionRenderer(),
                # print_exception,
                structlog.processors.JSONRenderer(serializer=json_dumps),
            ],
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=logger_factory,
            cache_logger_on_first_use=True,
        )
    else:
        formatter = structlog.stdlib.ProcessorFormatter(
            foreign_pre_chain=shared_processors,
            processors=[
                # Remove _record & _from_structlog.
                structlog.processors.CallsiteParameterAdder(
                    {
                        structlog.processors.CallsiteParameter.FILENAME,
                        structlog.processors.CallsiteParameter.FUNC_NAME,
                        structlog.processors.CallsiteParameter.LINENO,
                    }
                ),
                structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                structlog.dev.ConsoleRenderer(),
            ],
        )

        structlog.configure(
            processors=shared_processors
            + [
                structlog.dev.ConsoleRenderer(),
            ],
            logger_factory=logger_factory,
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger = logging.getLogger("app")
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    return logger
