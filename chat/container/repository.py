from logging import Logger

from dependency_injector import containers, providers
from dependency_injector.providers import Singleton

from chat.app.repository import AppRepositories
from chat.block_users.repository import BlockUserRepositoryMySQL
from chat.block_users.repository.interface import BlockUserRepository
from chat.cache import RedisCache
from chat.config import Settings
from chat.connections.gapo_client import GapoClient
from chat.counter.redis import Redis<PERSON>ounter
from chat.folders.repository import FolderRepository, FolderRepositoryMySQL
from chat.health_check.repository import (
    HealthCheckRepositoryCassandra,
    HealthCheckRepositoryMySQL,
    HealthCheckRepositoryRedis,
    RevisionRepository,
)
from chat.health_check.repository.composite import (
    CompositeHealthCheckRepository,
)
from chat.messages.repository import (
    MessageCache,
    MessageRepository,
    MessageRepositoryCassandra,
)
from chat.metrics.model import AppMetrics
from chat.participants.repository import (
    ParticipantCache,
    ParticipantThreadRepository,
    ParticipantThreadRepositoryMySQL,
)
from chat.quick_message.repository.interface import QuickMessageRepository
from chat.quick_message.repository.mysql import QuickMessageRepositoryMySQL
from chat.repositories.media_collection import MediaCollectionRepositoryMySQL
from chat.repositories.pin_collection import PinCollectionRepositoryMySQL
from chat.repositories.rate_limit import RateLimitRedisRepository
from chat.repositories.read import RedisThreadReadRepo
from chat.service_storages.external_info import InfoStorage
from chat.threads.repository import (
    ThreadCache,
    ThreadRepository,
    ThreadRepositoryMySQL,
)
from chat.users.repository import UserRepository, UserRepositoryMySQL


def init_gapo_client(conf: Settings, log: Logger, metrics: AppMetrics):
    return GapoClient(conf, log, metrics)


class AppRepoContainer(containers.DeclarativeContainer):
    conf: providers.Dependency[Settings] = providers.Dependency()
    log = providers.Dependency(Logger)
    metrics = providers.Dependency(AppMetrics)

    conn_container = providers.DependenciesContainer()
    thread_cache: providers.Singleton[ThreadCache] = providers.Singleton(
        ThreadCache, log, conn_container.redis_thread_c
    )

    part_cache: providers.Singleton[ParticipantCache] = providers.Singleton(
        ParticipantCache, log, conn_container.redis_thread_c
    )
    message_cache: providers.Singleton[MessageCache] = providers.Singleton(
        MessageCache, log, conn_container.redis_msg_c
    )

    quick_message_repo: providers.Singleton[QuickMessageRepository] = providers.Singleton(
        QuickMessageRepositoryMySQL
    )

    thread_read_repo = providers.Singleton(
        RedisThreadReadRepo, conf, conn_container.redis_persist_c
    )
    thread_repo: Singleton[ThreadRepository] = providers.Singleton(
        ThreadRepositoryMySQL, log, thread_cache
    )
    block_repo: Singleton[BlockUserRepository] = providers.Singleton(
        BlockUserRepositoryMySQL, log
    )
    media_collection_repo = providers.Singleton(
        MediaCollectionRepositoryMySQL, log
    )
    pt_repo: Singleton[ParticipantThreadRepository] = providers.Singleton(
        ParticipantThreadRepositoryMySQL, log, part_cache
    )

    cache_repo = providers.Singleton(
        RedisCache, conf, conn_container.redis_cache_c
    )
    counter_repo: providers.Singleton[RedisCounter] = providers.Singleton(
        RedisCounter, conf, log, conn_container.redis_persist_c
    )

    # health check
    gapo_client = providers.Singleton(GapoClient, conf, log, metrics)
    info_repo: Singleton[InfoStorage] = providers.Singleton(
        InfoStorage, gapo_client, cache_repo
    )

    user_repo: Singleton[UserRepository] = providers.Singleton(
        UserRepositoryMySQL, log
    )
    message_repo: Singleton[MessageRepository] = providers.Singleton(
        MessageRepositoryCassandra,
        conf,
        log,
        conn_container.cas_c,
        message_cache,
    )
    rate_limit_repo = providers.Singleton(
        RateLimitRedisRepository, conn_container.redis_persist_c
    )

    # folder feature
    pf_repo: Singleton[FolderRepository] = providers.Singleton(
        FolderRepositoryMySQL, log
    )

    pin_collection_repo = providers.Singleton(
        PinCollectionRepositoryMySQL, log
    )

    # healthcheck related repos
    hc_redis_repo = providers.Singleton(
        HealthCheckRepositoryRedis,
        log,
        providers.List(
            conn_container.redis_persist_c,
            conn_container.redis_cache_c,
            conn_container.redis_msg_c,
            conn_container.redis_thread_c,
        ),
    )
    hc_cas_repo = providers.Singleton(
        HealthCheckRepositoryCassandra, conf, log, conn_container.cas_c
    )
    hc_mysql_repo = providers.Singleton(
        HealthCheckRepositoryMySQL, conf, log, conn_container.mysql_c
    )
    hc_repo = providers.Singleton(
        CompositeHealthCheckRepository,
        repos=providers.List(hc_redis_repo, hc_cas_repo, hc_mysql_repo),
    )
    revision_db = providers.Singleton(
        RevisionRepository, conf, log, conn_container.mysql_c
    )

    # gapo = providers.Factory(
    # GapoClient, conf, log
    # )
    gapo = providers.Resource(init_gapo_client, conf, log, metrics)

    app_repos = providers.Singleton(
        AppRepositories,
        gapo,
        thread_repo,
        pin_collection_repo,
        pf_repo,
        user_repo,
        pt_repo,
        block_repo,
        message_repo,
        media_collection_repo,
        rate_limit_repo,
        thread_read_repo,
        info_repo,
        counter_repo,
        hc_repo,
        revision_db,
        thread_cache,
        part_cache,
        message_cache,
        quick_message_repo,
    )
