import traceback
from typing import Any, Dict, List, Optional, <PERSON>ple

import falcon
import structlog
from cassandra import Driver<PERSON>x<PERSON>
from paho.mqtt import MQ<PERSON><PERSON>xception
from pika.exceptions import AMQPError
from pydantic import ValidationError
from sqlalchemy.exc import Integ<PERSON><PERSON><PERSON><PERSON>, SQLAlchemyError

from chat import codes, response_messages
from chat.i18n import T
from chat.metrics.model import AppMetrics
from chat.utils.json import json_dumps


class ChatError(Exception):
    """Represents a generic HTTP error.

    Every exceptions should extend from this class.
    The service will handle exception correctly without making
    explcit http response.

    """

    i18n_message = response_messages.INTERNAL_ERROR
    """i18n message. """

    http_code = 500
    """Output http code """

    error = "internal_error"
    """Short, human readable code to describe error. """

    code = None
    """Custom code number (optional). """

    is_value = False
    loc = None

    def __init__(
        self,
        message: str = "",
        code: Optional[int] = None,
        error_details: Optional[List[Any]] = None,
        **params,
    ):
        if code:
            self.code = code
        self._params = params
        self.message = self.i18n_message
        self.error_details = error_details

    def to_dict(self):
        """Output the response format."""
        res = {
            "error": self.error,
            "message": self.message,
        }
        if self.error_details is not None:
            res["error_details"] = self.error_details  # type: ignore

        if self.code is not None:
            res["code"] = self.code  # type: ignore
        return res

    def translate(self, locale="vi"):
        if self.is_value:
            msg = T(self.i18n_message, locale=locale, **self._params)
            self.message = [
                {"loc": [self.loc], "msg": msg, "type": "value_error"},
            ]  # type: ignore
        else:
            self.message = T(self.i18n_message, locale=locale, **self._params)

    def __repr__(self):
        return "<%s: error=%s, status=%s>" % (
            self.__class__.__name__,
            self.error,
            self.http_code,
        )


class CustomHTTPError(Exception):
    def __init__(self, http_code: int, error: Dict):
        self.code = 500
        # guard check if user passing falcon status code such as
        # falcon.HTTP_400
        if isinstance(http_code, str):
            try:
                self.code = falcon.http_status_to_code(http_code)
            except Exception:
                self.code = 500
        else:
            self.code = http_code
        self.body = error

    def to_dict(self):
        return self.body


class External400(ChatError):
    http_code = 400
    error = "invalid_parameters"
    i18n_message = response_messages.INVALID_PARAMETERS


class RestrictedMembersInChatCreation(ChatError):
    http_code = 400
    error = "restricted_memers_per_chat"
    i18n_message = response_messages.RESTRICTED_MEMBERS_PER_CHAT


class AddingMembersDisabled(ChatError):
    http_code = 400
    error = "adding_member_disabled"
    i18n_message = response_messages.ADDING_MEMBERS_DISABLED


class LockTimeoutError(ChatError):
    http_code = 500
    error = "lock_error"
    i18n_message = response_messages.LOCK_ERROR


class External404(ChatError):
    http_code = 404
    error = "external_not_found"
    i18n_message = response_messages.EXTERNAL_NOT_FOUND


class PermissionDenied(ChatError):
    http_code = 403
    error = "access_denied"
    i18n_message = response_messages.PERMISSION_DENIED


class OnlyAdminCanCreate(ChatError):
    http_code = 403
    error = "access_denied"
    i18n_message = response_messages.PERMISSION_DENIED


class WorkspaceDisable(ChatError):
    http_code = 403
    error = "access_denied"
    i18n_message = response_messages.WORKSPACE_DISABLE


class DataNotFound(ChatError):
    http_code = 404
    i18n_message = response_messages.MESSAGE_NOT_FOUND
    code = codes.NOT_FOUND_CODE
    error = "not_found"


class UserNotFound(ChatError):
    http_code = 404
    error = "user_not_found"
    i18n_message = response_messages.USER_NOT_FOUND


class UserNotInThread(ChatError):
    http_code = 403
    error = "user_not_in_thread"
    i18n_message = response_messages.USER_NOT_BELONG


class AllUserStillRemainThread(ChatError):
    http_code = 400
    i18n_message = response_messages.ALL_MEMBER_REMAIN
    error = "all_member_remain"


class AllUserNotInThread(ChatError):
    http_code = 400
    i18n_message = response_messages.ALL_MEMBER_NOT_BELONG
    error = "all_members_not_belong"


class DirectThreadLimit(Exception):
    pass


class NoMoreData(ChatError):
    http_code = 400
    i18n_message = response_messages.NO_MORE_DATA
    error = "no_members"


class RateLimitExceeded(ChatError):
    http_code = 429
    i18n_message = response_messages.LIMIT_NOT_FRIEND
    code = codes.RATE_LIMIT_REACHED
    error = "ratelimited"


class PageNotFound(ChatError):
    http_code = 400
    i18n_message = response_messages.PAGE_NOT_FOUND
    error = "page_not_found"


class QueueIsFull(ChatError):
    http_code = 429
    i18n_message = response_messages.LIMIT_QUERY
    error = "queue_fulled"


class QueueError(ChatError):
    http_code = 500
    i18n_message = response_messages.INTERNAL_ERROR
    error = "queue_error"


class ConflictData(ChatError):
    http_code = 409
    i18n_message = response_messages.CONFLICT
    error = "conflict"


class ThreadBlockedByUserError(ChatError):
    http_code = 403
    i18n_message = response_messages.PERMISSION_DENIED
    code = codes.BLOCKED_CODE
    error = "thread_blocked"


class ReachedMaxPin(ChatError):
    http_code = 409
    i18n_message = response_messages.LIMIT_PIN
    code = codes.MAX_PIN
    error = "max_pins_reached"


class MessageLimitedDeleteTime(ChatError):
    http_code = 403
    code = codes.LIMIT_TIME_CODE
    i18n_message = response_messages.LIMIT_DELETE
    error = "edit_period_is_over"


class MessageAlreadyDeleted(Exception):
    pass


class ThreadNotFoundByLink(ChatError):
    http_code = 404
    i18n_message = response_messages.THREAD_NOT_FOUND
    error = "thread_not_found"


class WrongPasscode(ChatError):
    i18n_message = response_messages.WRONG_PASS_CODE
    code = codes.WRONG_PASS_CODE
    error = "wrong_passcode"
    http_code = 403


class MissingPasscode(ChatError):
    i18n_message = response_messages.MISSING_PASS_CODE
    code = codes.WRONG_PASS_CODE
    error = "missing_passcode"
    http_code = 401


class MessageNotFound(ChatError):
    i18n_message = response_messages.MESSAGE_NOT_FOUND
    http_code = 404
    error = "message_not_found"


class RateLimitReached(ChatError):
    i18n_message = response_messages.RATE_LIMIT_REACHED
    http_code = 429
    error = "rate_limit_reached"


class InternalServerError(ChatError):
    i18n_message = response_messages.INTERNAL_ERROR
    http_code = 500
    error = "internal_error"


class SendMessageForbidden(ChatError):
    http_code = 403
    code = codes.SEND_MESSAGE_FORBIDDEN
    i18n_message = response_messages.SEND_MESSAGE_FORBIDDEN
    error = "access_denied"


class FolderNameExistsError(ChatError):
    http_code = 409
    error = "folder_exists"
    i18n_message = response_messages.FOLDER_NAME_EXISTS


class UserBlockingFeatureDisabled(ChatError):
    http_code = 409
    error = "feature_disabled"
    i18n_message = response_messages.BLOCKING_USERS_FEATURE_DISABLED


class UserSecretFeatureDisabled(ChatError):
    http_code = 409
    error = "feature_disabled"
    i18n_message = response_messages.SECRET_FEATURE_DISABLED


class MultipleErrors(ChatError):
    """Wrapper for multiple errors."""

    def __init__(
        self,
        http_code: int,
        error_msg: str,
        errors: List[Tuple[str, Exception]],
        **params,
    ):
        self.http_code = http_code
        self.i18n_message = error_msg
        self.errors = errors
        self._params = params

    def translate(self, locale="vi"):
        super().translate(locale)
        self.message = T(self.i18n_message, locale=locale)

        # localize all errors
        error_details = []
        for loc, err in self.errors:
            localized_error = self._localize_error_msg(err, locale)
            error_details.append({"loc": [loc], "msg": localized_error})
            self.error_details = error_details

    def _localize_error_msg(self, err: Exception, locale: str) -> str:
        if isinstance(err, ChatError):
            return T(err.i18n_message, locale=locale)
        elif isinstance(err, CustomHTTPError):
            return "Unknown error"
        return T(response_messages.INTERNAL_ERROR, locale=locale)


class InvalidParameters(ChatError):
    http_code = 400
    i18n_message = response_messages.INVALID_PARAMETERS
    error = "invalid_parameters"


class ThreadNotFound(ChatError):
    http_code = 404
    i18n_message = response_messages.THREAD_NOT_FOUND
    error = "thread_not_found"


class CantCreateThread(ChatError):
    http_code = 404
    i18n_message = response_messages.THREAD_CANT_CREATE
    error = "cant_create_thread"


class OutOfMsgDeletionPeriodError(ChatError):
    http_code = 403
    i18n_message = response_messages.OUT_OF_MSG_DELETION_PERIOD
    error = "out_of_msg_deletion_period"


class OutOfMsgEditPeriodError(ChatError):
    http_code = 403
    i18n_message = response_messages.OUT_OF_MSG_EDIT_PERIOD
    error = "out_of_msg_edit_period"


def _get_client_ip(req: falcon.Request):
    # access_route store all client IPs
    # the real IP is the first ip
    client_ips = req.access_route
    return (
        client_ips[0]
        if (isinstance(client_ips, list) and len(client_ips) > 0)
        else None
    )


def get_log_params(req: falcon.Request, status_code: int):
    params = {}
    context = req.context

    request_id = context.get("request_id", None)
    if request_id:
        params["request_id"] = request_id

    user_id = context.get("user_id", None)
    if user_id:
        params["user_id"] = user_id

    workspace_id = context.get("workspace_id", None)
    if workspace_id:
        params["workspace_id"] = workspace_id

    duration = context.get("duration", None)
    if duration:
        params["duration"] = round(duration * 1000, 1)

    body = context.get("body", None)
    if body:
        try:
            params["body"] = json_dumps(body, indent=2)
        except Exception:
            pass

    user_agent = req.get_header("user-agent")
    if user_agent:
        params["ua"] = user_agent

    auth_type = context.get("auth_type", None)
    if auth_type:
        params["auth_type"] = auth_type

    params["status"] = status_code

    params["ip"] = _get_client_ip(req)

    return params


def _get_exception_trace(exc):
    try:
        return "".join(
            traceback.TracebackException.from_exception(exc).format()
        )
    except Exception:
        return "Exception: Unknown"


def add_error_handlers(
    api: falcon.API,
    logger=None,
    metrics: Optional[AppMetrics] = None,
    include_server_error_trace=False,
):
    """Add app error handlers to falcon App."""

    if logger is None:
        logger = structlog.get_logger()

    def handle_chat_error(
        req: falcon.Request, resp: falcon.Response, ex: ChatError, params
    ):
        if ex.http_code >= 500:
            request_id = req.context.get("request_id", None)

            logger.exception(
                "Chat error, details={} request_id: {}".format(
                    ex.error_details, request_id
                ),
                exc_info=True,
                **get_log_params(req, ex.http_code),
            )
            # hide detail errors
            if not include_server_error_trace:
                ex.error_details = []

        _response_error(req, resp, ex)

    def handle_validation_error(
        req: falcon.Request, resp: falcon.Response, ex: ValidationError, params
    ):
        request_id = req.context.get("request_id", None)

        logger.exception(
            "Validation error: {}, request_id: {}".format(ex, request_id),
            exc_info=True,
            **get_log_params(req, 500),
        )

        masked_error = InvalidParameters(error_details=ex.errors())
        _response_error(req, resp, masked_error, "validation_error")

    def handle_sql_error(
        req: falcon.Request, resp: falcon.Response, ex: SQLAlchemyError, params
    ):
        if not isinstance(ex, IntegrityError):
            # ignore SQL integrity error (i.e dupplicate entry on same index)
            request_id = req.context.get("request_id", None)

            logger.exception(
                "SQL error: {}, request_id: {}".format(ex, request_id),
                exc_info=True,
                **get_log_params(req, 500),
            )
        masked_error = InternalServerError(error_details=[])
        if include_server_error_trace:
            masked_error.error_details = [{"msg": _get_exception_trace(ex)}]

        _response_error(req, resp, masked_error, "sql_error")

    def handle_custom_http_error(
        req: falcon.Request, resp: falcon.Response, ex: CustomHTTPError, params
    ):
        if ex.code >= 500:
            request_id = req.context.get("request_id", None)

            logger.exception(
                "Custom http error: {}, request_id: {}".format(ex, request_id),
                exc_info=True,
                **get_log_params(req, ex.code),
            )

        resp.media = ex.to_dict()
        # passing request_id if it's set (i.e by RequestIDMiddleware)
        request_id = req.context.get("request_id", None)
        if request_id is not None:
            resp.media["request_id"] = request_id

        req.context.error_code = "custom_http_error"

        resp.status = ex.code

    def handle_mqtt_error(
        req: falcon.Request, resp: falcon.Response, ex: MQTTException, params
    ):
        if metrics:
            metrics.mqtt_call_errors.inc()

        request_id = req.context.get("request_id", None)
        logger.exception(
            "MQTT error: {}, request_id: {}".format(ex, request_id),
            exc_info=True,
            **get_log_params(req, 500),
        )

        masked_error = InternalServerError(error_details=[])
        if include_server_error_trace:
            masked_error.error_details = [{"msg": _get_exception_trace(ex)}]

        _response_error(req, resp, masked_error, "mqtt_error")

    def handle_amqp_error(
        req: falcon.Request, resp: falcon.Response, ex: AMQPError, params
    ):
        if metrics:
            metrics.amqp_call_errors.inc()

        request_id = req.context.get("request_id", None)
        logger.exception(
            "AMQP error: {}, request_id: {}".format(ex, request_id),
            exc_info=True,
            **get_log_params(req, 500),
        )

        masked_error = InternalServerError(error_details=[])
        if include_server_error_trace:
            masked_error.error_details = [{"msg": _get_exception_trace(ex)}]

        _response_error(req, resp, masked_error, "amqp_error")

    def handle_cassandra_error(
        req: falcon.Request, resp: falcon.Response, ex: ValidationError, params
    ):
        request_id = req.context.get("request_id", None)
        logger.exception(
            "Cassandra error: {}, request_id: {}".format(ex, request_id),
            exc_info=True,
            **get_log_params(req, 500),
        )

        masked_error = InternalServerError(error_details=[])
        if include_server_error_trace:
            masked_error.error_details = [{"msg": _get_exception_trace(ex)}]

        _response_error(req, resp, masked_error, "cassandra_error")

    def handle_general_exception(
        req: falcon.Request, resp: falcon.Response, ex: Exception, params
    ):
        request_id = req.context.get("request_id", None)
        logger.exception(
            "Unknown internal server error: {}, request_id: {}".format(
                ex, request_id
            ),
            exc_info=True,
            **get_log_params(req, 500),
        )
        masked_error = InternalServerError(error_details=[])
        if include_server_error_trace:
            masked_error.error_details = [{"msg": _get_exception_trace(ex)}]

        _response_error(req, resp, masked_error, "unknown_error")

    def _response_error(
        req: falcon.Request,
        resp: falcon.Response,
        ex: ChatError,
        error_code=None,
    ):
        _translate_error_message(req, ex)
        resp.media = ex.to_dict()

        # passing request_id if it's set (i.e by RequestIDMiddleware)
        request_id = req.context.get("request_id", None)
        if request_id is not None:
            resp.media["request_id"] = request_id

        req.context.error_code = (
            error_code if error_code is not None else ex.error
        )

        resp.status = ex.http_code

    def _translate_error_message(req: falcon.Request, ex: ChatError):
        lang = req.get_header("x-gapo-lang", default="vi")
        ex.translate(lang)

    api.add_error_handler(ChatError, handle_chat_error)
    api.add_error_handler(CustomHTTPError, handle_custom_http_error)
    api.add_error_handler(MQTTException, handle_mqtt_error)
    api.add_error_handler(AMQPError, handle_amqp_error)
    api.add_error_handler(SQLAlchemyError, handle_sql_error)
    api.add_error_handler(DriverException, handle_cassandra_error)
    api.add_error_handler(ValidationError, handle_validation_error)
    api.add_error_handler(Exception, handle_general_exception)
