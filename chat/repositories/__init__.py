from sqlalchemy.orm import Session


class BaseRepository(object):
    def _q_insert(self, session: Session, query, payload):
        session.execute(query, payload)

    def _q_update(self, session: Session, query, params):
        session.execute(query, params)

    def _q_delete(self, session: Session, query, payload):
        session.execute(query, payload)

    def _q_item(self, session: Session, query, params):
        record = session.execute(query, params).fetchone()
        return dict(record) if record else None

    def _q_items(self, session: Session, query, params):
        records = session.execute(query, params).fetchall()
        return [dict(record) for record in records] if records else records
