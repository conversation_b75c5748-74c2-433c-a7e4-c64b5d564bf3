from typing import Dict, List

from chat.config import Settings
from chat.connections.redis_c import RedisConnection


class RedisThreadReadRepo(object):
    """A repository that is used to track who has read a message in a thread."""

    def __init__(self, conf: Settings, conn: RedisConnection):
        self._client = conn.client
        self._thread_key_f = conf.redis_pcc.member_read_key_format

    def get_most_recent_thread_viewers(
        self, thread_id: int, top_most_count: int
    ) -> List[str]:
        """Gets list of members who have recently read a thread."""

        key = self._thread_key_f.format(thread_id=thread_id)
        return self._client.zrange(key, start=-top_most_count, end=-1)

    def update_read(self, thread_id: int, user_id: str, message_id: int):
        """Marks an user has read a message in a thread."""

        key = self._thread_key_f.format(thread_id=thread_id)
        self._client.zadd(key, {user_id: message_id})

    def get_message_read_users_for_range(
        self, thread_id: int, from_msg_id: int, to_msg_id: int
    ) -> Dict[int, List[str]]:
        """Gets list of read user list for messages in a specific range."""

        key = self._thread_key_f.format(thread_id=thread_id)
        datas = self._client.zrangebyscore(
            key,
            min=from_msg_id,
            max=to_msg_id,
            withscores=True,
            score_cast_func=int,
        )

        infos: Dict = {}
        for data in datas:
            user_id, message_id = data
            user_reads = infos.get(message_id, [])
            user_reads.append(user_id)
            infos[message_id] = user_reads
        return infos
