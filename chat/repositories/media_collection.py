import time
from logging import Logger
from typing import Dict, List, Union

from sqlalchemy.orm import Session

from chat.utils.json import json_dumps


class MediaCollectionInsertController(object):
    t_name = "media_collection"

    def __init__(self):
        super().__init__()

    def _q_insert(self, session: Session, query, payload):
        session.execute(query, payload)

    def add_delete_log(
        self, session: Session, thread_id: int, message_id: int, user_id: str
    ):
        insert_delete_log_q = (
            "INSERT media_collection_delete_logs("
            "thread_id, message_id, user_id) "
            "VALUES(:thread_id, :message_id, :user_id);"
        )

        payload = {
            "thread_id": thread_id,
            "message_id": message_id,
            "user_id": user_id,
        }
        self._q_insert(session, insert_delete_log_q, payload)


class MediaCollectionDeleteController(object):
    t_name = "media_collection"

    def __init__(self):
        super().__init__()
        self._delete_media_q = (
            f"DELETE FROM {self.t_name} "
            "WHERE thread_id=:thread_id "
            "AND message_id=:message_id "
        )

    def _q_delete(self, session: Session, query, payload):
        session.execute(query, payload)

    def delete_media(self, session: Session, thread_id: int, message_id):
        params = {
            "thread_id": thread_id,
            "message_id": message_id,
        }
        self._q_delete(session, self._delete_media_q, params)

    def update_link(
        self,
        session: Session,
        thread_id: int,
        message_id: int,
        user_id: Union[str, int],
        detail,
    ):
        try:
            user_id = int(user_id)
            thread_id = int(thread_id)
            _type = 0
            _length = 1

            delete_query = (
                f"DELETE FROM {self.t_name} "
                "WHERE thread_id=:thread_id "
                "AND message_id=:message_id "
                "AND type=:type"
            )

            session.execute(
                delete_query,
                {
                    "thread_id": thread_id,
                    "message_id": message_id,
                    "type": _type,
                },
            )

            if len(detail["body"]) == 0:
                return

            insert_query = f"""INSERT INTO {self.t_name} (
                    thread_id, 
                    type,
                    message_id,
                    user_id,
                    created_at,
                    detail,
                    length
                ) 
                VALUES (
                    :thread_id,
                    :_type,
                    :message_id,
                    :user_id, 
                    :created_at,
                    :detail,
                    :_length
                )"""

            session.execute(
                insert_query,
                {
                    "thread_id": thread_id,
                    "_type": _type,
                    "message_id": message_id,
                    "user_id": user_id,
                    "created_at": int(time.time()) * 1000,
                    "detail": json_dumps(detail),
                    "_length": _length,
                },
            )

        except Exception as err:
            print(f"Error is {err}")


class MediaCollectionListController(object):
    t_name = "media_collection"

    def __init__(self):
        super().__init__()

        common_fields = [
            "mc._created AS _created",
            "mc._deleted AS _deleted",
            "mc.thread_id AS thread_id",
            "mc.user_id AS user_id",
            "mc.message_id AS message_id",
            "mc.type AS type",
            "mc.created_at AS created_at",
            "mc.detail AS detail",
            "mc.length AS length",
        ]
        self._fields = ", ".join(common_fields)

    def _q_items(self, session: Session, query, params) -> List[Dict]:
        records = session.execute(query, params).fetchall()
        return [dict(record) for record in records] if records else []

    def get_between(
        self,
        session,
        thread_id: int,
        user_id,
        upper_bound: int,
        lower_bound: int,
        media_type_number: int,
    ) -> List[Dict]:
        get_between_q = (
            f"SELECT {self._fields} "
            f"FROM {self.t_name} AS mc "
            "LEFT JOIN media_collection_delete_logs AS mcdl "
            "ON (((mcdl.thread_id = :thread_id) "
            "AND (mcdl.message_id = mc.message_id))) "
            "WHERE ((mc.thread_id = :thread_id) "
            "   AND (mc.type = :media_type_number) "
            "   AND ((isnull(mcdl.thread_id) "
            "       AND isnull(mcdl.message_id)) "
            "       OR (mcdl.user_id <> :user_id)) "
            "   AND (mc.message_id > :lower_bound) "
            "   AND (mc.message_id <= :upper_bound)) "
            "GROUP BY mc.thread_id, mc.type, mc.message_id "
            "ORDER BY mc.message_id DESC"
        )

        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "upper_bound": upper_bound,
            "lower_bound": lower_bound,
            "media_type_number": media_type_number,
        }
        return self._q_items(session, get_between_q, params)

    def get_by_range(
        self,
        session,
        thread_id: int,
        user_id,
        media_type_number: int,
        last_id: int = 0,
        delete_to: int = 0,
        page_size: int = 10,
    ) -> List[Dict]:
        if last_id == 0:
            params = {
                "thread_id": thread_id,
                "user_id": user_id,
                "page_size": page_size,
                "media_type_number": media_type_number,
                "delete_to": delete_to,
            }

            get_first_page_q = (
                f"SELECT {self._fields} "
                f"FROM {self.t_name} AS mc "
                "LEFT JOIN media_collection_delete_logs AS mcdl "
                "ON ((mcdl.thread_id = :thread_id) "
                "   AND (mcdl.message_id = mc.message_id)) "
                "WHERE ((mc.thread_id = :thread_id) "
                "   AND (mc.type = :media_type_number) "
                "   AND ((isnull(mcdl.thread_id) "
                "       AND isnull(mcdl.message_id)) "
                "       OR (mcdl.user_id <> :user_id)) "
                "   AND (mc.message_id > :delete_to)) "
                "GROUP BY mc.thread_id,  mc.type, mc.message_id "
                "ORDER BY mc.message_id DESC "
                "LIMIT :page_size"
            )
            query = get_first_page_q
        else:
            params = {
                "thread_id": thread_id,
                "user_id": user_id,
                "page_size": page_size,
                "media_type_number": media_type_number,
                "delete_to": delete_to,
                "last_id": last_id,
            }

            get_next_page_q = (
                f"SELECT {self._fields} "
                f"FROM {self.t_name} AS mc "
                "LEFT JOIN media_collection_delete_logs AS mcdl "
                "ON ((mcdl.thread_id = :thread_id) "
                "   AND (mcdl.message_id = mc.message_id)) "
                "WHERE ((mc.thread_id = :thread_id) "
                "   AND (mc.type = :media_type_number) "
                "   AND ((isnull(mcdl.thread_id) "
                "       AND isnull(mcdl.message_id)) "
                "       OR (mcdl.user_id <> :user_id)) "
                "   AND (mc.message_id > :delete_to) "
                "   AND (mc.message_id <= :last_id)) "
                "GROUP BY mc.thread_id,  mc.type, mc.message_id "
                "ORDER BY mc.message_id DESC "
                "LIMIT :page_size"
            )

            query = get_next_page_q

        return self._q_items(session, query, params)

    def get_all_media_by_id(
        self, session: Session, thread_id: int, message_id: int
    ) -> List[Dict]:
        get_all_media_by_id_q = (
            "SELECT mc.type AS type, "
            "mc.length AS length "
            f"FROM {self.t_name} AS mc "
            "WHERE ((mc.thread_id = :thread_id) "
            "   AND (mc.message_id = :message_id))"
        )

        params = {
            "thread_id": thread_id,
            "message_id": message_id,
        }
        return self._q_items(session, get_all_media_by_id_q, params)

    def get_all_media_by_user(
        self, session: Session, thread_id: int, message_id: int, user_id=None
    ):
        params = {"thread_id": thread_id, "message_id": message_id}
        if user_id:
            get_all_media_by_user_id_q = (
                "SELECT mc.type AS type, "
                "mc.length AS length, "
                "mcdl.user_id AS user_delete_id "
                f"FROM {self.t_name} AS mc "
                "LEFT JOIN media_collection_delete_logs AS mcdl "
                "ON (((mcdl.thread_id = :thread_id) "
                "   AND (mcdl.message_id = :message_id) "
                "   AND (mcdl.user_id = :user_id ))) "
                "WHERE ((mc.thread_id = :thread_id) "
                "   AND (mc.message_id = :message_id)) "
            )
            query = get_all_media_by_user_id_q
            params["user_id"] = user_id
        else:
            get_all_media_by_user_q = (
                "SELECT mc.type AS type, "
                "mc.length AS length, "
                "mcdl.user_id AS user_delete_id "
                f"FROM {self.t_name} AS mc "
                "LEFT JOIN media_collection_delete_logs AS mcdl "
                "ON (((mcdl.thread_id = :thread_id) "
                "   AND (mcdl.message_id = :message_id))) "
                "WHERE ((mc.thread_id = :thread_id) "
                "   AND (mc.message_id = :message_id)) "
            )
            query = get_all_media_by_user_q

        return self._q_items(session, query, params)

    def get_all_by_user(self, session: Session, thread_id: int, user_id: str):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""
        SELECT * FROM {self.t_name}
        WHERE thread_id=:thread_id AND user_id=:user_id
        """

        return self._q_items(session, query, params)


class MediaCollectionRepositoryMySQL(
    MediaCollectionInsertController,
    MediaCollectionDeleteController,
    MediaCollectionListController,
):
    t_name = "media_collections"

    def __init__(self, log: Logger):
        super().__init__()
        self.log = log
