from logging import Logger
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from chat.utils import common


class PinCollectionInsertController(object):
    t_name = "pin_collections"

    def __init__(self):
        super().__init__()

    def _q_insert(self, session: Session, query, payload):
        session.execute(query, payload)

    def insert(self, session: Session, thread_id: int, message_id: int):
        payload = {
            "created_at": common.now(),
            "thread_id": thread_id,
            "message_id": message_id,
        }

        insert_q = (
            f"INSERT {self.t_name}(created_at, thread_id, message_id) "
            "VALUES(:created_at, :thread_id, :message_id) "
            "ON DUPLICATE KEY UPDATE created_at=:created_at"
        )

        self._q_insert(session, insert_q, payload)


class PinCollectionDeleteController(object):
    t_name = "pin_collections"

    def __init__(self):
        super().__init__()

    def _q_delete(self, session: Session, query, payload):
        session.execute(query, payload)

    def delete(self, session: Session, thread_id: int, message_id: int):
        delete_q = (
            f"DELETE FROM {self.t_name} "
            "WHERE thread_id=:thread_id "
            "AND message_id=:message_id "
        )

        params = {
            "thread_id": thread_id,
            "message_id": message_id,
        }
        self._q_delete(session, delete_q, params)


class PinCollectionListController(object):
    t_name = "pin_collections"

    def __init__(self):
        super().__init__()

        common_fields = [
            "pc.thread_id AS thread_id",
            "pc.message_id AS message_id",
            "pc.created_at AS created_at",
        ]

        self._fields = ", ".join(common_fields)

    def _q_items(self, session: Session, query, params) -> List[Dict]:
        records = session.execute(query, params).fetchall()
        return [dict(record) for record in records] if records else []

    def get_between(
        self,
        session: Session,
        thread_id: int,
        upper_bound: int,
        lower_bound: int,
    ) -> List[Dict]:
        params = {
            "thread_id": thread_id,
            "upper_bound": upper_bound,
            "lower_bound": lower_bound,
        }
        get_between_q = (
            f"SELECT {self._fields} "
            f"FROM {self.t_name} AS pc "
            "WHERE pc.thread_id = :thread_id "
            "   AND pc.created_at > :lower_bound "
            "   AND pc.created_at <= :upper_bound "
            "ORDER BY pc.created_at DESC"
        )

        return self._q_items(session, get_between_q, params)

    def get_by_range(
        self,
        session: Session,
        thread_id: int,
        created_at: int = 0,
        page_size: int = 10,
        delete_to: int = 0,
    ) -> List[Dict]:
        if created_at == 0:
            params = {
                "thread_id": thread_id,
                "page_size": page_size,
                "delete_to": delete_to,
            }
            get_first_page_q = (
                f"SELECT {self._fields} "
                f"FROM {self.t_name} AS pc "
                "WHERE (pc.thread_id = :thread_id) "
                "   AND (pc.message_id > :delete_to)"
                "ORDER BY pc.created_at DESC "
                "LIMIT :page_size"
            )
            query = get_first_page_q
        else:
            params = {
                "thread_id": thread_id,
                "page_size": page_size,
                "created_at": created_at,
                "delete_to": delete_to,
            }
            get_next_page_q = (
                f"SELECT {self._fields} "
                f"FROM {self.t_name} AS pc "
                "WHERE (pc.thread_id = :thread_id) "
                "   AND (pc.created_at < :created_at) "
                "   AND (pc.message_id > :delete_to)"
                "ORDER BY pc.created_at DESC "
                "LIMIT :page_size"
            )

            query = get_next_page_q
        return self._q_items(session, query, params)


class PinItemController(object):
    t_name = "pin_collections"

    def __init__(self):
        super().__init__()

        common_fields = [
            "pc.thread_id AS thread_id",
            "pc.message_id AS message_id",
            "pc.created_at AS created_at",
        ]
        self._fields = ", ".join(common_fields)

    def _q_item(self, session: Session, query, params) -> Optional[Dict]:
        record = session.execute(query, params).fetchone()
        return dict(record) if record else None

    def get_latest_pin(
        self,
        session: Session,
        thread_id: int,
    ) -> Optional[Dict]:
        params = {
            "thread_id": thread_id,
        }
        get_lastest_q = (
            f"SELECT {self._fields} "
            f"FROM {self.t_name} AS pc "
            "WHERE pc.thread_id = :thread_id "
            "ORDER BY pc.created_at DESC "
            "LIMIT 1"
        )

        return self._q_item(session, get_lastest_q, params)

    def item(
        self, session: Session, thread_id: int, message_id: int
    ) -> Optional[Dict]:
        params = {
            "thread_id": thread_id,
            "message_id": message_id,
        }

        get_item_q = (
            f"SELECT {self._fields} "
            f"FROM {self.t_name} AS pc "
            "WHERE pc.thread_id = :thread_id "
            " AND pc.message_id = :message_id "
        )

        return self._q_item(session, get_item_q, params)


class PinCollectionRepositoryMySQL(
    PinCollectionInsertController,
    PinCollectionDeleteController,
    PinCollectionListController,
    PinItemController,
):
    t_name = "pin_collections"

    def __init__(self, log: Logger):
        super().__init__()
        self.log = log
