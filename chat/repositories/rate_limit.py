from chat.connections.redis_c import RedisConnection


class RateLimitRedisRepository(object):
    def __init__(self, conn: RedisConnection):
        self._client = conn.client
        self.user_resource_key_count = "ratelimit:{resource}:{user_id}:count"
        self.thread_resource_key_count = (
            "ratelimit:{resource}:thread:{thread_id}:user:{user_id}:count"
        )

        self.rate_limit_persecond = 5

    def is_pass_code_limit(self, user_id):
        key = self.user_resource_key_count.format(
            resource="pass_code", user_id=user_id
        )
        ttl = self._client.ttl(key)
        return ttl > 0

    def set_pass_code_limit(self, user_id):
        key = self.user_resource_key_count.format(
            resource="pass_code", user_id=user_id
        )
        count = self._client.incr(key)
        if not count % 5:
            self._client.expire(key, time=60 * 60)

    def del_pass_code_limit(self, user_id):
        key = self.user_resource_key_count.format(
            resource="pass_code", user_id=user_id
        )
        self._client.delete(key)

    def is_allowed(self, thread_id: int, user_id: str):
        resource = "post"
        key = self.thread_resource_key_count.format(
            resource=resource, thread_id=thread_id, user_id=user_id
        )
        count = self._client.incr(key)

        ttl = self._client.ttl(key)
        if ttl < 0:
            self._client.expire(key, time=1)

        return count < self.rate_limit_persecond
