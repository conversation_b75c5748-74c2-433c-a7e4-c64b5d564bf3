import falcon

from chat.app.resource import UserAuthResource
from chat.hooks import load_request_body
from chat.model import make_input
from chat.tracking.model import EventRequest, Level


class EventTrackingResource(UserAuthResource):
    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(EventRequest, raw)
        if errors:
            self.log.warning(errors)
            return

        if body.level == Level.error:
            self.log.exception(
                f"Received error tracking event from {self.user_id}, event: {body.event}, payload: {body.payload}",
                user_id=self.user_id,
                workspace_id=self.workspace_id,
                event_type=body.event,
                event_payload=body.payload,
                event_level=body.level,
                exc_info=False,
            )  # type: ignore
        else:
            self.log.info(
                "Got event",
                user_id=self.user_id,
                workspace_id=self.workspace_id,
                event_type=body.event,
                event_payload=body.payload,
                event_level=body.level,
            )  # type: ignore

        resp.media = {"data": {"message": "ok"}}
