from dependency_injector import containers, providers

from chat.app.app import MyApp

from .deliver import EventTrackingResource


class TrackingResourceContainer(containers.DeclarativeContainer):
    tracking = providers.Singleton(EventTrackingResource)


class TrackingContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    resources = providers.Container(TrackingResourceContainer)
