from typing import Dict, List

from pydantic import BaseModel

from chat.threads.model import ThreadModel
from chat.users.model import UserInfo


class TaskCreateMassDirectMessages(BaseModel):
    sender_id: str
    user_ids: List[int]
    api_version: str
    body: Dict


class DeleteMessageEvent(BaseModel):
    user_id: str
    thread_id: int
    message_id: int
    user: UserInfo
    thread: ThreadModel
