import datetime

import user_agents

message_format = {
    "login": "Bạn đã đăng nhập lúc: {}",
    "password_changed": "Bạn đã đổi mật khẩu lúc: {}",
    "request_ip": "Đia chỉ IP: {}",
    "device_id": "Device ID: {}",
    "user_agent": "Tại: {}",
}


def _parse_created_at(nano_timestamp):
    date_o = datetime.datetime.fromtimestamp(
        nano_timestamp / 1000,
        tz=datetime.timezone(datetime.timedelta(hours=+7)),
    )
    return date_o.strftime("%H:%M, %d tháng %m, %Y")


def _parse_pc(parsed):
    text_output = []
    if parsed.browser.family != "Other":
        text_output.append(
            f"trình duyệt {parsed.browser.family}"
            f" phiên bản {parsed.browser.version_string}"
        )

    if parsed.os.family != "Other":
        text_output.append(f"hệ đi<PERSON><PERSON> hành {parsed.os.family}")

    if text_output:
        return " ".join(text_output)
    return parsed.ua_string


def _parse_mobile(parsed):
    text_output = []
    text_output.append(f"thiết bị {parsed.device.family}")

    if parsed.os.family != "Other":
        text_output.append(
            f"hệ điều hành {parsed.os.family}"
            f" phiên bản {parsed.os.version_string}"
        )

    if text_output:
        return " ".join(text_output)
    return parsed.ua_string


def _parse_tablet(parsed):
    text_output = []
    text_output.append(f"thiết bị {parsed.device.family}")

    if parsed.os.family != "Other":
        text_output.append(
            f"hệ điều hành {parsed.os.family}"
            f" phiên bản {parsed.os.version_string}"
        )

    if text_output:
        return " ".join(text_output)
    return parsed.ua_string


def _parse_bot(parsed):
    text_output = []
    text_output.append("BOT")

    if parsed.browser.family != "Other":
        text_output.append(f"tên {parsed.browser.family}")

    if parsed.os.family != "Other":
        text_output.append(
            f"hệ điều hành {parsed.os.family}"
            f" phiên bản {parsed.os.version_string}"
        )

    if text_output:
        return " ".join(text_output)
    return parsed.ua_string


def _parse_user_agent(user_agent):
    parsed = user_agents.parse(user_agent)
    if parsed.is_pc:
        return _parse_pc(parsed)
    elif parsed.is_mobile:
        return _parse_mobile(parsed)
    elif parsed.is_tablet:
        return _parse_tablet(parsed)
    elif parsed.is_bot:
        return _parse_bot(parsed)

    return parsed.ua_string


def build(event_type, information):
    lines = []
    lines.append(
        message_format[event_type].format(
            _parse_created_at(information["created_at"])
        )
    )
    for k, v in information.items():
        field_m_format = message_format.get(k)
        if field_m_format:
            if k == "user_agent":
                lines.append(field_m_format.format(_parse_user_agent(v)))
            else:
                lines.append(field_m_format.format(v))
    return "\n".join(lines)
