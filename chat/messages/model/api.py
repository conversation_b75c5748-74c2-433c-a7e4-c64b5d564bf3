import logging
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlencode
from uuid import UUID

from pydantic import Field, conint, constr, root_validator, validator

import chat.constant as constant
import chat.utils.cdn as cdn
import chat.utils.thumb as thumb
from chat.messages.model import auth_text_builder
from chat.model import BaseModel
from chat.utils import common
from chat.utils.validator_helper import (
    check_thread_id,
    is_bot_id,
    is_gapo_animated_sticker,
    is_gapo_file,
    is_gapo_image,
    is_gapo_sticker,
    is_gapo_video,
    is_object_id,
    is_system,
    is_user_id,
    is_uuid,
    is_valid_thread_id,
)

MAX_LENGTH_TEXT = 5000
MAX_QUICK_REPLY_OPTIONS = 15

MAX_SIZE = 2**32

BODY_TYPES = (
    "text",
    "image",
    "multi_image",
    "sticker",
    "story",
    "video",
    "file",
    "animated_sticker",
    "call",
    "call_group_chat",
    "test",
)
MAX_PASSCODE = 4

INT_64 = 2**64
INT_32 = 2**32
LIMIT_MESSAGE_ID = INT_64
FILE_NAME_MAXLENGTH = 255
PAGE_SIZE = 100

MESSAGE_UNSUPPORTED = {}
SUPPORTED_BUTTON_TYPES = ("postback", "web_url", "phone_number")


def make_unsupported_message(message):
    message["body"] = {"type": "text", "text": "Vui lòng cập nhật"}
    message["sender"] = constant.SYSTEM_OBJECT


def validator_mention(meta, text):
    text_size = common.count_unicode(text)
    last_index = None
    for m in meta.mentions:
        offset = m.offset
        length = m.length
        index = offset + length - 1
        if (
            last_index is not None and last_index >= offset
        ) or index >= text_size:
            raise ValueError(
                f"Invalid mention offset and length: {meta.mentions} textsize: {text_size}"  # noqa
            )
        else:
            last_index = index


def validate_pass_code(v: str) -> str:
    if v == "":
        return None  # type: ignore
    if not v.isdigit():
        raise ValueError(f"Type invalid {v}")
    if len(v) != MAX_PASSCODE:
        raise ValueError(f"Length invalid {v}")

    return v


def limit_text(v: str) -> str:
    if v is None:
        return v
    stripped = v.strip()
    if not 0 < common.count_unicode(stripped) <= MAX_LENGTH_TEXT:
        raise ValueError("Text too long")
    return stripped

def validate_body_message(v: Dict[Any, Any]) -> Dict[Any, Any]:
    body_type = v.get("type", None)
    if body_type is None:
        raise ValueError("Body type is required")

    schema = SCHEMA_MESSAGES.get(body_type, None)
    if not schema:
        raise ValueError(f"Invalid body type {body_type}")
    value = schema(**v)

    value: Dict[Any, Any] = value.dict()
    media = value.get("media", [])
    metadata = value.get("metadata", None)
    if metadata:
        image_thumbs = metadata.get("image_thumb", None)
        image_informations = metadata.get("image_information", None)
        video_informations = metadata.get("video_information", None)
        file_informations = metadata.get("file_information", None)
        voice_informations = metadata.get("voice_information", None)
        video_thumbs = metadata.get("video_thumb", None)

        # generate video thumb from video url
        if video_thumbs and media and video_informations:
            gen_video_thumb(media, video_thumbs)

        gen_image_thumb(media, image_thumbs, image_informations)
        gen_media_cdn(media, video_informations)
        gen_media_cdn(media, file_informations)
        gen_media_cdn(media, voice_informations)

    cdn_media = []
    for m in media:
        cdn_media.append(cdn.image(m) or cdn.video(m) or m)
    value["media"] = cdn_media

    return value



def limit_text_media(v: str) -> str:
    if v is None:
        return v
    stripped = v.strip()
    if not common.count_unicode(stripped) <= MAX_LENGTH_TEXT:
        raise ValueError("Text too long")
    return stripped


def gen_video_thumb(media: list, video_thumbs: list):
    """Update thumb video."""
    for video_url, thumb_obj in zip(media, video_thumbs):
        video_thumb = thumb.video_thumb(video_url)
        if video_thumb:
            thumb_obj["url"] = video_thumb


def gen_image_thumb(media: list, image_thumbs: list, image_informations: list):
    if image_thumbs:
        if not image_informations:
            for o, t in zip(media, image_thumbs):
                t["url"] = thumb.image(o)
        else:
            for o, t, i in zip(media, image_thumbs, image_informations):
                if i["type"] == "gif":
                    t["url"] = thumb.gif(o)
                else:
                    t["url"] = thumb.image(o)


def gen_media_cdn(media: list, media_infos: list):
    if not media_infos:
        return

    for o, t in zip(media, media_infos):
        t["cdn"] = cdn.video(o)


class SchemaPreviewLink(BaseModel):
    id: str
    source_url: constr(min_length=1, max_length=2000)  # type: ignore
    domain: constr(min_length=1, max_length=2000)  # type: ignore
    title: constr(min_length=1, max_length=2000)  # type: ignore
    description: constr(min_length=0, max_length=2000)  # type: ignore
    image: constr(min_length=0, max_length=2000)  # type: ignore
    url: constr(min_length=1, max_length=2000)  # type: ignore
    gapo_uri: constr(min_length=0, max_length=2000)  # type: ignore
    created_at: conint(gt=0, lt=INT_64)  # type: ignore
    updated_at: conint(gt=0, lt=INT_64)  # type: ignore
    scraped_at: conint(gt=0, lt=INT_64)  # type: ignore

    @validator("id")
    def validate_preview_id(cls, v):
        if not is_object_id(v):
            raise ValueError(f"Invalid preview id {v}")
        return v

    @validator("url")
    def escape_url(cls, v):
        if v.startswith("javascript:"):
            raise ValueError("Securiy alert xss")
        return v

    @validator("source_url")
    def escape_source_url(cls, v):
        if v.startswith("javascript:"):
            raise ValueError("Securiy alert xss")
        return v


class SchemaImageThumb(BaseModel):
    width: conint(ge=1)  # type: ignore
    height: conint(ge=1)  # type: ignore


class SchemaVideoThumb(BaseModel):
    width: conint(ge=1)  # type: ignore
    height: conint(ge=1)  # type: ignore
    url: Union[str, None] = None


class SchemaStoryThumb(BaseModel):
    width: conint(ge=1)  # type: ignore
    height: conint(ge=1)  # type: ignore


class SchemaStickerThumb(BaseModel):
    width: conint(ge=1)  # type: ignore
    height: conint(ge=1)  # type: ignore


class SchemaMention(BaseModel):
    target: str
    length: conint(ge=0)  # type: ignore

    offset: conint(ge=0)  # type: ignore

    @validator("target")
    def validate_target(cls, v):
        if v.isdigit() and int(v) > 0:
            return v
        elif v == "all":
            return v
        raise ValueError(f"v must be string-int > 0 or all {v}")


class SchemaImageInformation(BaseModel):
    type: constr(min_length=1, max_length=20)  # type: ignore
    size: conint(ge=0, lt=99999999)  # type: ignore

    quality: str = "unknown"
    id: str

    @validator("id")
    def validate_image_information_id(cls, v):
        if not is_uuid(v):
            raise ValueError(f"Invalid image information id {v}")
        return v


class SchemaStickerBuzzInformation(BaseModel):
    package_id: str
    id: str
    sound: str


class SchemaConferenceInformation(BaseModel):
    uuid: str
    id: int
    host_email: str
    topic: str
    type: int
    join_url: str
    password: str
    encrypted_password: str
    created_at: str
    start_time: str
    timezone: str


class SchemaActedByInformation(BaseModel):
    user_id: str
    user_name: str
    type: str
    status: str


class SchemaAsanaWorkspaceInformation(BaseModel):
    id: str
    name: str


class SchemaAsanaProjectInfo(BaseModel):
    id: str
    name: str


class SchemaAsanaAttachment(BaseModel):
    name: str
    download_url: str
    view_url: str


class SchemaAsanaInformation(BaseModel):
    id: str
    title: str
    description: str
    completed: bool
    completed_at: Union[None, int]
    due_at: Union[None, int]
    due_on: Union[None, int]
    name: str
    notes: str
    workspace: Union[None, SchemaAsanaWorkspaceInformation]
    start_at: Union[None, int]
    start_on: Union[None, int]
    tags: Union[None, List[str]]
    projects: Union[None, List[SchemaAsanaProjectInfo]]
    permalink_url: str
    modified_at: Union[None, int]
    attachment_info: Union[None, SchemaAsanaAttachment]


class SchemaMeetingInformation(BaseModel):
    meeting_name: str
    meeting_url: str
    start_time: str
    end_time: str = ""
    meeting_type: int
    meeting_status: str


class SchemaMeetingRecordInformation(BaseModel):
    file_size: conint(ge=0, lt=MAX_SIZE)  # type: ignore
    link: str


class SchemaVideoInformation(BaseModel):
    type: constr(min_length=1, max_length=20)  # type: ignore
    size: conint(ge=0, lt=MAX_SIZE)  # type: ignore
    name: constr(min_length=1, max_length=FILE_NAME_MAXLENGTH)  # type: ignore
    quality: str = "unknown"
    id: str
    duration: conint(ge=0, lt=MAX_SIZE) = 0  # type: ignore

    @validator("id")
    def validate_image_information_id(cls, v):
        if not is_uuid(v):
            raise ValueError(f"Invalid image information id {v}")
        return v


class SchemaVoiceInformation(BaseModel):
    size: conint(ge=0, lt=MAX_SIZE)  # type: ignore
    type: constr(min_length=1, max_length=20)  # type: ignore
    name: constr(min_length=1, max_length=FILE_NAME_MAXLENGTH)  # type: ignore
    id: str
    duration: conint(ge=0, lt=MAX_SIZE) = 0  # type: ignore

    @validator("id")
    def validate_voice_information_id(cls, v):
        if not is_uuid(v):
            raise ValueError(f"Invalid voice information id {v}")
        return v


class SchemaFileInformation(BaseModel):
    type: constr(min_length=1, max_length=20)  # type: ignore
    size: conint(ge=0, lt=MAX_SIZE)  # type: ignore
    name: constr(min_length=1, max_length=FILE_NAME_MAXLENGTH)  # type: ignore
    id: str

    @validator("id")
    def validate_image_information_id(cls, v):
        if not is_uuid(v):
            raise ValueError(f"Invalid image information id {v}")
        return v


def default_media(v: list) -> list:
    return []


class SchemaDonateInformation(BaseModel):
    amount: int
    message: Union[None, str]
    currency: str
    transaction_id: str


class SchemaAuthInformation(BaseModel):
    request_ip: Union[None, str]
    device_id: Union[None, str]
    user_agent: Union[None, str]
    created_at: int


class SchemaEmailInvalidInformation(BaseModel):
    email: str


class SchemaCallServiceInformation(BaseModel):
    id: str
    type: int
    status: int
    created_at: int
    stopped_at: int = 0
    duration: int = 0


class SchemaMetadataText(BaseModel):
    preview_link: Union[None, SchemaPreviewLink]
    mentions: Union[None, List[SchemaMention]]
    payload: str = ""

    @validator("preview_link", pre=True)
    def validate_preview_link(cls, v):
        if v is None:
            return v

        try:
            SchemaPreviewLink(**v)
        except Exception as e:
            logging.error(e)
            return None
        return v


class SchemaMetadataImage(BaseModel):
    image_thumb: Union[None, List[SchemaImageThumb]] = None
    image_information: Union[None, List[SchemaImageInformation]] = None
    mentions: Union[None, List[SchemaMention]]


class SchemaMetadataStory(BaseModel):
    story_react_type: conint(ge=0, le=8)  # type: ignore
    story_thumb: Union[None, List[SchemaImageThumb]] = None
    mentions: Union[None, List[SchemaMention]]


class SchemaMetadataDonate(BaseModel):
    donate_information: SchemaDonateInformation


class SchemaMetadataAuth(BaseModel):
    auth_information: SchemaAuthInformation


class SchemaMetadataEmailInvalid(BaseModel):
    email_invalid_information: SchemaEmailInvalidInformation


class SchemaMetadataCallSerivce(BaseModel):
    call_information: SchemaCallServiceInformation


class SchemaMetadataSticker(BaseModel):
    image_thumb: Union[None, List[SchemaImageThumb]] = None
    image_information: Union[None, List[SchemaImageInformation]] = None


class SchemaMetadataStickerBuzz(BaseModel):
    sticker_buzz_information: Union[
        None, List[SchemaStickerBuzzInformation]
    ] = None


class SchemaMetadataConference(BaseModel):
    conference_information: SchemaConferenceInformation


class SchemaMetadataAsana(BaseModel):
    acted_by: SchemaActedByInformation
    asana_task_information: Union[None, SchemaAsanaInformation]


class SchemaMetadataMeeting(BaseModel):
    meeting_information: SchemaMeetingInformation


class SchemaMetadataMeetingRecord(BaseModel):
    file_information: SchemaMeetingRecordInformation


class SchemaMetadataVoice(BaseModel):
    voice_information: List[SchemaVoiceInformation]


class SchemaMetadataVideo(BaseModel):
    video_thumb: Union[None, List[SchemaVideoThumb]] = None
    video_information: List[SchemaVideoInformation]
    mentions: Union[None, List[SchemaMention]]


class SchemaMetadataFile(BaseModel):
    file_information: List[SchemaFileInformation]
    mentions: Union[None, List[SchemaMention]]


class SchemaMetaDataMenu(BaseModel):
    payload: str
    mentions: Union[None, List[SchemaMention]]


class SchemaMetadataQuickReply(BaseModel):
    payload: str
    mentions: Union[None, List[SchemaMention]]


class QuickReplyOption(BaseModel):
    title: str
    payload: str


class SchemaMetadataQuickReplies(BaseModel):
    options: List[QuickReplyOption]
    mentions: Union[None, List[SchemaMention]]

    @validator("options")
    def validate_options(cls, v):
        if len(v) > MAX_QUICK_REPLY_OPTIONS:
            raise ValueError("Only maxium 15 options are allowed")

        return v


class SchemaBodyText(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    type: str
    text: str
    is_markdown_text: bool = False
    metadata: Union[None, SchemaMetadataText] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text)

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["media"] = []
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaMetadataEditText(BaseModel):
    preview_link: Union[None, SchemaPreviewLink] = {}  # type: ignore
    mentions: Union[None, List[SchemaMention]] = []
    layout: dict = dict()

    @validator("preview_link", pre=True)
    def validate_preview_link(cls, v):
        if v is None:
            return v

        try:
            SchemaPreviewLink(**v)
        except Exception as e:
            logging.error(e)
            return None
        return v


class SchemaBodyEdit(BaseModel):
    text: str
    is_markdown_text: bool = False
    metadata: Union[None, SchemaMetadataEditText] = SchemaMetadataEditText()

    _normalize_text = validator("text", allow_reuse=True)(limit_text)

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaBodyDonate(BaseModel):
    text: Union[str, None] = None
    type: str
    metadata: SchemaMetadataDonate


class SchemaBodyBot(BaseModel):
    text: Union[str, None] = None
    type: str
    metadata = []  # type: ignore


class SchemaBodyMenu(BaseModel):
    text: Union[str, None] = None
    type: str
    metadata: SchemaMetaDataMenu


class SchemaBodyQuickReply(BaseModel):
    text: Union[str, None] = None
    type: str
    metadata: SchemaMetadataQuickReply


class SchemaBodyQuickReplies(BaseModel):
    text: str
    type: str
    metadata: SchemaMetadataQuickReplies
    is_markdown_text: bool = False


class SchemaBodyAuth(BaseModel):
    type: str
    metadata: SchemaMetadataAuth
    text: Optional[str] = None

    @validator("type")
    def check_type(cls, v):
        if v not in ("login", "password_changed"):
            raise ValueError(f"Invalid body type {v}")
        return v

    @root_validator(skip_on_failure=True)
    def full_fill_text(cls, values):
        values["text"] = auth_text_builder.build(
            values["type"], values["metadata"].auth_information.dict()
        )

        values["type"] = "text"
        return values


class SchemaBodyCallService(BaseModel):
    type: str
    metadata: SchemaMetadataCallSerivce

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["text"] = None
        return values

    @validator("type")
    def check_type(cls, v):
        if v not in ("call", "call_group_chat"):
            raise ValueError(f"Invalid body type {v}")
        return v


class SchemaBodyImage(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    is_markdown_text: bool = False
    text: Union[None, str] = None
    metadata: Union[None, SchemaMetadataImage] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        cdn_images = []
        len_images = len(v)
        if len_images != 1:
            raise ValueError("Invalid length media")

        for image in v:
            if not is_gapo_image(image):
                raise ValueError(f"{image} invalid url")
            cdn_images.append(cdn.image(image))

        return cdn_images

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if not v:
            raise ValueError("Invalid metadata")

        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_image_thumb = 0 if v.image_thumb is None else len(v.image_thumb)
        len_image_info = (
            0 if v.image_information is None else len(v.image_information)
        )
        if len_media < len_image_thumb or len_media < len_image_info:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaBodyMultiImage(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    text: Union[None, str] = None
    is_markdown_text: bool = False
    metadata: Union[None, SchemaMetadataImage] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        cdn_images = []
        len_images = len(v)
        if 50 < len_images or len_images < 1:
            raise ValueError("Invalid length media")

        for image in v:
            if not is_gapo_image(image):
                raise ValueError(f"{image} invalid url")
            cdn_images.append(cdn.image(image))

        return cdn_images

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if not v:
            raise ValueError("Invalid metadata")

        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_image_thumb = 0 if v.image_thumb is None else len(v.image_thumb)
        len_image_info = (
            0 if v.image_information is None else len(v.image_information)
        )
        if len_media < len_image_thumb or len_media < len_image_info:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaBodyFile(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    is_markdown_text: bool = False
    text: Union[None, str] = None
    metadata: Union[None, SchemaMetadataFile] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        cdn_files = []
        len_files = len(v)
        if len_files != 1:
            raise ValueError("Invalid length media")

        for file_ in v:
            if not is_gapo_file(file_):
                raise ValueError(
                    f"commit_hash: {common.GIT_COMMIT_VERSION} {file_} invalid url"  # noqa
                )
            cdn_files.append(cdn.video(file_))

        return cdn_files

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaBodyVoice(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    text: Union[None, str] = None
    is_markdown_text: bool = False
    metadata: SchemaMetadataVoice

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        cdn_files = []
        len_voices = len(v)
        if len_voices != 1:
            raise ValueError("Invalid length media")

        for file_ in v:
            if not is_gapo_file(file_):
                raise ValueError(
                    f"commit_hash: {common.GIT_COMMIT_VERSION} {file_} invalid url"  # noqa
                )
            cdn_files.append(cdn.video(file_))

        return cdn_files


class SchemaBodyVideo(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    is_markdown_text: bool = False
    text: Union[None, str] = None
    metadata: Union[None, SchemaMetadataVideo] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        cdn_videos = []
        len_videos = len(v)
        if len_videos != 1:
            raise ValueError("'media' must have length 1")

        for video in v:
            if not is_gapo_video(video):
                raise ValueError(f"Invalid video url {video}")
            cdn_videos.append(cdn.image(video))

        return cdn_videos

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_video_thumb = 0 if v.video_thumb is None else len(v.video_thumb)
        len_video_information = len(v.video_information)
        if len_media < len_video_thumb or len_media < len_video_information:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaBodyStickerBuzz(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    type: str
    metadata: Union[None, SchemaMetadataStickerBuzz] = None
    text: Union[None, str] = None
    _normalize_text = validator("text", allow_reuse=True)(limit_text)

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["media"] = []
        return values


class SchemaBodyConference(BaseModel):
    type: str
    metadata: SchemaMetadataConference

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["media"] = []
        values["text"] = values["metadata"].conference_information.topic

        return values


class SchemaBodyAsana(BaseModel):
    type: str
    text: str
    metadata: SchemaMetadataAsana

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["media"] = []

        if values["type"] != "asana":
            raise ValueError("Type must be asana")

        return values


class SchemaBodyMeeting(BaseModel):
    type: str
    text: str
    is_markdown_text: bool = False
    metadata: SchemaMetadataMeeting

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["media"] = []

        if values["type"] != "meeting":
            raise ValueError("type must be meeting")

        return values


class SchemaBodyMeetingRecord(BaseModel):
    type: str
    text: str
    metadata: SchemaMetadataMeetingRecord

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):

        if values["type"] != "meeting_record":
            raise ValueError("type must be meeting_record")

        return values


class SchemaBodySticker(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    metadata: Union[None, SchemaMetadataSticker] = None

    @validator("media")
    def validate_media(cls, v):
        cdn_images = []
        len_images = len(v)
        if len_images != 1:
            raise ValueError("Invalid length media")

        for image in v:
            if not is_gapo_sticker(image):
                raise ValueError(f"{image} invalid url")
            cdn_images.append(cdn.image(image))

        return cdn_images

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if not v:
            raise ValueError("Invalid metadata")

        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_image_thumb = 0 if v.image_thumb is None else len(v.image_thumb)
        len_image_info = (
            0 if v.image_information is None else len(v.image_information)
        )
        if len_media < len_image_thumb or len_media < len_image_info:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["text"] = None
        return values


class SchemaBodyAnimatedSticker(BaseModel):
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    media: List[str]
    type: str
    metadata: Union[None, SchemaMetadataSticker] = None

    @validator("media")
    def validate_media(cls, v):
        # cdn_stickers = []
        len_sticker = len(v)
        if len_sticker != 1:
            raise ValueError("Invalid length media")

        for sticker in v:
            if not is_gapo_animated_sticker(sticker):
                raise ValueError(f"{sticker} is invalid animated sticker url")
        return v

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if not v:
            raise ValueError("Invalid metadata")

        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_image_thumb = 0 if v.image_thumb is None else len(v.image_thumb)
        len_image_info = (
            0 if v.image_information is None else len(v.image_information)
        )
        if len_media < len_image_thumb or len_media < len_image_info:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["text"] = None
        return values


class SchemaBodyStory(BaseModel):
    text: str
    media: List[str]
    type: str
    metadata: Union[None, SchemaMetadataStory] = None

    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)

    @validator("media")
    def validate_media(cls, v):
        len_images = len(v)
        if len_images != 1:
            raise ValueError("Invalid length media")
        return v

    @validator("metadata")
    def validate_metadata(cls, v, values, **kwargs):
        if "media" not in values:
            raise ValueError("Media required")
        len_media = len(values["media"])
        len_story_thumb = 0 if v.story_thumb is None else len(v.story_thumb)
        if len_media < len_story_thumb:
            raise ValueError("Invalid metadata length")
        return v

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        values["reply_to_msg"] = None
        # meta = values["metadata"]
        # if meta and meta.mentions:
        #     text = values["text"]
        #     validator_mention(meta, text)
        return values


class SchemaCreateWalletMessage(BaseModel):
    partner_id: str
    body: dict

    @validator("partner_id")
    def check_partner_id(cls, v):
        if not is_user_id(v):
            raise ValueError(f"Invalid partner_id {v}")
        return v

    @validator("body")
    def check_body(cls, v):
        body_type = v.get("type", None)
        if body_type != "donate":
            raise ValueError(f"Invalid body type {body_type}")
        value = SchemaBodyDonate(**v)
        value = value.dict()
        return value


# Carousel message schema


class MessageButton(BaseModel):
    type: str
    payload: str = Field(max_length=1000)
    title: str = Field(max_length=1000)
    open_in_app: bool = False

    @root_validator(pre=True)
    def validate_button(cls, values):
        button_type = values.get("type")
        payload = values.get("payload")
        if button_type not in SUPPORTED_BUTTON_TYPES:
            raise ValueError(
                f"""Invalid button type.
                Valid value is one of {SUPPORTED_BUTTON_TYPES}"""
            )

        if button_type == "web_url":
            if not common.is_valid_http_url(payload):
                raise ValueError("Invalid web link")
            values["payload"] = common.encode_url(payload)

        elif button_type == "phone_number":
            normalized_phone_number = common.normalize_and_valid_phone_number(
                payload
            )
            if not normalized_phone_number:
                raise ValueError("Invalid phone number")
            values["payload"] = normalized_phone_number

        return values


class CarouselCard(BaseModel):
    title: str = Field(default="", max_length=500)
    subtitle: Union[str, None] = Field(default=None, max_length=500)
    image_url: Union[str, None] = None
    buttons: List[MessageButton] = Field([], max_items=3)

    @validator("image_url", pre=True)
    def validate_image_url(cls, v):
        if v and not common.is_valid_image_url(v):
            raise ValueError(
                """Invalid image url.
                Supported image file extensions are png, jpg, jpeg."""
            )
        return v


class NameValueStrObject(BaseModel):
    type: str
    name: str
    value: str


class WidgetObject(BaseModel):
    text_objects: List[NameValueStrObject] = []
    buttons: List[MessageButton] = []


class SchemaCardInformation(BaseModel):
    type: str
    created_at: int
    title: str = Field(default="", max_length=1000)
    sub_title: Union[str, None] = Field(default=None, max_length=1000)
    widget: WidgetObject = WidgetObject()

    @validator("type", pre=True)
    def validate_type_card(cls, v):
        if v not in ("approval.yes_no", "approval.detail", "approval.confirm"):
            raise ValueError("""Invalid type card.""")
        return v


class SchemaMetadataCarousel(BaseModel):
    carousel_cards: List[CarouselCard] = Field(min_items=1, max_items=10)


class SchemaBodyCarouselMessage(BaseModel):
    type: str
    metadata: SchemaMetadataCarousel


class SchemaMetadataCard(BaseModel):
    card_information: dict


class SchemaBodyCardMessage(BaseModel):
    type: str
    text: Union[str, None] = None
    is_markdown_text: bool = False
    metadata: SchemaMetadataCard

    _normalize_text = validator("text", allow_reuse=True)(limit_text)


class SchemaBodyDynamicMessage(BaseModel):
    type: str
    text: str
    is_markdown_text: bool = False
    metadata: dict

    _normalize_text = validator("text", allow_reuse=True)(limit_text)


class SchemaVoteBody(BaseModel):
    title: str = "Votes Default"


class SchemaPollBody(BaseModel):
    title: str = "Poll Default"
    allow_add_choice: bool = False
    allow_multiple_choice: bool = False
    incognito: bool = False
    end_at: int = 0


class PollPayload(SchemaPollBody):
    votes: List[SchemaVoteBody] = []


class SchemaBodyPollVote(PollPayload):
    text: Union[str, None] = None
    type: str = "poll"
    reply_to_msg: Union[None, conint(gt=0, lt=LIMIT_MESSAGE_ID)] = None  # type: ignore # noqa
    _normalize_text = validator("text", allow_reuse=True)(limit_text_media)


class SchemaPollCreate(BaseModel):
    thread_id: int
    client_id: Union[None, str] = None
    body: SchemaBodyPollVote

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaEditBotMessage(BaseModel):
    thread_id: int
    message_id: int
    bot_id: str
    body: SchemaBodyEdit
    bypass_all: bool = False

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaCreateBotMessage(BaseModel):
    thread_id: Optional[int] = None
    partner_id: Optional[str] = None
    collab_id: Optional[str] = None
    bypass_all: bool = False
    message_id: int = 0
    bot_id: str
    body: dict

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )
    _normalize_body = validator("body", allow_reuse=True)(validate_body_message)

class SchemaCreateMassDirectMessages(BaseModel):
    user_ids: List[int] = Field(min_items=1, max_items=1000)
    sender_id: str
    body: dict

    _normalize_body = validator("body", allow_reuse=True)(validate_body_message)


class SchemaCreateAuthServiceMessage(BaseModel):
    body: SchemaBodyAuth


class SchemaCreateCallServiceMessage(BaseModel):
    thread_id: Optional[int] = None
    partner_id: Optional[str] = None
    body: SchemaBodyCallService

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        partner_id = values.get("partner_id")
        thread_id = values.get("thread_id")
        call_type = values["body"].type

        if not partner_id and not thread_id:
            raise ValueError("Require thread_id or partner_id")

        if (
            partner_id
            and not is_user_id(partner_id)
            and not call_type != "call"
        ):
            raise ValueError(f"Invalid partner_id {partner_id}")

        if (
            thread_id
            and not is_valid_thread_id(thread_id)
            and not call_type != "call_group_chat"
        ):
            raise ValueError(f"Invalid thread_id value {thread_id}")

        return values


class SchemaCreateZoomMessage(BaseModel):
    thread_id: int
    user_id: str
    body: SchemaBodyConference

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


SCHEMA_MEETING_MESSAGES = {
    "text": SchemaBodyText,
    "meeting_record": SchemaBodyMeetingRecord,
    "meeting": SchemaBodyMeeting,
}


class SchemaCreateMeetingMessage(BaseModel):
    thread_id: Optional[int] = None
    user_id: str
    partner_id: Optional[str] = None
    collab_id: Optional[str] = None
    body: dict

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        user_id = values["user_id"]
        if is_bot_id(user_id):
            values["user_type"] = "bot"
        else:
            values["user_type"] = "user"

        return values

    @validator("body")
    def check_body(cls, v):
        body_type = v.get("type", None)
        schema = SCHEMA_MEETING_MESSAGES.get(body_type, None)
        if not schema:
            raise ValueError(f"Invalid body type {body_type}")
        value = schema(**v)
        value = value.dict()
        return value


SCHEMA_MESSAGES = {
    # "markdown": SchemaBodyText,
    "text": SchemaBodyText,
    "image": SchemaBodyImage,
    "multi_image": SchemaBodyMultiImage,
    "sticker": SchemaBodySticker,
    "animated_sticker": SchemaBodyAnimatedSticker,
    "story": SchemaBodyStory,
    "video": SchemaBodyVideo,
    "file": SchemaBodyFile,
    "sticker_buzz": SchemaBodyStickerBuzz,
    "conference": SchemaBodyConference,
    "meeting": SchemaBodyMeeting,
    "asana": SchemaBodyAsana,
    "voice": SchemaBodyVoice,
    # for testing, generate un-expected message
    "test": SchemaBodyText,
    "menu": SchemaBodyMenu,
    "quick_reply": SchemaBodyQuickReply,
    "quick_replies": SchemaBodyQuickReplies,
    "carousel": SchemaBodyCarouselMessage,
    "card": SchemaBodyCardMessage,
    "dynamic": SchemaBodyDynamicMessage,
    "poll": SchemaBodyPollVote,
}


class SchemaCreate(BaseModel):
    collab_id: Optional[str] = None
    thread_id: Optional[int] = None
    partner_id: Optional[str] = None
    client_id: Union[None, str] = None
    message_id: int = 0
    body: dict
    pass_code: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )
    _normalize_pass_code = validator("pass_code", allow_reuse=True)(
        validate_pass_code
    )

    @validator("body")
    def check_body(cls, v):
        body_type = v.get("type", None)
        schema = SCHEMA_MESSAGES.get(body_type, None)
        if not schema:
            raise ValueError(f"Invalid body type {body_type}")
        value = schema(**v)
        value = value.dict()
        media = value.get("media", [])
        metadata = value.get("metadata", None)
        if metadata:
            image_thumbs = metadata.get("image_thumb", None)
            image_informations = metadata.get("image_information", None)
            video_informations = metadata.get("video_information", None)
            file_informations = metadata.get("file_information", None)
            voice_informations = metadata.get("voice_information", None)
            video_thumbs = metadata.get("video_thumb", None)

            # generate video thumb from video url
            if video_thumbs and media and video_informations:
                gen_video_thumb(media, video_thumbs)

            gen_image_thumb(media, image_thumbs, image_informations)
            gen_media_cdn(media, video_informations)
            gen_media_cdn(media, file_informations)
            gen_media_cdn(media, voice_informations)

        cdn_media = []
        for m in media:
            cdn_media.append(cdn.image(m) or cdn.video(m) or m)
        value["media"] = cdn_media

        return value


class SchemaEditMessage(BaseModel):
    thread_id: int
    body: SchemaBodyEdit

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaUser(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str


class SchemaOutput(BaseModel):
    id: int
    body: dict
    created_at: int
    sender: SchemaUser = Field(..., alias="user")
    deleted: bool
    event_id: UUID = Field(..., alias="uuid")
    react: dict
    read: list = []
    edited_at: int = 0

    @validator("body")
    def normalize_detail(cls, value):
        media = value["media"]
        metadata = value.get("metadata", None)
        if metadata:
            image_thumbs = metadata.get("image_thumb", None)
            image_informations = metadata.get("image_information", None)
            video_informations = metadata.get("video_information", None)
            file_informations = metadata.get("file_information", None)
            voice_informations = metadata.get("voice_information", None)

            gen_image_thumb(media, image_thumbs, image_informations)
            gen_media_cdn(media, video_informations)
            gen_media_cdn(media, file_informations)
            gen_media_cdn(media, voice_informations)

        cdn_media = []
        for m in media:
            cdn_media.append(cdn.image(m) or cdn.video(m) or m)
        value["media"] = cdn_media
        return value

    @validator("event_id")
    def normalize_event_id(cls, v):
        return str(v)

    # @root_validator(skip_on_failure=True)
    # def add_default_field(cls, values):
    #     values.pop("react", None)
    #     return values


class SchemaGetItemParams(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    pass_code: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )
    _normalize_pass_code = validator("pass_code", allow_reuse=True)(
        validate_pass_code
    )


class SchemaGetItemReactParams(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    last_time: int = 0
    react_type: int = 0

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    def get_next_link(self, next_last_time):
        query = {
            "thread_id": self.thread_id,
            "last_time": next_last_time,
            "react_type": self.react_type,
        }
        return urlencode(query)


class SchemaPostReact(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    react_type: conint(ge=0, le=10)  # type: ignore

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaPostForward(BaseModel):
    thread_id: Optional[int] = None
    partner_id: Optional[str] = None
    source_message_id: conint(gt=0)  # type: ignore
    source_thread_id: int
    client_id: Union[None, str] = None

    partner_type: str = ""  # filled later

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    _normalize_source_thread_id = validator(
        "source_thread_id", allow_reuse=True
    )(check_thread_id)

    @validator("partner_id")
    def check_partner_id(cls, v, values, **kwargs):
        if is_user_id(v):
            values["partner_type"] = constant.USER_TYPE
        elif is_system(v):
            values["partner_type"] = constant.SYSTEM_TYPE
        elif is_bot_id(v):
            values["partner_type"] = constant.BOT_TYPE
        else:
            raise ValueError(f"Invalid partner_id {v}")
        return v


class SchemaPostForwards(BaseModel):
    thread_id: Optional[int] = None
    partner_id: Optional[str] = None
    partner_type: Optional[str] = None
    source_message_ids: list
    source_thread_id: int
    client_id: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    _normalize_source_thread_id = validator(
        "source_thread_id", allow_reuse=True
    )(check_thread_id)

    @validator("partner_id")
    def check_partner_id(cls, v, values, **kwargs):
        if is_user_id(v):
            values["partner_type"] = constant.USER_TYPE
        elif is_system(v):
            values["partner_type"] = constant.SYSTEM_TYPE
        elif is_bot_id(v):
            values["partner_type"] = constant.BOT_TYPE
        else:
            raise ValueError(f"Invalid partner_id {v}")
        return v

    @validator("source_message_ids")
    def check_message_id(cls, v):
        v.sort()
        for msg in v:
            if type(msg) is not int:
                raise ValueError("Message id must be int")
            if msg <= 0 or msg >= INT_32:
                raise ValueError("Invalid message id")

        return v


class SchemaDeleteItems(BaseModel):
    thread_id: int
    message_ids: list
    level: int

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @validator("message_ids")
    def check_message_ids(cls, v):
        for msg in v:
            if type(msg) is not int:
                raise ValueError("Message id must be int")
            if msg <= 0 or msg >= INT_32:
                raise ValueError("Invalid message id")
        return v

    @validator("level")
    def check_delete_level(cls, v):
        if v not in [
            constant.DELETE_MSG_ONE_SIDE,
            constant.DELETE_MSG_TWO_SIDE,
        ]:
            raise ValueError(
                "Invalid delete level value. The valid value is one of [1, 2]."
            )

        return v


class SchemaPollVoteCreate(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    body: SchemaVoteBody

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaPollVoteEdit(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    vote_id: str
    body: SchemaVoteBody

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaPollVoteDelete(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    vote_id: str
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaUserVoteUnchoiceCreate(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    vote_id: str
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaGetUserVoteParams(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    vote_id: str
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )
    skip: int = 0
    limit: int = 10


class SchemaUserVoteChoiceCreate(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    vote_id: str
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaPollEdit(BaseModel):
    thread_id: int
    message_id: conint(gt=0)  # type: ignore
    client_id: Union[None, str] = None
    body: SchemaPollBody

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )
