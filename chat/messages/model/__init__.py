from __future__ import annotations

from typing import Dict, Optional, Union

import pydantic
from typing_extensions import TypedDict
from urlextract import URLExtract

from chat.model import BaseModel
from chat.profile.model import SchemaUser
from chat.utils import common
from chat.utils.json import json_loads
from chat.utils import react_helper
from chat import constant

extractor = URLExtract()

MSG_DELETION_PERIOD = 60 * 60 * 1000  # 1 hour

MSG_POLL_TYPE = "poll"
MSG_DONATE_TYPE = "donate"
MSG_CALL_TYPE = "call"
MSG_CALL_GROUP_TYPE = "call_group_chat"
MSG_MEETING_TYPE = "meeting"
MSG_CARD_TYPE = "card"

CURRENT_VERSION = "3"
_MESSAGE_DELETE_DEFAULT = "Tin nhắn đã bị xóa"
_MESSGAE_MEDIA_DEFAULT = "đã gửi tin nhắn media"
_MESSAGE_MEDIA_DEFINED = {
    "image": "đã gửi {} bức ảnh",
    "multi_image": "đã gửi {} bức ảnh",
    "sticker": "đã gửi {} sticker",
    "animated_sticker": "đã gửi {} sticker",
    "video": "đã gửi {} video",
    "file": "đã gửi {} file",
}

_MESSAGE_DONATE_DEFINED = "Gửi tặng {money} 💰"
_MESSAGE_MEDIA_DEFINED = {
    "image": "đã gửi {} bức ảnh",
    "multi_image": "đã gửi {} bức ảnh",
    "sticker": "đã gửi {} sticker",
    "animated_sticker": "đã gửi {} sticker",
    "video": "đã gửi {} video",
    "file": "đã gửi {} file",
    "voice": "đã gửi {} tin nhắn âm thanh",
}

_MESSAGE_CALL_DEFINED = {
    "default": "đã thực hiện 1 cuộc gọi",
    6: "Bạn đã bỏ lỡ cuộc gọi rồi",
    8: "Bạn đã bỏ lỡ cuộc gọi rồi",
}

_MESSAGE_CALL_GROUP_DEFINED = {
    "default": "Cuộc gọi nhóm",
    5: "Cuộc gọi nhóm kết thúc",
    7: "Cuộc gọi nhóm kết thúc",
}
_MESSAGE_CARD_DEFINED = "Bạn có thông báo mới"

# CREATE TABLE chat_server.messages (
#    thread_id bigint,
#    bucket bigint,
#    id bigint,
#    body text,
#    created_at bigint,
#    delete_level int,
#    deleted_by set<text>,
#    deliver_status int,
#    react_binary tinyint,
#    reply_to frozen<reply_message>,
#    type text,
#    user_id text,
#    uuid uuid,
#    version text,
#    PRIMARY KEY ((thread_id, bucket), id)


def _parse_old_msg(old_body, msg_type):
    if msg_type is not None:
        body = json_loads(old_body)
        body["text"] = body["body"]
        body["type"] = msg_type
    else:
        body = {}
        body["text"] = old_body
        body["media"] = []
        body["type"] = "text"
    body["mention"] = []
    body["reply_to_msg"] = None
    return body


def _body_versioning(values):
    if not isinstance(values["body"], str):
        return
    version = values.get("version", None)
    if version == CURRENT_VERSION:
        values["body"] = json_loads(values["body"])
    else:
        msg_type = values["type"]
        values["body"] = _parse_old_msg(values["body"], msg_type)


def _body_delete(values):
    is_deleted = values["delete_level"] == constant.DELETE_MSG_TWO_SIDE
    if is_deleted:
        values["deleted"] = True
        values["body"] = MessageBody(
            text=_MESSAGE_DELETE_DEFAULT, type="text"
        ).dict()


class DeleteEventBody(BaseModel):
    thread_id: int
    thread_auto_delete_day: int = 0  # now
    message_id: int
    will_deleted_at: int


class DeleteEvent(BaseModel):
    body: DeleteEventBody
    event_type: str = "auto_delete_message"
    uuid: str
    version: str = "3"
    event_at: int


class MessageBody(BaseModel):
    media: list = []
    reply_to_msg: Union[None, int] = None
    reply_to_msg_object: Union[None, dict] = None
    forward: Optional[Dict] = None
    text: Union[None, str] = ""
    is_markdown_text: bool = False
    tmp_text: Union[None, str]

    metadata: Union[None, dict] = None
    type: str
    edited_at: Union[None, int] = 0
    workspace_id: Union[None, int] = 0

    def forward_body(self):
        return MessageBody(
            media=self.media,
            text=self.text,
            tmp_text=self.tmp_text,
            metadata=self.metadata,
            type=self.type,
        )

    def is_poll(self):
        return self.type == MSG_POLL_TYPE

    def __eq__(self, other):
        if not isinstance(other, MessageBody):
            raise NotImplementedError

        return (
            self.media == other.media
            and self.reply_to_msg == other.reply_to_msg
            and self.text == other.text
            and self.is_markdown_text == other.is_markdown_text
            and self.type == other.type
            and self.edited_at == other.edited_at
            and self.workspace_id == other.workspace_id
        )

    def fill_body_tmp(self):
        body_type = self.type
        metadata = self.metadata or {}
        tmp_text = ""

        if body_type in constant.MEDIA_MESSAGE_TYPES:
            if not self.text:
                media_count = len(self.media)
                tmp_text = _MESSAGE_MEDIA_DEFINED[body_type].format(
                    media_count
                )
        elif body_type == MSG_CALL_TYPE:
            call_info = metadata.get("call_information")
            call_info = call_info or {}
            tmp_text = _MESSAGE_CALL_DEFINED.get(
                call_info.get("status", "default"),
                _MESSAGE_CALL_DEFINED["default"],
            )
        elif body_type == MSG_CALL_GROUP_TYPE:
            call_info = metadata.get("call_information")
            call_info = call_info or {}
            tmp_text = _MESSAGE_CALL_GROUP_DEFINED.get(
                call_info.get("status", "default"),
                _MESSAGE_CALL_GROUP_DEFINED["default"],
            )
        elif body_type == MSG_MEETING_TYPE:
            meeting_info = metadata.get("meeting_information", {})
            meeting_info = meeting_info or {}
            tmp_text = meeting_info.get("meeting_name", "Default name")
        elif body_type == MSG_CARD_TYPE:
            if not self.text:
                tmp_text = _MESSAGE_CARD_DEFINED

        elif body_type == MSG_POLL_TYPE:
            if not self.text:
                tmp_text = "Poll đã được tạo"

        if self.is_markdown_text:
            tmp_text = common.markdown_to_text(self.text)

        self.tmp_text = tmp_text

    @classmethod
    def from_poll(cls, poll: dict, text: str = "") -> MessageBody:
        return cls(
            text=text,
            tmp_text=text if text else "Poll đã được tạo",
            metadata={"poll_information": poll},
            type=MSG_POLL_TYPE,
        )


class ThreadObjectDict(TypedDict):
    id: int
    collab_id: Union[None, str]
    name: Union[None, str]
    pair_ids: Union[None, str]
    type: str
    avatar: Union[None, str]


class UserObjectDict(TypedDict):
    name: str
    id: str
    avatar: Union[None, str]
    status_verify: int
    type: str


class LastMessageModel(BaseModel):
    body: Union[None, str] = None
    user_id: str
    id: int
    created_at: int
    raw_body: Union[None, dict]
    sender: Union[SchemaUser, None]
    delete_level: int = 0

    def update_sender(self, profile):
        self.sender = SchemaUser(**profile)


class MessageModel(BaseModel):
    thread_id: int

    sub_thread_id: Union[int, None] = None
    """sub thread id corresponding to this message,
    None if message isn't associated to any thread."""

    bucket: int
    id: int
    body: MessageBody
    created_at: int
    will_deleted_at: int = 0
    delete_level: int
    deleted_by: set = ()  # type: ignore
    deleted: bool = False
    react_binary: int = 0
    type: Union[None, str]
    user_id: str
    uuid: str
    version: Union[None, str]
    pinned_at: int = 0

    user: Optional[Dict] = None
    react: Optional[Dict] = None
    read: list = []

    edited_at: Union[None, int] = 0

    @pydantic.root_validator(pre=True)
    def fill_default_payload(cls, values):
        values["uuid"] = str(values["uuid"])

        react_binary = values.get("react_binary", 0)
        values["react_binary"] = react_binary or 0
        values["deleted_by"] = list(values.get("deleted_by", set()) or [])
        if values.get("will_deleted_at", None) is None:
            values["will_deleted_at"] = 0

        _body_versioning(values)

        _body_delete(values)
        return values

    def fill_reply_message_object(self, obj):
        self.body.reply_to_msg_object = {
            "body": obj.body.dict(),
            "sender": obj.user,
        }

    def is_deleted_2_side(self):
        return self.delete_level == constant.DELETE_MSG_TWO_SIDE

    def is_deleted_1_side(self, user_id: str):
        return user_id in self.deleted_by

    def is_owner(self, user_id: str) -> bool:
        return self.user_id == user_id

    def is_forward(self):
        return self.body.forward

    def delete_1_side(self, user_id):
        self.delete_level = constant.DELETE_MSG_ONE_SIDE
        self.deleted_by.add(user_id)

    def delete_2_side(self):
        self.set_delete_two_side()
        self.delete()

    def delete(self):
        self.body.text = _MESSAGE_DELETE_DEFAULT
        self.deleted = True
        self.body.media = []
        self.body.type = "text"

    def set_delete_two_side(self):
        self.delete_level = constant.DELETE_MSG_TWO_SIDE

    def fill_user(self, user_info):
        self.user = user_info

    def fill_react(self, react_info):
        self.react = react_info

    def fill_read(self, read_info):
        self.read = read_info

    def edit(self, new_body):
        self.body.edited_at = common.now()
        self.body.text = new_body.text
        self.body.is_markdown_text = new_body.is_markdown_text
        if new_body.metadata is not None:
            self.body.metadata = (
                new_body.metadata.dict()
                if self.body.metadata is None
                else self.body.metadata
            )
            self.body.metadata["preview_link"] = new_body.metadata.preview_link
            self.body.metadata["mentions"] = new_body.metadata.mentions
            self.body.metadata["layout"] = new_body.metadata.layout
        else:
            self.body.metadata = {
                "preview_link": {},
                "mentions": [],
                "layout": {},
            }

    def is_expired(self, ttl: int, now=None):
        if not now:
            now = common.now()
        compare_time = ttl
        last_edit = (
            self.created_at
            if (self.body.edited_at == 0)
            else self.body.edited_at
        )
        return (now - last_edit) > compare_time

    def is_in_deletion_period(self, ttl: int = MSG_DELETION_PERIOD):
        if ttl == 0:
            return True
        now = common.now()
        return now - self.created_at <= ttl

    def is_poll_type(self):
        return self.body.type == MSG_POLL_TYPE

    def get_poll_id(self) -> Optional[str]:
        if not self.body.metadata:
            return None
        poll_i = self.body.metadata["poll_information"]
        if isinstance(poll_i, dict):
            return poll_i["id"]  # type: ignore
        elif isinstance(poll_i, str):
            return poll_i
        else:
            return poll_i.id  # type: ignore

    def update_poll(self, poll):
        self.body.metadata["poll_information"] = poll  # type: ignore

    def build_media_link(self):
        temp_body = self.body.dict()
        metadata = temp_body.get("metadata", None)

        urls = set()
        if metadata:
            preview_link = metadata.get("preview_link", None)
            if preview_link:
                urls.add(preview_link["source_url"])

        match_urls = extractor.find_urls(self.body.text)
        for url in match_urls:
            urls.add(url)

        return {
            "body": list(urls),
            "media": self.body.media,
            "metadata": metadata,
            "created_at": self.created_at,
        }

    def get_action_note_type(self):
        if not isinstance(self.body, str) and self.body.metadata is not None:
            return self.body.metadata.get("action_note_type", None)
        return None

    def is_action_note(self):
        return self.get_action_note_type() is not None

    def build_last_message(self) -> LastMessageModel:
        return LastMessageModel(
            id=self.id,
            body=self.body.text,
            raw_body=self.body.dict(),
            user_id=self.user_id,
            created_at=self.created_at,
            delete_level=self.delete_level,
        )

    def update_react(self, react_type, position):
        self.react_binary = react_helper.encode(
            self.react_binary, react_type, position
        )

    def mark_react(self) -> bool:
        if self.has_react():
            return False
        self.react_binary = constant.HAS_REACT
        return True

    def has_react(self) -> bool:
        return self.react_binary == constant.HAS_REACT

    def decode_react(self):
        return react_helper.decode(self.react_binary)

    def attach_sub_thread(self, sub_thread_id: int):
        self.sub_thread_id = sub_thread_id

    def __eq__(self, other):
        if not isinstance(other, MessageModel):
            raise NotImplementedError
        return (
            self.bucket == other.bucket
            and self.id == other.id
            and self.created_at == self.created_at
            and self.will_deleted_at == other.will_deleted_at
            and self.delete_level == other.delete_level
            and self.body == other.body
            and self.uuid == other.uuid
            and self.version == other.version
            and self.pinned_at == other.pinned_at
            and self.edited_at == other.edited_at
            and self.type == other.type
            and self.deleted == other.deleted
        )

    def is_system(self) -> bool:
        return self.user_id == constant.SYSTEM_ID

    def to_delete_event(self) -> DeleteEvent:
        if not self.will_deleted_at:
            raise Exception("wrong will deleted_at")
        return DeleteEvent(
            body=DeleteEventBody(
                thread_id=self.thread_id,
                message_id=self.id,
                will_deleted_at=(self.will_deleted_at),
            ),
            uuid=self.uuid,
            event_at=common.now(),
        )
