import falcon

from pydantic import ValidationError

from chat.app.app import <PERSON>App
from chat.app.resource import UserAndServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreate
from chat.messages.model import MessageBody
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.model import make_input


class MessageResource(UserAndServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.usecase = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        try:
            params = CreateMessageParams(
                collab_id=body.collab_id if body.collab_id else "",
                thread_id=body.thread_id if body.thread_id else 0,
                partner_id=body.partner_id if body.partner_id else "",
                client_id=body.client_id if body.client_id else "",
                pass_code=body.pass_code if body.pass_code else "",
                user_id=self.user_id,
                role=self.role,
                message_id=body.message_id,
                body=MessageBody(**body.body),
                workspace_id=self.workspace_id,
                api_version=self.api_version,
                lang=self.lang,
            )
        except ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())

        thread_id, sub_thread_id, new_uuid, msg_id = self.usecase.create_v2(
            params
        )

        data = {
            "event_id": new_uuid,
            "thread_id": thread_id,
            "sub_thread_id": sub_thread_id,
            "message_id": msg_id,
            "client_id": params.client_id,
        }

        resp.media = {"data": data}
        resp.status = falcon.HTTP_200
