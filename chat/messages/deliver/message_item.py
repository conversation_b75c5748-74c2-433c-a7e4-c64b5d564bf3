import falcon

from chat.app import User<PERSON><PERSON>Resource, InternalServiceResource
from chat.app.app import My<PERSON><PERSON>
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaDeleteItems, SchemaEditMessage
from chat.messages.usecase import MessageUseCases
from chat.model import make_input


class MessageItemResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecases = usecases

    @falcon.before(making_message_hook)
    def on_put(self, req: falcon.Request, resp: falcon.Response, message_id):
        user_id = self.user_id

        raw = req.context.body
        body, errors = make_input(SchemaEditMessage, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        self.usecases.edit_message.update(
            user_id, body.thread_id, message_id, body.body
        )
        resp.status = falcon.HTTP_200
        resp.media = {"message": "Thành công"}

    def on_delete(
        self, req: falcon.Request, resp: falcon.Response, message_id
    ):
        thread_id = req.get_param_as_int("thread_id", required=True)
        level = req.get_param_as_int(
            "level", min_value=1, max_value=2, required=True
        )
        user_id = self.user_id
        self.usecases.delete_message.delete(
            user_id, thread_id, message_id, level
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class MessageInternalItemResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecases = usecases

    def on_delete(
        self, req: falcon.Request, resp: falcon.Response, message_id
    ):
        thread_id = req.get_param_as_int("thread_id", required=True)
        level = req.get_param_as_int(
            "level", min_value=1, max_value=2, required=True
        )
        user_id = self.user_id
        self.usecases.delete_message.delete(
            user_id, thread_id, message_id, level, bypass=True
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class MessageDeleteItemsResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.delete_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaDeleteItems, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        self.usecase.delete_many(
            user_id, body.thread_id, body.message_ids, body.level,
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
