import falcon
import pydantic

from chat import constant
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateBotMessage, PollPayload
from chat.messages.model.api import SchemaEditBotMessage
from chat.messages.model import MessageBody
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.model import make_input


class MessageBotResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.create_usc = usecases.create_message
        self.edit_usc = usecases.edit_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaCreateBotMessage, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        try:
            params = CreateMessageParams(
                collab_id=body.collab_id if body.collab_id else "",
                thread_id=body.thread_id if body.thread_id else 0,
                partner_id=body.partner_id if body.partner_id else "",
                user_id=body.bot_id,
                message_id=body.message_id,
                body=MessageBody(**body.body),
                workspace_id=self.workspace_id,
                api_version=self.api_version,
                bypass=body.bypass_all,
                skip_deactivated_user=True,
                role=constant.USER_ROLE,  # bot like user role
                poll_vote=PollPayload(**body.body),
                lang=self.lang,
            )
        except pydantic.ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())
        thread_id, sub_thread_id, new_uuid, msg_id = self.create_usc.create_v2(
            params
        )

        # if thread_id:
        #     if message_id == 0:
        #         new_uuid, msg_id = self.create_usc._create(
        #             user_id=bot_id,
        #             thread_id=thread_id,
        #             body=m_body,
        #             api_version=self.api_version,
        #             client_id=None,
        #             bypass_all=bypass_all,
        #         )
        #     else:
        #         (
        #             sub_thread_id,
        #             new_uuid,
        #             msg_id,
        #         ) = self.create_usc.create_msg_subthread(
        #             user_id=bot_id,
        #             thread_id=thread_id,
        #             message_id=message_id,
        #             body=m_body,
        #             api_version=self.api_version,
        #             client_id=None,
        #             bypass_all=bypass_all,
        #         )
        # elif collab_id:
        #     thread_id, new_uuid, msg_id = (
        #         self.create_usc.create_collab_message(
        #             user_id=bot_id,
        #             collab_id=collab_id,
        #             body=m_body,
        #             api_version=self.api_version,
        #             client_id=None,
        #             bypass_all=bypass_all,
        #         )
        #     )
        # else:
        #     assert partner_id
        #     (
        #         thread_id,
        #         new_uuid,
        #         msg_id,
        #     ) = self.create_usc.create_message_to_partner(
        #         user_id=bot_id,
        #         partner_id=partner_id,
        #         body=m_body,
        #         api_version=self.api_version,
        #         partner_type="user",
        #         client_id=None,
        #         bypass_all=bypass_all,
        #         # don't send message to deactivated users
        #         skip_deactivated_user=True,
        #     )

        resp.media = {
            "data": {
                "event_id": new_uuid,
                "thread_id": thread_id,
                "sub_thread_id": sub_thread_id,
                "message_id": msg_id,
                "client_id": params.client_id,
            }
        }
        resp.status = falcon.HTTP_200
        self.log.info(f"Created message {new_uuid}")

    @falcon.before(making_message_hook)
    def on_put(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaEditBotMessage, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        # self.log.info("body edit %s", body.dict())
        self.edit_usc.update(
            body.bot_id,
            body.thread_id,
            body.message_id,
            body.body,
            bypass=body.bypass_all,
        )
        resp.status = falcon.HTTP_200
        resp.media = {"message": "Thành công"}
