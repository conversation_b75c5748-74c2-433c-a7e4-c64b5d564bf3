import falcon

from chat.app.app import MyApp
from chat.app.resource import UserAuthResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaPostForward, SchemaPostForwards
from chat.messages.usecase import MessageUseCases
from chat.model import make_input
from chat.utils import common


class MessageForwardResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.forward_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaPostForward, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        if body.thread_id:
            self.usecase.forward(
                user_id,
                body.source_thread_id,
                body.source_message_id,
                body.thread_id,
                self.api_version,
                body.client_id,
            )
        else:
            self.usecase.forward_to_partner(
                user_id,
                body.source_thread_id,
                body.source_message_id,
                body.partner_id,  # type: ignore
                body.partner_type,
                self.api_version,
                body.client_id,
            )

        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class MessageForwardsResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.forward_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaPostForwards, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        if body.thread_id:
            event_id, thread_id = self.usecase.forward_many(
                user_id,
                body.source_thread_id,
                body.source_message_ids,
                body.thread_id,
                self.api_version,
                body.client_id,
            )

            resp.media = {"data": {"event_id": event_id}}
        else:
            assert body.partner_id
            partner_type = common.get_account_type(body.partner_id)
            event_id, thread_id = self.usecase.forward_many_to_partner(
                user_id,
                body.source_thread_id,
                body.source_message_ids,
                body.partner_id,
                partner_type,
                self.api_version,
                body.client_id,
            )
            resp.media = {"data": {"event_id": event_id}}
        resp.status = falcon.HTTP_200
