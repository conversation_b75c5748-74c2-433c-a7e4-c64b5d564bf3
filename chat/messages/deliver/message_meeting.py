import falcon
import pydantic

from chat import constant
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateMeetingMessage
from chat.messages.model import MessageBody
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.model import make_input


class MessageMeetingResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.usecase = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaCreateMeetingMessage, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        try:
            params = CreateMessageParams(
                collab_id=body.collab_id if body.collab_id else "",
                thread_id=body.thread_id if body.thread_id else 0,
                partner_id=body.partner_id if body.partner_id else "",
                user_id=body.user_id,
                body=MessageBody(**body.body),
                workspace_id=self.workspace_id,
                api_version=self.api_version,
                role=constant.USER_ROLE,
                bypass=True,
                lang=self.lang,
            )
        except pydantic.ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())
        thread_id, sub_thread_id, new_uuid, msg_id = self.usecase.create_v2(
            params
        )

        # if collab_id:
        #     thread_id, new_uuid, msg_id = self.usecase.create_collab_message(
        #         str(user_id),
        #         collab_id,
        #         m_body,
        #         self.api_version,
        #         client_id=None,
        #         user_type=body["user_type"],
        #         bypass_all=True,
        #     )
        # elif thread_id:
        #     new_uuid, msg_id = self.usecase._create(
        #         str(user_id),
        #         thread_id,
        #         m_body,
        #         self.api_version,
        #         client_id=None,
        #         bypass_all=True,
        #         user_type=body["user_type"],
        #     )
        # else:
        #     thread_id, new_uuid, __ = self.usecase.create_message_to_partner(
        #         str(user_id),
        #         partner_id,
        #         m_body,
        #         self.api_version,
        #         "user",
        #         client_id=None,
        #         bypass_all=True,
        #         user_type=body["user_type"],
        #     )

        resp.media = {
            "data": {
                "event_id": new_uuid,
                "thread_id": thread_id,
                "sub_thread_id": sub_thread_id,
                "message_id": msg_id,
                "client_id": params.client_id,
            }
        }
        resp.status = falcon.HTTP_200
        self.log.info(f"create message {new_uuid}")
