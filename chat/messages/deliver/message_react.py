import falcon

from chat.app import User<PERSON>uthResource
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaPostReact
from chat.messages.usecase import MessageUseCases
from chat.model import make_input


class MessageReactResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.react_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaPostReact, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        self.usecase.react_message(
            thread_id=body.thread_id,
            message_id=body.message_id,
            user_id=user_id,
            react_type=body.react_type,
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
