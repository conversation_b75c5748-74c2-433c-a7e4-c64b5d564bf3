import falcon
import pydantic

from chat import constant
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateCallServiceMessage
from chat.messages.model import MessageBody
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.model import make_input


class MessageCallServiceResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.create_usc = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaCreateCallServiceMessage, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        try:
            user_id = self.user_id
            if self.user_id == constant.SYSTEM_ID:
                user_id = self.config.gapo.call_bot_id

            params = CreateMessageParams(
                thread_id=body.thread_id if body.thread_id else 0,
                user_id=user_id,
                partner_id=body.partner_id if body.partner_id else "",
                body=MessageBody(**body.body.dict()),
                api_version=self.api_version,
                role=constant.USER_ROLE,
                bypass=True,
                lang=self.lang,
            )
        except pydantic.ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())

        thread_id, sub_thread_id, new_uuid, msg_id = self.create_usc.create_v2(
            params
        )

        # body = body.dict()
        # m_body = body["body"]
        # partner_id = body["partner_id"]
        # thread_id = body["thread_id"]
        # user_id = self.user_id

        # new_uuid = self.usecase.create_call_service_message(
        #     user_id,
        #     partner_id,
        #     thread_id,
        #     m_body,
        #     self.api_version,
        # )
        resp.media = {
            "data": {
                "event_id": new_uuid,
                "thread_id": thread_id,
                "sub_thread_id": sub_thread_id,
                "message_id": msg_id,
                "client_id": params.client_id,
            }
        }
        resp.status = falcon.HTTP_200
        self.log.info(f"create message {new_uuid}")
