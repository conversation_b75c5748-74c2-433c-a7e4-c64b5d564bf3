import falcon
import pydantic

from chat import constant
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateZoomMessage
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.messages.model import MessageBody
from chat.model import make_input


class MessageZoomResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.create_usc = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaCreateZoomMessage, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        # body = body.dict()
        # m_body = body["body"]
        # thread_id = body["thread_id"]
        # user_id = body["user_id"]

        try:
            params = CreateMessageParams(
                thread_id=body.thread_id if body.thread_id else 0,
                user_id=body.user_id,
                body=MessageBody(**body.body.dict()),
                api_version=self.api_version,
                role=constant.USER_ROLE,
                bypass=True,
                lang=self.lang,
            )
        except pydantic.ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())

        thread_id, sub_thread_id, new_uuid, msg_id = self.create_usc.create_v2(
            params
        )

        # new_uuid, msg_id = self.usecase._create(
        #     str(user_id),
        #     thread_id,
        #     m_body,
        #     self.api_version,
        #     client_id=None,
        #     bypass_all=True,
        # )

        resp.media = {
            "data": {
                "event_id": new_uuid,
                "thread_id": thread_id,
                "sub_thread_id": sub_thread_id,
                "message_id": msg_id,
                "client_id": params.client_id,
            }
        }
        resp.status = falcon.HTTP_200
        self.log.info(f"Created message {new_uuid}")
