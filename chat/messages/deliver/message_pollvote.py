import falcon

from chat.app import UserAuthResource
from chat.app.app import <PERSON><PERSON>pp
from chat.exception import (
    InternalServerError,
    InvalidParameters,
    MessageNotFound,
)
from chat.hooks import making_message_hook
from chat.messages.model.api import (
    SchemaOutput,
    SchemaPollCreate,
    SchemaPollEdit,
    SchemaPollVoteCreate,
    SchemaPollVoteDelete,
    SchemaPollVoteEdit,
    SchemaUserVoteChoiceCreate,
    SchemaUserVoteUnchoiceCreate,
)
from chat.messages.usecase import MessageUseCases
from chat.model import make_input, make_output


class MessageUserVoteChoiceResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pollvote_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaUserVoteChoiceCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        message = self.usecase.choose_vote_by_user(
            user_id=user_id,
            thread_id=body.thread_id,
            message_id=body.message_id,
            vote_id=body.vote_id,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)
        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200


class MessageUserVoteUnchoiceResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pollvote_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaUserVoteUnchoiceCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        message = self.usecase.unvote_by_user(
            user_id=user_id,
            thread_id=body.thread_id,
            message_id=body.message_id,
            vote_id=body.vote_id,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)
        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200


class MessagePollVoteResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pollvote_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaPollVoteCreate, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        message = self.usecase.create_poll_vote(
            user_id=user_id,
            thread_id=body.thread_id,
            message_id=body.message_id,
            body=body.body,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)

        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200

    @falcon.before(making_message_hook)
    def on_patch(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaPollVoteEdit, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        message = self.usecase.update_poll_vote(
            user_id=user_id,
            thread_id=body.thread_id,
            message_id=body.message_id,
            body=body.body,
            vote_id=body.vote_id,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)
        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200

    def on_delete(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        params = req.params or {}
        parse_params, errors = make_input(SchemaPollVoteDelete, params)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        message = self.usecase.delete_poll_vote(
            user_id=user_id,
            thread_id=parse_params.thread_id,
            message_id=parse_params.message_id,
            vote_id=parse_params.vote_id,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)
        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200


class MessagePollResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pollvote_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaPollCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        new_uuid, msg_id = self.usecase.create_poll_message(
            user_id=user_id,
            thread_id=body.thread_id,
            body=body.body,
            api_version=self.api_version,
            client_id=body.client_id,
        )
        response = {
            "data": {
                "event_id": new_uuid,
                "thread_id": body.thread_id,
                "message_id": msg_id,
            }
        }
        if body.client_id:
            response["data"]["client_id"] = body.client_id
        resp.media = response
        resp.status = falcon.HTTP_200

    @falcon.before(making_message_hook)
    def on_patch(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaPollEdit, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        message = self.usecase.update_poll(
            user_id=user_id,
            thread_id=body.thread_id,
            message_id=body.message_id,
            body=body.body,
            api_version=self.api_version,
        )
        if not message:
            raise MessageNotFound()

        result, errors = make_output(SchemaOutput, message.dict())
        if errors:
            raise InternalServerError(error_details=errors)
        resp.media = {"data": result.dict()}
        resp.status = falcon.HTTP_200
