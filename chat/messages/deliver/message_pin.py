import falcon

from chat.app.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.messages.usecase import MessageUseCases


class MessagePinResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pin_message

    def on_post(self, req: falcon.Request, resp: falcon.Response, message_id):
        thread_id = req.get_param_as_int("thread_id", required=True)
        pass_code = req.get_param("pass_code")
        user_id = self.user_id
        self.usecase.pin(thread_id, message_id, user_id, pass_code)

        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class MessageUnpinResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pin_message

    def on_post(self, req: falcon.Request, resp: falcon.Response):
        thread_id = req.get_param_as_int("thread_id", required=True)
        message_id = req.get_param_as_int(
            "id", default=0, min_value=0, max_value=2**64
        )
        user_id = self.user_id
        self.usecase.unpin(thread_id, user_id, message_id)

        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
