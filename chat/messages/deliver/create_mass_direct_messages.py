import falcon
from falcon import Request, Response

from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InternalServerError, InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateMassDirectMessages
from chat.messages.usecase import MessageUseCases
from chat.model import make_input


class CreateMassDirectMessagesResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.log = app.log
        self.usecase = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: Request, resp: Response):

        raw = req.context.body
        body, errors = make_input(SchemaCreateMassDirectMessages, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        message_body = body.body
        sender_id = body.sender_id
        partner_ids = body.user_ids

        event_id = self.usecase.create_mass_direct_messages(
            sender_id=sender_id,
            user_ids=partner_ids,
            body=message_body,
            api_version=self.api_version,
        )
        if not event_id:
            raise InternalServerError(
                error_details=[{"msg": "Failed to create tasks"}]
            )

        resp.media = {"data": {"event_id": event_id}}
        resp.status = falcon.HTTP_200
