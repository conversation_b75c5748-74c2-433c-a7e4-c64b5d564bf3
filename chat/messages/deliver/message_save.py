import falcon

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaPostForwards
from chat.messages.usecase import MessageUseCases
from chat.model import make_input


class MessageSaveMessage(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.forward_message
        self.api_version = self.config.api_version

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaPostForwards, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id

        event_id, thread_id = self.usecase.forward_many_to_partner(
            user_id,
            body.source_thread_id,
            body.source_message_ids,
            body.partner_id,  # type: ignore
            body.partner_type,  # type: ignore
            self.api_version,
            body.client_id,
        )
        resp.status = falcon.HTTP_200
        resp.media = {"thread_id": thread_id, "event_id": event_id}
