import falcon
import pydantic

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_message_hook
from chat.messages.model.api import SchemaCreateAuthServiceMessage
from chat.messages.model import MessageBody
from chat.messages.usecase import MessageUseCases, CreateMessageParams
from chat.model import make_input


class MessageAuthServiceResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: MessageUseCases):
        self.config = app.conf
        self.api_version = self.config.api_version
        self.PAGE_SIZE = self.config.page_size
        self.log = app.log
        self.usecase = usecases.create_message

    @falcon.before(making_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):

        raw = req.context.body
        body, errors = make_input(SchemaCreateAuthServiceMessage, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        try:
            params = CreateMessageParams(
                partner_id=self.user_id,
                role=self.role,
                body=MessageBody(**body.body.dict()),
                workspace_id=self.workspace_id,
                api_version=self.api_version,
                lang=self.lang,
            )
        except pydantic.ValidationError as e:
            self.log.warning(e)
            raise InvalidParameters(error_details=e.errors())

        thread_id, sub_thread_id, new_uuid, msg_id = self.usecase.create_v2(
            params
        )

        # body = body.dict()
        # m_body = body["body"]
        # user_id = self.user_id

        # new_uuid = self.usecase.create_auth_message(
        #     user_id, m_body, self.api_version
        # )
        data = {
            "event_id": new_uuid,
            "thread_id": thread_id,
            "sub_thread_id": sub_thread_id,
            "message_id": msg_id,
            "client_id": params.client_id,
        }

        resp.media = {"data": data}
        resp.status = falcon.HTTP_200
        self.log.info(f"create message {new_uuid}")
