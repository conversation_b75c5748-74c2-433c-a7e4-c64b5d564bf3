from dependency_injector import containers, providers

from chat.app import MyApp
from chat.messages.deliver import (
    CreateMassDirectMessagesResource,
    MessageAuthServiceResource,
    MessageBotResource,
    MessageCallServiceResource,
    MessageDeleteItemsResource,
    MessageForwardResource,
    MessageForwardsResource,
    MessageItemResource,
    MessageMeetingResource,
    MessagePinResource,
    MessagePollResource,
    MessagePollVoteResource,
    MessageReactResource,
    MessageResource,
    MessageSaveMessage,
    MessageUnpinResource,
    MessageUserVoteChoiceResource,
    MessageUserVoteUnchoiceResource,
    MessageWalletResource,
    MessageZoomResource,
    MessageInternalItemResource,
)
from chat.messages.usecase import MessageUseCases
from chat.participants.usecase import ParticipantUseCases
from chat.threads.usecase import ThreadUsecases


class MessageResourcesContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(MessageUseCases)

    messages = providers.Singleton(MessageResource, app, usecases)
    message_item = providers.Singleton(MessageItemResource, app, usecases)
    pin = providers.Singleton(MessagePinResource, app, usecases)
    unpin = providers.Singleton(MessageUnpinResource, app, usecases)
    wallet = providers.Singleton(MessageWalletResource, app, usecases)
    call = providers.Singleton(MessageCallServiceResource, app, usecases)
    auth = providers.Singleton(MessageAuthServiceResource, app, usecases)
    react = providers.Singleton(MessageReactResource, app, usecases)
    forward = providers.Singleton(MessageForwardResource, app, usecases)
    forwards = providers.Singleton(MessageForwardsResource, app, usecases)
    delete_item = providers.Singleton(
        MessageDeleteItemsResource, app, usecases
    )
    internal_message_item = providers.Singleton(
        MessageInternalItemResource, app, usecases
    )
    zoom = providers.Singleton(MessageZoomResource, app, usecases)
    meeting = providers.Singleton(MessageMeetingResource, app, usecases)
    bot = providers.Singleton(MessageBotResource, app, usecases)
    mass_direct_messages = providers.Singleton(
        CreateMassDirectMessagesResource, app, usecases
    )
    poll = providers.Singleton(MessagePollResource, app, usecases)
    vote = providers.Singleton(MessagePollVoteResource, app, usecases)
    vote_choice = providers.Singleton(
        MessageUserVoteChoiceResource, app, usecases
    )
    unvote_choice = providers.Singleton(
        MessageUserVoteUnchoiceResource, app, usecases
    )
    save_message = providers.Singleton(MessageSaveMessage, app, usecases)


class MessageContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    thread_usc = providers.Dependency(ThreadUsecases)
    participant_usc = providers.Dependency(ParticipantUseCases)
    usecases = providers.Singleton(
        MessageUseCases,
        app=app,
        thread_usc=thread_usc,
        participant_usc=participant_usc,
    )

    resources = providers.Singleton(
        MessageResourcesContainer, app=app, usecases=usecases
    )
