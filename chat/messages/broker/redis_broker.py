# from logging import Logger

# from chat.config import Settings
# from chat.connections.redis_c import RedisConnection


# class MessageRedisBroker(object):
#     def __init__(self, config: Settings, log: Logger, engine: RedisConnection):
#         self.config = config.redis
#         self.log = log
#         self.engine = engine
#         self.msf = self.config.message_score_format
#         self.tsf = self.config.thread_score_format
#         self.tfsf = self.tsf + "_{folder}"
#         self.bad_comment_key = self.config.bad_comment_key
#         self.member_key = self.config.thread_member_format

#     # def remove_score(self, user_ids, thread_id):
#     #     if not isinstance(user_ids, list)::
#     #         user_ids = [user_ids]
#     #     for user_id in user_ids:
#     #         self.engine.client.delete(
#     #             self.msf.format(user_id=user_id, thread_id=thread_id)
#     #         )

#     # def del_thread(self, thread_id: int, member):
#     #     c = self.engine.client
#     #     threadScore = self.tsf.format(user_id=member["user_id"])
#     #     folderThreadScore = self.tfsf.format(
#     #         user_id=member["user_id"], folder=member["folder"]
#     #     )
#     #     p = c.pipeline()
#     #     p.zrem(threadScore, thread_id)
#     #     p.zrem(folderThreadScore, thread_id)
#     #     p.execute()

#     # def badList(self):
#     #     return self.engine.client.smembers(self.badKey)

#     # def remove_message_score(self, user_ids, thread_id: int, msg_id):
#     #     if not isinstance(user_ids, list):
#     #         user_ids = [user_ids]
#     #     for user_id in user_ids:
#     #         self.engine.client.zrem(
#     #             self.msf.format(user_id=user_id, thread_id=thread_id), msg_id
#     #         )

#     # def get_last_message_score(self, user_id: str, thread_id):
#     #     result = self.engine.client.zrevrangebyscore(
#     #         self.msf.format(user_id=user_id, thread_id=thread_id),
#     #         "+inf",
#     #         "-inf",
#     #         start=0,
#     #         num=1,
#     #     )
#     #     if result:
#     #         return int(result[0])
#     #     return None

#     # def update_score(self, thread_id: int, member, score):
#     #     folder = member["folder"]
#     #     user_id = member["user_id"]
#     #     if folder in constant.FOLDER_COUNT_ALL:
#     #         self.engine.client.zadd(
#     #             self.tsf.format(user_id=user_id), {thread_id: score}
#     #         )
#     #     self.engine.client.zadd(
#     #         self.tfsf.format(user_id=user_id, folder=folder),
#     #         {thread_id: score},
#     #     )

#     # def active_count(self, thread_id):
#     #     less_than_10_min = now() - 60 * 1000 * 10
#     #     key = self.member_key.format(thread_id=thread_id)
#     #     return self.engine.client.zcount(key, less_than_10_min, "+inf")
