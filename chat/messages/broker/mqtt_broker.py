from logging import Logger
from typing import Dict, Iterable, List, Optional

from chat import constant
from chat.config import Settings
from chat.publishers.mqtt import MqttPublisher


class MessageMQTTBroker(object):
    def __init__(self, config: Settings, log: Logger, engine: MqttPublisher):
        self.config = config.mqtt
        self.log = log
        self.engine = engine

    def publish_message_deleted_event(
        self,
        member_ids: List[str],
        thread_id: int,
        message_id: int,
        level: int,
    ):
        msg = {
            "thread_id": thread_id,
            "message_id": message_id,
            "level": level,
        }
        event = {"event_type": "message_deleted", "body": msg}
        self.engine.publish_status(member_ids, event)

    def publish_message_reaction_event(
        self,
        member_ids: Iterable[str],
        thread_id: int,
        message_id: int,
        react_type: int,
        react_user_id: str,
        previous_reaction: Optional[str] = None,
    ):
        msg = {
            "thread_id": thread_id,
            "message_id": message_id,
            "react_type": react_type,
            "react_user_id": react_user_id,
        }
        if previous_reaction is not None:
            msg["previous_reaction"] = previous_reaction

        event = {"event_type": "message_react", "body": msg}
        self.engine.publish_status(member_ids, event)

    def publish_message_edited_event(
        self,
        actor_id: str,
        member_ids: List[str],
        thread_id: int,
        message_id: int,
        message_user_id: str,
        body: Dict,
    ):
        msg = {
            "thread_id": thread_id,
            "message_id": message_id,
            "actor_id": actor_id,
            "message_user_id": message_user_id,
            "body": body,
        }
        event = {"event_type": constant.TASK_EDIT_MESSAGE, "body": msg}
        self.engine.publish_status(member_ids, event)
