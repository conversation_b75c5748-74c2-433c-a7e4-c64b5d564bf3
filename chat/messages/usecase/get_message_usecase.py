import concurrent.futures
from typing import Dict, List, Optional

from chat import constant
from chat.messages.model import MessageModel
from chat.users.model import PublicProfile
from chat.utils import common as utils
from chat.utils import react_helper
from chat.utils.common import get_account_type
from chat.threads.model import ThreadInformation

from .base import MessageUsecase

HAS_REACT = -1
NO_REACT = (None, 0)


class GetMessageUseCase(MessageUsecase):
    def get_by_id(
        self,
        user_id: str,
        thread_id: int,
        message_id: Optional[int] = None,
        pass_code=None,
    ) -> Optional[MessageModel]:
        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            delete_to = p_thread.participant.delete_to or 0
            message_count = p_thread.thread.message_count
            if delete_to == message_count:
                return None

            thread_data = p_thread.thread.information or ThreadInformation()

            if not isinstance(message_id, int):
                # invalid message id
                return None

            if message_id > message_count or message_id <= delete_to:
                message = None

            # check permission
            else:
                message = self.m_repo.get_by_id(thread_id, message_id)
                if not message:
                    message = None
                else:
                    messages = self._validate_and_add_message_details(
                        thread_id,
                        user_id,
                        [message],
                        thread_data,
                        p_thread.thread.member_count,
                        p_thread.thread.pair_ids,
                    )
                    if not messages:
                        self.log.warning(
                            "Found invalid message after validating messages"
                        )
                        message = None
                    else:
                        message = messages[0]
            return message

    def _update_user_info_message(
        self,
        message_senders: Dict[int, PublicProfile],
        user_info: Dict[int, str],
    ):
        if user_info:
            users = self._get_users(list(user_info.values()))
            for m, k in user_info.items():
                info = users[k]
                info["type"] = constant.USER_TYPE
                message_senders[m] = info

    def _get_users(self, user_ids: List[str]):
        return self.i_storage.get_users(user_ids)

    def _get_user(self, user_id: str):
        return self.i_storage.get_users([user_id]).get(str(user_id))

    def _update_forward_messages(self, messages: List[MessageModel]):
        forwards: Dict = {}
        forward_users: Dict = {}
        message_has_forwards: List[MessageModel] = []
        for m in messages:
            if not m.body.forward:
                continue
            fw_t = m.body.forward["thread_id"]
            ms_fw = forwards.get(fw_t, set())
            fw_m_id = m.body.forward["message_id"]
            ms_fw.add(fw_m_id)
            forwards[fw_t] = ms_fw
            forward_users[f"{fw_t}_{fw_m_id}"] = m.body.forward.get(
                "user_id", "system"
            )
            message_has_forwards.append(m)

        mapping_messages = {}
        user_ids = set()
        for k, v in forwards.items():
            f_messages = self.m_repo.get_by_ids(k, v)

            for m in f_messages:
                mapping_messages[f"{k}_{m.id}"] = m.user_id
                user_ids.add(m.user_id)

            # fix missing forward but forward is deleted
            for k, v in forward_users.items():
                if not mapping_messages.get(k):
                    mapping_messages[k] = v
                    user_ids.add(v)

        users = self._get_users(list(user_ids))

        for m in message_has_forwards:
            t_fw = m.body.forward["thread_id"]  # type: ignore
            m_fw = m.body.forward["message_id"]  # type: ignore
            m.body.forward["user"] = users[mapping_messages[f"{t_fw}_{m_fw}"]]  # type: ignore # noqa

    def _fill_reply_message(
        self, messages: List[MessageModel], mapping_reply_messages: Dict
    ):
        for m in messages:
            if not m.is_deleted_2_side():
                reply_id = m.body.reply_to_msg
                reply_msg = mapping_reply_messages.get(reply_id, None)
                if not reply_msg:
                    continue
                m.fill_reply_message_object(reply_msg)

    def _fill_sender_info(
        self,
        owner_id: str,
        messages: List[MessageModel],
        message_senders: Dict[int, PublicProfile],
        user_info: Dict[int, str],
        page_info,
        bot_info: Dict[int, str],
        system_msg_ids: List[int],
    ):
        for m in messages:
            user_type = get_account_type(m.user_id)
            if user_type == constant.USER_TYPE:
                user_info[m.id] = m.user_id
            elif user_type == constant.PAGE_TYPE:
                page_info[m.id] = m.user_id
            elif user_type == constant.BOT_TYPE:
                bot_info[m.id] = m.user_id
            elif user_type == constant.SYSTEM_TYPE:
                system_msg_ids.append(m.id)
            else:
                self.log.warning("Check type user with {}".format(m.user_id))
                raise Exception("User type not found")

    def _get_mapping_reply_messages(
        self,
        thread_data: ThreadInformation,
        thread_id: int,
        messages: List[MessageModel],
        message_ids: List[int],
        reply_ids: List[int],
        deleted_ids: List[int],
    ):
        mapping_reply_messages: Dict[int, MessageModel] = {}
        reply_messages = []
        reply_ids = set(reply_ids)
        message_ids = set(message_ids)
        deleted_ids = set(deleted_ids)

        need_get_reply_ids = reply_ids - message_ids - deleted_ids
        already_get_ids = reply_ids & message_ids

        if need_get_reply_ids:
            reply_messages = self.m_repo.get_by_ids(
                thread_id, list(need_get_reply_ids)
            )

        for m in reply_messages:
            if not m:
                continue
            mapping_reply_messages[m.id] = m
            if self._is_clear(thread_data, m) and not m.is_deleted_2_side():
                m.delete()

        for m in messages:
            if m.id in already_get_ids:
                mapping_reply_messages[m.id] = m

        return mapping_reply_messages

    def _update_reacts(
        self,
        thread_id: int,
        messages: List[MessageModel],
        user_id: str,
        pair_ids=None,
    ):
        react_remote_messages = {}
        react_remote_created_at = []
        react_remote_message_id = []
        for i, message in enumerate(messages):
            react_binary = message.react_binary
            if react_binary == HAS_REACT:
                react_remote_messages[i] = message.id
                react_remote_message_id.append(message.id)
                react_remote_created_at.append(message.created_at)
            elif react_binary in NO_REACT:
                react_info = self.gapo_client.react.get_default(
                    thread_id, message.id, message.created_at
                )
                message.fill_react(react_info)
            else:
                position = utils.get_position(user_id, pair_ids)
                react_types = react_helper.decode(react_binary)
                react = self.gapo_client.react.build_local(
                    thread_id,
                    message.id,
                    message.created_at,
                    react_types,
                    react_types[position],
                )
                message.react = react

        if react_remote_messages:
            reacts = self.gapo_client.react.get_react(
                user_id,
                thread_id,
                react_remote_message_id,
                react_remote_created_at,
            )
            for index, msg_id in react_remote_messages.items():
                messages[index].react = reacts[str(msg_id)]  # type: ignore
        return messages

    def _update_reads(
        self, thread_id: int, member_count: int, messages: List[MessageModel]
    ) -> List[MessageModel]:
        if member_count > self._max_member_status:
            return messages

        if not messages:
            return messages
        # vi sap xep tu to den nho dan cassandra
        min_message_id = messages[-1].id
        max_message_id = messages[0].id
        read_infos = self.redis_read.get_message_read_users_for_range(
            thread_id, min_message_id, max_message_id
        )
        for m in messages:
            read_users = read_infos.get(m.id, [])
            m.fill_read(read_users)
        return messages

    def _update_bot_info_message(
        self,
        message_senders: Dict[int, PublicProfile],
        bot_info: Dict[int, str],
    ):
        if bot_info:
            bots = self.i_storage.get_bots(list(bot_info.values()))
            for m, k in bot_info.items():
                info = bots[k]
                info["type"] = constant.BOT_TYPE
                message_senders[m] = info

    def _update_poll_info_message(self, poll_info: Dict, user_id: str):
        if poll_info:
            poll_m = self.gapo_client.poll.get_polls(
                user_id, set(poll_info.values())
            )
            for message_id, poll_id in poll_info.items():
                info = poll_m[poll_id]
                poll_info[message_id] = info

    def _update_system_info_message(
        self,
        message_senders: Dict[int, PublicProfile],
        system_msg_ids: List[int],
    ):
        for message_id in system_msg_ids:
            message_senders[message_id] = self.i_storage.get_system_user()

    def _validate_and_add_message_details(
        self,
        thread_id: int,
        owner_id: str,
        messages: List[MessageModel],
        thread_data: ThreadInformation,
        member_count: int,
        pair_ids=None,
    ) -> List[MessageModel]:
        if not isinstance(messages, list):
            messages = [messages]

        # map message_id -> user_id (for message by user)
        user_info: Dict[int, str] = {}
        page_info: Dict = {}

        # map message_id -> bot_id (for message by bot)
        bot_info: Dict[int, str] = {}
        poll_info: Dict[int, str] = {}

        system_msg_ids: List[int] = []
        message_senders: Dict[int, PublicProfile] = {}
        reply_ids: List[int] = []
        message_ids: List[int] = []
        deleted_ids: List[int] = []

        for m in messages[:]:
            if self._validate_message_permission(owner_id, m) is None:
                messages.remove(m)
                deleted_ids.append(m.id)
                continue

            message_ids.append(m.id)

            if self._is_clear(thread_data, m):
                if not m.is_deleted_2_side():
                    m.delete()

            if m.body.reply_to_msg:
                reply_ids.append(m.body.reply_to_msg)

            if m.is_poll_type():
                poll_info[m.id] = m.get_poll_id()  # type: ignore

        self._fill_sender_info(
            owner_id,
            messages,
            message_senders,
            user_info,
            page_info,
            bot_info,
            system_msg_ids,
        )

        mapping_reply_messages = self._get_mapping_reply_messages(
            thread_data,
            thread_id,
            messages,
            message_ids,
            reply_ids,
            deleted_ids,
        )

        self._update_forward_messages(messages)

        self._fill_sender_info(
            owner_id,
            mapping_reply_messages.values(),
            message_senders,
            user_info,
            page_info,
            bot_info,
            system_msg_ids,
        )

        jobs = []
        with concurrent.futures.ThreadPoolExecutor() as executor:
            jobs.append(
                executor.submit(
                    self._update_user_info_message, message_senders, user_info
                )
            )
            jobs.append(
                executor.submit(
                    self._update_bot_info_message, message_senders, bot_info
                )
            )
            jobs.append(
                executor.submit(
                    self._update_system_info_message,
                    message_senders,
                    system_msg_ids,
                )
            )
            jobs.append(
                executor.submit(
                    self._update_poll_info_message,
                    poll_info,
                    owner_id,
                )
            )

            for job in concurrent.futures.as_completed(jobs):
                continue

        for m in messages:
            m.fill_user(message_senders[m.id])
            if poll_info.get(m.id):
                m.update_poll(poll_info.get(m.id))

        for m in mapping_reply_messages.values():
            m.fill_user(message_senders[m.id])

        self._fill_reply_message(messages, mapping_reply_messages)

        messages = self._update_reacts(
            thread_id, messages, owner_id, pair_ids=pair_ids
        )
        messages = self._update_reads(thread_id, member_count, messages)

        return messages

    def _get_messages(
        self,
        thread_id: int,
        message_from: int,
        message_to: int,
        delete_to: int,
        message_count: int,
    ):
        if delete_to == message_count:
            return []

        is_invalid = message_from <= delete_to or message_to >= message_count
        if is_invalid:
            return []

        if message_from >= message_count:
            message_from = message_count

        if message_to <= delete_to:
            message_to = delete_to

        messages = self.m_repo.get_between(
            thread_id=thread_id, from_=message_from, to=message_to
        )
        return messages

    def _get_message_by_user(
        self, user_id: str, thread_id: int, message_id: int, is_reply=False
    ) -> MessageModel:
        message = self._get_message(thread_id, message_id)
        if not is_reply:
            message = self._validate_message_permission(
                user_id, message, max_level=constant.DELETE_MSG_TWO_SIDE
            )

        self._check_exists(message)
        return message
