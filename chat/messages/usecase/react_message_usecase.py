from typing import Optional, Union

from chat import constant
from chat.exception import (
    DataNotFound,
    MessageNotFound,
)
from chat.utils import common as utils
from chat.utils import react_helper
from chat.messages.model import MessageModel
from .get_message_usecase import GetMessageUseCase


class ReactMessageUseCase(GetMessageUseCase):
    def get_reacted_users_by_react_type(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        react_type: int,
        last_time: int,
    ):
        message = self.get_by_id(user_id, thread_id, message_id)
        if not message:
            return None
        data = self.gapo_client.react.get_react_users(
            user_id,
            thread_id,
            message.id,
            message.created_at,
            react_type,
            last_time,
        )
        return data

    def react_message(
        self,
        thread_id: int,
        message_id: int,
        user_id: str,
        react_type: int,
    ):
        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)

            delete_to = p_thread.participant.delete_to or 0
            message_count = p_thread.thread.message_count
            if message_count < message_id or message_id <= delete_to:
                raise DataNotFound("Can't react message not exists")

            message = self.m_repo.get_by_id(thread_id, message_id)
            if not message:
                raise MessageNotFound()

            message = self._validate_message_permission(
                user_id, message, max_level=constant.DELETE_MSG_TWO_SIDE
            )
            self._check_exists(message)
            react_user_id = user_id
            sender_id = message.user_id  # type: ignore

            status_message = self._react_update(
                p_thread.thread.type,
                p_thread.thread.id,
                message,
                sender_id,
                react_user_id,
                react_type,
                p_thread.thread.pair_ids,
            )

            active_member = self._get_active_members(
                thread_id, p_thread.thread.member_count
            )
            active_member.extend([react_user_id, sender_id])

            self.mqtt_broker.publish_message_reaction_event(
                set(active_member), **status_message
            )

    def _react_direct(
        self,
        thread_id: int,
        message: MessageModel,
        sender_id: str,
        react_user_id: str,
        react_type: int,
        pair_ids: Optional[str],
    ):
        position = utils.get_position(react_user_id, pair_ids)
        message.update_react(react_type, position)
        self.m_repo.update_react(message)

    def _react_group(
        self,
        thread_id: int,
        message: MessageModel,
        sender_id: str,
        react_user_id: str,
        react_type,
    ):
        self.gapo_client.react.post_react(
            react_user_id,
            thread_id,
            message.id,
            message.created_at,
            react_type,
        )

        message.mark_react()
        self.m_repo.update_react(message)

    def _get_user_message_reaction_in_direct_chat(
        self, user_id: str, thread_id: int, message_id: int, pair_ids
    ):
        # reactions are stored inside message in direct chat
        # so we need to get message from database
        if not pair_ids:
            # invalid data
            return None

        msg = self.m_repo.get_by_id(thread_id, message_id)
        if not msg:
            return None

        react_binary_code = msg.react_binary
        reactions = react_helper.decode(react_binary_code)
        user_position = utils.get_position(user_id, pair_ids)
        user_reaction = reactions[user_position]
        return user_reaction

    def _get_user_message_reaction_in_group_chat(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        message_created_at: int,
    ):
        user_reaction: Union[int, None] = None
        # so client can know what to remove
        msg_id_str = str(message_id)
        msg_reactions = self.gapo_client.react.get_react(
            user_id, thread_id, [message_id,], [message_created_at,]
        )

        if msg_id_str in msg_reactions:
            user_reaction = msg_reactions[msg_id_str].get("react_yourself", 0)

        return user_reaction

    def _get_user_message_reaction(
        self,
        user_id: str,
        thread_type: str,
        thread_id: int,
        message: MessageModel,
        pair_ids=None,
    ):
        if message.has_react():
            return self._get_user_message_reaction_in_group_chat(
                user_id, thread_id, message.id, message.created_at
            )
        elif message.react_binary in constant.NO_REACT:
            return None
        else:
            # reaction is stored inside message
            all_reactions = message.decode_react()
            user_reaction_pos = utils.get_position(user_id, pair_ids)
            user_reaction = all_reactions[user_reaction_pos]
            return user_reaction

        # QUESTION: do we need to check message first ?
        # if thread_type == constant.DIRECT_THREAD_TYPE:
        #     return self._get_user_message_reaction_in_direct_chat(
        #         user_id, thread_id, message_id, pair_ids
        #     )

        # return self._get_user_message_reaction_in_group_chat(
        #     user_id, thread_id, message_id
        # )

    def _react_update(
        self,
        thread_type: str,
        thread_id: int,
        message: MessageModel,
        sender_id: str,
        react_user_id: str,
        react_type: int,
        pair_ids=None,
    ):
        previous_reaction = self._get_user_message_reaction(
            react_user_id,
            thread_type,
            thread_id,
            message,
            pair_ids=pair_ids,
        )

        if thread_type in (
            constant.PAGE_THREAD_TYPE,
            constant.DIRECT_THREAD_TYPE,
        ):
            self._react_direct(
                thread_id,
                message,
                sender_id,
                react_user_id,
                react_type,
                pair_ids,
            )
        else:
            self._react_group(
                thread_id,
                message,
                sender_id,
                react_user_id,
                react_type,
            )

        status_message = {
            "react_type": react_type,
            "thread_id": thread_id,
            "message_id": message.id,
            "react_user_id": react_user_id,
        }

        status_message["previous_reaction"] = previous_reaction or 0

        return status_message
