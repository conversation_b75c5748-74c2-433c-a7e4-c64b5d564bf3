from sqlalchemy.orm import Session

from chat import action_type, constant
from chat.app.app import <PERSON>App
from chat.exception import (
    DataNotFound,
    MessageNotFound,
    MissingPasscode,
    PermissionDenied,
    UserNotFound,
    UserNotInThread,
    WrongPasscode,
    QueueIsFull,
)
from chat.messages.model import MessageModel
from chat.participants.model import ParticipantModel
from chat.participants.usecase import ParticipantUseCases
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.model import ThreadInformation
from chat.security import pass_code as PassCode
from chat.threads.usecase import ThreadUsecases
from chat.utils.validator_helper import is_bot_id

TIME_TO_DELETE = 1 * 3600


class MessageUsecase(object):
    def __init__(
        self,
        app: MyApp,
        thread_usc: ThreadUsecases,
        participant_usc: ParticipantUseCases,
    ):
        self.i_storage = app.repos.info
        self.mqtt_broker = app.brokers.message_mqtt
        self.thread_usc = thread_usc.create_thread
        self.thread_repo = app.repos.thread
        self.rb_broker = app.brokers.message_rb
        self.m_repo = app.repos.message
        self.user_repo = app.repos.user
        self.pt_repo = app.repos.pt
        self.mdc_repo = app.repos.media_collection
        self.pin_repo = app.repos.pin_collection
        self.thread_broker = app.brokers.thread_rd
        self.redis_counter = app.repos.counter
        self.redis_read = app.repos.thread_read
        self.redis_rate_limit = app.repos.rate_limit
        self.gapo_client = app.repos.gapo
        self.config = app.conf
        self.log = app.log
        self.bucket_factor = self.config.bucket_message_factor
        self.message_delete_default = "Tin nhắn đã bị xóa"
        self.expire_message = self.config.expire_message_delete_level_2
        self.message_media_default = "đã gửi tin nhắn media"
        self.admin_ids = self.config.admin_api
        self._max_member_status = self.config.max_member_sync_status
        self._max_member_tag = self.config.max_member_tag_all
        self.ttl_edit = self.config.ttl_edit_message
        self.ttl_delete = self.config.ttl_message_deletion
        self.participant_usc = participant_usc
        self.session_m = app.conns.mysql
        self.metrics = app.app_metrics

    def _check_pass_code(self, session: Session, user_id: str, pass_code):
        if pass_code is None:
            raise MissingPasscode("Error no passcode")

        if self.redis_rate_limit.is_pass_code_limit(user_id):
            raise WrongPasscode()

        user_info = self.user_repo.get_pass_code(session, user_id)
        if not user_info or not user_info["pass_code"]:
            raise MissingPasscode("Error no passcode")

        hashed_pass_code = user_info["pass_code"]
        if not PassCode.validate(pass_code, hashed_pass_code):
            self.redis_rate_limit.set_pass_code_limit(user_id)
            raise WrongPasscode("Error no passcode")
        self.redis_rate_limit.del_pass_code_limit(user_id)

    def _get_active_members(self, thread_id: int, member_count: int):
        if member_count > self._max_member_status:
            return []
        return self.redis_read.get_most_recent_thread_viewers(
            thread_id, self._max_member_status
        )

    def _get_bot(self, bot_id):
        bot_info = self.i_storage.get_bots([bot_id])[bot_id]
        return bot_info

    def _get_user(self, user_id: str):
        return self.i_storage.get_users([user_id]).get(str(user_id))

    def _get_thread_for_user(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
    ) -> ParticipantThreadModel:
        """Gets thread chat info for user,
        raises an error if user is not in that thread."""

        p_thread = self.thread_usc.get_thread_for_user(
            session, thread_id, user_id
        )

        return p_thread

    def _is_clear(self, thread_data: ThreadInformation, message: MessageModel):
        sender_id = message.user_id
        clear_user_id = thread_data.clear_msg.get(sender_id, 0)
        return message.id <= clear_user_id

    def _check_exists(self, message):
        """Check if message is a valid message, raise erro if it's not."""
        if not message:
            raise DataNotFound("Not found message")
        return True

    def _check_valid_message_for_deletion(
        self, p_thread: ParticipantThreadModel, message_id: int
    ):
        """Checks if a message is within a valid range for deletion.
        Raises an error if it is not."""
        delete_to = p_thread.participant.delete_to or 0
        if message_id <= delete_to:
            raise PermissionDenied("Message not found")

    def _validate_message_permission(
        self,
        owner_id: str,
        message: MessageModel,
        max_level=constant.DELETE_MSG_ONE_SIDE,
    ):
        if message is None:
            return None

        invalid = message.is_deleted_1_side(owner_id)
        if not invalid and max_level > constant.DELETE_MSG_ONE_SIDE:
            invalid = message.is_deleted_2_side()

        return message if not invalid else None

    def _unpin_message(
        self,
        session: Session,
        p_thread: ParticipantThreadModel,
        thread_id: int,
        user_id: str,
        message_id: int,
    ):
        pinned_message = p_thread.thread.pinned_message_id
        # -1 - ignore
        # 0 - lastest
        # 1 - in collection
        flag = 0

        if not message_id:
            flag = -1 if not pinned_message else 0
        elif pinned_message == message_id:
            flag = 0
        else:
            flag = 1
            pinned_message = message_id
            if not self.pin_repo.item(session, thread_id, message_id):
                flag = -1

        if flag != -1:
            self.thread_repo.decrease_pin_count(
                session, thread_id, pinned_message
            )
            self.pin_repo.delete(session, thread_id, pinned_message)

            if flag == 1:
                n_pinned_message = 0
            else:
                n_pin = self.pin_repo.get_latest_pin(session, thread_id)
                if n_pin:
                    n_pinned_message = n_pin["message_id"]
                else:
                    n_pinned_message = 0
                self.thread_repo.pin_message(
                    session, thread_id, n_pinned_message
                )

            self.thread_usc.publish_action_on_data(
                session,
                p_thread.thread,
                user_id,
                {"unpinned_message_id": pinned_message},
                action_type.UNPIN_MESSAGE,
                skip_message=False,
            )

    def _get_message(self, thread_id: int, message_id: int) -> MessageModel:
        """Gets message by id, raises exception if not found."""
        old_message = self.m_repo.get_by_id(thread_id, message_id)
        if not old_message:
            raise MessageNotFound()

        return old_message

    def _get_thread_for_user_by_collab_id(self, user_id: str, collab_id: str):
        """Gets thread info by collab_id,
        raises error if user is not in thread."""
        with self.session_m.get_session() as session:
            thread = self.pt_repo.get_thread_for_user_by_collab_id(
                session, user_id, collab_id
            )
            if not thread:
                raise UserNotInThread()

            return thread

    def _get_thread_by_partner_id(
        self,
        user_id: str,
        partner_id: str,
        partner_type: str,
        skip_deactivated_user=False,
        # page_id=None,
    ) -> ParticipantThreadModel:
        self.log.info(
            f"""_get_thread_by_partner uid: {user_id},
            pid: {partner_id},
            ptype: {partner_type}"""
        )
        if partner_type == constant.USER_TYPE:
            # check if partner is a valid user
            partner = self._get_user(partner_id)
            if not partner:
                raise UserNotFound()

            if (
                skip_deactivated_user
                and partner.get("status") == constant.USER_STATUS_DEACTIVATED
            ):
                raise UserNotFound()

            # from user
            if is_bot_id(user_id):
                p_thread = self.thread_usc.create_system_direct(
                    partner_id, user_id, actor_id=user_id  # user_id is bot_id
                )
            else:

                p_thread = self.thread_usc.create_direct(
                    partner_id, user_id, bypass_code=True
                )

        elif partner_type == constant.SYSTEM_TYPE:
            p_thread = self.thread_usc.create_system_direct(
                user_id, "system", actor_id="system"
            )
        elif partner_type == constant.BOT_TYPE:
            p_thread = self.thread_usc.create_system_direct(
                partner_id, user_id, actor_id=user_id  # user_id is bot_id
            )
        return p_thread

    def _update_thread_score_in_folders(
        self,
        user_id: str,
        thread_id: int,
        part: ParticipantModel,
        thread_score: int,
    ):
        folders = self._get_unpinned_folders(part)
        for folder in folders:
            self.thread_broker.set_thread_score(
                user_id,
                thread_id,
                folder,
                thread_score,
                set_default_folder=False,
            )

    def _get_unpinned_folders(self, part: ParticipantModel):
        """Gets list of folders where a thread isn't pinned."""
        folders = []
        if part.pin_pos == 0 and part.pin_default_pos == 0:
            folders.append(part.folder)

        if part.pin_default_pos == 0 and part.folder not in (
            constant.FOLDER_SUB_THREAD,
            constant.FOLDER_SECRET,
        ):
            folders.append(constant.FOLDER_DEFAULT)

        return folders

    def _get_pinned_folders(self, part: ParticipantModel):
        folders = []
        if part.pin_pos > 0:
            folders.append(part.folder)

        if part.pin_default_pos > 0:
            folders.append(constant.FOLDER_DEFAULT)

        return folders

    def _check_queue(self):
        if self.rb_broker.is_queue_full():
            raise QueueIsFull("Quả tải rồi")
