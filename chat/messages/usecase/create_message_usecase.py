from typing import Dict, List, <PERSON><PERSON>

import pydantic

from sqlalchemy.orm import Session

from chat.model import BaseModel
from chat import constant
from chat.exception import (
    InternalServerError,
    MessageNotFound,
    QueueError,
    SendMessageForbidden,
    Thread<PERSON>lockedByUserError,
    <PERSON><PERSON><PERSON><PERSON><PERSON>ound,
    UserNotInThread,
    RateLimitReached,
    # WorkspaceDisable,
)
from chat.messages.model import MessageBody, MessageModel
from chat.message_event.model import MessageEvent
from chat.messages.model.api import SchemaOutput
from chat.messages.model.api import PollPayload
from chat.messages.model.task import TaskCreateMassDirectMessages
from chat.model import make_output
from chat.participants.model import ParticipantModel
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.exception import ConflictData
from chat.threads.model import ThreadSettings, ThreadModel
from chat.utils.validator_helper import is_bot_id, is_system, is_user_id

from .get_message_usecase import GetMessageUseCase


class CreateMessageParams(BaseModel):
    collab_id: str = ""
    thread_id: int = 0
    partner_id: str = ""
    client_id: str = ""
    message_id: int = 0
    body: MessageBody
    pass_code: str = ""

    lang: str = "vi"

    user_id: str = constant.SYSTEM_ID
    role: str = constant.USER_ROLE

    workspace_id: str = ""

    api_version: str = "3"
    partner_type: str = constant.USER_TYPE
    bypass: bool = False
    skip_deactivated_user: bool = False
    poll_vote: PollPayload = PollPayload()

    @pydantic.validator("partner_id")
    def check_partner_id(cls, v, values, **kwargs):
        if v == "":
            values["partner_type"] = constant.SYSTEM_TYPE
        elif is_user_id(v):
            values["partner_type"] = constant.USER_TYPE
        elif is_system(v):
            values["partner_type"] = constant.SYSTEM_TYPE
        elif is_bot_id(v):
            values["partner_type"] = constant.BOT_TYPE
        else:
            raise ValueError(f"Invalid partner_id {v}")
        return v

    @pydantic.root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        thread_id = values.get("thread_id", None)
        partner_id = values.get("partner_id", None)
        collab_id = values.get("collab_id", None)

        if thread_id:
            values["collab_id"] = ""
            values["partner_id"] = ""
        elif partner_id:
            values["thread_id"] = 0
            values["collab_id"] = ""
        elif collab_id:
            values["thread_id"] = 0
            values["partner_id"] = ""
        else:
            raise ValueError(
                "Either thread_id, partner_id or collab_id must be set"
            )
        return values

    def is_service_role(self) -> bool:
        return self.role == constant.SERVICE_ROLE

    def is_bot_send(self) -> bool:
        return is_bot_id(self.user_id)


class CreateMessageUseCase(GetMessageUseCase):
    def create_mass_direct_messages(
        self,
        sender_id,
        user_ids: List,
        body: Dict,
        api_version: str,
    ):
        """Sends mass direct messages from a sender to a list of users.

        This only triggers a background task to send messages later
        """

        # Sending a lot of messages
        # can slowdown other messages. We assume that these messages
        # aren't quite as important as other messages. So we will put
        # them in queue with lower priority level. These are only processed
        # after other messages are processed
        event_id = self.rb_broker.publish_background_task(
            constant.TASK_CREATE_MASS_DIRECT_MESSAGES,
            TaskCreateMassDirectMessages(
                sender_id=sender_id,
                user_ids=user_ids,
                body=body,
                api_version=api_version,
            ).dict(),
            priority=constant.AMQP_PRIORITY_LOW,
        )
        self.metrics.sent_messages.inc(amount=len(user_ids))
        return event_id

    def create_mass_direct_messages_bg(
        self,
        sender_id,
        user_ids: List[int],
        body: Dict,
        api_version: str,
        client_id=None,
    ):
        """Creates mass direct messages. Only used for background worker."""
        for user_id in user_ids:
            try:
                # cannot send message to yourself
                if str(user_id) == sender_id:
                    continue

                p_thread = self._get_thread_by_partner_id(
                    sender_id, str(user_id), constant.USER_TYPE
                )

                event_id, _ = self._create(
                    sender_id,
                    p_thread.thread.id,
                    MessageBody(**body),
                    api_version,
                    client_id=client_id,
                    bypass_all=True,  # ignore all checking here
                    workspace_id=None,
                )
            except UserNotFound:
                # invalid sender_id
                # ignore
                pass
            except ConflictData:
                # cannot create thread
                pass
            except ThreadBlockedByUserError:
                # user don't want to receive message
                # from sender, so ignore this
                pass
            except MessageNotFound:
                # invalid message, just ignore
                pass
            except QueueError:
                # failed to publish event to create_message
                # just ignore
                self.log.exception("Failed to publish event", exc_info=True)
            except Exception:
                self.log.exception("Got unknown exception", exc_info=True)
                raise
            # TODO: any missing error here ?

    def create_v2(
        self, params: CreateMessageParams
    ) -> Tuple[int, int, str, int]:
        thread_id = params.thread_id
        sub_thread_id = 0
        send_by_user_id = params.user_id
        send_to_thread_id = params.thread_id
        self.log.info(f"""CreateMessageParams workspace_id {params.workspace_id} thread_id {params.thread_id} user_id {params.user_id}""") # noqa

        try:
            if (
                not params.bypass
                and not params.is_service_role()
                and not params.is_bot_send()
            ):
                is_allowed = self.redis_rate_limit.is_allowed(
                    send_to_thread_id, send_by_user_id
                )
            else:
                is_allowed = True
        except Exception as e:
            self.log.exception(e, exc_info=True)
            is_allowed = False

        if not is_allowed:
            raise RateLimitReached

        if params.is_service_role():
            params.bypass = True
            if params.user_id == "":
                send_by_user_id = constant.SYSTEM_ID

            if params.partner_id:
                p_thread = self.thread_usc.create_system_direct(
                    params.partner_id,
                    constant.SYSTEM_ID,
                    actor_id=constant.SYSTEM_ID,
                )
                params.thread_id = p_thread.thread.id
                send_to_thread_id = params.thread_id
                thread_id = params.thread_id
        else:
            if params.collab_id:
                p_thread = self.thread_usc.get_thread_id_by_collab(
                    params.collab_id, params.user_id, params.bypass
                )
                params.thread_id = p_thread.thread.id
                send_to_thread_id = params.thread_id
                thread_id = params.thread_id
            elif params.partner_id:
                p_thread = self._get_thread_by_partner_id(
                    params.user_id,
                    params.partner_id,
                    params.partner_type,
                    skip_deactivated_user=params.skip_deactivated_user,
                )
                params.thread_id = p_thread.thread.id
                send_to_thread_id = params.thread_id
                thread_id = params.thread_id
        if params.message_id:
            sub_p_thread = self.thread_usc.create_sub_thread(
                params.thread_id,
                params.message_id,
                params.user_id,
                partner_ids=[],
            )
            sub_thread_id = sub_p_thread.thread.id
            send_to_thread_id = sub_thread_id

        new_uuid, msg_id = self._create(
            send_by_user_id,
            send_to_thread_id,
            params.body,
            params.api_version,
            client_id=params.client_id,
            workspace_id=params.workspace_id,
            bypass_all=params.bypass,
            poll_vote=params.poll_vote,
            lang=params.lang,
        )

        return thread_id, sub_thread_id, new_uuid, msg_id

    def _create(
        self,
        user_id: str,
        thread_id: int,
        body: MessageBody,
        api_version: str,
        client_id=None,
        bypass_all=False,
        workspace_id=None,
        event_at=0,
        poll_vote: PollPayload = PollPayload(),
        lang: str = "vi",
    ):
        """Creates new message to a thread."""
        # if workspace_id:
        #     if not self.gapo_client.feature.is_ws_enable(workspace_id):
        #         raise WorkspaceDisable()
        try:
            body.workspace_id = int(workspace_id)
        except Exception:
            pass

        self._check_queue()
        if body.is_poll():
            text = body.text or ""
            payload = poll_vote.dict()
            payload["user_id"] = user_id

            self.log.info(f"Create_poll_message: {payload}")
            poll = self.gapo_client.poll.create_poll(payload)
            body = MessageBody.from_poll(poll, text)

        with self.session_m.get_session() as session:
            page = None
            # check_id = user_id

            if is_bot_id(user_id):
                user = self._get_bot(user_id)
                sender_type = constant.BOT_TYPE
            elif is_system(user_id):
                user = constant.SYSTEM_OBJECT
                sender_type = constant.SYSTEM_TYPE
            else:
                user = self._get_user(user_id)
                sender_type = constant.USER_TYPE

            if not user:
                raise UserNotFound()

            if not bypass_all:
                p_thread = self._require_thread_with_sufficient_permissions(
                    session, thread_id, user_id
                )

            else:
                thread = self.thread_repo.get_by_id(session, thread_id)
                if not thread:
                    raise UserNotInThread("Not be part of thread")
                new_part = ParticipantModel.new_participant(
                    user_id=user_id, thread_id=thread_id
                )
                p_thread = ParticipantThreadModel(
                    thread=thread, participant=new_part
                )

            if (
                is_user_id(user_id)
                and not bypass_all
                and p_thread.participant.folder == "stranger"
            ):
                self.pt_repo.move_folder(
                    session,
                    p_thread.participant.user_id,
                    p_thread.participant.thread_id,
                    p_thread.participant.folder,
                )
                self.thread_broker.remove_thread_scores_for_users(
                    thread_id,
                    [p_thread.participant],
                )
                session.commit()

            # self._fill_body_tmp(body)
            body.fill_body_tmp()

            # TODO: get message reply
            force_notify_o = self._get_tag(
                session, user_id, thread_id, p_thread, body
            )

            current_message_count = self.redis_counter.get_counter(thread_id)
            if body.reply_to_msg:  # use for return reply msg object [MQTT]
                msg_id = body.reply_to_msg
                if (
                    msg_id > (current_message_count + 2)  # bias for async
                    or msg_id <= 0
                ):
                    raise MessageNotFound

                # TODO: get message reply
                msg_info = self.get_by_id(
                    user_id=user_id, thread_id=thread_id, message_id=msg_id
                )

                if msg_info:
                    msg_output, errors = make_output(
                        SchemaOutput, msg_info.dict()
                    )
                    if errors:
                        raise InternalServerError(error_details=errors)
                    body.reply_to_msg_object = msg_output.dict()
                    if msg_info.user_id != user_id:
                        partner_id = msg_info.user_id
                        force_ids = force_notify_o.get("force_ids", [])
                        force_ids.append(partner_id)
                        force_notify_o["force_ids"] = list(set(force_ids))

            if p_thread.thread.is_subthread():
                if not bypass_all:
                    self._require_subthread_with_sufficient_permissions(
                        session, p_thread, user_id
                    )

                self._inject_referenced_message_preview_to_sub_thread(
                    user_id, p_thread
                )
                self._add_sender_and_mentioned_users_to_sub_thread(
                    user_id,
                    p_thread,
                    force_notify_o.get("force_ids") or [],
                )
                commenters = p_thread.thread.commenters
                if commenters is None:
                    commenters = []

                if user_id not in commenters:
                    self._add_sender_to_commenter_list(
                        session, user_id, thread_id
                    )
                    commenters.append(user_id)
                p_thread.thread.commenters = commenters
                session.commit()

            message_count = self._incr_message_counter(
                session, p_thread.thread
            )
            # update message_count in thread
            p_thread.thread.message_count = message_count

            self._update_thread_name_if_needed(session, user_id, p_thread)

            message_o = MessageEvent(
                body=body,
                user=user,
                page=page,
                message_id=message_count,
                thread=p_thread.thread,  # type: ignore
                sender_type=sender_type,
                sender_lang=lang,
                client_id=client_id,
                notify=force_notify_o,
                mentions=force_notify_o,
            )
            message_o.after_parse()
            # print(f"Message 0: {message_o.dict()}")
            new_uuid = self.rb_broker.publish_message(
                constant.TASK_CREATE_MESSAGE,
                message_o.dict(),
                api_version=api_version,
                event_at=event_at if bypass_all else 0,
            )
            if not new_uuid:
                # failed to publish message
                raise QueueError()

            self.metrics.sent_messages.inc()

            if body.forward:
                # update unread_count with partner
                is_save = body.forward.get("is_save_message")
                if is_save:
                    self.participant_usc.participant_action._update_read_from_partner(  # noqa
                        session, thread_id, user_id, message_count
                    )
            session.commit()

            return new_uuid, message_count

    def _inject_referenced_message_preview_to_sub_thread(
        self, user_id: str, p_thread: ParticipantThreadModel
    ):
        """Inject referenced message preview to thread info.

        Worker use this information to generate notification title.

        """
        parent_id = p_thread.thread.parent_id
        root_message_id = p_thread.thread.root_message_id
        if not parent_id:
            return
        if not root_message_id:
            return

        root_msg = self.get_by_id(user_id, parent_id, root_message_id)
        if not root_msg:
            return

        self._build_preview_msg(root_msg, p_thread)
        # generate preview message

    def _build_preview_msg(
        self, message: MessageModel, p_thread: ParticipantThreadModel
    ):
        media_msg_preview_map = {
            "image": "Hình ảnh",
            "multi_image": "Hình ảnh",
            "file": "Tệp tin",
            "video": "Video",
        }

        preview_msg = None
        if message.body.type == "text":
            preview_msg = (message.body.text or "")[:100]
        else:
            preview_msg = media_msg_preview_map.get(
                message.body.type, "Đa phương tiện"
            )

        p_thread.thread.referenced_message_preview = preview_msg

    def _require_thread_with_sufficient_permissions(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        """Checks if user has permission to send message in thread.

        It will return thread if user has permission, raise error if not.
        """
        p_thread = self._get_thread_for_user(session, thread_id, user_id)

        self._require_send_message_permission(
            p_thread.thread.type,
            p_thread.participant.role,
            p_thread.thread.group_level,
        )
        return p_thread

    def _require_subthread_with_sufficient_permissions(
        self, session: Session, p_thread: ParticipantThreadModel, user_id: str
    ) -> ParticipantThreadModel:
        """Checks if user has permission to send message in thread.

        It will return thread if user has permission, raise error if not.
        """
        if not p_thread.thread.is_subthread():
            raise Exception("wrong thread type")

        if not p_thread.thread.parent_id:
            raise Exception("wrong thread type")

        parent_thread = self._get_thread_for_user(
            session, p_thread.thread.parent_id, user_id
        )

        self._require_send_message_subthread_permission(
            parent_thread.thread.type,
            parent_thread.participant.role,
            parent_thread.thread.group_level,
        )

        self._require_send_message_permission(
            parent_thread.thread.type,
            parent_thread.participant.role,
            p_thread.thread.group_level,
        )
        return p_thread

    def _incr_message_counter(self, session: Session, thread: ThreadModel):
        thread_id = thread.id
        current_count = thread.message_count
        counter = self.redis_counter.inc_counter(thread_id)
        if counter <= current_count:
            counter = self.thread_repo.incr_thread_msg_counter(
                session, thread_id
            )
            self.redis_counter.set_counter(thread_id, counter)
        return counter

    def _add_sender_to_commenter_list(
        self, session: Session, user_id: str, thread_id: int
    ):
        self.thread_repo.add_thread_commenter(session, user_id, thread_id)

    def _add_sender_and_mentioned_users_to_sub_thread(
        self,
        user_id: str,
        p_thread: ParticipantThreadModel,
        mentions: List[str],
    ):
        # NOTE not a create message business
        if not p_thread.thread.is_subthread():
            return

        # Remove: all in mention subthread
        if "all" in mentions:
            mentions.remove("all")

        thread_id = p_thread.thread.id
        thread_parent_id = p_thread.thread.parent_id

        if not thread_parent_id:
            return

        self.participant_usc.add_participant.add_mention_to_subthread(
            user_id, thread_id, thread_parent_id, mentions
        )

    def _extract_target(self, member_count: int, mentions):
        if not mentions:
            return []
        user_ids = []
        for m in mentions:
            target = m["target"]
            if target == "all" and member_count <= self._max_member_tag:
                return "all"

            user_ids.append(target)
        return user_ids

    def _get_tag(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        p_thread: ParticipantThreadModel,
        body: MessageBody,
    ):
        metadata = body.metadata
        force_ids = set()
        member_count = p_thread.thread.member_count
        if metadata:
            tags = self._extract_target(member_count, metadata.get("mentions"))
            if tags == "all":
                return {"force_ids": ["all"]}
            force_ids.update(tags)
        force_ids.discard(user_id)
        return {"force_ids": list(force_ids)}

    def _require_send_message_permission(
        self, thread_type: str, user_role: str, group_level: int
    ):
        if not thread_type == constant.GROUP_THREAD_TYPE:
            return
        settings = ThreadSettings.decode(group_level)

        if not settings.disable_member_send_message:
            return

        if user_role in ("owner", "admin"):
            return

        raise SendMessageForbidden("You shall not pass")

    def _require_send_message_subthread_permission(
        self, thread_type: str, user_role: str, group_level: int
    ):
        if not thread_type == constant.GROUP_THREAD_TYPE:
            return
        settings = ThreadSettings.decode(group_level)

        if not settings.disable_member_send_message:
            return

        if not settings.disable_member_send_sub_message:
            return

        if user_role in ("owner", "admin"):
            return

        raise SendMessageForbidden("You shall not pass")

    def _update_thread_name_if_needed(
        self, session: Session, user_id: str, p_thread: ParticipantThreadModel
    ):
        """Updates thread's name if thread is a direct chat or sub-thread of
        a direct chat."""
        if p_thread.thread.is_direct():
            # fill thread_name & avatar with partner name & avatar
            partner = self._get_partner(user_id, p_thread)
            if partner:
                p_thread.thread.name = partner.get("name")
                p_thread.thread.avatar = partner.get("avatar")
        elif (
            p_thread.thread.is_subthread()
            and p_thread.thread.is_parent_direct()
        ):
            # fill thread name with partner name in parent thread
            partner = self._get_partner_from_parent_thread(
                session, user_id, p_thread
            )
            if partner:
                p_thread.thread.name = partner.get("name")
                p_thread.thread.avatar = partner.get("avatar")

    def _get_partner(self, user_id: str, p_thread: ParticipantThreadModel):
        user_id = str(user_id)
        partner_id = p_thread.participant.partner_id
        if not partner_id:
            # try to get partner_id from pair_ids
            try:
                pair_ids = p_thread.thread.pair_ids
                if not pair_ids or not isinstance(pair_ids, str):
                    return None
                parts = pair_ids.split("|")
                if len(parts) != 2:
                    return None
                if parts[0] == user_id:
                    partner_id = parts[1]
                else:
                    partner_id = parts[0]
            except Exception:
                return None
        if is_bot_id(partner_id):
            return self._get_bot(partner_id)
        elif is_user_id(partner_id):
            return self._get_user(partner_id)
        elif is_system(partner_id):
            return constant.SYSTEM_OBJECT

        return None

    def _get_partner_from_parent_thread(
        self, session: Session, user_id: str, p_thread: ParticipantThreadModel
    ):
        parent_thread_id = p_thread.thread.parent_id
        if not parent_thread_id:
            return None

        parent_thread = self._get_thread_for_user(
            session, parent_thread_id, user_id
        )
        if not parent_thread:
            return None

        partner = self._get_partner(user_id, parent_thread)
        return partner
