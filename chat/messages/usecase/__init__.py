from chat.app import <PERSON><PERSON><PERSON>
from chat.participants.usecase import ParticipantUseCases
from chat.threads.usecase import ThreadUsecases

from .create_message_usecase import CreateMessageUseCase, CreateMessageParams
from .delete_message_usecase import DeleteMessageUseCase
from .edit_message_usecase import EditMessageUseCase
from .forward_message_usecase import ForwardMessageUseCase
from .get_message_usecase import GetMessageUseCase
from .pin_message_usecase import Pin<PERSON>essageUseCase
from .poll_vote_message_usecase import PollVoteMessageUseCase
from .react_message_usecase import ReactMessageUseCase


class MessageUseCases(object):
    def __init__(
        self,
        app: MyApp,
        thread_usc: ThreadUsecases,
        participant_usc: ParticipantUseCases,
    ):
        self.app = app
        self.log = app.log
        self.config = app.conf
        self.thread_usc = thread_usc
        self.participant_usc = participant_usc

        self._init_create_message()
        self._init_delete_message()
        self._init_edit_message()
        self._init_forward_message()
        self._init_get_message()
        self._init_pin_message()
        self._init_pollvote_message()
        self._init_react_message()

        self._inject_usecases_to_thread_usecases()

    def _inject_usecases_to_thread_usecases(self):
        # inject message usecase to thread usecase
        # because we need to use this to create
        # message for sub-thread
        self.thread_usc.create_thread.inject_message_usecases(self)
        self.thread_usc.get_thread.inject_message_usecase(self)
        self.thread_usc.setup_thread.inject_message_usecase(self)
        self.thread_usc.join_thread.inject_message_usecase(self)
        self.thread_usc.pin_thread.inject_message_usecase(self)
        self.thread_usc.leave_thread.inject_message_usecase(self)
        self.thread_usc.unread_thread.inject_message_usecase(self)

    def _init_create_message(self):
        self.create_message = CreateMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_delete_message(self):
        self.delete_message = DeleteMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_edit_message(self):
        self.edit_message = EditMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_forward_message(self):
        self.forward_message = ForwardMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_get_message(self):
        self.get_message = GetMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_pin_message(self):
        self.pin_message = PinMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_pollvote_message(self):
        self.pollvote_message = PollVoteMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )

    def _init_react_message(self):
        self.react_message = ReactMessageUseCase(
            self.app, self.thread_usc, self.participant_usc
        )


__all__ = [
    "MyApp",
    "ParticipantUseCases",
    "ThreadUsecases",
    "CreateMessageUseCase",
    "DeleteMessageUseCase",
    "EditMessageUseCase",
    "ForwardMessageUseCase",
    "GetMessageUseCase",
    "PinMessageUseCase",
    "PollVoteMessageUseCase",
    "ReactMessageUseCase",
    "MessageUseCases",
    "CreateMessageParams",
]
