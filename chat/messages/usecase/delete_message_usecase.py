from typing import Any, List, Optional, Tuple

from sqlalchemy.orm import Session

from chat import acl, constant, media_code
from chat.exception import OutOfMsgDeletionPeriodError, PermissionDenied
from chat.messages.exception import DeleteMultipleMessagesError
from chat.messages.model import MessageModel
from chat.messages.model.task import DeleteMessageEvent
from chat.messages.usecase.base import MessageUsecase
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.model import ThreadModel
from chat.utils import validator_helper as vh


class DeleteMessageUseCase(MessageUsecase):
    def delete_many(
        self, user_id: str, thread_id: int, msg_ids: List[int], level=0
    ):
        """Deletes multiple messages."""

        # if there is only single message, we will fallback
        # to logic to process one item, so client will get detail error
        if len(msg_ids) == 1:
            self.delete(user_id, thread_id, msg_ids[0], level=level)
            return

        exceptions: List[Tuple[str, Exception]] = []
        for msg_id in msg_ids:
            try:
                self.delete(user_id, thread_id, msg_id, level=level)
            except Exception as e:
                exceptions.append((str(msg_id), e))

            # don't put into background tasks
            # self.rb_broker.publish_background_event(
            #     constant.TASK_DELETE_MESSAGE,
            #     {
            #         "user_id": user_id,
            #         "thread_id": thread_id,
            #         "message_id": msg_id,
            #         "level": level,
            #     },
            # )

        if len(exceptions) > 0:
            raise DeleteMultipleMessagesError(exceptions)

    def delete(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        level=0,
        bypass=False,
    ):
        """Deletes a single message."""

        sync_delete_message = None
        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)

            # message in save message chat
            # will be deleted one side
            is_save_msg_thread = vh.is_save_bot_id(
                p_thread.participant.partner_id or ""
            )
            if is_save_msg_thread:
                level = constant.DELETE_MSG_ONE_SIDE
            else:
                # NOTE: disable for anything but saved message for delete 1
                # side
                level = constant.DELETE_MSG_TWO_SIDE

            self._check_delete_msg_with_level(
                p_thread.thread, level, is_save_msg_thread
            )
            self._check_valid_message_for_deletion(p_thread, message_id)

            # checkExpire = level == constant.TWO_SIDE
            old_message = self._get_message(thread_id, message_id)
            old_message = self._validate_message_permission(
                user_id, old_message, max_level=level
            )
            self._check_exists(old_message)

            # if message to save message bot
            # we can delete message
            # otherwise, you can only delete if the message
            # is in deletion period
            if (
                not is_save_msg_thread
                and not bypass
                and not old_message.is_in_deletion_period(self.ttl_delete)
            ):
                raise OutOfMsgDeletionPeriodError()

            if level == constant.DELETE_MSG_ONE_SIDE:
                self._delete_item_one_side(
                    session,
                    user_id,
                    p_thread,
                    old_message,
                )
                member_ids = [user_id]

            elif level == constant.DELETE_MSG_TWO_SIDE:
                # cache_messages = {}
                self._delete_item_two_side(
                    session,
                    user_id,
                    p_thread,
                    old_message,
                )
                self._unpin_message(
                    session, p_thread, thread_id, user_id, message_id
                )
                member_ids = self._get_active_members(
                    thread_id, p_thread.thread.member_count
                )
            else:
                raise Exception("Doens't support delete level %s", level)
            sync_delete_message = {
                "thread_id": thread_id,
                "message_id": message_id,
                "member_ids": member_ids,
                "level": level,
            }
            session.commit()

        if sync_delete_message:
            self.mqtt_broker.publish_message_deleted_event(
                **sync_delete_message
            )
            self.rb_broker.publish_status(
                event_type=constant.EVENT_MESSAGE_DELETED_OLD,
                body=sync_delete_message,
            )

    def _delete_item_one_side(
        self,
        session: Session,
        user_id: str,
        p_thread: ParticipantThreadModel,
        old_message: MessageModel,
    ):
        """Delete message one side."""
        old_message.delete_1_side(user_id)

        message_count = p_thread.thread.message_count
        read_count = p_thread.participant.read_count

        update_last_message = False

        if read_count == message_count:
            lm = p_thread.participant.last_message
            c_lm = p_thread.thread.centralize_last_message

            last_message = lm or c_lm
            last_message = last_message or None
            if last_message and last_message.id == old_message.id:
                update_last_message = True
        else:
            self.pt_repo.update_max_read_count(
                session, old_message.thread_id, user_id
            )
            if old_message.id == message_count:
                update_last_message = True

        self._delete_message(old_message)
        self._update_media_count_for_direct_chat(
            session, old_message.thread_id, old_message.id, user_id=user_id
        )

        if not update_last_message:
            return

        lower_bound = p_thread.participant.delete_to
        upper_bound = old_message.id - 1
        new_message = self._get_previous_message(
            old_message.thread_id, user_id, lower_bound, upper_bound
        )
        if new_message is not None:
            last_msg = new_message.build_last_message()
            self.pt_repo.update_last_message(
                session, user_id, old_message.thread_id, last_msg.json()
            )
            thread_score = new_message.created_at

            self._update_thread_score_in_folders(
                user_id,
                old_message.thread_id,
                p_thread.participant,
                thread_score,
            )

        else:
            self.pt_repo.update_last_message(
                session, user_id, old_message.thread_id, None
            )
            self.thread_broker.remove_thread_scores_for_users(
                old_message.thread_id, [p_thread.participant]
            )

    def _delete_item_two_side(
        self,
        session: Session,
        user_id: str,
        p_thread: ParticipantThreadModel,
        old_message: MessageModel,
    ):
        if old_message.is_system():
            raise PermissionDenied("Cant delete action note")

        old_message.delete_2_side()

        thread_id = p_thread.thread.id
        if p_thread.thread.is_group():
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            if vh.is_user_id(old_message.user_id):
                sender_part = self.pt_repo.get_thread_for_user(
                    session, thread_id, old_message.user_id
                )

                if sender_part is None:
                    return

                sender_role = sender_part.role

                if not old_message.is_owner(user_id):
                    (
                        has_permission,
                        permission_name,
                    ) = acl.get_permission_to_user(
                        settings.is_public,
                        member_role,
                        sender_role,
                        constant.PERMISSION_DELETE_MESSAGE,
                    )

                    if not has_permission:
                        raise PermissionDenied("Not owner message")

            self._update_last_message_for_group(session, p_thread, old_message)
            self._update_media_count_for_group(
                session, thread_id, old_message.id
            )
        else:
            if not old_message.is_owner(user_id):
                raise PermissionDenied("Not owner message")

            self._update_last_message_for_direct_chat(
                session, thread_id, old_message
            )
            self._update_media_count_for_direct_chat(
                session, thread_id, old_message.id, user_id=None
            )
        self._delete_message(old_message)

        # publish
        user = self._get_user(user_id)
        self.rb_broker.publish_status(
            constant.EVENT_MESSAGE_DELETED,
            DeleteMessageEvent(
                user_id=user_id,
                thread_id=thread_id,
                message_id=old_message.id,
                user=user,
                thread=p_thread.thread,  # type: ignore
            ).dict(),
        )

    def _update_last_message_for_group(
        self,
        session: Session,
        p_thread: ParticipantThreadModel,
        new_message: MessageModel,
    ):
        last_msg = new_message.build_last_message()
        self.thread_repo.update_last_message(
            session, p_thread.thread.id, new_message.id, last_msg.json()
        )

    def _update_last_message_for_direct_chat(
        self,
        session: Session,
        thread_id: int,
        new_message: MessageModel,
    ):
        last_msg = new_message.build_last_message()
        last_msg_str = last_msg.json()
        self.pt_repo.update_all_last_message(
            session, thread_id, new_message.id, last_msg_str
        )
        self.thread_repo.update_last_message(
            session, thread_id, new_message.id, last_msg_str
        )

    def _update_media_count_for_group(
        self, session: Session, thread_id: int, message_id: int
    ):
        media_collections = self.mdc_repo.get_all_media_by_id(
            session, thread_id, message_id
        )
        if not media_collections:
            return
        media_infos = (
            (media_code.media_collection_str[m["type"]], m["length"])
            for m in media_collections
        )
        self.thread_repo.update_thread_media_count(
            session, thread_id, media_infos, decrease=True
        )
        self.mdc_repo.delete_media(session, thread_id, message_id)

    def _update_media_count_for_direct_chat(
        self,
        session: Session,
        thread_id: int,
        message_id: int,
        user_id: Optional[Any] = None,
    ):
        media_collections = self.mdc_repo.get_all_media_by_user(
            session, thread_id, message_id, user_id
        )
        if not media_collections:
            return

        media_infos = [
            (media_code.media_collection_str[m["type"]], m["length"])
            for m in media_collections
        ]
        if user_id is None:
            user_delete_ids = [
                m["user_delete_id"]
                for m in media_collections
                if m["user_delete_id"] is not None
            ]
            self.thread_repo.update_thread_media_count(
                session, thread_id, media_infos, decrease=True
            )
            if user_delete_ids:
                self.pt_repo.update_media_count_users(
                    session,
                    thread_id,
                    user_delete_ids,
                    media_infos,
                    is_decrease=False,
                )
            self.mdc_repo.delete_media(session, thread_id, message_id)
        else:
            self.pt_repo.update_media_count_users(
                session, thread_id, [user_id], media_infos, is_decrease=True
            )
            self.mdc_repo.add_delete_log(
                session, thread_id, message_id, user_id
            )

    def _delete_message(
        self,
        old_message: MessageModel,
    ):
        self.m_repo.update_delete_bulk(old_message)

    def _check_delete_msg_with_level(
        self,
        thread: ThreadModel,
        delete_level: int,
        is_save_msg_thread: bool,
    ):
        # if thread["type"] == "group" and levelDelete == 1:
        #     raise PermissionDenied("Cant delete one way from group")

        if (
            delete_level == constant.DELETE_MSG_ONE_SIDE
            and not is_save_msg_thread
        ):
            raise PermissionDenied("Cant delete one way")

        elif (
            thread.is_group() and delete_level == constant.DELETE_MSG_TWO_SIDE
        ):
            return True

    def _get_previous_message(
        self,
        thread_id: int,
        user_id: str,
        lower_bound: int,
        upper_bound: int,
        step=1,
    ):
        jump_step = 2 * step
        next_bound = upper_bound - jump_step
        if next_bound <= lower_bound:
            next_bound = lower_bound

        if (
            (lower_bound < 0)
            or (lower_bound > upper_bound)
            or (next_bound == upper_bound)
        ):
            return None

        messages = self.m_repo.get_between(
            thread_id=thread_id, from_=upper_bound, to=next_bound
        )

        for message in messages:
            new_message = self._validate_message_permission(
                user_id, message, max_level=1
            )
            if new_message:
                return new_message
        return self._get_previous_message(
            thread_id,
            user_id,
            lower_bound,
            next_bound,
            step + 1,
        )
