from typing import Optional

from chat import action_type, constant
from chat.messages.exception import DisallowToPinMessageTypeError

from chat.participant_threads.model import ParticipantThreadModel

from .base import MessageUsecase


class PinMessageUseCase(MessageUsecase):
    def pin(
        self,
        thread_id: int,
        message_id: int,
        user_id: str,
        pass_code: Optional[str] = None,
    ) -> ParticipantThreadModel:
        """Pins a message in a thread."""

        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            delete_to = p_thread.participant.delete_to
            if delete_to is None:
                delete_to = 0

            message_count = p_thread.thread.message_count
            if message_count < message_id or message_id <= delete_to:
                message = None

            # check permission
            else:
                message = self.m_repo.get_by_id(thread_id, message_id)
                if not message:
                    message = None
                elif message.is_deleted_2_side():
                    message = None

                # disallow to pin action note
                if message is not None and message.body.type == "action_note":
                    raise DisallowToPinMessageTypeError()

            self._check_exists(message)
            if p_thread.thread.pinned_message_id != message_id:
                self.thread_repo.pin_message(session, thread_id, message_id)
                self.thread_repo.increase_pin_count(
                    session, thread_id, message_id
                )
                self.pin_repo.insert(session, thread_id, message_id)
                session.commit()
                p_thread = self._get_thread_for_user(
                    session, thread_id, user_id
                )
                self.thread_usc.publish_action_on_data(
                    session,
                    p_thread.thread,
                    user_id,
                    {"pinned_message_id": message_id},
                    action_type.PIN_MESSAGE,
                    skip_message=False,
                )
        return p_thread

    def unpin(self, thread_id: int, user_id: str, message_id=0):
        """Unpins a message in thread."""

        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            self._unpin_message(
                session, p_thread, thread_id, user_id, message_id
            )
            session.commit()
            self.thread_usc._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
