from chat.exception import PermissionDenied
from chat.messages import model as m_models
from chat.messages.model.api import SchemaBodyPollVote

from .create_message_usecase import CreateMessageUseCase


class PollVoteMessageUseCase(CreateMessageUseCase):
    def create_poll_message(
        self,
        user_id: str,
        thread_id: int,
        body: SchemaBodyPollVote,
        api_version: str,
        client_id=None,
    ):

        return self._create(
            user_id,
            thread_id,
            m_models.MessageBody(**body.dict()),
            api_version,
            client_id,
            poll_vote=body,
        )

    def update_poll(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        body,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_owner(user_id):
            raise PermissionDenied("Not owner message")

        if not message.is_poll_type():
            raise PermissionDenied("Not owner message")

        payload = body.dict()
        poll_id = message.get_poll_id()
        poll = self.gapo_client.poll.update_poll(poll_id, payload)
        message.update_poll(poll)
        self._sync_poll(user_id, thread_id, message)
        return message

    def create_poll_vote(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        body,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_poll_type():
            raise PermissionDenied("Not owner message")

        payload = body.dict()
        payload["user_id"] = user_id
        poll_id = message.get_poll_id()
        assert poll_id

        self.gapo_client.poll.create_vote(poll_id, payload)
        poll = self.gapo_client.poll.get_polls(user_id, [poll_id])[poll_id]
        message.update_poll(poll)

        self._sync_poll(user_id, thread_id, message)

        return message

    def update_poll_vote(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        vote_id: str,
        body,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_owner(user_id):
            raise PermissionDenied("Not owner message")

        if not message.is_poll_type():
            raise PermissionDenied("Not owner message")

        payload = body.dict()
        poll_id = message.get_poll_id()
        assert poll_id

        payload["user_id"] = user_id
        self.gapo_client.poll.update_vote(poll_id, vote_id, payload)
        poll = self.gapo_client.poll.get_polls(user_id, [poll_id])[poll_id]
        message.update_poll(poll)
        self._sync_poll(user_id, thread_id, message)

        return message

    def delete_poll_vote(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        vote_id: str,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_owner(user_id):
            raise PermissionDenied("Not owner message")

        if not message.is_poll_type():
            raise PermissionDenied("Not owner message")

        poll_id = message.get_poll_id()
        assert poll_id

        self.gapo_client.poll.delete_vote(user_id, poll_id, vote_id)
        poll = self.gapo_client.poll.get_polls(user_id, [poll_id])[poll_id]
        message.update_poll(poll)
        self._sync_poll(user_id, thread_id, message)

        return message

    def choose_vote_by_user(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        vote_id: str,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_poll_type():
            raise PermissionDenied("Not owner message")

        poll_id = message.get_poll_id()
        assert poll_id

        self.gapo_client.poll.vote_by_user(user_id, poll_id, vote_id)
        poll = self.gapo_client.poll.get_polls(user_id, [poll_id])[poll_id]
        message.update_poll(poll)
        self._sync_poll(user_id, thread_id, message)

        return message

    def unvote_by_user(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        vote_id: str,
        api_version: str,
    ):
        message = self.get_by_id(user_id, thread_id, message_id, None)
        if not message:
            return None

        if not message.is_poll_type():
            raise PermissionDenied("Not a poll message")

        poll_id = message.get_poll_id()
        assert poll_id

        self.gapo_client.poll.unvote_by_user(user_id, poll_id, vote_id)
        poll = self.gapo_client.poll.get_polls(user_id, [poll_id])[poll_id]
        message.update_poll(poll)
        self._sync_poll(user_id, thread_id, message)

        return message

    def _sync_poll(
        self, user_id: str, thread_id: int, message: m_models.MessageModel
    ):
        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)

            member_ids = self._get_active_members(
                thread_id, p_thread.thread.member_count
            )

            sync_edit_message = {
                "thread_id": thread_id,
                "message_id": message.id,
                "message_user_id": message.user_id,
                "actor_id": user_id,
                "member_ids": member_ids,
                "body": message.body.dict(),
            }
            self.mqtt_broker.publish_message_edited_event(**sync_edit_message)

            session.commit()
