from typing import List

from sqlalchemy.orm import Session

from chat import constant
from chat.exception import UserNotFound
from chat.utils.validator_helper import is_bot_id, is_save_bot_id

from .create_message_usecase import CreateMessageUseCase


class ForwardMessageUseCase(CreateMessageUseCase):
    def forward(
        self,
        user_id: str,
        source_thread_id: int,
        source_message_id: int,
        thread_id: int,
        api_version: str,
        client_id=None,
        partner_id=None,
    ):
        # wiki $/message_forward.md
        self.log.info(
            f"""forward with partner_id: {partner_id},
            uid: {user_id},
            t_id: {thread_id},
            s_mid: {source_message_id}"""
        )
        with self.session_m.get_session() as session:
            body = self._forward_from_user(
                session,
                user_id,
                source_thread_id,
                source_message_id,
                partner_id,
            )
            session.commit()

        return self._create(
            user_id,
            thread_id,
            body,
            api_version,
            client_id=client_id,
        )

    def forward_to_partner(
        self,
        user_id: str,
        source_thread_id: int,
        source_message_id: int,
        partner_id: str,
        partner_type: str,
        api_version: str,
        client_id=None,
    ):
        p_thread = self._get_thread_by_partner_id(
            user_id, partner_id, partner_type
        )

        return self.forward(
            user_id,
            source_thread_id,
            source_message_id,
            p_thread.thread.id,
            api_version,
            client_id=client_id,
        )

    def forward_many(
        self,
        user_id: str,
        source_thread_id: int,
        source_message_ids: List[int],
        thread_id: int,
        api_version: str,
        client_id=None,
    ):

        event_id = self.rb_broker.publish_background_task(
            constant.TASK_FORWARD_MULTIPLE_MESSAGES,
            {
                "user_id": user_id,
                "source_thread_id": source_thread_id,
                "source_message_ids": source_message_ids,
                "thread_id": thread_id,
                "api_version": api_version,
            },
        )

        return event_id, thread_id

    def forward_many_to_partner(
        self,
        user_id: str,
        source_thread_id: int,
        source_message_ids: List[int],
        partner_id: str,
        partner_type: str,
        api_version: str,
        client_id=None,
    ):
        self.log.info(
            f"""forwards partner uid: {user_id},
            pid: {partner_id},
            ptype: {partner_type},
            s_tid: {source_thread_id},
            s_mid: {source_message_ids},
            api_version: {api_version}"""
        )
        p_thread = self._get_thread_by_partner_id(
            user_id, partner_id, partner_type
        )

        event_id = self.rb_broker.publish_background_task(
            constant.TASK_FORWARD_MULTIPLE_MESSAGES,
            {
                "user_id": user_id,
                "source_thread_id": source_thread_id,
                "source_message_ids": source_message_ids,
                "thread_id": p_thread.thread.id,
                "api_version": api_version,
                "partner_id": partner_id
                if partner_type == constant.BOT_TYPE
                else None,
            },
        )

        return event_id, p_thread.thread.id

    def _forward_from_user(
        self,
        session: Session,
        user_id: str,
        source_thread_id: int,
        source_message_id: int,
        partner_id=None,
    ):
        temp_uid = user_id
        is_save_msg = False
        if partner_id:
            if is_save_bot_id(user_id):
                temp_uid = partner_id
                is_save_msg = True

        # verify that user has permission in source thread
        self._get_thread_for_user(session, source_thread_id, temp_uid)

        fw_message = self._get_message_by_user(
            temp_uid, source_thread_id, source_message_id
        )
        body = fw_message.body.forward_body()
        # fw_user_id = partner_id if is_save_msg else fw_message.user_id
        fw_user_id = fw_message.user_id
        fw_user = (
            self._get_bot(fw_user_id)
            if is_bot_id(fw_user_id)
            else self._get_user(fw_user_id)
        )
        if not fw_user:
            raise UserNotFound()

        body.is_markdown_text = fw_message.body.is_markdown_text
        body.forward = {
            "thread_id": source_thread_id,
            "message_id": source_message_id,
            "user_id": fw_user_id,
            "user_name": fw_user["name"],
            "is_save_message": is_save_msg,
        }

        return body
