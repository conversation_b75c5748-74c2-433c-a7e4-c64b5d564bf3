from chat import constant
from chat.exception import OutOfMsgEditPeriodError, PermissionDenied

from .base import MessageUsecase


class EditMessageUseCase(MessageUsecase):
    def update(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        new_body,
        bypass=False,
    ):
        """Updates message."""

        sync_edit_message = None
        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            self._check_valid_message_for_deletion(p_thread, message_id)

            # checkExpire = level == constant.TWO_SIDE
            message = self._get_message(thread_id, message_id)
            message = self._validate_message_permission(
                user_id, message, max_level=constant.DELETE_MSG_TWO_SIDE
            )
            if message is None:
                return

            is_forward = message.is_forward()
            is_owner = message.is_owner(user_id)
            is_expired = message.is_expired(self.ttl_edit)
            if not is_owner or is_forward:
                raise PermissionDenied("Not owner message")

            if is_expired and not bypass:
                raise OutOfMsgEditPeriodError()

            self._check_exists(message)
            message.edit(new_body)
            self.m_repo.replace_message(message)

            sync_edit_message = {
                "thread_id": thread_id,
                "message_id": message_id,
                "user_id": message.user_id,
                "body": message.body.dict(),
            }

            # self.log.info("Sync edit message %s", sync_edit_message)

            session.commit()

        if sync_edit_message:
            self.rb_broker.publish_background_task(
                event_type=constant.TASK_EDIT_MESSAGE, body=sync_edit_message
            )

    def update_bg(
        self, message, user_id: str, message_id: int, thread_id: int
    ):

        with self.session_m.get_session() as session:
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            message = self._get_message(thread_id, message_id)
            last_msg = message.build_last_message().json()
            self.thread_repo.update_last_message_when_edit(
                session=session,
                thread_id=thread_id,
                message_id=message_id,
                body=last_msg,
            )

            detail = message.build_media_link()

            self.mdc_repo.update_link(
                session=session,
                thread_id=thread_id,
                message_id=message_id,
                user_id=user_id,
                detail=detail,
            )
            member_ids = self._get_active_members(
                thread_id, p_thread.thread.member_count
            )
            session.commit()
            p_thread = self._get_thread_for_user(session, thread_id, user_id)
            self.thread_usc.after_update(
                session, p_thread.participant.user_id, p_thread.thread.dict()
            )

        self.mqtt_broker.publish_message_edited_event(
            actor_id=message.user_id,
            member_ids=member_ids,
            thread_id=thread_id,
            message_id=message_id,
            message_user_id=message.user_id,
            body=message.body.dict(),
        )
