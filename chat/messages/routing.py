import falcon

from chat.messages.container import MessageContainer


def routing(router: falcon.API, container: MessageContainer):
    resources = container.resources()

    router.add_route("/messages", resources.messages())

    router.add_route("/messages/{message_id:int}", resources.message_item())
    router.add_route(
        "/internal/messages/{message_id:int}",
        resources.internal_message_item(),
    )

    router.add_route(
        "/messages/{message_id:int}/pin", resources.pin(),
    )

    router.add_route(
        "/messages/unpin", resources.unpin(),
    )

    router.add_route(
        "/messages/wallet", resources.wallet(),
    )

    router.add_route(
        "/messages/call", resources.call(),
    )

    router.add_route(
        "/messages/auth", resources.auth(),
    )

    router.add_route(
        "/messages/react", resources.react(),
    )

    router.add_route(
        "/messages/forward", resources.forward(),
    )

    router.add_route(
        "/messages/forwards", resources.forwards(),
    )

    router.add_route(
        "/messages/deletes", resources.delete_item(),
    )

    router.add_route("/messages/zoom", resources.zoom())

    router.add_route("/messages/meeting", resources.meeting())

    router.add_route(
        "/messages/bot", resources.bot(),
    )

    router.add_route(
        "/messages/internal/mass_direct_messages",
        resources.mass_direct_messages(),
    )

    router.add_route(
        "/messages/polls", resources.poll(),
    )

    router.add_route(
        "/messages/votes", resources.vote(),
    )

    router.add_route("/messages/votes/choose", resources.vote_choice())

    router.add_route("/messages/votes/unchoose", resources.unvote_choice())

    router.add_route("/messages/save_message", resources.save_message())

    router.add_route(
        "/internal/messages/mass_direct_messages",
        resources.mass_direct_messages(),
    )
