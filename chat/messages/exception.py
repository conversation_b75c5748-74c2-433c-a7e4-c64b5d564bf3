from typing import List, Tuple

from chat import response_messages
from chat.exception import ChatError, MultipleErrors


class DisallowToPinMessageTypeError(ChatError):
    error = "cant_pin_message_type"
    i18n_message = response_messages.PERMISSION_DENIED
    http_code = 403


class DeleteMultipleMessagesError(MultipleErrors):
    error = "delete_messsages_error"

    def __init__(self, errors: List[Tuple[str, Exception]], **params):
        super().__init__(
            403,
            response_messages.CANNOT_DELETE_SOME_MESSAGES,
            errors,
            **params
        )

    def translate(self, locale="vi"):
        super().translate(locale)

        # just improve response a little bit
        # when we get same errors to messages we perform delete action
        # we return this error
        errors = list(set([err["msg"] for err in self.error_details or []]))
        if len(errors) == 1:
            self.message = errors[0]
