from logging import Logger

from chat.connections.redis_c import RedisConnection
from chat.utils.json import json_loads

from chat.messages.model import MessageModel


class MessageCache(object):
    """
    Updates thread scores in folders and tracks active members.
    Each thread will have a score in a folder. Thread with higher score will be
    positioned at higher position.

    A pinned thread has much higher score, so it will stay on top:
        score = timestamp * 1000
    """

    # FIXME: will we have overflow problem ? timestamp + 1000 years
    def __init__(
        self,
        log: Logger,
        engine: RedisConnection,
    ):
        self.log = log
        self.client = engine.client

        self.last_msg_key = "chat:cache:last_messages:{thread_id}"
        self.lock_key = "chat:lock:last_message:{thread_id}"

        self.msg_ids_key = "chat:cache:messages:{thread_id}:ids"
        self.msg_key = "chat:cache:messages:{thread_id}"

    def add(self, message: MessageModel):
        thread_id = str(message.thread_id)
        msg_id = str(message.id)

        key_ids = self.msg_ids_key.format(thread_id=thread_id)
        key_msg = self.msg_key.format(thread_id=thread_id)
        self.client.zadd(key_ids, {msg_id: message.created_at})
        self.client.hset(key_msg, mapping={msg_id: message.json()})

    def replace_last_message(self, message: MessageModel):
        thread_id = str(message.thread_id)
        key = self.last_msg_key.format(thread_id=thread_id)
        old_message_cache = self.client.get(key)
        if old_message_cache:
            old_message = MessageModel(**json_loads(old_message_cache))
            if old_message.id != message.id:
                return
        self.client.set(key, message.json())

    def update(self, message: MessageModel):
        thread_id = str(message.thread_id)
        msg_id = str(message.id)

        key_ids = self.msg_ids_key.format(thread_id=thread_id)
        key_msg = self.msg_key.format(thread_id=thread_id)

        score = self.client.zscore(key_ids, msg_id)
        if not score:
            return

        self.client.hset(key_msg, mapping={msg_id: message.json()})

    def get(self, thread_id: int, msg_id: int):
        key_msg = self.msg_key.format(thread_id=thread_id)

        message_cache = self.client.hget(key_msg, str(msg_id))

        if not message_cache:
            return None

        return MessageModel(**json_loads(message_cache))

    def get_last_message(self, thread_id: int):
        key = self.last_msg_key.format(thread_id=thread_id)
        message_cache = self.client.get(key)

        if not message_cache:
            return None

        return MessageModel(**json_loads(message_cache))
