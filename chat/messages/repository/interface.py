from abc import ABC, abstractmethod
from typing import List, Optional

from chat.messages.model import MessageModel


class MessageRepository(ABC):
    @abstractmethod
    def get_by_id(
        self, thread_id: int, message_id: int
    ) -> Optional[MessageModel]:
        """Get message by"""

    @abstractmethod
    def get_by_ids(
        self, thread_id: int, message_ids: List[int]
    ) -> List[MessageModel]:
        pass

    @abstractmethod
    def get_between(self, thread_id: int, from_, to) -> List[MessageModel]:
        pass

    @abstractmethod
    def update_delete_bulk(self, message: MessageModel):
        pass

    @abstractmethod
    def update_react(self, message: MessageModel):
        pass

    @abstractmethod
    def replace_message(self, message: MessageModel):
        pass

    @abstractmethod
    def attach_sub_thread_to_message(self, message: MessageModel):
        pass
