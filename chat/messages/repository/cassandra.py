import concurrent
from logging import Logger
from math import floor
from typing import Optional, List
from uuid import UUID

from cassandra.query import BatchStatement, SimpleStatement

from chat.config import Settings
from chat.connections.cassandra_c import CassandraConnection
from chat.messages.model import MessageModel
from chat.utils.common import get_pagination
from chat.utils.common import order_with_page

from .interface import MessageRepository
from .cache import MessageCache


class MessageRepositoryMock(object):
    def __init__(self, storages):
        self.t_name = "messages"
        self.storages = storages

    def get_by_id(self, thread_id: int, message_id: int):
        return MessageModel(
            thread_id=thread_id, **self.storages[message_id - 1]
        )

    @classmethod
    def init_storage(cls, user_id: str):
        storages = [
            {
                "bucket": 0,
                "id": 1,
                "body": """{
                    "reply_to_msg": null,
                    "media": [],
                    "type": "text",
                    "tmp_text": null,
                    "text": "12341241241241",
                    "metadata": {
                        "preview_link": null,
                        "mentions": [
                            {"target": "all", "length": 3, "offset": 0},
                            {"target": "70", "length": 10, "offset": 3},
                        ],
                    },
                    "forward": null,
                }""",
                "created_at": 1617680762140,
                "will_deleted_at": 0,
                "delete_level": 0,
                "deleted_by": None,
                "deliver_status": 0,
                "react_binary": None,
                "reply_to": None,
                "type": None,
                "user_id": str(user_id),
                "uuid": UUID("0c14092a-5c67-4d6d-bd21-be1839404337"),
                "version": "3",
            }
        ]

        return cls(storages)


class MessageRepositoryCassandra(MessageRepository):
    def __init__(
        self,
        config: Settings,
        log: Logger,
        engine: CassandraConnection,
        cache: MessageCache,
    ):
        self.cc = config.cassandra
        self.log = log
        self.session = engine
        self.BUCKET_FACTOR = config.bucket_message_factor
        self.cache: MessageCache = cache

        self.t_name = "messages"

    def _get_bucket(self, message_id: int):
        return floor(message_id / self.BUCKET_FACTOR)

    def get_by_id(self, thread_id: int, message_id: int):
        bucket = self._get_bucket(message_id)
        get_by_id_q = (
            "SELECT * "
            f"FROM {self.t_name} "
            "WHERE thread_id=%s "
            "AND bucket=%s "
            "AND id=%s "
            "LIMIT 1"
        )
        result = self.session.execute(
            get_by_id_q,
            (
                thread_id,
                bucket,
                message_id,
            ),
        )
        record = result.one()
        return MessageModel(**record) if record else record

    def get_by_ids(self, thread_id: int, message_ids):
        records = []
        with concurrent.futures.ThreadPoolExecutor() as executor:  # type: ignore # noqa
            futures = []
            for message_id in message_ids:
                futures.append(
                    executor.submit(self.get_by_id, thread_id, message_id)
                )
            for future in concurrent.futures.as_completed(futures):  # type: ignore # noqa
                record = future.result()
                if not record:
                    continue
                records.append(record)
        return records

    def get_between(self, thread_id: int, from_, to):
        pagination = get_pagination(to, from_, self.BUCKET_FACTOR)
        rows = []
        get_between_q = (
            "SELECT * "
            f"FROM {self.t_name} "
            "WHERE thread_id=%s "
            "AND bucket=%s "
            "AND id > %s "
            "AND id <= %s"
        )
        messages: List[MessageModel] = []

        for pa in reversed(pagination):
            t, f, bucket = pa
            t_result = self.session.execute(
                get_between_q, (thread_id, bucket, t, f)
            )
            rows.extend(t_result.current_rows)

        for r in rows:
            try:
                messages.append(MessageModel(**r))
            except Exception as e:
                print(e)
                continue
        return messages

    def update_delete_bulk(
        self,
        message: MessageModel,
    ):
        self._update(message)

        # delete_infos = order_with_page(delete_message_ids,
        # self.BUCKET_FACTOR)
        # update_delete_level_q = (
        #     f"UPDATE {self.t_name} "
        #     "SET delete_level=%s "
        #     "WHERE thread_id=%s "
        #     "AND bucket=%s "
        #     "AND id=%s "
        #     "IF EXISTS"
        # )

        # update_delete_by_q = (
        #     f"UPDATE {self.t_name} "
        #     "SET deleted_by = deleted_by + %s "
        #     "WHERE thread_id=%s "
        #     "AND bucket=%s "
        #     "AND id=%s "
        #     "IF EXISTS"
        # )

        # for k, v in delete_infos.items():
        #     batch = BatchStatement()
        #     for i in v:
        #         batch.add(
        #             SimpleStatement(update_delete_level_q),
        #             (level, thread_id, k, i),
        #         )
        #         if user_id:
        #             batch.add(
        #                 SimpleStatement(update_delete_by_q),
        #                 ({user_id}, thread_id, k, i),
        #             )

        #     self.session.execute(batch)

    def _update(self, message: MessageModel):
        update_q = f"""UPDATE {self.t_name}
        SET body=%s,
            sub_thread_id=%s,
            delete_level=%s,
            deleted_by=%s,
            react_binary=%s
        WHERE thread_id=%s
            AND bucket=%s
            AND id=%s
            IF EXISTS
        """
        bucket = self._get_bucket(message.id)
        self.session.execute(
            update_q,
            (
                message.body.json(),
                message.sub_thread_id,
                message.delete_level,
                message.deleted_by,
                message.react_binary,
                message.thread_id,
                bucket,
                message.id,
            ),
        )
        self._update_cache(message)

    def update_react(self, message: MessageModel):
        self._update(message)
        # update_react_q = (
        #     f"UPDATE {self.t_name} "
        #     "SET react_binary=%s "
        #     "WHERE thread_id=%s "
        #     "AND bucket=%s "
        #     "AND id=%s "
        #     "IF EXISTS"  # noqa
        # )

        # bucket = self._get_bucket(message.id)
        # self.session.execute(
        #     update_react_q,
        #     (message.react_binary, message.thread_id, bucket, message.id),
        # )
        # self._update_cache(message)

    def replace_message(self, message: MessageModel):
        self._update(message)
        # replace_body_q = (
        #     f"UPDATE {self.t_name} "
        #     "SET body=%s "
        #     "WHERE thread_id=%s "
        #     "AND bucket=%s "
        #     "AND id=%s "
        #     "IF EXISTS"  # noqa
        # )

        # bucket = self._get_bucket(message.id)
        # self.session.execute(
        #     replace_body_q,
        #     (message.body.json(), message.thread_id, bucket, message.id),
        # )
        # self._update_cache(message)

    def attach_sub_thread_to_message(self, message: MessageModel):
        self._update(message)
        # query = (
        #     f"UPDATE {self.t_name} "
        #     "SET sub_thread_id=%s "
        #     "WHERE thread_id=%s "
        #     "AND bucket=%s "
        #     "AND id=%s "
        #     "IF EXISTS"  # noqa
        # )
        # bucket = self._get_bucket(message.id)
        # self.session.execute(
        #     query,
        #     (message.sub_thread_id, message.thread_id, bucket, message.id),
        # )
        # self._update_cache(message)

    def _update_cache(self, message: MessageModel):
        self.cache.update(message)
        # last message
        self.cache.replace_last_message(message)
