import select
import threading
import time
from logging import Logger
from queue import Empty as QueueEmpty
from queue import Full as QueueFull
from queue import Queue
from typing import Any

import pika
from pika.channel import Channel

from chat.connections.rabbitmq_c import RabbitMQConnection
from chat.metrics.model import AppMetrics
from chat.utils.json import json_loads
from chat import constant


def NOOP(x):
    pass


class AmqpClient(object):
    def __init__(self, rabbit_c: RabbitMQConnection, logger: Logger):
        self.rabbit_c = rabbit_c
        self._connection = self.rabbit_c.connect()
        self.log = logger

    @property
    def connection(self):
        return self._connection

    def reconnect(self, first_try=True):
        while True:
            try:
                self._connection = self.rabbit_c.connect()
                self.log.info("RabbitMQ re-connected success")
                break
            except pika.exceptions.AMQPConnectionError as e:
                if first_try:
                    self.log.error("RabbitMQ can't connect in first time")
                    raise e
                self.log.error("RabbitMQ can't connect, retry after 3s")
                time.sleep(3)

    def __getattr__(self, name):
        """
        for example:

        >>> client = Amqpclient()
        >>> channel = client.channnel()
        """

        def wrapped(*args, **kwargs):
            for _ in range(3):
                method = getattr(self._connection, name)
                try:
                    return method(*args, **kwargs)
                except CONNECTIVITY_ERRORS:
                    self._connection = self.rabbit_c.connect()

        return wrapped

    def sleep(self, dur):
        self.log.debug("Rabbit connection sleep!!!")
        while True:
            try:
                self._connection.sleep(dur)
                break
            except CONNECTIVITY_ERRORS:
                self.log.error("RabbitMQ is closed, retry after 3s")
                time.sleep(3)
                self.reconnect(False)


CONNECTIVITY_ERRORS = (
    pika.exceptions.AMQPConnectionError,
    pika.exceptions.ConnectionClosed,
    pika.exceptions.ChannelClosed,
    pika.exceptions.StreamLostError,
    pika.exceptions.ChannelWrongStateError,
    pika.exceptions.ConnectionClosedByBroker,
    select.error,  # XXX: https://github.com/pika/pika/issues/412
    ValueError,  # ValueError("Timeout closed before call")
)


class Publisher(threading.Thread):
    def __init__(self, log: Logger, client: AmqpClient, metrics: AppMetrics):
        super().__init__()
        self.daemon = True
        self.is_running = True
        self.client = client
        self.log = log
        self.channel: Channel = None
        self.metrics = metrics
        self.connection = self.client.connection
        self.channel = self.connection.channel()
        # queue to store pending tasks
        self.pending_tasks: Queue = Queue(100)

    def run(self):
        while self.is_running:
            try:
                # ping back server every 1 second
                self.connection.process_data_events(time_limit=1)
            except Exception as e:
                self.log.exception(
                    f"[AMQP] Error while sending heartbeat: {e}",
                    exc_info=False,
                )
                # try to restart
                self.client.sleep(1)
                self.log.info("[AMQP] Reconnecting to brokers ...")
                self.client.reconnect()
                self.connection = self.client.connection

    def _run_pending_tasks(self):
        pending_tasks = 0
        while True:
            try:
                job = self.pending_tasks.get_nowait()
                self.publish(**job)
                pending_tasks += 1
            except QueueEmpty:
                break
            except Exception:
                self.log.exception(
                    """[AMQP] Got unexpected errors {e} while processing
                        pending tasks""",
                    exc_info=False,
                )
                break

        if pending_tasks > 0:
            self.log.info(f"[AMQP] Processed {pending_tasks} pending tasks")

    def _publish(
        self,
        exchange: str,
        routing_key: str,
        body: Any,
        properties=None,
        mandatory: bool = False,
    ):
        for _ in range(3):
            try:
                ti = time.time()
                self.channel.basic_publish(
                    exchange, routing_key, body, properties, mandatory
                )
                ti = time.time() - ti
                self.log.info(f"[AMQP] Published message in {ti}s")

                return True
            except pika.exceptions.AMQPChannelError as e:
                self.metrics.amqp_call_errors.inc()
                self.log.exception(
                    f"[AMQP] Got channel error: {e}", exc_info=False
                )
                self.channel = self.connection.channel()
                self.channel.confirm_delivery()
                self.log.info("[AMQP] Reinitializing channel ...")
                self._run_pending_tasks()
            except CONNECTIVITY_ERRORS as e:
                self.metrics.amqp_call_errors.inc()
                self.log.exception(
                    f"Got connectivity exception: {e}", exc_info=False
                )
                self.client.reconnect()
                self.connection = self.client.connection
                self.log.info("[AMQP] Restarting connection ...")
                self.channel = self.connection.channel()
                self.channel.confirm_delivery()
                self._run_pending_tasks()
            except Exception as e:
                self.metrics.amqp_call_errors.inc()
                self.log.exception(
                    f"Got unexpected exception: {e}", exc_info=False
                )
        return False

    def publish(
        self,
        exchange: str,
        routing_key: str,
        body: Any,
        properties=None,
        mandatory: bool = False,
    ):
        # return self._publish(exchange, routing_key, body, properties, mandatory) # noqa

        # non-blocking style
        # NOTE: it will send command to active connection
        # so it will ignore if connection is error
        # however, it will ignore errors, such as queue is closed by broker
        # it's also the right approach to use in multi-thread applications
        # so we will use this.
        if self.connection.is_closed:
            try:
                self.log.info(
                    """[AMQP] Channel is closed, putting message in retry
                    queue ..."""
                )
                # put in queue so we will try later when we reconnected
                self.pending_tasks.put(
                    {
                        "exchange": exchange,
                        "routing_key": routing_key,
                        "body": body,
                        "mandatory": mandatory,
                    }
                )
                return True
            except Exception:
                self.metrics.amqp_call_errors.inc()
                # failed to put to queue
                return False

        self.connection.add_callback_threadsafe(
            lambda: self._publish(
                exchange, routing_key, body, properties, mandatory
            )
        )
        return True

    def stop(self):
        self.log.info("[AMQP] Stopping...")
        if not self.is_running:
            return
        self.is_running = False
        # Wait until all the data events have been processed
        try:
            self.connection.process_data_events(time_limit=1)
            if self.connection.is_open:
                self.connection.close()
        except Exception:
            pass
        self.log.info("[AMQP] Stopped!")


class AmqpProducer(object):
    """New AMQP publisher.

    Old rabbitmq have several problems:
    + It's CPU intensive due to infite loop call in _communicate method
    + May have issue if it tries to call process_data_events in a closed
        channel

    Adapted from
    https://github.com/pika/pika/blob/main/examples/long_running_publisher.py
    """

    def __init__(
        self,
        name: str,
        client: AmqpClient,
        logger: Logger,
        metrics: AppMetrics,
    ):
        self._name = name
        self._publisher = Publisher(logger, client, metrics)
        self._publisher.daemon = True
        self._publisher.start()
        self.log = logger

        # is using queue needed here?
        # just add queue to make it compatible with old code
        self._queue: Queue = Queue(1000)

    def publish(
        self,
        exchange: str,
        routing_key: str,
        body,
        properties=None,
        mandatory=False,
    ):
        """Publishes a message to rabbitmq."""
        meta = dict(
            exchange=exchange,
            routing_key=routing_key,
            body=body,
            properties=properties,
            mandatory=mandatory,
        )
        try:
            # now = time.time()
            self._queue.put_nowait(meta)
            self.log.info("[AMQP] Queue size {}".format(self._queue.qsize()))
            status = self._publisher.publish(
                exchange, routing_key, body, properties, mandatory
            )
            self._queue.get_nowait()
            return status
        except QueueFull:
            self.log.error("[AMQP] Rabbit Queue is full")
            return False
        except Exception:
            return False

    def close(self):
        self._publisher.stop()


class AmqpProducerMock(AmqpProducer):
    def __init__(self, handler=NOOP):
        self._queue: Queue = Queue(1000)
        self._handler = handler
        self.messages = []
        self.heavy_messages = []

    def publish(
        self, exchange, routing_key, body, properties, mandatory=False
    ):
        try:
            event = json_loads(body)
            if event["event_type"] == constant.TASK_ADD_MEMBER_COLLAB:
                event["event_type"] = constant.TASK_ADD_MEMBER
            self._handler(event)
        except Exception as e:
            raise e
        if routing_key == constant.TASK_HEAVY_ROUTING_KEY:
            self.heavy_messages.append(json_loads(body))
        else:
            self.messages.append(json_loads(body))
        return True

    def close(self):
        pass
