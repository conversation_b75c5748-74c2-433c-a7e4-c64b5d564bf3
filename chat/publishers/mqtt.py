from logging import Logger
from typing import Any, Dict, Iterable, List

from typing_extensions import TypedDict

from chat.utils.common import ticktock
from chat.utils.json import json_dumps


class _MQTTMessage(TypedDict):
    user_ids: List[str]
    msg: Any


class MqttPublisher(object):
    def __init__(
        self,
        client,
        prefix_version: str,
        postfix_status_channel: str,
        log: Logger,
    ):
        self._client = client
        self._pref_ver = prefix_version
        self._postf_stt = postfix_status_channel
        self.log = log
        self._make_channel()

    def _make_channel(self):
        self.stt_chan_f = f"{self._pref_ver}/{{}}/{self._postf_stt}"
        self.stt_page_chan_f = f"{self._pref_ver}/page/{{}}/{self._postf_stt}"

    def publish_status(self, user_ids: Iterable[str], msg: Dict):
        if not user_ids:
            return
        msgs = []
        serialized_msg = json_dumps(msg)
        for user_id in user_ids:
            msgs.append(
                {
                    "topic": self.stt_chan_f.format(user_id),
                    "payload": serialized_msg,
                    "qos": 0,
                    "retain": False,
                }
            )

        tick = ticktock()
        for m in msgs:
            self._client.publish(
                m["topic"],
                payload=m["payload"],
                qos=m["qos"],
                retain=m["retain"],
            )
            # self.log.info("PUBLISED user status: m %s ", m)
            self.log.debug("PUBLISED: took {}".format(tick()))

    def publish_page_status(self, user_ids: Iterable[str], msg: Dict):
        if not user_ids:
            return
        msgs = []
        serialized_msg = json_dumps(msg)
        for user_id in user_ids:
            msgs.append(
                {
                    "topic": self.stt_page_chan_f.format(user_id),
                    "payload": serialized_msg,
                    "qos": 0,
                    "retain": False,
                }
            )

        tick = ticktock()
        for m in msgs:
            self._client.publish(
                m["topic"],
                payload=m["payload"],
                qos=m["qos"],
                retain=m["retain"],
            )
            # self.log.info("PUBLISED page status: m %s ", m)
            self.log.debug("PUBLISED: took {}".format(tick()))


class MqttPublisherMock(MqttPublisher):
    def __init__(self) -> None:
        self.messages: List[_MQTTMessage] = []

    def publish_status(self, user_ids: Iterable[str], msg):
        self.messages.append({"user_ids": list(user_ids), "msg": msg})

    def publish_page_status(self, user_ids: Iterable[str], msg):
        self.messages.append({"user_ids": list(user_ids), "msg": msg})
