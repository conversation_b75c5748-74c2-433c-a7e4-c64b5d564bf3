from logging import Logger

from chat.config import Settings
from chat.connections.cassandra_c import CassandraConnection

from .interface import HealthCheckRepository


class HealthCheckRepositoryCassandra(HealthCheckRepository):
    def __init__(
        self, config: Settings, log: Logger, engine: CassandraConnection
    ):
        self.cc = config.cassandra
        self.log = log
        self.session = engine

    def ping(self):
        self.session.execute("SELECT uuid() FROM system.local")
