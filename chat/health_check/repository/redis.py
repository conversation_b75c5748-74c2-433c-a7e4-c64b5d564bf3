from typing import List
from logging import Logger
from chat.connections.redis_c import RedisConnection

from .interface import HealthCheckRepository


class HealthCheckRepositoryRedis(HealthCheckRepository):
    def __init__(self, log: Logger, engines: List[RedisConnection]):
        self.log = log
        self.engines = engines

    def ping(self):
        for engine in self.engines:
            engine.client.ping()
