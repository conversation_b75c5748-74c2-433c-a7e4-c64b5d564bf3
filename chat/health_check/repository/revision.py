from logging import Logger

from chat.config import Settings
from chat.connections.mysql_c import MySQLConnection
from chat.repositories import BaseRepository


class RevisionRepository(BaseRepository):
    def __init__(self, conf: Settings, log: Logger, mysql_c: MySQLConnection):
        self.conf = conf
        self.log = log
        self.session_m = mysql_c

    def get_current_revision(self):
        with self.session_m.get_session() as session:
            result = self._q_item(
                session, "SELECT version_num from alembic_version limit 1", {}
            )
            if not result:
                return 0
            try:
                return int(result["version_num"])
            except:
                return None
