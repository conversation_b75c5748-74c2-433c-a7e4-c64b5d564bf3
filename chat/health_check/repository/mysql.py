from logging import Logger

from chat.config import Settings
from chat.connections.mysql_c import MySQLConnection

from .interface import HealthCheckRepository


class HealthCheckRepositoryMySQL(HealthCheckRepository):
    def __init__(self, config: Settings, log: Logger, engine: MySQLConnection):
        self.cc = config.mysql
        self.log = log
        self.Session = engine.session

    def ping(self):
        session = self.Session()
        session.execute("SELECT 1")
        session.commit()
