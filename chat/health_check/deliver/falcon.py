import json
from typing import Optional

import falcon

from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import GuestResource
from chat.health_check.usecase import HealthCheckUsecase
from chat.utils.common import cache_health, GIT_COMMIT_VERSION


class HealthCheckResource(GuestResource):
    def __init__(self, app: MyApp, usecases: HealthCheckUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    def on_get(self, req: falcon.Request, resp: falcon.Response):
        @cache_health
        def check_health():
            with open(self.config.cdn_mapping_filepath) as json_file:
                _file = json.load(json_file)

            self.usecase.ping()
            revision = self.usecase.get_current_revision()
            revision = None
            resp.status = falcon.HTTP_200
            if revision is not None:
                return {
                    "message": "OK",
                    "revision": revision,
                    "commit_hash": GIT_COMMIT_VERSION,
                    "cdn_mapping": _file,
                }
            return {
                "message": "OK",
                "commit_hash": GIT_COMMIT_VERSION,
                "cdn_mapping": _file,
            }

        result = check_health()
        resp.media = result


class MemoryTraceResource(GuestResource):
    def __init__(self, app: MyApp, usecases: HealthCheckUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    def on_get(self, req: falcon.Request, resp: falcon.Response):
        tracing = self.usecase.memory_trace()
        resp.status = falcon.HTTP_200
        resp.media = {"message": tracing}
