from dependency_injector import containers, providers

from chat.app import MyApp
from chat.health_check.deliver import HealthCheckResource, MemoryTraceResource
from chat.health_check.usecase import HealthCheckUsecase


class HealtCheckResourcesContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(HealthCheckUsecase)

    healthz = providers.Singleton(HealthCheckResource, app, usecases)
    memory_trace = providers.Singleton(MemoryTraceResource, app, usecases)


class HealthCheckContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Singleton(HealthCheckUsecase, app=app)

    resources = providers.Singleton(
        HealtCheckResourcesContainer, app=app, usecases=usecases
    )
