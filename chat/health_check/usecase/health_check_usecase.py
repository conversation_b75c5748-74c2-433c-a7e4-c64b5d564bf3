from chat.app import MyApp
from chat.utils.memory_profile import trace


class HealthCheckUsecase(object):
    def __init__(self, app: MyApp):
        self.log = app.log
        self.hc_repo = app.repos.health_check
        self.revision_repo = app.repos.revision

    def ping(self):
        self.hc_repo.ping()

    def memory_trace(self):
        return trace()

    def get_current_revision(self):
        return self.revision_repo.get_current_revision()
