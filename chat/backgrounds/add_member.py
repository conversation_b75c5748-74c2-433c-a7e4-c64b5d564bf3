from chat.participants.usecase.add_participant_usecase import (
    AddParticipantUseCase,
)


def handle(log, p_usecase: AddParticipantUseCase, body_d, task_id=None):
    try:
        p_usecase.add_members_bg(
            body_d["user_id"],
            body_d["thread_id"],
            body_d["invite_ids"],
            body_d["type"],
            body_d.get("receiver_infos"),
            skip_action_notes=body_d.get("skip_action_notes") or False,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
