from logging import Logger
from typing import Dict

from chat.folders.usecase import FolderUsecase


def handle(
    log: Logger, folder_usecase: FolderUsecase, body_d: Dict, task_id=None
):
    try:
        folder_usecase.sort_folders_bg(
            user_id=body_d["user_id"],
            ordered_aliases=body_d["aliases"],
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
