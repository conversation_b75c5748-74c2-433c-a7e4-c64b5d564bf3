from logging import Logger
from typing import Dict

from chat.messages.usecase.delete_message_usecase import DeleteMessageUseCase


def handle(
    log: Logger, m_usecase: DeleteMessageUseCase, body_d: Dict, task_id=None
):
    try:
        m_usecase.delete(
            user_id=body_d["user_id"],
            thread_id=body_d["thread_id"],
            message_id=body_d["message_id"],
            level=body_d["level"],
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
