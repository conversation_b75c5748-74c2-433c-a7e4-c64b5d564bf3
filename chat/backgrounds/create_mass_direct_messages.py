from logging import Logger

import structlog

from chat import constant
from chat.container.app import <PERSON><PERSON><PERSON><PERSON><PERSON>
from chat.messages.model.task import TaskCreateMassDirectMessages

logger = structlog.get_logger()
BATCH_SIZE = 50
NUM_THREADS = 2


def handle(log: Logger, container: AppContainer, body, task_id=None):
    try:
        usecase = container.message().usecases().create_message
        parsed = TaskCreateMassDirectMessages(**body)
        log.info(
            "Running task {}, id: {}".format(
                constant.TASK_CREATE_MASS_DIRECT_MESSAGES, task_id
            )
        )
        usecase.create_mass_direct_messages_bg(
            sender_id=parsed.sender_id,
            user_ids=parsed.user_ids,
            body=parsed.body,
            api_version=parsed.api_version,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
