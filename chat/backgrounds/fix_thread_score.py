# from collections import defaultdict
# from concurrent.futures import ThreadPoolExecutor
# from logging import Logger
# from time import time
# from typing import List, Optional

# import structlog

# from chat import constant
# from chat.admin.model import TaskFixThreadScore
# from chat.container.app import AppContainer

# logger = structlog.get_logger()
# BATCH_SIZE = 50
# NUM_THREADS = 2


# def pin_count_key(user_id, folder):
#     if folder == "default":
#         return f"{user_id}_threads_pin"

#     return f"{user_id}_threads_pin_{folder}"


# def folder_score_key(user_id, folder):
#     if folder == "default":
#         return f"{user_id}_threads"

#     return f"{user_id}_threads_{folder}"


# def fix_thread_score_for_workspace(
#     logger: Logger,
#     container: AppContainer,
#     workspace_id: Optional[str] = None,
#     user_ids: List[str] = [],
#     file: Optional[str] = None,
#     task_id=None,
# ):
#     return
#     redis_c = container.conns().redis
#     mysql_c = container.conns().mysql

#     total_deletions = 0
#     total_insertions = 0
#     user_pinned = defaultdict(defaultdict)  # type: ignore

#     def fill_data(result):
#         while True:
#             chunk = result.fetchmany(100)
#             if not chunk:
#                 break

#             # logger.info("Chunk len: {}".format(len(chunk)))
#             for row in chunk:
#                 user_id = row["user_id"]
#                 thread_id = int(row["thread_id"])
#                 folder = row["folder"]
#                 pin_pos = row["pin_pos"]
#                 pin_default_pos = row["pin_default_pos"]

#                 if folder not in user_pinned[user_id]:
#                     user_pinned[user_id][folder] = {}

#                 if "default" not in user_pinned[user_id]:
#                     user_pinned[user_id]["default"] = {}

#                 if pin_default_pos > 0:
#                     user_pinned[user_id]["default"][
#                         thread_id
#                     ] = pin_default_pos

#                 if pin_pos > 0:
#                     user_pinned[user_id][folder][thread_id] = pin_pos

#     with mysql_c.get_session() as session:
#         logger.info("{} - Loading data from database ...".format(task_id))
#         ti = time()
#         if workspace_id:
#             query = """SELECT p.user_id,
#                         p.thread_id,
#                         p.folder,
#                         p.pin_default_pos,
#                         p.pin_pos
#                 FROM participant_threads AS p
#                 JOIN threads AS t ON p.thread_id = t.id
#             WHERE t.message_count > p.delete_to
#                 AND p.is_removed=0
#                 AND p.user_id != 'system'
#                 AND t.workspace_id=:workspace_id"""
#             result = session.execute(query, {"workspace_id": workspace_id})
#             fill_data(result)
#         elif len(user_ids) > 0:
#             query = """SELECT p.user_id,
#                         p.thread_id,
#                         p.folder,
#                         p.pin_default_pos,
#                         p.pin_pos
#                 FROM participant_threads AS p
#                 JOIN threads AS t ON p.thread_id = t.id
#                 WHERE t.message_count > p.delete_to
#                 AND p.is_removed=0
#                 AND p.user_id in :user_ids"""
#             result = session.execute(
#                 query,
#                 {"user_ids": user_ids},
#             )
#             fill_data(result)
#         elif file is not None:
#             # read userids from file
#             user_ids = []
#             try:
#                 with open(file) as f:
#                     for line in f:
#                         line = line.strip()
#                         if line:
#                             user_ids.append(line)
#             except Exception:
#                 logger.error(
#                     "{} Failed to load user_ids from file {}".format(
#                         task_id, file
#                     )
#                 )
#             for i in range(len(user_ids), 50):
#                 chunk_user_ids = user_ids[i : i + 50]
#                 if len(chunk_user_ids) == 0:
#                     break
#                 query = """SELECT p.user_id,
#                             p.thread_id,
#                             p.folder,
#                             p.pin_default_pos,
#                             p.pin_pos
#                         FROM participant_threads AS p
#                         JOIN threads AS t ON p.thread_id = t.id
#                         WHERE t.message_count > p.delete_to
#                         AND p.is_removed=0
#                         AND p.user_id in :user_ids"""
#                 result = session.execute(
#                     query,
#                     {"user_ids": chunk_user_ids},
#                 )
#                 fill_data(result)

#         ti = time() - ti
#         logger.info("{} - Data loaded in {:.2f}s!".format(task_id, ti))

#         def sync_user_pinned_threads_list(args):
#             (user_id, pinned) = args
#             # batch_id = 0
#             total_deletions = 0
#             total_insertions = 0
#             batch_size = 0
#             redis_pipe = redis_c.client.pipeline()

#             for folder, thread_pinned_times in pinned.items():
#                 redis_pin_count_key = pin_count_key(user_id, folder)
#                 redis_folder_score_key = folder_score_key(user_id, folder)

#                 thread_ids = set(thread_pinned_times.keys())
#                 redis_thread_ids = set(
#                     [
#                         int(th)
#                         for th in redis_c.client.zrange(
#                             redis_pin_count_key, 0, -1
#                         )
#                     ]
#                 )
#                 to_delete = redis_thread_ids - thread_ids
#                 to_insert = thread_ids - redis_thread_ids
#                 to_insert = thread_ids  # try overwrite pinned values

#                 if len(to_delete) > 0:
#                     # remove all scores related to thread in redis

#                     # logger.info(
#                     #     """Removing in redis: user_id: {},
#                     #                       folder: {},
#                     #                       threads: {}""".format(
#                     #         user_id, folder, to_delete
#                     #     )
#                     # )
#                     redis_pipe.zrem(redis_pin_count_key, *to_delete)
#                     redis_pipe.zrem(redis_folder_score_key, *to_delete)

#                     total_deletions += len(to_delete)
#                     batch_size += 2

#                     if batch_size == BATCH_SIZE:
#                         batch_size = 0
#                         redis_pipe.execute()

#                 if len(to_insert) > 0:
#                     thread_scores = {}
#                     for th in to_insert:
#                         thread_scores[th] = thread_pinned_times[th] * 1000

#                     redis_pipe.zadd(redis_pin_count_key, thread_scores)
#                     redis_pipe.zadd(redis_folder_score_key, thread_scores)
#                     # logger.info(
#                     #     """Inserting to redis: user_id: {},
#                     #                           folder: {},
#                     #                           threads: {},
#                     #                           thread_scores: {}""".format(
#                     #         user_id, folder, to_insert, thread_scores
#                     #     )
#                     # )

#                     total_insertions += len(to_insert)
#                     batch_size += 2

#                     if batch_size == BATCH_SIZE:
#                         batch_size = 0
#                         redis_pipe.execute()

#             if batch_size > 0:
#                 redis_pipe.execute()

#             return (total_insertions, total_deletions)

#         logger.info(
#             "{} - Processing data with {} threads".format(task_id, NUM_THREADS)
#         )
#         with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
#             jobs = [
#                 (user_id, pinned) for (user_id, pinned) in user_pinned.items()
#             ]
#             results = list(executor.map(sync_user_pinned_threads_list, jobs))
#             total_deletions = 0
#             total_insertions = 0
#             for r in results:
#                 total_deletions += r[1]
#                 total_insertions += r[0]
#             logger.info(
#                 "{} - Total insertion: {}, total deletions: {}".format(
#                     task_id, total_insertions, total_deletions
#                 )
#             )

#     logger.info("{} - Done!".format(task_id))


# def handle(log: Logger, container: AppContainer, body, task_id=None):
#     try:
#         parsed = TaskFixThreadScore(**body)
#         log.info(
#             "Running task {}, id: {}, body: {}".format(
#                 constant.TASK_FIX_THREAD_SCORE, task_id, body
#             )
#         )
#         fix_thread_score_for_workspace(
#             log,
#             container,
#             parsed.workspace_id or "",
#             parsed.user_ids,
#             parsed.file or "",
#             task_id=task_id,
#         )
#         logger.info("Done task {}".format(task_id))
#     except Exception as error:
#         log.warning(
#             "Worker error: {},  task_id {}".format(error, task_id),
#             exc_info=True,
#         )
