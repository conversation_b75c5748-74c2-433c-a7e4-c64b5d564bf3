from logging import Logger

import structlog

from chat import constant
from chat.model import make_input
from chat.container.app import AppContainer
from chat.threads.model.task import TaskReactivateMemberInGroup

logger = structlog.get_logger()
BATCH_SIZE = 50
NUM_THREADS = 2


def handle(log: Logger, container: AppContainer, body, task_id=None):
    try:
        usecase = container.thread().usecases().member_activation
        parsed, errors = make_input(TaskReactivateMemberInGroup, body)
        if not parsed and errors:
            log.warning("Invalid job input: {}".format(errors))
            return

        log.info(
            "Running task {}, id: {}".format(
                constant.TASK_REACTIVATE_MEMBER_IN_GROUP, task_id
            )
        )
        usecase.reactivate_bg(
            user_id=parsed.user_id,
            workspace_id=parsed.workspace_id,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
