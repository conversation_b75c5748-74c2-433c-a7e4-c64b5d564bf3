from logging import Logger
from typing import Dict

from chat.threads.usecase.create_thread_usecase import CreateThreadUseCase


def handle(
    log: Logger, t_usecase: CreateThreadUseCase, body_d: Dict, task_id=None
):
    try:
        t_usecase.create_members_bg(
            body_d["user_id"],
            body_d["thread_id"],
            body_d["members"],
            body_d["folder"],
            body_d.get("set_thread_score", True),
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
