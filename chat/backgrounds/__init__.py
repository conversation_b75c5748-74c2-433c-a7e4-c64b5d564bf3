import functools
import time

from sqlalchemy import exc

from chat import constant
from chat.backgrounds import (
    add_member,
    create_mass_direct_messages,
    create_member,
    delete_message,
    disband_group,
    edit_message,
    forward_message,
    forward_messages,
    join_collab,
    leave_group,
    move_folder,
    remove_member,
    sort_folder,
    add_member_collab,
    reactivate,
    deactivate,
)
from chat.container.app import App<PERSON>ontainer
from chat.utils.json import json_loads
from chat.utils.common import ticktock


def setup(
    container: AppContainer,
):
    log = container.log()
    part_usecase = container.participant().usecases()
    thread_usecase = container.thread().usecases()
    msg_usecase = container.message().usecases()
    folder_usecase = container.folder().usecases()

    _handlers = {
        constant.TASK_ADD_MEMBER: functools.partial(
            add_member.handle,
            log,
            part_usecase.add_participant,
        ),
        constant.TASK_REMOVE_MEMBER: functools.partial(
            remove_member.handle, log, part_usecase.ban_participant
        ),
        constant.TASK_DISBAND_GROUP: functools.partial(
            disband_group.handle, log, thread_usecase.disband_group
        ),
        constant.TASK_LEAVE_GROUP: functools.partial(
            leave_group.handle,
            log,
            thread_usecase.leave_thread,
        ),
        constant.TASK_CREATE_MEMBER: functools.partial(
            create_member.handle,
            log,
            thread_usecase.create_thread,
        ),
        constant.TASK_FORWARD_MESSEAGE: functools.partial(
            forward_message.handle,
            log,
            msg_usecase.forward_message,
        ),
        constant.TASK_FORWARD_MULTIPLE_MESSAGES: functools.partial(
            forward_messages.handle,
            log,
            msg_usecase.forward_message,
        ),
        constant.TASK_DELETE_MESSAGE: functools.partial(
            delete_message.handle,
            log,
            msg_usecase.delete_message,
        ),
        constant.TASK_EDIT_MESSAGE: functools.partial(
            edit_message.handle,
            log,
            msg_usecase.edit_message,
        ),
        constant.TASK_MOVE_FOLDER: functools.partial(
            move_folder.handle,
            log,
            folder_usecase,
        ),
        constant.TASK_SORT_FOLDER: functools.partial(
            sort_folder.handle, log, folder_usecase
        ),
        constant.TASK_CREATE_MASS_DIRECT_MESSAGES: functools.partial(
            create_mass_direct_messages.handle, log, container
        ),
        constant.TASK_JOIN_COLLAB: functools.partial(
            join_collab.handle, log, container
        ),
        constant.TASK_ADD_MEMBER_COLLAB: functools.partial(
            add_member_collab.handle,
            log,
            part_usecase.add_participant,
        ),
        constant.TASK_REACTIVATE_MEMBER_IN_GROUP: functools.partial(
            reactivate.handle, log, container
        ),
        constant.TASK_DEACTIVATE_MEMBER_IN_GROUP: functools.partial(
            deactivate.handle, log, container
        ),
    }

    def handler(event):
        if not isinstance(event, dict):
            log.error("Error event body: %s is not type dict", event)
        else:
            event_type = event["event_type"]
            task_id = event.get("uuid")
            if event_type in _handlers:
                log.info(f"[Background] execute task ({event_type})")
                handler = _handlers[event_type]
                handler(event["body"], task_id)
            else:
                log.warning(
                    "[Background Job] Unknown event type: {}, ignored".format(
                        event_type
                    )
                )

    def on_message(chan, method_frame, _header_frame, body, userdata=None):
        start = ticktock()
        try:
            event = json_loads(body)
        except Exception as e:
            log.error(e)
            log.error(body)
            chan.basic_ack(delivery_tag=method_frame.delivery_tag)
            return
        try:
            handler(event)
        except exc.OperationalError as e:
            log.error("Retry OperationalError reason %s in 3s", e)
            time.sleep(3)
            chan.basic_nack(delivery_tag=method_frame.delivery_tag)
            return

        except Exception as e:
            log.exception("There is error when handle and ignore %s", e)
        chan.basic_ack(delivery_tag=method_frame.delivery_tag)
        log.info(f"[Background] event took {start()} seconds")

    return handler, on_message
