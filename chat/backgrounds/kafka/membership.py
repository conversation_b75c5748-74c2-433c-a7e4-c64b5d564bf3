import signal
from logging import Logger
from time import sleep
from typing import Any, Callable, Dict, List, Optional, Union

import structlog
from pydantic import BaseModel

from chat.connections.gapo_client.membership.membership_client import (
    CollabFeature,
    CollabGroupRole,
    Member,
)
from chat.connections.gapo_client.membership.proto import (
    membership_pb2 as membership,
)
from chat.connections.kafka.membership import KafkaMembershipConsumer
from chat.constant import ADMIN_ROLE, MEMBER_ROLE, OWNER_ROLE
from chat.container.app import AppContainer
from chat.model import make_input
from chat.utils.json import json_loads
from chat import action_type

EVENT_MEMBER_REMOVED = "member_removed"
EVENT_MEMBER_ADDED = "member_added"
EVENT_ROLE_ASSIGNED = "role_assigned"
EVENT_ROLE_REMOVED = "role_retracted"
EVENT_COLLAB_CREATED = "collaborator_group_created"
EVENT_COLLAB_UPDATED = "collaborator_group_updated"
EVENT_ENABLE_FEATURE = "enable_feature"
EVENT_DISABLE_FEATURE = "disable_feature"
JOIN_REQUEST_ACCEPTED = "request_join_accepted"
EVENT_MEMBER_REQUEST = "member_request_created"


# data source constants
# which indicate the source of event

ROLE_MAP: Dict[str, str] = {
    str(CollabGroupRole.Admin.value): ADMIN_ROLE,
    str(CollabGroupRole.Member.value): MEMBER_ROLE,
    str(CollabGroupRole.Owner.value): OWNER_ROLE,
}


class BaseEventBody(BaseModel):
    data_source: int = membership.DATA_SOURCE_UNSPECIFIED


class BaseEvent(BaseModel):
    topic: str
    routing_key: str
    issue_at: int
    message: BaseEventBody


class CollabGroup(BaseModel):
    id: str
    name: str
    type: int
    status: int
    workspace_id: str
    creator: str
    created_at: int
    updated_at: int

    # just ignore some fields that we don't care about
    # privacy: int
    # auto_acept: bool
    # context: Any


class CollabMember(BaseModel):
    id: Union[str, None]
    user_id: str
    type: str
    collab_group_id: str
    roles: Optional[List[int]] = []
    added_by: Union[str, None]
    created_at: int
    updated_at: int


class CollabCreatedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    collab_group: CollabGroup


class CollabCreatedEvent(BaseEvent):
    """Event fired when a collab group is created."""

    message: CollabCreatedEventBody


class CollabUpdatedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    collab_group: CollabGroup


class CollabUpdatedEvent(BaseEvent):
    """Event fired when a collab group is updated."""

    message: CollabUpdatedEventBody


class MemberRemovedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    member: CollabMember


class MemberRemovedEvent(BaseEvent):
    message: MemberRemovedEventBody


class RoleAssignedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    user_id: str
    collab_group_id: str
    roles: List[int]


class RoleAssignedEvent(BaseEvent):
    message: RoleAssignedEventBody


class RoleRemovedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    user_id: str
    roles: List[int]
    collab_group_id: str


class RoleRemovedEvent(BaseEventBody):
    """Event fired when a role is removed."""

    message: RoleRemovedEventBody


class MemberAddedEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    # user_id: str
    member: CollabMember
    data_source: int

    def to_action_type(self) -> str:
        if self.caller_id != self.member.user_id:
            _source = action_type.INVITE_MEM
        else:
            _source = action_type.JOIN_BY_LINK

        if self.data_source == membership.DATA_SOURCE_CHAT_DEPARTMENT:
            _source = action_type.INVITE_ORG_BY_DEPARTMENT
        elif self.data_source == membership.DATA_SOURCE_CHAT_TITLE:
            _source = action_type.INVITE_ORG_BY_TITLE
        elif self.data_source == membership.DATA_SOURCE_CHAT_FILE:
            _source = action_type.INVITE_BY_FILE
        elif self.data_source == membership.DATA_SOURCE_CHAT_THREAD:
            _source = action_type.INVITE_BY_THREAD_CHAT
        elif self.data_source == membership.DATA_SOURCE_CHAT_JOIN_GROUP:
            _source = action_type.JOIN_BY_GROUP
        elif self.data_source == membership.DATA_SOURCE_CHAT_JOIN_DEPARTMENT:
            _source = action_type.JOIN_BY_DEPARTMENT

        return _source


class MemberAddedEvent(BaseEvent):
    """Event fired when new member is added to group."""

    message: MemberAddedEventBody


class FeatureEnabledEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    feature: int
    collab_group_id: str


class FeatureEnabledEvent(BaseEvent):
    """event fired when a collab feature is enabled."""

    message: FeatureEnabledEventBody


class FeatureDisabledEventBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    feature: int
    collab_group_id: str


class FeatureDisabledEvent(BaseEvent):
    """Event fired when a collab feature is disabled."""

    message: FeatureDisabledEventBody


class JoinRequestAcceptedBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    member: CollabMember
    data_source: int

    def to_action_type(self) -> str:
        _source = action_type.JOIN_BY_APPROVED
        if self.caller_id == self.member.user_id:
            _source = action_type.JOIN_BY_LINK
        return _source


class JoinRequestAcceptedEvent(BaseEvent):
    """Event fired when admin accept a join request."""

    message: JoinRequestAcceptedBody


class CreateRequestJoinBody(BaseEventBody):
    caller_id: str
    workspace_id: str
    member_request: CollabMember
    data_source: int

    def to_action_type(self) -> str:
        _source = action_type.JOIN_BUT_IN_REVIEW
        return _source


class CreateRequestJoinEvent(BaseEvent):
    """Event fired when admin accept a join request."""

    message: CreateRequestJoinBody


# global counter to track pending messages
# so we can exit worker gracefully
PENDING_MESSAGES = 0


def setup_kafka_worker(container: AppContainer):
    app = container.app()  # noqa
    conf = container.conf()
    log: Logger = structlog.get_logger()
    log.info("Setting kafka ...{}".format(conf.kafka.bootstrap_servers))
    if not conf.kafka.bootstrap_servers:
        log.warning("No kafka boostrap config is set, stop now!!!")
        return

    consumer = KafkaMembershipConsumer(log, conf)

    event_handler = create_event_handler(container)

    def on_message(kafka_msg):
        """
        Processes kafka events

        In order to process events in correct order,
        Events from same collab must be located in same partition.
        As my checking, msg_id -> collab_id, so we're fine now.

        NOTE: because all events in a collab is processed by SINGLE
        worker only, you may want to process many tasks in background workers
        as possible.
        """
        global PENDING_MESSAGES
        PENDING_MESSAGES += 1
        try:
            msg_key = kafka_msg.key()
            try:
                msg_key = msg_key.decode()
            except Exception:
                pass

            msg_offset = kafka_msg.offset()
            msg_partition = kafka_msg.partition()
            # parse kafka message
            # msg.id() -> message id
            # msg.partition() -> partition
            # msg.value() -> value
            # msg.key() -> key
            msg: Dict = json_loads(kafka_msg.value())

            log.info(
                f"""[Membership] New message key={msg_key},
                offset={msg_offset},
                part={msg_partition},
                val: {msg}"""
            )
        except Exception as exc:
            log.error(
                f"[Membership] Can't load message due to error {exc}",
                exc_info=True,
            )
            PENDING_MESSAGES -= 1
            return

        if not isinstance(msg, dict):
            log.warning(
                "[Membership] Got invalid message: {}".format(kafka_msg)
            )
            PENDING_MESSAGES -= 1
            return

        event_handler(msg)

    # capture signal to kill worker gracefully
    # It's important because we can't re-process a received message again
    def on_exit(*args):
        global PENDING_MESSAGES
        log.info(
            f"""Exiting worker,
            waiting to process {PENDING_MESSAGES} remaining messages"""
        )
        consumer.stop()
        # wait sometime to make sure
        # that we have processed all pending messages
        sleep(0.2)
        while True:
            if PENDING_MESSAGES <= 0:
                log.info("Processed all pending messaged")
                exit(0)
            sleep(0.2)

    signal.signal(signal.SIGINT, on_exit)
    signal.signal(signal.SIGTERM, on_exit)

    # start consumer
    consumer.start(on_message)


def create_event_handler(container: AppContainer):
    app = container.app()
    conf = container.conf()  # noqa
    log: Logger = structlog.get_logger()

    collab_client = app.repos.gapo.membership

    thread_repo = app.repos.thread
    session_m = app.conns.mysql

    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    def handle_member_added_events(msg):
        event, errors = make_input(MemberAddedEvent, msg)
        if errors:
            log.warning(
                f"Invalid member_added event: error: {errors}, body: {msg}"
            )
            return

        message = event.message

        with session_m.get_session() as session:
            collab_id = message.member.collab_group_id
            actor_id = message.caller_id
            user_id = message.member.user_id
            workspace_id = message.workspace_id

            log.info(
                f"""[Membership] Adding {user_id} to collab: {collab_id},
                actor_id: {actor_id},
                workspace: {workspace_id} ..."""
            )

            thread = thread_repo.get_by_collab_id(session, collab_id)

            if not thread:
                log.warning(
                    """[Membership] Cannot find group
                        corresponding to collab_id: {}""".format(
                        collab_id
                    )
                )
                return

            thread_id = thread.id
            if actor_id == '1500299925':
                log.info("[Membership] Adding ignore user")
                return

            if actor_id == user_id:
                # user join collab event
                thread_uscs.join_thread.join_collab(
                    user_id, collab_id, message.to_action_type()
                )
            else:
                participant_uscs.add_participant.publish_add_task(
                    actor_id, thread_id, user_id, message.to_action_type()
                )

    def handle_create_request_join_events(msg):
        event, errors = make_input(CreateRequestJoinEvent, msg)
        if errors:
            log.warning(
                f"Invalid member_added event: error: {errors}, body: {msg}"
            )
            return

        message = event.message

        with session_m.get_session() as session:
            collab_id = message.member_request.collab_group_id
            actor_id = message.caller_id
            user_id = message.member_request.user_id
            workspace_id = message.workspace_id

            log.info(
                f"""[Membership] Join request by {user_id} to
                collab {collab_id} accepted,
                actor_id: {actor_id},
                workspace: {workspace_id}"""
            )

            thread = thread_repo.get_by_collab_id(session, collab_id)

            if not thread:
                log.warning(
                    """[Membership] Cannot find group
                    corresponding to collab_id: {}""".format(
                        collab_id
                    )
                )
                return

            thread_id = thread.id
            participant_uscs.add_participant.publish_add_task(
                actor_id,
                thread_id,
                user_id,
                message.to_action_type(),
                skip=False,
            )

    def handle_join_request_accepted_events(msg):
        event, errors = make_input(JoinRequestAcceptedEvent, msg)
        if errors:
            log.warning(
                f"Invalid member_added event: error: {errors}, body: {msg}"
            )
            return

        message = event.message

        with session_m.get_session() as session:
            collab_id = message.member.collab_group_id
            actor_id = message.caller_id
            user_id = message.member.user_id
            workspace_id = message.workspace_id

            log.info(
                f"""[Membership] Join request by {user_id} to
                collab {collab_id} accepted,
                actor_id: {actor_id},
                workspace: {workspace_id}"""
            )

            thread = thread_repo.get_by_collab_id(session, collab_id)

            if not thread:
                log.warning(
                    """[Membership] Cannot find group
                    corresponding to collab_id: {}""".format(
                        collab_id
                    )
                )
                return

            thread_id = thread.id

            if actor_id == user_id:
                thread_uscs.join_thread.join_collab(
                    user_id, collab_id, message.to_action_type()
                )
            else:
                # TODO: is this safe to call 2 sequential background jobs?
                participant_uscs.add_participant.publish_add_task(
                    actor_id,
                    thread_id,
                    user_id,
                    message.to_action_type(),
                    skip=False,
                )
                # participant_uscs.add_participant.add_members_bg(
                #     actor_id, thread_id, [user_id], "invite_mem", None
                # )

    def handle_member_removed_events(msg):
        event, errors = make_input(MemberRemovedEvent, msg)
        if errors:
            log.warning(
                f"""[Membership] Invalid member_removed
                event: body: {msg}, error: {errors}"""
            )
            return

        message = event.message

        if is_event_from_chat_service(message.data_source):
            return

        actor_id = message.caller_id
        user_id = message.member.user_id
        collab_id = message.member.collab_group_id
        workspace_id = message.workspace_id

        log.info(
            f"""[Membership] {actor_id} is removing
            member {user_id} from
            collab {collab_id} in
            workspace: {workspace_id} ..."""
        )

        with session_m.get_session() as session:
            thread = thread_repo.get_by_collab_id(session, collab_id)
            if not thread:
                log.warning(
                    """[Membership] Cannot find group corresponding to
                        collab_id: {}""".format(
                        collab_id
                    )
                )
                return

            thread_id = thread.id
            participant_uscs.ban_participant.remove_member(
                actor_id, thread_id, user_id
            )

    def handle_role_assigned_events(msg):
        event, errors = make_input(RoleAssignedEvent, msg)
        if errors:
            log.warning(
                f"""[Membership] Invalid role_assigned event,
                    errors: {errors}, body: {msg}"""
            )
            return

        message = event.message

        if is_event_from_chat_service(message.data_source):
            return

        actor_id = message.caller_id
        user_id = message.user_id
        collab_id = message.collab_group_id
        workspace_id = message.workspace_id

        member_role = get_collab_role_from_membership_roles(message.roles)

        if not member_role or not collab_id:
            log.warning(
                f"""[Membership] Invalid member role  in role_assigned
                event: {event}"""
            )
            # not role assignment related to collab group
            return

        log.info(
            f"""[Membership] Assigning role {member_role} to
            user {user_id} in
            collab {collab_id} by
            actor {actor_id} in
            workspace {workspace_id} ..."""
        )

        with session_m.get_session() as session:
            thread = thread_repo.get_by_collab_id(session, collab_id)
            if not thread:
                log.warning(
                    "Cannot find group corresponding to collab_id: {}".format(
                        collab_id
                    )
                )
                return

            thread_id = thread.id
            participant_uscs.participant_action.update_role(
                actor_id, user_id, thread_id, member_role
            )

    def handle_role_removed_events(msg):
        event, errors = make_input(RoleRemovedEvent, msg)
        if errors:
            log.warning(
                f"""[Membership] Invalid role_removed event: {errors},
                body: {msg}"""
            )
            return

        message = event.message

        if is_event_from_chat_service(message.data_source):
            return

        # actor_id = message.caller_id
        # user_id = message.user_id
        collab_id = message.collab_group_id
        member_role = get_collab_role_from_membership_roles(message.roles)

        if not collab_id or not member_role:
            log.warning(
                f"""[Membership] Invalid collab or role in role_removed
                event: {errors},
                body: {msg}"""
            )
            return

    def handle_collab_created_events(msg):
        event, errors = make_input(CollabCreatedEvent, msg)
        if errors:
            log.warning("Invalid event: {}".format(errors))
            return
        message = event.message

        if is_event_from_chat_service(message.data_source):
            return

        log.info(
            "Collab group created: caller_id={}, collab: {}".format(
                message.caller_id, message.collab_group
            )
        )

    def handle_collab_updated_events(msg):
        event, errors = make_input(CollabUpdatedEvent, msg)
        if errors:
            log.warning("Invalid event: {}".format(errors))
            return

        log.info(f"Event: {event}")
        message = event.message

        if is_event_from_chat_service(message.data_source):
            return

        log.info(
            "Collab group updated: caller_id={}, collab: {}".format(
                message.caller_id, message.collab_group
            )
        )

    def handle_feature_enabled_events(msg):
        event, errors = make_input(FeatureEnabledEvent, msg)
        if errors:
            log.warning("Invalid event: {}".format(errors))
            return

        body = event.message
        if event.message.feature != CollabFeature.Chat.value:
            return

        # create chat with current its member list
        collab_id = body.collab_group_id
        caller_id = int(body.caller_id)
        workspace_id = int(body.workspace_id)

        log.info(
            f"""[Membership] Enable chat feature to
            collab {collab_id} by
            caller {caller_id} in
            workspace {workspace_id} ..."""
        )

        collab = collab_client.get_collab_group(
            caller_id, collab_id, body.workspace_id
        )
        members = collab_client.list_members(caller_id, collab_id)
        owner = next(
            (member for member in members if is_collab_owner(member)), None
        )
        if not owner:
            log.exception(
                f"[Membership] Cannot find owner of collab {collab_id}"
            )
            return

        owner_id = str(owner.user_id)
        # create chat associated with this collab
        part_ids = [
            str(member.user_id)
            for member in members
            if member.user_id != owner.user_id
        ]
        p_thread = thread_uscs.create_thread.create_group(
            part_ids,
            caller_id,
            name=collab.name,
            workspace_id=collab.workspace_id,
            collab_id=collab_id,
        )
        thread_id = p_thread.thread.id

        # set admin roles if needed
        # NOTE: possible bug here
        # what if these events are processed before adding member events
        # QUESTION: how to avoid this?
        admin_user_ids = [
            str(member.user_id)
            for member in members
            if get_collab_role_from_membership_roles(member.roles)
            == ADMIN_ROLE
        ]
        for user_id in admin_user_ids:
            participant_uscs.participant_action.update_role(
                owner_id, user_id, thread_id, ADMIN_ROLE
            )

    all_handlers: Dict[str, Callable[[Any], None]] = {
        EVENT_COLLAB_CREATED: handle_collab_created_events,
        EVENT_COLLAB_UPDATED: handle_collab_updated_events,
        EVENT_MEMBER_ADDED: handle_member_added_events,
        EVENT_MEMBER_REMOVED: handle_member_removed_events,
        EVENT_ROLE_ASSIGNED: handle_role_assigned_events,
        EVENT_ENABLE_FEATURE: handle_feature_enabled_events,
        JOIN_REQUEST_ACCEPTED: handle_join_request_accepted_events,
        EVENT_MEMBER_REQUEST: handle_create_request_join_events,
        # Don't need to handle role_removed events
        # when there is a role change,
        # two events are fired: role_retracted and role_assigned.
        # So we just need to process role_assigned events.
        # EVENT_ROLE_REMOVED: handle_role_removed_events,
    }

    def on_message(msg: Dict):
        global PENDING_MESSAGES

        event_type: Optional[str] = msg.get("routing_key")
        if not event_type:
            PENDING_MESSAGES -= 1
            return
        try:
            handler = all_handlers.get(event_type)
            if handler is not None:
                handler(msg)
            else:
                log.warning(f"Unknown event type: {event_type}")
        except Exception as e:
            # what happen if we failed to process event ?
            log.exception(
                f"[Membership] Failed to process event due to {e}",
                exc_info=False,
            )

        PENDING_MESSAGES -= 1

    log.info(
        "[Membership] Start to consume membership's messages from kafka ...."
    )

    return on_message


def is_event_from_chat_service(data_source: int):
    """Checks whether this event created from chat-service side."""
    return data_source == membership.DATA_SOURCE_CHAT_SERVICE


def is_collab_owner(member: Member):
    for role in member.roles:
        if role == CollabGroupRole.Owner:
            return True
    return False


def get_collab_role_from_membership_roles(
    roles: Union[List[int], List[CollabGroupRole], None]
):
    """Extract collab role from membership event"""
    if not roles:
        return None

    for role in roles:
        role_id = str(role) if isinstance(role, int) else str(role.value)
        if role_id in ROLE_MAP:
            return ROLE_MAP[role_id]

    return None
