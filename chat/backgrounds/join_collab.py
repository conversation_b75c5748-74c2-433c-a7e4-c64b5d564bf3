from logging import Logger

import structlog

from chat import constant
from chat.model import make_input
from chat.container.app import AppContainer
from chat.participants.model.task import TaskJoinCollab

logger = structlog.get_logger()
BATCH_SIZE = 50
NUM_THREADS = 2


def handle(log: Logger, container: AppContainer, body, task_id=None):
    try:
        usecase = container.thread().usecases().join_thread
        parsed, errors = make_input(TaskJoinCollab, body)
        if not parsed and errors:
            log.warning("Invalid job input: {}".format(errors))
            return

        log.info(
            "Running task {}, id: {}".format(
                constant.TASK_JOIN_COLLAB, task_id
            )
        )
        usecase.join_collab_bg(
            user_id=parsed.user_id,
            collab_id=parsed.collab_id,
            join_type=parsed.data_source,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
