from logging import Logger
from typing import Dict

from chat.messages.usecase import EditMessageUseCase


def handle(
    log: Logger, m_usecase: EditMessageUseCase, body_d: Dict, task_id=None
):
    try:
        m_usecase.update_bg(
            message=body_d["body"],
            user_id=body_d["user_id"],
            message_id=body_d["message_id"],
            thread_id=body_d["thread_id"],
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
