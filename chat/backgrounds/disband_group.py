from logging import Logger
from typing import Dict

from chat.threads.usecase import DisbandGroupUsecase


def handle(
    log: Logger, usecase: DisbandGroupUsecase, body_d: Dict, task_id=None
):
    try:
        usecase.disband_group_bg(body_d["user_id"], body_d["group_id"])
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
