from chat.participants.usecase.add_participant_usecase import (
    AddParticipantUseCase,
)


def handle(log, p_usecase: AddParticipantUseCase, body_d, task_id=None):
    try:
        thread_id = body_d["thread_id"]
        invite_ids = body_d["invite_ids"]
        log.info(
            f"Processing background task: thread {thread_id} ids {len(invite_ids)}" # noqa
        )
        p_usecase.add_members_to_collab_bg(
            body_d["user_id"],
            thread_id,
            invite_ids,
            body_d,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
