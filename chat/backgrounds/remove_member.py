from logging import Logger
from typing import Dict

from chat.model import make_input
from chat.participants.model.task import TaskRemoveParticipant
from chat.participants.usecase import BanParticipantUseCase


def handle(
    log: Logger, t_usecase: BanParticipantUseCase, body_d: Dict, task_id=None
):
    try:
        task, errors = make_input(TaskRemoveParticipant, body_d)
        if errors:
            log.warning(
                "Worker error: {}, task_id: {}, invalid data: {}".format(
                    errors, task_id, body_d
                )
            )
            return
        t_usecase.remove_member(
            thread_id=task.thread_id,
            user_id=task.user_id,
            will_remove_id=task.to_remove_id,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
