from logging import Logger
from typing import Dict

from chat.model import make_input
from chat.threads.model.task import Task<PERSON>eaveGroup
from chat.threads.usecase import LeaveThreadUseCase


def handle(
    log: Logger, t_usecase: LeaveThreadUseCase, body_d: Dict, task_id=None
):
    try:
        task, errors = make_input(TaskLeaveGroup, body_d)
        if errors:
            log.warning(
                "Worker error: {}, task_id: {}, invalid data: {}".format(
                    errors, task_id, body_d
                )
            )
            return
        t_usecase.leave_group(
            thread_id=task.thread_id,
            user_id=task.user_id,
            send_action_note=task.send_action_note,
            clear_msg=task.clear_msg,
            promote_new_owner_if_needed=task.promote_new_owner_if_needed,
            deactivate_only=task.deactivate_only,
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
