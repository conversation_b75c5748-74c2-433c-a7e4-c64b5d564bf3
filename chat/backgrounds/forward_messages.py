from logging import Logger
from typing import Dict

from chat.messages.usecase import Forward<PERSON>essageUseCase


def handle(
    log: Logger, m_usecase: ForwardMessageUseCase, body_d: Dict, task_id=None
):
    try:
        for source_message_id in body_d["source_message_ids"]:
            m_usecase.forward(
                user_id=body_d["user_id"],
                source_thread_id=body_d["source_thread_id"],
                source_message_id=source_message_id,
                thread_id=body_d["thread_id"],
                api_version=body_d["api_version"],
                partner_id=body_d.get("partner_id", None),
            )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
