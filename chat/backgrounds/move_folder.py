from chat.folders.usecase.folder_usecase import FolderUsecase


def handle(log, folder_usecase: FolderUsecase, body_d, task_id=None):
    try:
        folder_usecase.move_to_folder_bg(
            user_id=body_d["user_id"],
            remove_folder=body_d.get("remove_folder", None),
            new_folder=body_d["new_folder"],
            thread_ids=body_d.get("thread_ids", None),
        )
    except Exception as error:
        log.error(
            "Worker error: {}, task_id: {}".format(error, task_id),
            exc_info=True,
        )
