from time import time
from typing import Sequence

import structlog
from falcon import Request, Response

from .model import DefaultInstrumentation, Info, Instrumentation

__all__ = ["PrometheusMiddleware", "DEFAULT_INSTRUMENTATIONS"]

DEFAULT_INSTRUMENTATIONS = [DefaultInstrumentation("http")]


class PrometheusMiddleware(object):
    def __init__(
        self,
        instrumentations: Sequence[Instrumentation] = DEFAULT_INSTRUMENTATIONS,
    ):
        self.instrumentations = instrumentations
        self.logger = structlog.get_logger()

    def _get_status_string(self, status):
        if isinstance(status, int):
            return str(status)
        return status.split()[0]

    def process_request(self, req: Request, resp: Response):
        req.context.start_time = time()

    def get_client_ip(self, req: Request):
        # access_route store all client IPs
        # the real IP is the first ip
        client_ips = req.access_route
        return (
            client_ips[0]
            if (isinstance(client_ips, list) and len(client_ips) > 0)
            else None
        )

    def get_log_params(self, req: Request, info: Info):
        params = {}
        state = req.context

        request_id = state.get("request_id", None)
        if request_id is not None:
            params["request_id"] = request_id

        user_id = state.get("user_id", None)
        if user_id is not None:
            params["user_id"] = user_id

        # log thread_id
        thread_id = state.get("thread_id", None) or req.get_param("thread_id")
        if thread_id is not None:
            params["thread_id"] = thread_id

        auth_type = state.get("auth_type", None)
        if auth_type:
            params["auth_type"] = auth_type

        bot_id = state.get("bot_id", None)
        if bot_id is not None:
            params["bot_id"] = bot_id

        workspace_id = state.get("workspace_id", None)
        if workspace_id is not None:
            params["workspace_id"] = workspace_id

        user_agent = req.get_header("user-agent")
        if user_agent:
            params["ua"] = user_agent

        params["duration"] = round(info.duration * 1000, 1)

        error_code = state.get("error_code", None)
        if error_code:
            params["error_code"] = error_code

        params["ip"] = self.get_client_ip(req)

        return params

    def process_response(
        self, req: Request, resp: Response, resource, req_succeeded
    ):
        process_time = time() - req.context.start_time
        # TODO: what if resource=None. Should we mask all these kind of urls?

        url = req.uri_template
        if url is None:
            # not found uri
            # uri = req.path
            url = "/notfound"

        info = Info(
            req,
            resp,
            method=req.method,
            url=url,
            status=self._get_status_string(resp.status),
            duration=process_time,
        )
        # log duration
        req.context.duration = process_time

        for instrumentation in self.instrumentations:
            try:
                instrumentation.instrument(info)
            except Exception:
                pass

        status_int = int(info.status)

        log_params = self.get_log_params(req, info)

        self.logger.info(
            f"{info.status} - {req.method} {req.relative_uri} in {process_time * 1000:0.1f}ms",  # noqa
            status=status_int,
            **log_params,
        )
