"""
This module contains ready-to-use functions that can be passed on to the
instrumentator instance with the `add()` method. The idea behind this is to
make the types of metrics you want to export with the instrumentation easily
customizable. The default instrumentation function `default` can also be found
here.

If your requirements are really specific or very extensive it makes sense to
create your own instrumentation function instead of combining several functions
from this module.
"""

from abc import ABC
from socket import gethostname
from typing import Optional
import structlog

import nanoid
from falcon import Request, Response
from prometheus_client import Counter, Histogram, Summary, Gauge


def get_hostname():
    try:
        # hostname + random id, in case we launch multiple instances per server
        # return gethostname() + "-" + nanoid.generate(size=5)
        return gethostname()
    except Exception as e:
        print("Exception", e)
        return "hostname-" + nanoid.generate()


HOSTNAME = get_hostname()
APP_NAMESPACE = "chat"


class Info:
    def __init__(
        self,
        request: Request,
        response: Optional[Response],
        method: str,
        url: str,
        status: str,
        duration: float,
    ):
        """Creates Info object that is used for instrumentation functions.

        This is the only argument that is passed to the instrumentation
        functions.

        Args:
            request (Request): Python Requests request object.
            response (Response or None): Python Requests response object.
            method (str): Unmodified method of the request.
            url (str): Handler representation after processing by
                instrumentator. For example grouped to `none` if not templated.
            status (int): Status code representation after processing
                by instrumentator. For example grouping into `2xx`, `3xx`
                    and so on.
            duration (float): Latency representation after processing
                by instrumentator. For example rounding of decimals (ms).
        """

        self.request = request
        self.response = response
        self.method = method
        self.url = url
        self.status = status
        self.duration = duration


class Instrumentation(ABC):
    def instrument(self, info: Info):
        """Update metrics"""


LATENCY_BUCKETS = (
    0.01,
    0.025,
    0.05,
    0.075,
    0.1,
    0.25,
    0.5,
    0.75,
    1,
    1.5,
    2,
    2.5,
    3,
    3.5,
    4,
    5,
    7.5,
    10,
)


class AppMetrics:
    """All metrics aggregated by this app."""

    def __init__(self):
        self._api_call_failures = Counter(
            name="api_call_failures",
            documentation="How many fail internal API calls that has occured",
            labelnames=("host", "service"),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._api_call_interface_errors = Counter(
            name="api_call_interface_errors",
            documentation="How many API failures due to API interface changes",
            labelnames=("host",),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._amqp_call_errors = Counter(
            name="amqp_call_errors",
            documentation="How many AMQP failures have occured",
            labelnames=("host",),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._mqtt_call_errors = Counter(
            name="mqtt_call_errors",
            documentation="How many MQTT failures have occured",
            labelnames=("host",),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._sent_messages = Counter(
            name="sent_messages",
            documentation="How many messages sent",
            labelnames=("host",),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._sub_threads = Counter(
            name="sub_threads",
            documentation="How many sub-threads have been created",
            labelnames=("host",),
            # _labelvalues=[HOSTNAME],
            namespace=APP_NAMESPACE,
            subsystem="",
        )

        self._mysql_duration = Gauge(
            name="mysql_duration_seconds",
            documentation=("The mysql duration in seconds"),
            labelnames=("host",),
            namespace=APP_NAMESPACE,
            subsystem="",
        )

    @property
    def sub_threads(self):
        return self._sub_threads.labels(HOSTNAME)

    @property
    def api_call_failures(self):
        return self._api_call_failures

    @property
    def api_call_interface_errors(self):
        return self._api_call_interface_errors.labels(HOSTNAME)

    @property
    def mqtt_call_errors(self):
        return self._mqtt_call_errors.labels(HOSTNAME)

    @property
    def amqp_call_errors(self):
        return self._amqp_call_errors.labels(HOSTNAME)

    @property
    def sent_messages(self):
        return self._sent_messages.labels(HOSTNAME)

    @property
    def mysql_report(self):
        return self._mysql_duration.labels(HOSTNAME)


class DefaultInstrumentation(Instrumentation):
    """Contains multiple metrics to cover multiple things.

    Combines several metrics into a single function. Also more efficient than
    multiple separate instrumentation functions that do more or less the same.

    You get the following:

    * `http_requests_total`: Total number of
        requests by host, method, status, url.
    * `http_request_size_bytes` : Total number of incoming
        content length bytes by host, method, status, url.
    * `http_response_size_bytes` : Total number of outgoing
        content length bytes by host, method, status, url.
    * `http_request_duration_seconds`: High number of buckets
        leading to more accurate calculation of percentiles by
        host, method, status, url.

    Args:
        metric_namespace (str, optional): Namespace of all  metrics in this
            metric function. Defaults to "".

        metric_subsystem (str, optional): Subsystem of all  metrics in this
            metric function. Defaults to "".
    """

    def __init__(
        self,
        metric_namespace: str = APP_NAMESPACE,
        metric_subsystem: str = "",
    ):
        self.logger = structlog.get_logger()
        labels = ("host", "method", "status", "url")

        request_count = Counter(
            name="http_requests_total",
            documentation="""How many HTTP requests processed, partitioned by 
                status code and HTTP method.""",
            labelnames=labels,
            namespace=metric_namespace,
            subsystem=metric_subsystem,
        )

        request_size = Summary(
            name="http_request_size_bytes",
            documentation=(
                "Content length of incoming requests by handler. "
                "Only value of header is respected. Otherwise ignored. "
                "No percentile calculated. "
            ),
            labelnames=labels,
            namespace=metric_namespace,
            subsystem=metric_subsystem,
        )

        response_size = Summary(
            name="http_response_size_bytes",
            documentation=(
                "Content length of outgoing responses by handler. "
                "Only value of header is respected. Otherwise ignored. "
                "No percentile calculated. "
            ),
            labelnames=labels,
            namespace=metric_namespace,
            subsystem=metric_subsystem,
        )

        REQUEST_DURATION = Histogram(
            name="request_duration_seconds",
            documentation=("The HTTP request latencies in seconds. "),
            buckets=LATENCY_BUCKETS,
            labelnames=labels,
            namespace=metric_namespace,
            subsystem=metric_subsystem,
        )

        def instrumentation(info: Info) -> None:
            try:
                request_count.labels(
                    HOSTNAME, info.method, info.status, info.url
                ).inc()

                request_size.labels(
                    HOSTNAME, info.method, info.status, info.url
                ).observe(int(info.request.headers.get("Content-Length", 0)))

                if info.response and hasattr(info.response, "headers"):
                    response_size.labels(
                        HOSTNAME, info.method, info.status, info.url
                    ).observe(
                        int(info.response.headers.get("Content-Length", 0))
                    )
                else:
                    response_size.labels(
                        HOSTNAME, info.method, info.status, info.url
                    ).observe(0)

                REQUEST_DURATION.labels(
                    HOSTNAME, info.method, info.status, info.url
                ).observe(info.duration)
            except Exception as e:
                self.logger.error(e)

        self._handle = instrumentation

    def instrument(self, info: Info):
        self._handle(info)
