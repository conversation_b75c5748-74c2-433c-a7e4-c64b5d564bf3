import os

from falcon import Request, Response
from prometheus_client import (
    CONTENT_TYPE_LATEST,
    REGISTRY,
    CollectorRegistry,
    generate_latest,
    multiprocess,
)

from chat.app.resource import GuestResource

# handle multiprocess prometheus
# adapted from https://github.com/trallnag/prometheus-fastapi-instrumentator/blob/a9e68e3fcaa6d3f669fe5f73607c483a53b391d2/prometheus_fastapi_instrumentator/instrumentation.py#L190 # noqa
if "PROMETHEUS_MULTIPROC_DIR" in os.environ:
    pmd = os.environ["PROMETHEUS_MULTIPROC_DIR"]
    if os.path.isdir(pmd):
        registry = CollectorRegistry()
        multiprocess.MultiProcessCollector(registry)
    else:
        raise ValueError(
            f"Env var PROMETHEUS_MULTIPROC_DIR='{pmd}' not a directory."
        )
else:
    registry = REGISTRY


class MetricResource(GuestResource):
    def on_get(self, req: Request, resp: Response):
        data = generate_latest(registry)
        resp.content_type = CONTENT_TYPE_LATEST
        resp.body = str(data.decode("utf-8"))
        resp.status = 200
