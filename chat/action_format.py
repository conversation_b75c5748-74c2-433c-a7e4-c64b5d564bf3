from typing import Dict, Union

from typing_extensions import NotRequired, TypedDict

from chat import action_type


class NotifyConf(TypedDict):
    enable: bool
    format: NotRequired[str]


class ActionNote(TypedDict):
    code: int
    format: Union[str, Dict]
    notify: NotifyConf


ACTION_NOTES: Dict[str, ActionNote] = {
    action_type.GROUP_CREATE: {
        "code": 0,
        "format": "{maker} đã tạo nhóm",
        "notify": {"enable": True},
    },
    action_type.INVITE_MEM: {
        "code": 1,
        "format": "{maker} đã thêm {receiver} vào nhóm",
        "notify": {"enable": True, "format": "{maker} đã thêm bạn vào nhóm"},
    },
    action_type.KICK_MEM: {
        "code": 2,
        "format": "{maker} đã xóa {receiver} khỏi nhóm",
        "notify": {"enable": False},
    },
    action_type.JOIN_BY_LINK: {
        "code": 3,
        "format": "{maker} đã tham gia nhóm qua link",
        "notify": {"enable": False},
    },
    action_type.LEAVE_GROUP: {
        "code": 4,
        "format": "{maker} đã rời khỏi nhóm",
        "notify": {"enable": False},
    },
    action_type.UPDATE_GROUP: {
        "code": 5,
        "format": {
            "name": "{maker} đã đổi tên nhóm thành {dataChange}",
            "avatar": "{maker} đã đổi ảnh đại diện của nhóm",
        },
        "notify": {"enable": True},
    },
    action_type.PIN_MESSAGE: {
        "code": 6,
        "format": {"pinned_message_id": "{maker} đã ghim "},
        "notify": {"enable": True, "format": "{maker} đã ghim một tin nhắn"},
    },
    action_type.UNPIN_MESSAGE: {
        "code": 7,
        "format": {"unpinned_message_id": "{maker} đã bỏ ghim "},
        "notify": {
            "enable": True,
            "format": "{maker} đã bỏ ghim một tin nhắn",
        },
    },
    action_type.INVITE_ORG_BY_DEPARTMENT: {
        "code": 8,
        "format": "{maker} đã thêm thành viên {receiver} vào nhóm qua sơ đồ tổ chức",  # noqa
        "notify": {"enable": True, "format": "{maker} đã thêm bạn vào nhóm"},
    },
    action_type.INVITE_ORG_BY_TITLE: {
        "code": 9,
        "format": "{maker} đã thêm thành viên {receiver} vào nhóm qua sơ đồ tổ chức",  # noqa
        "notify": {"enable": True, "format": "{maker} đã thêm bạn vào nhóm"},
    },
    action_type.SET_AUTO_DELETE_MESSAGE: {
        "code": 10,
        "format": "{maker} đã bật tự động xóa tin nhắn trong {day_num} ngày",
        "notify": {"enable": False},
    },
    action_type.DISABLE_AUTO_DELETE_MESSAGE: {
        "code": 11,
        "format": "{maker} đã tắt tự động xóa tin nhắn",
        "notify": {"enable": False},
    },
    action_type.DISABLE_USER_SEND_MESSAGE: {
        "code": 12,
        "format": "{maker} đã cài đặt chỉ chủ nhóm/quản trị viên có thể gửi tin nhắn",  # noqa
        "notify": {"enable": False},
    },
    action_type.ENABLE_USER_SEND_MESSAGE: {
        "code": 13,
        "format": "{maker} đã cài đặt tất cả mọi người có thể gửi tin nhắn",
        "notify": {"enable": False},
    },
    action_type.INVITE_BY_FILE: {
        "code": 14,
        "format": "{maker} đã thêm thành viên {receiver} vào nhóm qua file excel",  # noqa
        "notify": {"enable": False},
    },
    action_type.INVITE_ANNOUNCE: {
        "code": 15,
        "format": "{maker} đã thêm thành viên{info}",  # noqa
        "notify": {"enable": False},
    },
    action_type.INVITE_BY_THREAD_CHAT: {
        "code": 16,
        "format": "{maker} đã thêm thành viên {receiver} vào nhóm qua nhóm chat",  # noqa
        "notify": {"enable": False},
    },
    action_type.JOIN_BY_DEPARTMENT: {
        "code": 17,
        "format": "{maker} đã tự động tham gia qua phòng ban",  # noqa
        "notify": {"enable": False},
    },
    action_type.JOIN_BY_GROUP: {
        "code": 18,
        "format": "{maker} đã tự động tham gia qua nhóm",  # noqa
        "notify": {"enable": False},
    },
    action_type.LEAVE_GROUP_BY_DEPARTMENT: {
        "code": 19,
        "format": "{maker} đã tự động rời nhóm qua phòng ban",  # noqa
        "notify": {"enable": False},
    },
    action_type.LEAVE_GROUP_BY_GROUP: {
        "code": 20,
        "format": "{maker} đã tự động rời nhóm qua nhóm",  # noqa
        "notify": {"enable": False},
    },
    action_type.INVITE_ANNOUNCE_APPROVED: {
        "code": 21,
        "format": "{maker} đã thêm thành viên{info} và đang chờ phê duyệt",  # noqa
        "notify": {"enable": False},
    },
    action_type.JOIN_BUT_IN_REVIEW: {
        "code": 22,
        "format": "{maker} đã thêm {receiver} vào nhóm và đang chờ phê duyệt",  # noqa
        "notify": {"enable": False},
    },
    action_type.JOIN_BY_APPROVED: {
        "code": 23,
        "format": "{maker} đã phê duyệt {receiver} vào nhóm",  # noqa
        "notify": {"enable": False},
    },
    action_type.JOIN_BY_OPENAPI: {
        "code": 24,
        "format": "{maker} tham gia nhóm qua opneapi",  # noqa
        "notify": {"enable": False},
    },
    action_type.LEAVE_GROUP_BY_OPENAPI: {
        "code": 24,
        "format": "{maker} đã tự động rời nhóm qua openapi",  # noqa
        "notify": {"enable": False},
    },
}