from logging import Logger

from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat import constant


class RedisCounter(object):
    def __init__(self, config: Settings, log: Logger, engine: RedisConnection):
        self.config = config.redis_pcc
        self.log = log
        self.engine = engine
        self.tcn = self.config.thread_counter_name  # THREAD_COUNTER_NAME
        self.tckf = self.config.thread_counter_key_format  # thread:{thread_id}
        self.back_pressure_key = "backpressure:thread:{thread_id}:counter"

    def inc_counter(self, thread_id):
        key = self.tckf.format(thread_id=thread_id)
        try:
            counter = self.engine.client.hincrby(self.tcn, key)
            return counter
        except Exception as e:
            self.log.error(e)
            return 0

    def dcr_counter(self, thread_id):
        key = self.tckf.format(thread_id=thread_id)
        try:
            counter = self.engine.client.hincrby(self.tcn, key, -1)
            return counter
        except Exception as e:
            self.log.error(e)
            return 0

    def set_counter(self, thread_id: int, counter):
        key = self.tckf.format(thread_id=thread_id)
        try:
            self.engine.client.hset(self.tcn, key, counter)
        except Exception as e:
            self.log.error(e)

    def set_backpressure(self, thread_id: int, amount: int) -> bool:
        key = self.back_pressure_key.format(thread_id=thread_id)
        counter = 0
        try:
            counter = self.engine.client.incrby(key, amount)
            self.engine.client.expire(key, 15)
        except Exception as e:
            self.log.error(e)
            return False
        return counter >= constant.MAX_INVITE_PER_JOB

    def get_counter(self, thread_id: int) -> int:
        key = self.tckf.format(thread_id=thread_id)
        try:
            counter = self.engine.client.hget(self.tcn, key)
            if not counter:
                return 0
            return int(counter)
        except Exception as e:
            self.log.error(e)
            return 0
