USER_TYPE = "user"
PAGE_TYPE = "page"
BOT_TYPE = "bot"
SYSTEM_TYPE = "system"
SAVE_MESSAGE_TYPE = "save_message"
BOT_SPECIAL_VARPART = {"1": "save_message"}


SERVICE_ROLE = "service"
GUEST_ROLE = "guest"
USER_ROLE = "user"
SYSTEM_ROLE = "system"

OWNER_ROLE = "owner"
ADMIN_ROLE = "admin"
MEMBER_ROLE = "member"
ALL_ROLES = (OWNER_ROLE, ADMIN_ROLE, MEMBER_ROLE)

# All permission that you can do
PERMISSION_DISBAND_GROUP = "DISBAND"
PERMISSION_TOGGLE_PRIVACY = "TOGGLE"
PERMISSION_TOGGLE_ONLY_ADMIN_CAN_ADD_MEMBER = "TOGGLE_ADD_MEM"
PERMISSION_TOGGLE_ONLY_ADMIN_CAN_UPDATE_INFO = "TOGGLE_UPDATE_INFO"
PERMISSION_TOGGLE_MUTE_ALL_NOTIS = "TOGGLE_MUTE_ALL"
PERMISSION_PIN_MESSAGE = "PIN_MESSAGE"
PERMISSION_TOGGLE_LINK = "TOGGLE_LINK"
PERMISSION_EDIT_GROUP = "EDIT_GROUP_INFO"
PERMISSION_LEAVE_GROUP = "LEAVE"
PERMISSION_ADD_MEMBER = "ADD"
PERMISSION_VIEW_BAN_LIST = "VIEW_BAN_LIST"
PERMISSION_REMOVE_MEMBER = "REMOVE"
PERMISSION_BAN_MEMBER = "BAN"
PERMISSION_UNBAN_MEMBER = "UNBAN"
PERMISSION_REVOKE_TO_MEMBER = "REVOKE_TO_MEMBER"
PERMISSION_AUTHORIZE_TO_ADMIN = "AUTHORIZE_TO_ADMIN"
PERMISSION_AUTHORIZE_TO_OWNER = "AUTHORIZE_TO_OWNER"
PERMISSION_DELETE_MESSAGE = "DELETE_MESSAGE"
PERMISSION_AUTO_DELETE_MESSAGE = "AUTO_DELETE_MESSAGE"
PERMISSION_TOGGLE_LINK = "TOGGLE_LINK"

# all action note type here
# an action note is a message that will sent to client
# so they can update their view
ACTION_NOTE_DISBAND_GROUP = "disband_group"


# task name
TASK_CREATE_MESSAGE = "create_message"
TASK_ADD_MEMBER = "add_mem"
TASK_CREATE_MEMBER = "create_mem"
TASK_FORWARD_MESSEAGE = "forward"
TASK_DISBAND_GROUP = "disband_group"
TASK_FORWARD_MULTIPLE_MESSAGES = "forwards"
TASK_DELETE_MESSAGE = "delete_message"
TASK_EDIT_MESSAGE = "message_edited"
TASK_MOVE_FOLDER = "move_folder"
TASK_SORT_FOLDER = "sort_folder"
TASK_LEAVE_GROUP = "leave_group"
TASK_DEACTIVATE_MEMBER_IN_GROUP = "deactivate_in_group"
TASK_REACTIVATE_MEMBER_IN_GROUP = "reactivate_in_group"
TASK_REMOVE_MEMBER = "remove_mem"
TASK_FIX_THREAD_SCORE = "fix_thread_score"
TASK_CREATE_MASS_DIRECT_MESSAGES = "create_mass_direct_message"
TASK_BAN_MEMBER = "ban_mem"
TASK_TOGGLE_NOTI = "toggle_noti"
TASK_JOIN_COLLAB = "join_collab"
TASK_ADD_MEMBER_COLLAB = "add_mem_collab"

# Events
EVENT_MESSAGE_DELETED = "delete_message"
EVENT_MESSAGE_DELETED_OLD = (
    "message_deleted"  # old event, that search service uses to update index
)
EVENT_USER_SEEN_THREAD = "read_at"
EVENT_THREAD_DELETED = "thread_deleted"


FACTOR_SCORE = 10000000000000

SYSTEM_ID = "system"
SAVE_MESSAGE_ID = "save_message"
SAVE_MESSAGE_VARPART = 1

PAGE_THREAD_TYPE = "page"
GROUP_THREAD_TYPE = "group"
SUB_THREAD_TYPE = "subthread"

SUPER_GROUP_THREAD_TYPE = "super_group"
DIRECT_THREAD_TYPE = "direct"
THREAD_TYPES = (
    PAGE_THREAD_TYPE,
    GROUP_THREAD_TYPE,
    DIRECT_THREAD_TYPE,
    SUPER_GROUP_THREAD_TYPE,
)


# All folders
FOLDER_NOT_FRIEND = "stranger"
"""Folder to store all threads from stranger goes here. """

FOLDER_DEFAULT = "default"
"""Folder that contains all threads of an user."""

FOLDER_SUB_THREAD = "subthread"
"""Folder that contains all sub-threads of an user."""

FOLDER_SECRET = "secret"
"""Secret folder, pass_code is required. """

FOLDER_PIN = "pin"
"""Just dummy value for tracking number of pinned items by user."""

FOLDER_UNREAD = "unread"
"""Folder that contains all unread threads. """

FOLDER_COUNT_ALL = (FOLDER_DEFAULT, FOLDER_NOT_FRIEND)

SYSTEM_OBJECT = {
    "name": "GapoSystem",
    "avatar": "https://cdn-thumb-image-1.gapo.vn/168x168/smart/759acdcb-8fdc-4d93-a158-db84f4b805c5.jpeg",  # noqa
    "status_verify": 1,
    "id": "system",
    "type": "system",
}

MAX_MEMBER = 10000
MAX_SUPER_MEMBER = 200000
LENGTH_FOLDER_ALIAS = 32  # folder created by user always length 32
IMAGE_UPLOAD_URL = "https://gapo-image.s3-ap-southeast-1.amazonaws.com/images"
ALIAS_IMAGE_UPLOAD_URL = "https://image-1.gapo.vn/images"
PREFIX_THUMB_URL = "https://cdn-thumb-image-1.gapo.vn"

IMAGE_UPLOAD_URL_V2 = "https://gapo-image.s3.storage.com.vn/images"
ALIAS_IMAGE_UPLOAD_URL_V2 = "https://image-2.gapo.vn/images"
PREFIX_THUMB_URL_V2 = "https://cdn-thumb-image-2.gapo.vn"


IMAGE_UPLOAD_URL_V3 = "https://gapo-image.s3south.storage.com.vn/images"
ALIAS_IMAGE_UPLOAD_URL_V3 = "https://image-3.gapo.vn/images"
PREFIX_THUMB_URL_V3 = "https://cdn-thumb-image-3.gapo.vn"

IMAGE_UPLOAD_URL_V5 = "https://gapo-image.statics.pancake.vn/images"
ALIAS_IMAGE_UPLOAD_URL_V5 = "https://image-5.gapo.vn/images"
PREFIX_THUMB_URL_V5 = "https://cdn-thumb-image-5.gapo.vn"

# PREFIX_THUMB_IMAGE = {
#     IMAGE_UPLOAD_URL: PREFIX_THUMB_URL,
#     ALIAS_IMAGE_UPLOAD_URL: PREFIX_THUMB_URL,
#     IMAGE_UPLOAD_URL_V2: PREFIX_THUMB_URL_V2,
#     ALIAS_IMAGE_UPLOAD_URL_V2: PREFIX_THUMB_URL_V2,
#     IMAGE_UPLOAD_URL_V3: PREFIX_THUMB_URL_V3,
#     ALIAS_IMAGE_UPLOAD_URL_V3: PREFIX_THUMB_URL_V3,
#     IMAGE_UPLOAD_URL_V5: PREFIX_THUMB_URL_V5,
#     ALIAS_IMAGE_UPLOAD_URL_V5: PREFIX_THUMB_URL_V5,
# }
THUMB_SIZE = "168x168"

MEDIA_MESSAGE_TYPES = (
    "image",
    "multi_image",
    "video",
    "sticker",
    "animated_sticker",
    "file",
    "voice",
)

# FILE_UPLOAD_URL_VINACIS = "https://gapo-files.s3.storage.com.vn/files/origin"
# FILE_CDN_URL_VINACIS = "https://files-2.gapo.vn/files/origin"

# FILE_UPLOAD_URL_AWS = "https://gapo-files.s3-ap-southeast-1.amazonaws.com/files/origin"
# FILE_CDN_URL_AWS = "https://files-1.gapo.vn/files/origin"

# FILE_UPLOAD_URL_V3 = "https://gapo-files.s3south.storage.com.vn/files/origin"
# FILE_CDN_URL_V3 = "https://files-3.gapo.vn/files/origin"

# FILE_UPLOAD_URL_V5 = "https://gapo-files.statics.pancake.vn/files/origin"
# FILE_CDN_URL_V5 = "https://files-5.gapo.vn/files/origin"

# PREFIX_IMAGE_URLS = (
#     IMAGE_UPLOAD_URL,
#     IMAGE_UPLOAD_URL_V2,
#     IMAGE_UPLOAD_URL_V3,
#     IMAGE_UPLOAD_URL_V5,
#     ALIAS_IMAGE_UPLOAD_URL,
#     ALIAS_IMAGE_UPLOAD_URL_V2,
#     ALIAS_IMAGE_UPLOAD_URL_V3,
#     ALIAS_IMAGE_UPLOAD_URL_V5,
# )

# PREFIX_FILE_URLS = {
#     FILE_UPLOAD_URL_VINACIS,
#     FILE_CDN_URL_VINACIS,
#     FILE_UPLOAD_URL_AWS,
#     FILE_CDN_URL_AWS,
#     FILE_UPLOAD_URL_V3,
#     FILE_CDN_URL_V3,
#     FILE_UPLOAD_URL_V5,
#     FILE_CDN_URL_V5,
# }

# PREFIX_FILE_CDN_URLS = {
#     FILE_UPLOAD_URL_VINACIS: FILE_CDN_URL_VINACIS,
#     FILE_UPLOAD_URL_AWS: FILE_CDN_URL_AWS,
#     FILE_UPLOAD_URL_V3: FILE_CDN_URL_V3,
#     FILE_UPLOAD_URL_V5: FILE_CDN_URL_V5,
# }
# PREFIX_IMAGE_CDN_URLS = {
#     IMAGE_UPLOAD_URL: ALIAS_IMAGE_UPLOAD_URL,
#     IMAGE_UPLOAD_URL_V2: ALIAS_IMAGE_UPLOAD_URL_V2,
#     IMAGE_UPLOAD_URL_V3: ALIAS_IMAGE_UPLOAD_URL_V3,
#     IMAGE_UPLOAD_URL_V5: ALIAS_IMAGE_UPLOAD_URL_V5,
# }


DELETE_MSG_TWO_SIDE = 2
DELETE_MSG_ONE_SIDE = 1


# thread creation source
SOURCE_CONTACT = "contact"
SOURCE_NORMAL = "normal"
SOURCE_ACCEPT_FRIEND = "accept_friend"


# Group level config
GROUP_LEVEL_PRIVATE = 0
GROUP_LEVEL_PUBLIC = 1


# ORGC group type
ORGC_GROUP_BY_DEPARTMENT = "department"
ORGC_GROUP_BY_ROLE = "role"


# Thread events
THREAD_EVENT_THREAD_CREATED = "thread_created"


# reaction constants
HAS_REACT = -1
NO_REACT = (None, 0)


# AMQP messagee priority constants
# docs: https://www.rabbitmq.com/priority.html
# These constants are introduced to
# prioritize messages in AMQP queue
AMQP_PRIORITY_HIGH = 2
AMQP_PRIORITY_MEDIUM = 1
AMQP_PRIORITY_LOW = 0

GAPO_BOT_SYSTEM_IDS = ["5822892804281558525", "5822866018669849029"]

# user status constants
USER_STATUS_DEACTIVATED = 2

SAVE_MESSAGE_BOT_ID = "5802302828844154881"


# usecase call sources
CALL_SOURCE_API = "api"


# special constants
DROPPI_WORKSPACE_ID = "***************"

MAX_CREATE_THREAD_FOR_USER = 1000

MAX_INVITE_PER_TASK = 5000
MAX_INVITE_PER_JOB = 1000

TASK_ROUTING_KEY = "background_task"
TASK_HEAVY_ROUTING_KEY = "background_heavy_task"
STATUS_ROUTING_KEY = "status"

DELETE_EX = "message_delete_exchange"
DELETE_RK = "message_delete_0_day_key_test"  # de nham rk la test
