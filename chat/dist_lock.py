import redis_lock

from chat.connections.redis_c import RedisConnection
from chat.exception import LockTimeoutError

LOCK_DURATION_SECONDS = 5  # 5s
LOCK_KEY_PREFIX = "__lock_chat_"


class Lock:
    def __init__(
        self, lock: redis_lock.Lock, timeout=LOCK_DURATION_SECONDS
    ) -> None:
        self._lock = lock
        self._timeout = timeout

    def acquire(self):
        return self._lock.acquire(timeout=self._timeout)

    def release(self):
        return self._lock.release()

    def __enter__(self):
        acquired = self.acquire()
        if not acquired:
            raise LockTimeoutError()
        return self

    def __exit__(self, exc_type=None, exc_value=None, traceback=None):
        self.release()


class DistLockManager:
    """Distributed lock based on redlock.
    https://redis.io/docs/reference/patterns/distributed-locks/

    NOTE that redlock isn't safe for critical cases because of
    its flaw. See more:
    https://martin.kleppmann.com/2016/02/08/how-to-do-distributed-locking.html
    """

    def __init__(self, redis_c: RedisConnection) -> None:
        self.redis_c = redis_c

    def get_lock(self, lock_key: str, timeout: int = LOCK_DURATION_SECONDS):
        """Gets distributed lock.

        Usage:
        try:
            with lock_manager.get_lock(lock_key):
                do_whatever()
        except LockTimeoutError as e:
            # cannot acquire lock

        """
        lock_key = LOCK_KEY_PREFIX + lock_key
        return Lock(
            redis_lock.Lock(
                self.redis_c.client,
                lock_key,
                expire=timeout,
                auto_renewal=True,
            ),
            timeout,
        )
