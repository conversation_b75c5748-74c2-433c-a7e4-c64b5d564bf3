from logging import Logger
from typing import Callable, Dict

from confluent_kafka import Consumer, KafkaError, KafkaException

from chat.config import Settings

MIN_COMMIT_COUNT = 10


class KafkaMembershipConsumer:
    def __init__(self, log: Logger, conf: Settings) -> None:
        self.cfg = conf.kafka
        self.log = log
        self.topic = self.cfg.membership_topic

        self._running = False

    def start(self, on_message: Callable[[Dict], None]):
        try:
            consumer_conf = {
                "bootstrap.servers": self.cfg.bootstrap_servers,
                # set group id, so we won't consume message twice
                "group.id": self.cfg.membership_consumer_group,
                "auto.offset.reset": "smallest",
                "enable.auto.commit": True,  # auto commit
            }
            if self.cfg.password:
                consumer_conf["sasl.password"] = self.cfg.password
                consumer_conf["sasl.username"] = self.cfg.username
                consumer_conf["sasl.mechanisms"] = self.cfg.mechanisms
                consumer_conf["security.protocol"] = "SASL_PLAINTEXT"

            self.consumer = Consumer(consumer_conf)
            self.log.info("Subscring to topic {}".format(self.topic))
            self.consumer.subscribe([self.topic])

            msg_count = 0
            self._running = True
            while self._running:
                # self.log.info("Polling message ...")
                msg = self.consumer.poll(timeout=1.0)
                if msg is None:
                    continue

                if msg.error():
                    if msg.error().code() == KafkaError._PARTITION_EOF:
                        # End of partition event
                        self.log.warning(
                            "%% %s [%d] reached end at offset %d\n"
                            % (msg.topic(), msg.partition(), msg.offset())
                        )
                    elif msg.error():
                        raise KafkaException(msg.error())
                else:
                    on_message(msg)
                    msg_count += 1

                    # auto commit after every MIN_COMMIT_COUNT messages
                    if msg_count % MIN_COMMIT_COUNT == 0:
                        self.consumer.commit(asynchronous=True)
        finally:
            # Close down consumer to commit final offsets.
            self.consumer.close()

    def stop(self):
        self._running = False
