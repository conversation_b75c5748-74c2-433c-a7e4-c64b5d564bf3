from __future__ import unicode_literals

import random
import functools
import logging

from urllib import parse
from logging import Logger
from typing import Dict, List, Any

import pika
from pika.channel import Channel
from pika.connection import Connection

# from Queue import Empty
import pika.exceptions

from chat.config import MessageQueueInfo
from chat.utils.common import ticktock

logging.getLogger("pika").setLevel(logging.DEBUG)


class RabbitMQConnectionV2(object):
    def __init__(
        self,
        name: str,
        urls: str,
        event_route: Dict[str, MessageQueueInfo],
        log: Logger,
    ):
        self.log = log
        self.urls = urls.split(" ")
        self._name = name
        # self.old_queue_name = queue_config["NAME"]
        # """Old queue without prioritity. Will be deprecated soon!."""

        # self.queue_name = self.old_queue_name + "_new"
        # self.exchange_routings = queue_config["EXCHANGE_ROUTINGS"].split(",")
        self._prepare_argument()
        self.init_queue = False
        self._event_route = event_route

        self._was_consuming: Dict[str, bool] = {}
        self._consumer_tags: List[str] = []
        self._consuming: Dict[str, bool] = {}
        self._closing = False
        self._connection: Connection = None
        self._channel: Channel = None
        self._prefetch_count = 3

        self.queues: List[MessageQueueInfo] = []

        for _, queue in self._event_route.items():
            self.queues.append(queue)

        self.handlers: Dict[str, Any] = {}

    def _prepare_argument(self):
        all_endpoints = []
        for e in self.urls:
            new_e = parse.urlparse(e)
            options = parse.urlencode(
                {"heartbeat": 6000, "blocked_connection_timeout": 6000}
            )
            new_e = new_e._replace(query=options)

            all_endpoints.append(pika.URLParameters(new_e.geturl()))
            print(new_e.geturl())
        self.parameters = all_endpoints

    def connect(self):
        # Shuffle the hosts list before reconnecting.
        # This can help balance connections.
        random.shuffle(self.parameters)

        tick = ticktock()

        self.log.info("RABBITMQ: connecting")

        self._connection = pika.SelectConnection(
            parameters=self.parameters[0],
            on_open_callback=self.on_connection_open,
            on_open_error_callback=self.on_connection_open_error,
        )
        self.log.info(f"Connected rabbitmq {self._name} took {tick()}")

        return self._connection

    def close(self):
        self.close_connection()

    def close_connection(self):
        if not self._connection:
            return

        for k in self._consuming.keys():
            self._consuming[k] = False
        if self._connection.is_closing or self._connection.is_closed:
            self.log.info("RABBITMQ: Connection is closing or already closed")
        else:
            self.log.info("RABBITMQ: Closing connection")
            self._connection.close()

    def on_connection_open(self, _unused_connection: Connection):
        self.log.info("RABBITMQ: Connection opened")
        self.open_channel()

    def on_connection_open_error(
        self, _unused_connection: Connection, err: str
    ):
        self.log.error("RABBITMQ: connection open failed: %s", err)
        self.reconnect()

    def on_connection_closed(
        self, _unused_connection: Connection, reason: str
    ):
        self._channel = None
        if self._closing:
            self._connection.ioloop.stop()
        else:
            self.log.warning(
                "RABBITMQ: Connection closed, reconnect necessary: %s", reason
            )
            self.reconnect()

    def reconnect(self):
        self.should_reconnect = True
        self.stop()

    def open_channel(self):
        self.log.info("RABBITMQ: creating a new channel")
        self._connection.channel(on_open_callback=self.on_channel_open)

    def on_channel_open(self, channel: Channel):
        self._channel = channel
        self.add_on_channel_close_callback()
        self.log.info("RABBITMQ: created channel")

        for queue in self.queues:
            self.setup_exchange(queue)

    def add_on_channel_close_callback(self):
        self.log.info("RABBITMQ: adding channel close callback")
        self._channel.add_on_close_callback(self.on_channel_closed)

    def on_channel_closed(self, channel: Channel, reason: str):
        self.log.info("RABBITMQ: channel %i was closed: %s", channel, reason)
        self.close_connection()

    def setup_exchange(self, queue: MessageQueueInfo):
        self.log.info("RABBITMQ: declaring exchange: %s", queue.exchange_name)
        cb = functools.partial(self.on_exchange_declareok, userdata=queue)

        self._channel.exchange_declare(
            exchange=queue.exchange_name,
            exchange_type=queue.exchange_type,
            durable=True,
            callback=cb,
        )

    def on_exchange_declareok(
        self, _unused_frame: str, userdata: MessageQueueInfo
    ):
        queue = userdata
        self.log.info("RABBITMQ: exchange declared: %s", queue.exchange_name)
        self.setup_queue(queue)

    def setup_queue(self, queue: MessageQueueInfo):
        self.log.info("RABBITMQ: declaring queue %s", queue.queue_name)
        cb = functools.partial(self.on_queue_declareok, userdata=queue)
        self._channel.queue_declare(
            queue=queue.queue_name,
            durable=True,
            exclusive=False,
            auto_delete=False,
            callback=cb,
        )

    def on_queue_declareok(
        self, _unused_frame: str, userdata: MessageQueueInfo
    ):
        queue = userdata
        self.log.info(
            "RABBITMQ: binding q %s to ex %s with rk %s",
            queue.queue_name,
            queue.exchange_name,
            queue.routing_key,
        )
        cb = functools.partial(self.on_bindok, userdata=userdata)
        self._channel.queue_bind(
            queue.queue_name,
            queue.exchange_name,
            routing_key=queue.routing_key,
            callback=cb,
        )

    def on_bindok(self, _unused_frame: str, userdata: MessageQueueInfo):
        queue = userdata
        self.log.info(
            "RABBITMQ: queue %s bound %s",
            queue.queue_name,
            queue.exchange_name,
        )
        self.set_qos()

    def set_qos(self):
        self._channel.basic_qos(
            prefetch_count=self._prefetch_count,
            callback=self.on_basic_qos_ok,
        )

    def on_basic_qos_ok(self, _unused_frame: str):
        self.log.info("RABBITMQ: QOS set to %d", self._prefetch_count)
        self.start_consuming()

    def start_consuming(self):
        self.log.info("RABBITMQ: start consuming")

        self.add_on_cancel_callback()
        if not self.handlers:
            raise Exception("consumer handlers is None")

        for queue_name, handler in self.handlers.items():
            _consumer_tag = self._channel.basic_consume(queue_name, handler)
            self._consumer_tags.append(_consumer_tag)
            self._consuming[_consumer_tag] = True
            self._was_consuming[_consumer_tag] = True

    def add_on_cancel_callback(self):
        self.log.info("RABBITMQ: adding consumer cancellation callback")
        self._channel.add_on_cancel_callback(self.on_consumer_cancelled)

    def on_consumer_cancelled(self, method_frame: str):
        self.log.info(
            "RABBITMQ: consumer was cancelled remotely, shutting down %r",
            method_frame,
        )

        if self._channel:
            self.close_channel()

    def stop_consuming(self):
        if self._channel:
            self.log.info("RABBITMQ: sending a Basic.Cancel to RabbitMQ")
            for consumer_tag in self._consumer_tags:
                cb = functools.partial(self.on_cancelok, userdata=consumer_tag)
                self._channel.basic_cancel(consumer_tag, cb)
            self.close_channel()

    def on_cancelok(self, _unused_frame, userdata):
        _consumer_tag = userdata
        self._consuming[_consumer_tag] = False
        self.log.info(
            "RABBITMQ: acknowledged the cancellation of the consumer %s",
            _consumer_tag,
        )

    def close_channel(self):
        self.log.info("RABBITMQ: closing the channel")
        self._channel.close()

    def run(self):
        self._connection = self.connect()
        self._connection.add_on_close_callback(self.on_connection_closed)
        self._connection.ioloop.start()

    def stop(self):
        """Cleanly shutdown the connection to RabbitMQ by stopping the consumer
        with RabbitMQ. When RabbitMQ confirms the cancellation, on_cancelok
        will be invoked by pika, which will then closing the channel and
        connection. The IOLoop is started again because this method is invoked
        when CTRL-C is pressed raising a KeyboardInterrupt exception. This
        exception stops the IOLoop which needs to be running for pika to
        communicate with RabbitMQ. All of the commands issued prior to starting
        the IOLoop will be buffered but not processed.
        """

        if not self._closing:
            self._closing = True
            self.log.info("RABBITMQ: Stopping")
            for v in self._consuming.values():
                if v:
                    self.stop_consuming()
                    # self._connection.ioloop.start()
                    break
            self._connection.ioloop.stop()
            self.log.info("RABBITMQ: Stopped")
            self.should_reconnect = False


class RabbitMQConnection(object):
    def __init__(
        self,
        name: str,
        urls: str,
        event_route: Dict[str, MessageQueueInfo],
        log: Logger,
    ):
        self._name = name
        self._urls = urls.split(" ")
        self._event_route = event_route
        self.log = log
        self.prepare_argument()
        self.queues: List[MessageQueueInfo] = []

        for _, queue in self._event_route.items():
            self.queues.append(queue)

    def prepare_argument(self):
        all_endpoints = []
        for e in self._urls:
            all_endpoints.append(pika.URLParameters(e))
        self.parameters = all_endpoints

    def connect(self):
        tick = ticktock()
        self.log.info(f"Connecting rabbitmq {self._name}")

        con = pika.BlockingConnection(parameters=self.parameters)
        self.log.info(f"Connected rabbitmq {self._name} took {tick()}")
        return con

    def prepare_channel(self):
        while True:
            try:
                connection = self.connect()
                channel = connection.channel()
                break
            except Exception:
                connection = self.connect()
                channel = connection.channel()
                break

        for queue in self.queues:
            channel.queue_declare(
                queue=queue.queue_name,
                durable=True,
                exclusive=False,
                auto_delete=False,
            )
            channel.exchange_declare(
                queue.exchange_name,
                exchange_type=queue.exchange_type,
                durable=True,
            )

            channel.queue_bind(
                queue.queue_name,
                queue.exchange_name,
                routing_key=queue.routing_key,
            )
        channel.close()
        connection.close()

    def close(self):
        # TODO: how to close connection
        pass

    def __getattr__(self, name: str):
        """
        for example:

        >>> client = Amqpclient()
        >>> channel = client.channnel()
        """

        def wrapped(*args, **kwargs):
            for _ in range(3):
                # pylint: disable=access-member-before-definition
                method = getattr(self._connection, name)  # type: ignore
                try:
                    return method(*args, **kwargs)
                except pika.exceptions.AMQPConnectionError:
                    self._connection = pika.BlockingConnection(
                        self._parameters
                    )

        return wrapped
