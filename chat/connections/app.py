from logging import Logger
from typing import Any, List

from chat.config import Settings
from chat.publishers.rabbitmq import AmqpProducer

from ..publishers import <PERSON>qp<PERSON><PERSON>, MqttPublisher
from .cassandra_c import CassandraConnection
from .gapo_client import GapoClient
from .mqtt_c import MQTTConnection
from .mysql_c import MySQLConnection
from .rabbitmq_c import RabbitMQConnection
from .redis_c import RedisConnection

from chat.metrics.model import AppMetrics


class AppConnection:
    """A class that holds all connections if app."""

    def __init__(self, config: Settings, log: Logger, metrics: "AppMetrics"):
        self.metrics = metrics
        self.config = config
        self.log = log
        self.connections: List[Any] = []

        self.init_gapo_client()
        self.init_cassandra_conn()
        self.init_mysql_conn(self.metrics)
        self.init_redis_conn()
        self.init_redis_cache_conn()
        self.init_mqtt_conn()
        self.init_rabbit_conn()

    def init_gapo_client(self):
        self.gapo_client = GapoClient(self.config, self.log, self.metrics)

    def init_cassandra_conn(self):
        cas_cfg = self.config.cassandra
        self.cas_c = CassandraConnection(
            "Chat DB",
            cas_cfg.host,
            cas_cfg.username,
            cas_cfg.password,
            cas_cfg.keyspace,
            self.log,
        )

        self.connections.append(self.cas_c)

    def init_mysql_conn(self, metric: AppMetrics):
        mysql_cfg = self.config.mysql
        self.mysql_session = MySQLConnection(
            "Mysql Chat DB", mysql_cfg.url, metric, self.log
        )

        self.connections.append(self.mysql_session)

    def init_redis_conn(self):
        r_cfg = self.config.redis_pcc
        # redis connection to redis client
        self.redis_client = RedisConnection(
            "Redis Persistent DB",
            r_cfg.host,
            r_cfg.port,
            r_cfg.db,
            r_cfg.password,
            self.log,
        )

        self.connections.append(self.redis_client)

    def init_redis_cache_conn(self):
        r_cache_cfg = self.config.redis_cache
        self.redis_cache_c = RedisConnection(
            "Redis Cache DB",
            r_cache_cfg.host,
            r_cache_cfg.port,
            r_cache_cfg.db,
            r_cache_cfg.password,
            self.log,
        )

        self.connections.append(self.redis_cache_c)

    def init_mqtt_conn(self):
        mqtt_cfg = self.config.mqtt
        self.mqtt_c = MQTTConnection(
            "Mqtt publisher",
            mqtt_cfg.host,
            int(mqtt_cfg.port),
            mqtt_cfg.username,
            mqtt_cfg.password,
            self.log,
        )

        self.connections.append(self.mqtt_c)
        self.mqtt_pub = MqttPublisher(
            self.mqtt_c.client,
            mqtt_cfg.prefix_version,
            mqtt_cfg.postfix_status_channel,
            self.log,
        )

    def init_rabbit_conn(self):
        # FIXME
        rb_cfg = self.config.rabbitmq
        self.rb_c = RabbitMQConnection(
            "Chat Publisher Connection",
            rb_cfg.urls,
            rb_cfg.event_route,
            self.log,
        )
        self.rb_producer = AmqpProducer(
            "Api-producer",
            AmqpClient(self.rb_c, self.log),
            self.log,
            self.metrics,
        )

        self.connections.append(self.rb_producer)
