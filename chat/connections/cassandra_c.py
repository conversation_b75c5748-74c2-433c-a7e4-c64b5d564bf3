from typing import Optional
from logging import Logger

from cassandra.auth import PlainTextAuthProvider
from cassandra.cluster import (
    EXEC_PROFILE_DEFAULT,
    Cluster,
    ExecutionProfile,
    NoHostAvailable,
    Session,
)
from cassandra.query import dict_factory

from chat.utils.common import ticktock


class Policy(object):
    def __init__(self, id, body, user_id):
        self.id = id
        self.body = body
        self.user_id = user_id


class CassandraConnectionMock(object):
    def __init__(self, *args):
        self.connect()

    def connect(self):
        self.session = True

    def close(self):
        self.session = False


class CassandraConnection(object):
    def __init__(
        self,
        name: str,
        host: str,
        username: str,
        password: str,
        keyspace: str,
        log: Logger,
    ):
        self._name = name
        self._hosts = host.split(",")
        self._username = username
        self._password = password
        self._keyspace = keyspace
        self.log = log
        self.session: Optional[Session] = None
        # self.connect()

    def connect(self) -> Session:
        tick = ticktock()
        self.log.info(
            f"Connecting cassandra {self._name} keyspace {self._keyspace}"
        )
        ap = None
        if self._username:
            ap = PlainTextAuthProvider(self._username, self._password)

        # TODO: Speculative execution can improve query latency by execute
        # extra queries to different node
        # We may want to enable this to check performance impact
        # However, execute extra query in update/insert query MAYNOT be what
        # you expect
        # links: https://docs.datastax.com/en/devapp/doc/devapp/driversSpeculativeRetry.html # noqa
        # https://docs.datastax.com/en/developer/python-driver/3.23/getting_started/#speculative-execution # noqa
        #
        # speculative_policy = ConstantSpeculativeExecutionPolicy(
        #   delay=.5,
        #   max_attempts=10
        # )

        profile = ExecutionProfile(
            request_timeout=5,
            row_factory=dict_factory,
            # speculative_execution_policy=speculative_policy,
            # don't use roundrobin policy, it isn't the best way to query data
            # in cassandra
            # default balancing policy is
            # TokenAwarePolicy(DCAwareRoundRobinPolicy())
            # load_balancing_policy=WhiteListRoundRobinPolicy(self._hosts),
        )
        if ap:
            cluster = Cluster(
                self._hosts,
                auth_provider=ap,
                execution_profiles={EXEC_PROFILE_DEFAULT: profile},
                protocol_version=5,
            )
        else:
            cluster = Cluster(
                self._hosts,
                execution_profiles={EXEC_PROFILE_DEFAULT: profile},
                protocol_version=5,
            )
        try:
            session = cluster.connect(self._keyspace)
        except NoHostAvailable:
            session = cluster.connect(self._keyspace)
        cluster.register_user_type(self._keyspace, "policies", Policy)
        # session.row_factory = dict_factory
        session.execute("SELECT uuid() FROM system.local")
        self.log.info(
            f"Connected cassandra {self._name} keyspace {self._keyspace} took {tick()}"  # noqa
        )

        return session

    def execute(self, *query):
        if self.session is None:
            self.session = self.connect()
        return self.session.execute(*query)

    def close(self):
        self.log.info(
            f"Closing cassandra {self._name} keyspace {self._keyspace}"
        )
        if self.session is not None:
            self.session.shutdown()
        self.log.info(
            f"Closed cassandra {self._name} keyspace {self._keyspace}"
        )
