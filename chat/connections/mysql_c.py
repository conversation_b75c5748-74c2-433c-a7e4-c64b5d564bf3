import time
from contextlib import contextmanager
from logging import Logger

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, scoped_session, sessionmaker

from chat.utils.common import ticktock
from chat.metrics.model import AppMetrics


class MySQLConnection(object):
    def __init__(self, name: str, url: str, metrics: AppMetrics, log: Logger):
        self.log = log
        self._name = name
        self._url = url
        self.connect()
        self.metrics = metrics

    def connect(self):
        tick = ticktock()
        self.log.info(f"Connecting mysql {self._name}")
        self.engine = create_engine(
            self._url,
            pool_pre_ping=True,
            pool_size=3,
            pool_recycle=60,
            max_overflow=1,
            connect_args={'connect_timeout': 10},
        )
        session_factory = sessionmaker(bind=self.engine)
        # NOTE: we might have trouble
        # if
        self.Session = scoped_session(session_factory)
        self.log.info(f"Connected mysql {self._name} took {tick()}")

    def session(self):
        # Commit before query
        local_session = self.Session()
        local_session.commit()
        return local_session

    @contextmanager
    def get_session(self, autocommit=False):
        """Get database session.
        Note that you don't need to handle rollback yourself.

        Example usage:

        ```python
        with mysql_c.get_session() as session:
            do_some_thing(session)
            do_something_else(session)
            session.commit()
        ```

        """
        session: Session = self.Session()
        tick = ticktock()
        try:

            yield session
            if autocommit:
                session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            # session.remove()
            session.close()
            self.metrics.mysql_report.set(tick())

    def close(self):
        self.log.info(f"Closing mysql {self._name}")
        self.engine.dispose()
        self.log.info(f"Closed mysql {self._name}")
