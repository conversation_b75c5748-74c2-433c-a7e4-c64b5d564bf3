from logging import Logger
from typing import Dict, Optional

import redis

from chat.utils.common import ticktock


class RedisConnection(object):
    def __init__(
        self,
        name: str,
        host: str,
        port: int,
        db: int,
        password: Optional[str],
        log: Logger,
    ):
        self.log = log
        self._name = name
        self._host = host
        self._port = port
        self._db = db
        self._password = password
        self.connect()

    def connect(self):

        self.log.info(f"Connecting redis {self._name}")
        tick = ticktock()
        params: Dict = {
            "host": self._host,
            "port": self._port,
            "db": self._db,
            "encoding": "utf-8",
            "decode_responses": True,
        }
        if self._password:
            params["password"] = self._password
        self.client = redis.Redis(**params)
        self.client.ping()
        self.log.info(f"Connected redis {self._name} took {tick()}")

    def close(self):
        self.log.info(f"Closing redis {self._name}")
        self.client.close()
        self.log.info(f"Closed redis {self._name}")
