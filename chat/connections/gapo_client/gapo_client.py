from logging import Logger

from chat.config import Settings
from chat.metrics.model import AppMetrics

from .base import <PERSON><PERSON>rg<PERSON>hart
from .bot import GapoBot
from .contact import GapoContact
from .group import GapoGroupAPI
from .iam import GapoIAM
from .membership import MembershipClient

# from .membership import MembershipMock
from .org_chart import <PERSON>o<PERSON><PERSON><PERSON><PERSON>, GapoOrgChartV3
from .poll_vote import GapoPoll
from .react import GapoReact
from .relation import GapoRelationV2
from .user import Gapo<PERSON>ser
from .workspace import GapoWorkspace
from .feature import GapoFeature


class GapoClient(object):
    __instances = {}  # type: ignore

    def __call__(cls, *args, **kwargs):
        if cls not in cls.__instances:
            # pylint: disable=no-member
            cls.__instances[cls] = super(GapoClient, cls).__call__(*args, **kwargs)  # type: ignore # noqa
        return cls.__instances[cls]

    def __init__(self, config: Settings, log: Logger, app_metrics: AppMetrics):
        self.log = log
        self.config = config
        self.app_metrics = app_metrics
        self.init_relation_info()
        self.init_user_info()
        self.init_bot_info()
        self.init_contact_info()
        self.init_react_info()
        self.init_workspace_info()
        self.init_poll_info()
        self.init_org_info()
        self.init_membership()
        self.init_group_api()
        self.init_iam()
        self.init_feature()

    def init_org_info(self):
        self.orgc: IOrgChart = GapoOrgChart(
            self.config, self.log, self.app_metrics
        )
        use_orgc_chat_v3 = self.config.gapo.use_orgc_chart_v3
        if use_orgc_chat_v3:
            self.log.info("Using orgc chart v3 API...")
            self.orgc = GapoOrgChartV3(self.config, self.log, self.app_metrics)
        else:
            self.log.info("Using orgc chart v2 API...")

    def init_relation_info(self):
        self.relation = GapoRelationV2(self.config, self.log, self.app_metrics)

    def init_user_info(self):
        self.user = GapoUser(self.config, self.log, self.app_metrics)

    def init_bot_info(self):
        self.bot = GapoBot(self.config, self.log, self.app_metrics)

    def init_contact_info(self):
        self.contact = GapoContact(self.config, self.log, self.app_metrics)

    def init_react_info(self):
        self.react = GapoReact(self.config, self.log, self.app_metrics)

    def init_workspace_info(self):
        self.workspace = GapoWorkspace(self.config, self.log, self.app_metrics)

    def init_poll_info(self):
        self.poll = GapoPoll(self.config, self.log, self.app_metrics)

    def init_membership(self):
        # self.membership = MembershipMock()
        self.membership = MembershipClient(
            self.config, self.log, self.app_metrics
        )

    def init_group_api(self):
        self.group = GapoGroupAPI(self.config, self.log, self.app_metrics)

    def init_iam(self):
        self.iam = GapoIAM(self.config, self.log, self.app_metrics)

    def init_feature(self):
        self.feature = GapoFeature(self.config, self.log, self.app_metrics)
