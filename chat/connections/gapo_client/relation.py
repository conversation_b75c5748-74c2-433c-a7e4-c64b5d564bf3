from logging import Logger
from typing import List

import requests

from chat.config import Settings
from chat.metrics.model import AppMetrics

from .base import GapoBase, IRelationClient


class GapoRelation(GapoBase, IRelationClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoRelation, self).__init__(
            config, log, "relation-service", metrics
        )

        self.header = {
            "x-gapo-role": "service",
            "User-Agent": self._service_user_agent,
        }

        self.block_user_url = self.cfg.block_user_url
        self.unblock_user_url = self.cfg.unblock_user_url
        self.is_friend_url = self.cfg.is_friend_url
        self.is_friends_url = self.cfg.is_friends_url
        self.white_list = self.cfg.white_list_is_friend
        self.session = requests.Session()
        self.init_response_codes()

    def init_response_codes(self):
        # block-400 code
        # https://docs.contentcms.online/api.html?urls.primaryName=api.user-relation-v1.0#/block/post_user_relation_block # noqa

        # Thông tin người dùng không được để trống
        self.CODE_1100 = 1100

        # Tài khoản đã bị khoá hoặc không tồn tại
        self.CODE_2100 = 2100

        # Thành viên này đã bị bạn chặn
        self.CODE_2101 = 2101

        # unblock-400 code
        # https://docs.contentcms.online/api.html?urls.primaryName=api.user-relation-v1.0#/block/post_user_relation_un_block
        # Không tìm thấy yêu cầu
        self.CODE_2102 = 2102

    def block(self, bearer_token, user_id: str, target_id: str):
        header = dict(self.header)
        header["Authorization"] = bearer_token
        res = self.session.post(
            url=self.block_user_url,
            json={"request_user_id": user_id, "target_user_id": target_id},
            headers=header,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code == self.SUCCESS_CODE:
            return True
        elif res.status_code == self.BAD_REQUEST_CODE:
            body = res.json()
            if body["code"] in (self.CODE_2100, self.CODE_2101):
                return True
            else:
                self.log.error(res.text)
                self.report_api_call_errors()
                return False
        else:
            self.log.error(res.text)
            return False
        return True

    def unblock(self, bearer_token, user_id: str, target_id):
        header = dict(self.header)
        header["Authorization"] = bearer_token
        res = self.session.post(
            url=self.unblock_user_url,
            json={"request_user_id": user_id, "target_user_id": target_id},
            headers=header,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code == self.SUCCESS_CODE:
            return True
        elif res.status_code == self.BAD_REQUEST_CODE:
            body = res.json()
            if body["code"] == self.CODE_2102:
                return True
            else:
                self.log.error(res.text)
                self.report_api_call_errors()

                return False
        else:
            self.report_api_call_errors()
            self.log.error(res.text)
            return False
        return True

    # deprecated method
    # def is_friend(self, user_id: str, part_id):
    def is_friend(self, user_id: str, part_id: str):
        if user_id in self.white_list:
            return True

        params = {"request_user_id": user_id, "receive_user_id": part_id}
        res = self.session.get(
            url=self.is_friend_url,
            params=params,
            headers=self.header,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()
            self.log.error(res.text)

            return False
        body = res.json()
        if body["isFriend"] == 1:
            return True
        else:
            self.log.error(
                "user %s is not friend with %s, because %s",
                user_id,
                part_id,
                str(body),
            )
            return False

    # deprecated method
    # def is_friends(self, user_id: str, part_ids):
    def get_friends_from_list(self, user_id: str, part_ids: List[str]):
        if user_id in self.white_list:
            return part_ids

        part_ids = [str(p) for p in part_ids]
        params = {
            "request_user_id": user_id,
            "ids": ",".join(part_ids),
        }
        res = self.session.get(
            url=self.is_friends_url,
            params=params,
            headers=self.header,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()
            self.log.error(res.text)
            return None

        body = res.json()
        is_friends = []
        not_friends = []
        for p in part_ids:
            if body["isFriends"][p]["status"] == 1:
                is_friends.append(p)
            else:
                not_friends.append(p)

        if not_friends:
            self.log.error(
                "user %s is not friends with %s",
                user_id,
                not_friends,
                extra=body,
            )
        return is_friends


class GapoRelationV2(GapoBase, IRelationClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoRelationV2, self).__init__(
            config, log, "relation-service", metrics
        )
        self.white_list = self.cfg.white_list_is_friend
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.relation_v2_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.relation_url = self.cfg.relation_v2_url
        self.session = requests.Session()
        self.init_status_codes()

    def init_status_codes(self):
        self.CODE_SENT = 2
        self.CODE_RECEIVED = -2
        self.CODE_BLOCKED = 1
        self.CODE_BE_BLOCKED = -1
        self.CODE_FRIEND = 3
        # nothing
        self.CODE_UNKNOWN = 0

    def is_friend(self, user_id: str, part_id):
        if user_id in self.white_list:
            return True

        params = {"subject_user_id": user_id, "target_user_ids": part_id}
        try:
            res = self.session.get(
                url=self.relation_url,
                params=params,
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
        except requests.exceptions.Timeout as e:
            self.report_api_call_errors()
            self.log.error("Timeout {}".format(e))
            return False
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()
            self.log.error(
                "Error {} code {} ".format(res.text, res.status_code)
            )
            return False
        body = res.json()
        if body["data"][str(part_id)] == self.CODE_FRIEND:
            return True

        self.log.debug(
            "user %s is not friend with %s, because %s",
            user_id,
            part_id,
            str(body),
        )
        return False

    def get_friends_from_list(self, user_id: str, part_ids: List[str]):
        part_ids = [str(p) for p in part_ids]
        # Quangvn, [26.09.20 13:03]
        # anh Kiên confirm giúp em là có được  add được người lạ vào chat group
        # nhé anh
        #
        # Trần Nhật, [26.09.20 13:05]
        # Khi nào chuẩn bị gom thì pm anh nhé . Để anh cho người chuẩn bị
        #
        # Kien Ha, [26.09.20 13:28]
        # [In reply to Quangvn]
        # Anh confirm Quang nhé
        return part_ids
        if user_id in self.white_list:
            return part_ids
        params = {
            "subject_user_id": user_id,
            "target_user_ids": ",".join(part_ids),
        }
        try:
            res = self.session.get(
                url=self.relation_url,
                params=params,
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
        except requests.exceptions.Timeout as e:
            self.log.error("Timeout {}".format(e))
            return None
        if res.status_code != self.SUCCESS_CODE:
            self.log.error(res.text)
            return None
        body = res.json()
        data = body["data"]
        is_friends = []
        not_friends = []
        for p in part_ids:
            if data[p] == self.CODE_FRIEND:
                is_friends.append(p)
            else:
                not_friends.append(p)

        if not_friends:
            self.log.debug(
                "user %s is not friends with %s",
                user_id,
                not_friends,
                extra=body,
            )
        return is_friends
