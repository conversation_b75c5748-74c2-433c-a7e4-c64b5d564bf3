from logging import Logger
from typing import Dict, Iterable

import requests

from chat import constant
from chat.config import Settings
from chat.metrics.model import AppMetrics
from chat.users.model import UserInfo

from .base import GapoBase, IUserClient, SchemaUserProfile


class GapoUser(GapoBase, IUserClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoUser, self).__init__(config, log, "user-service", metrics)

        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.user_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.info_url = self.cfg.user_info_url
        self.required_field = (
            "display_name,id,status_verify,avatar,info,status"
        )
        self.session = requests.Session()

    def get_by_ids(self, user_ids: Iterable[str]) -> Dict[str, UserInfo]:
        objects: Dict[str, UserInfo] = dict()

        params = {
            "ids": ",".join(user_ids),
            "fields": self.required_field,
        }
        try:
            res = self.session.get(
                url=self.info_url,
                params=params,
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                result = res.json()
                users = result["data"]
                objects.update(
                    {str(u["id"]): SchemaUserProfile(**u).dict() for u in users}  # type: ignore # noqa
                )
            else:
                self.report_api_call_errors()
                self.log.error(res.text)
        except requests.exceptions.Timeout as e:
            self.report_api_call_errors()
            self.log.error("Timeout {}".format(e))
        missing_users = set(user_ids) - objects.keys()

        # TODO: Need to check this
        # Right now, if an user is deactivated the API doesn't
        # include the user in the response.
        # So that, we don't know if an user is invalid or deactivated
        # Our temporaral solution is we will treat all of them as a
        # deactivated users.
        objects.update(
            {
                u: SchemaUserProfile(
                    id=u,
                    status=constant.USER_STATUS_DEACTIVATED,
                ).dict()  # type: ignore
                for u in missing_users
            }
        )

        return objects
