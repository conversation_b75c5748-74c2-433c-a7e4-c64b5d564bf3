from logging import Logger
from typing import Dict, Iterable, List
from urllib.parse import urljoin

import requests

from chat.config import Settings
from chat.exception import External400, External404
from chat.metrics.model import AppMetrics

from .base import GapoBase, IPollClient, SchemaPoll


class GapoPoll(GapoBase, IPollClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "poll-service", metrics)
        self.name = "gapo_poll"
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.poll_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.poll_base_url
        self.log = log
        self.session = requests.Session()

    def _handle_resp(self, resp):
        if resp.status_code == self.SUCCESS_CODE:
            return resp.json()
        else:
            self.report_api_call_errors()

        if resp.status_code == 400:
            output = resp.json()
            self.log.warning(
                "Calling pollvote service error {}".format(output)
            )
            raise External400(error_details=output)
        if resp.status_code == 404:
            raise External404("poll object not found")

        resp.raise_for_status()

    def create_poll(self, object_payload):
        poll_type = "chat"
        payload = object_payload
        payload["type"] = poll_type
        url = urljoin(self.root_url, "internal/polls")
        resp = self.session.post(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )

        data = self._handle_resp(resp)
        return SchemaPoll(**data["data"])

    def get_polls(
        self, user_id: str, poll_ids: Iterable[str]
    ) -> Dict[str, SchemaPoll]:
        url = urljoin(self.root_url, "internal/polls")
        params = {"ids": poll_ids, "user_id": user_id}
        resp = self.session.get(
            url,
            headers=self.header,
            params=params,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )

        data = self._handle_resp(resp)
        polls = {poll["id"]: SchemaPoll(**poll) for poll in data["data"]}
        return polls

    def update_poll(self, poll_id, update_d):
        url = urljoin(self.root_url, f"internal/polls/{poll_id}")
        payload = update_d
        resp = self.session.patch(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )

        data = self._handle_resp(resp)
        return SchemaPoll(**data["data"])

    def create_vote(self, poll_id, vote_d):
        url = urljoin(self.root_url, f"internal/polls/{poll_id}/votes")
        payload = vote_d
        resp = self.session.post(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        return self._handle_resp(resp)

    def delete_vote(self, user_id: str, poll_id, vote_id):
        url = urljoin(
            self.root_url, f"internal/polls/{poll_id}/votes/{vote_id}"
        )
        params = {"user_id": user_id}
        resp = self.session.delete(
            url,
            headers=self.header,
            params=params,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        return self._handle_resp(resp)

    def update_vote(self, poll_id, vote_id, update_d):
        url = urljoin(
            self.root_url, f"internal/polls/{poll_id}/votes/{vote_id}"
        )
        payload = update_d
        resp = self.session.patch(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        return self._handle_resp(resp)

    def vote_by_user(self, user_id: str, poll_id: str, vote_id: str):
        url = urljoin(self.root_url, f"internal/polls/{poll_id}/vote")
        payload = {"user_id": user_id, "vote_id": vote_id}
        resp = self.session.post(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        return self._handle_resp(resp)

    def unvote_by_user(self, user_id: str, poll_id: str, vote_id: str):
        url = urljoin(self.root_url, f"internal/polls/{poll_id}/vote")
        payload = {"user_id": user_id, "vote_id": vote_id}
        resp = self.session.delete(
            url,
            headers=self.header,
            json=payload,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        return self._handle_resp(resp)

    # def get_voted_users(self, poll_id: str, vote_id: str, params) -> List[str]:
    #     url = urljoin(
    #         self.root_url, f"internal/polls/{poll_id}/votes/{vote_id}/users"
    #     )
    #     resp = self.session.get(
    #         url,
    #         headers=self.header,
    #         params=params,
    #         timeout=self.REQ_TIMEOUT_SECS,
    #         verify=False,
    #     )
    #     data = self._handle_resp(resp)
    #     return data["data"]  # type: ignore
