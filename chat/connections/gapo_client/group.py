from logging import Logger
from urllib.parse import urljoin

import requests

from chat.config import Settings
from chat.metrics.model import AppMetrics

from .base import GapoBase, IGroup


class GapoGroupAPI(GapoBase, IGroup):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "group-service", metrics)
        self.name = "gapo_group_api"
        self.headers = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.group_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.group_base_url
        self.session = requests.Session()
        self.log = log

    def unlink_thread_from_group(self, group_id, thread_id):
        url = urljoin(
            self.root_url, f"internal/groups/{group_id}/chats/{thread_id}"
        )
        res = self.session.delete(
            url=url,
            headers=self.headers,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()
            self.log.critical(f"Group API error: path:{url} res:{res.json()}")

        res.raise_for_status()
