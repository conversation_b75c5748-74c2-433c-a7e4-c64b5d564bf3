from typing import Dict
from logging import Logger

import requests
from urllib.parse import urljoin

from chat.config import Settings
from chat.metrics.model import AppMetrics
from chat.model import BaseModel

from .base import GapoBase, IFeatureClient


class SchemaFeatureWorkspace(BaseModel):
    data: Dict[str, bool] = dict()


class GapoFeature(GapoBase, IFeatureClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoFeature, self).__init__(
            config, log, "feature-service", metrics
        )
        self.name = "gapo_feature"
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.feature_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.feature_url
        self.session = requests.Session()

    def is_ws_enable(self, workspace_id: str) -> bool:
        header = dict(self.header)
        post_fix = "internal/workspaces/enable"
        url = urljoin(self.root_url, post_fix)

        try:
            res = self.session.get(
                url=url,
                headers=header,
                timeout=self.REQ_TIMEOUT_SECS,
                params={"ids": workspace_id},
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                result = res.json()
                data = SchemaFeatureWorkspace(**result)
                return data.data.get(workspace_id, True)
            else:
                self.log.error(f"{self.name} error http code", resp=res.text)  # type: ignore # noqa
                self.report_api_call_errors()
                return True
        except requests.exceptions.Timeout:
            self.log.error(f"{self.name} Timeout ")
            return True
