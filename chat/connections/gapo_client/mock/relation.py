from collections import defaultdict
from typing import List, Tuple

from ..base import IRelationClient


class GapoRelationMock(IRelationClient):
    def __init__(self, data: List[Tuple[str, str]]):
        self.data = data
        self.init_reponse_codes()
        self.init_friends()

        self.blocked = defaultdict(bool)  # type: ignore

    def init_friends(self):
        self.friends = defaultdict(bool)
        for pair in self.data:
            (user1, user2) = pair
            self.friends[self._pair(user1, user2)] = True
            self.friends[self._pair(user2, user1)] = True

    def init_reponse_codes(self):
        self.CODE_SENT = 2
        self.CODE_RECEIVED = -2
        self.CODE_BLOCKED = 1
        self.CODE_BE_BLOCKED = -1
        self.CODE_FRIEND = 3
        # nothing
        self.CODE_UNKNOWN = 0

    # def block(self, bearer_token, user_id: str, target_id):
    #     self.blocked[user_id + '_' + target_id] = True

    # def unblock(self, bearer_token, user_id: str, target_id):
    #     return self.blocked[self._pair(user_id, target_id)]

    def _pair(self, user1, user2):
        return "{}_{}".format(user1, user2)

    def is_friend(self, user_id: str, part_id):
        return self.friends.get(self._pair(user_id, part_id), False)

    def get_friends_from_list(self, user_id: str, part_ids):
        return part_ids
