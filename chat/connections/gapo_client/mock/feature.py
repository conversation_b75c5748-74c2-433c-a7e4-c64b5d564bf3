from typing import List

from pydantic import BaseModel

from ..base import IFeatureClient


class SchemaFeatureWorkspace(BaseModel):
    data: dict = dict()


class GapoFeatureMock(IFeatureClient):
    def __init__(self, data: List[SchemaFeatureWorkspace] = []):
        self.data = data

    def is_ws_enable(self, workspace_id: str) -> bool:

        for i in self.data:
            if workspace_id in i.data.keys():
                return i.data[workspace_id]  # type: ignore
        return True
