from typing import Iterable, List

from ..base import IUserClient, SchemaUserProfile


class GapoUserMock(IUserClient):
    def __init__(self, data: List[SchemaUserProfile]):
        self.data = data

    def get_by_ids(self, user_ids: Iterable[str]):
        user_ids = [str(user_id) for user_id in user_ids]

        output = {}
        found_users = [user for user in self.data if user.id in user_ids]
        for user in found_users:
            output[user.id] = user.dict()
        return output
