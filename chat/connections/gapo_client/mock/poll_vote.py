from typing import List

from nanoid import generate

from chat.utils.common import now

from ..base import IPollClient, SchemaPoll, SchemaVote


class GapoPollMock(IPollClient):
    def __init__(self, data: List[SchemaPoll]) -> None:
        self.data = data
        self.idx = 0
        self.create_poll_calls = 0

    def create_poll(self, object_payload):
        poll_type = "chat"
        payload = object_payload
        payload["type"] = poll_type
        self.idx += 1
        payload["id"] = str(self.idx)
        payload["total_users"] = 0
        payload["myself_votes"] = []
        payload["created_at"] = now()
        payload["updated_at"] = now()
        payload["end_at"] = 0

        votes = payload["votes"]
        user_id = payload["user_id"]
        for vote in votes:
            vote["id"] = generate()
            vote["user_id"] = user_id
        payload["votes"] = votes

        self.create_poll_calls += 1
        poll = SchemaPoll(**payload)
        self.data.append(poll)

        return poll

    def get_polls(self, user_id: str, ids):
        return {poll.id: poll for poll in self.data if poll.id in ids}

    def update_poll(self, poll_id, update_d):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                data = poll.dict()
                data.update(update_d)
                poll = SchemaPoll(**data)
                self.data[idx] = poll
                return poll

    def create_vote(self, poll_id, vote_d):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                vote = SchemaVote(**vote_d)
                vote.id = str(len(poll.votes))
                poll.votes.append(vote)

    def delete_vote(self, user_id, poll_id, vote_id):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                for idxv, v in enumerate(poll.votes):
                    if v.id == vote_id:
                        del poll.votes[idxv]

    def update_vote(self, poll_id, vote_id, update_d):
        vote = SchemaVote(**update_d)
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                for idxv, v in enumerate(poll.votes):
                    if v.id == vote_id:
                        poll.votes[idxv] = vote

    def vote_by_user(self, user_id: str, poll_id, vote_id):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                for idxv, v in enumerate(poll.votes):
                    if v.id == vote_id:
                        v.user_votes.append(user_id)

    def unvote_by_user(self, user_id: str, poll_id, vote_id):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                for idxv, v in enumerate(poll.votes):
                    if v.id == vote_id:
                        v.user_votes.remove(user_id)

    def get_voted_users(self, poll_id, vote_id, params):
        for idx, poll in enumerate(self.data):
            if poll.id == poll_id:
                for idxv, v in enumerate(poll.votes):
                    if v.id == vote_id:
                        return v.user_votes
