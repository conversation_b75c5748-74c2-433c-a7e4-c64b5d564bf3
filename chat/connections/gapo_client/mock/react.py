from typing import List

from pydantic import BaseModel

from ..base import IReactClient


class SchemaReactCount(BaseModel):
    react_type_1: int = 0
    react_type_2: int = 0
    react_type_3: int = 0
    react_type_4: int = 0
    react_type_5: int = 0
    react_type_6: int = 0
    react_type_7: int = 0
    react_type_8: int = 0
    react_type_9: int = 0
    react_type_10: int = 0


class SchemaReact(BaseModel):
    id: str
    react_count: SchemaReactCount = SchemaReactCount()
    react_yourself: int = 0
    react_count_total: int = 0


class GapoReactMock(IReactClient):
    def __init__(self, data: List[SchemaReact] = []):
        self.data = data

    def post_react(
        self,
        user_id: str,
        thread_id: int,
        msg_id: int,
        msg_created_at: int,
        react_type: int,
    ):
        return True

    def get_react(
        self,
        suer_id,
        thread_id: int,
        msg_ids: List[int],
        msg_created_ats: List[int],
    ):
        """Just return empty reacts"""
        return []

    def get_react_users(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        message_created_at: int,
        react_type,
        last_time,
    ):
        return []

    def get_default(
        self, thread_id: int, message_id: int, message_created_at: int
    ):
        _id = f"{thread_id}_{message_id}_{message_created_at}"
        return SchemaReact(id=_id).dict()

    def build_local(self):
        pass
