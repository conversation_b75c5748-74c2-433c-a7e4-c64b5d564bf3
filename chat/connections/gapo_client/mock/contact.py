from typing import List

from chat.model import BaseModel

from ..base import IContactClient


class Contact(BaseModel):
    u1: str = ""
    u2: str = ""
    code: int = 1


class GapoContactMock(IContactClient):
    def __init__(self, data: List[Contact]):
        self.data = data
        self.init_status_codes()

    def init_status_codes(self):
        # 0 => A & B k lưu nhau
        self.CODE_NONE_A_B = 0
        # 1 => A & B lưu nhau
        self.CODE_BOTH_A_B = 1
        # 2 => B lưu A && A không lưu B
        self.CODE_B_HAS_A = 2
        # 3 => A lưu B && B không lưu A
        self.CODE_A_HAS_B = 3

    def _pair(self, u1, u2):
        return "{}_{}".format(u1, u2)

    def get_relationship_status(self, user_id: str, part_id) -> int:
        for contact in self.data:
            if (contact.u1 == user_id and contact.u2 == part_id) or (
                contact.u1 == part_id and contact.u2 == user_id
            ):
                return contact.code
        return self.CODE_NONE_A_B
