from dataclasses import dataclass
from typing import List

from ..base import <PERSON><PERSON>rg<PERSON><PERSON>


@dataclass
class OrgcMember:
    user_id: str
    department: str
    role: str
    workspace_id: str


class GapoOrgChatMock(IOrgChart):
    def __init__(self, data: List[OrgcMember] = []):
        self.data = data
        self.unlink_thread_in_department_calls = 0

    def get_members_in_departments(
        self, user_id: str, workspace_id, department_ids
    ):
        output = []
        for d in department_ids:
            user_ids = [
                u.user_id
                for u in self.data
                if u.department == d and u.workspace_id == workspace_id
            ]
            output.append({"name": d, "user_ids": user_ids})
        return output

    def get_members_by_roles(self, user_id: str, workspace_id, role_ids):
        output = []
        for role in role_ids:
            user_ids = [
                u.user_id
                for u in self.data
                if u.role == role and u.workspace_id == workspace_id
            ]
            output.append({"name": role, "user_ids": user_ids})
        return output

    def get_department_info(self, workspace_id, department_id):
        return {"fullpath_name": "test"}

    def unlink_thread_from_department(self, workspace_id, department_id):
        self.unlink_thread_in_department_calls += 1
