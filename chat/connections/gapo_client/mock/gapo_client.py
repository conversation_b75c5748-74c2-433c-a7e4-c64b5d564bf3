from typing import Dict, List, Tuple

from ..base import SchemaBotProfile, SchemaUserProfile
from ..membership import Collab<PERSON>roup, Member, MembershipMock
from .bot import GapoBotMock
from .contact import Contact, GapoContactMock
from .group import GapoGroupMock
from .iam import Gapo<PERSON><PERSON>ock
from .org_chat import GapoOrgC<PERSON>Mock, OrgcMember
from .poll_vote import GapoPollMock
from .react import GapoReactMock
from .relation import GapoRelationMock
from .user import GapoUserMock
from .workspace import GapoWorkspaceMock
from .feature import GapoFeatureMock, SchemaFeatureWorkspace


class GapoClientMock(object):
    def __init__(
        self,
        relation_data: List[Tuple[str, str]] = [],
        user_data: List[SchemaUserProfile] = [],
        bot_data: List[SchemaBotProfile] = [],
        orgc_data: List[OrgcMember] = [],
        contact_data: List[Contact] = [],
        company_data: Dict[str, str] = {},
        role_data: Dict[str, bool] = {},
        file_data: Dict[str, List[str]] = {},
        membership_collabs: List[CollabGroup] = [],
        membership_member_data: Dict[str, List[Member]] = {},
        iam_data: Dict[str, str] = {},
        feature_data: List[SchemaFeatureWorkspace] = [],
    ) -> None:
        self.relation = GapoRelationMock(relation_data)
        self.user = GapoUserMock(user_data)
        self.bot = GapoBotMock(bot_data)
        self.workspace = GapoWorkspaceMock(
            company_data=company_data, file_data=file_data, role_data=role_data
        )
        self.contact = GapoContactMock(contact_data)
        self.poll = GapoPollMock([])
        self.react = GapoReactMock([])
        self.orgc = GapoOrgChatMock(orgc_data)
        self.membership = MembershipMock(
            membership_collabs, membership_member_data
        )
        self.group = GapoGroupMock()
        self.iam = GapoIAMMock(iam_data)
        self.feature = GapoFeatureMock(feature_data)

    def reset(self):
        self.poll.data = []
        self.poll.create_poll_calls = 0
        self.poll.idx = 0
        self.orgc.unlink_thread_in_department_calls = 0
        self.group.unlink_thread_from_group_calls = 0
        self.membership.request_join_calls = 0
        self.membership.join_collab_calls = 0
