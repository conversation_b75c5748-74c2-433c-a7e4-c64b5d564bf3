from typing import Dict, List, Set

from ..base import IWorkspaceClient


class GapoWorkspaceMock(IWorkspaceClient):
    def __init__(
        self,
        company_data: Dict[str, str],
        file_data: Dict[str, List[str]],
        role_data: Dict[str, bool],
    ) -> None:
        self.data = company_data
        self.file_data = file_data
        self.role_data = role_data

    def _get_company(self, user_id) -> str:
        return self.data.get(user_id, "unknown")

    def check_if_same_company(
        self, from_user_id: str, to_user_id: str
    ) -> bool:
        return self._get_company(from_user_id) == self._get_company(to_user_id)

    def get_users_from_imported_file(
        self, workspace_id: str, file_id: str
    ) -> List[str]:
        if file_id in self.file_data:
            return [v for v in self.file_data[file_id]]

        return []

    def is_admin(self, workspace_id: str, user_id: str) -> bool:
        return self.role_data.get(user_id, False)

    def get_workspaces(self, user_id: str) -> Set[str]:
        return set([self._get_company(user_id)])
