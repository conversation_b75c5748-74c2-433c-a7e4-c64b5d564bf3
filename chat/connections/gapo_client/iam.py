from logging import Logger
from typing import Dict

import requests

from chat.config import Settings
from chat.connections.gapo_client.base import <PERSON><PERSON><PERSON><PERSON>, IIAMClient
from chat.metrics.model import AppMetrics


class GapoIAM(GapoBase, IIAMClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "iam-service", metrics)
        self.name = "iam"
        self.headers = {
            "x-gapo-role": "service",
            "x-gapo-api-key": config.iam_api_key,
        }
        self.base_url = config.iam_base_url

    def get_all_api_keys(self) -> Dict[str, str]:
        r = requests.get(self.base_url, headers=self.headers, timeout=3)
        if r.status_code != 200:
            self.report_api_call_errors()
            self.log.error(r.text)
            raise Exception("Error while getting keys")

        result = r.json()
        key_pairs: Dict[str, str] = {}
        for r in result["data"]:
            key_pairs[r["apiKey"]] = r["scopes"]

        return key_pairs
