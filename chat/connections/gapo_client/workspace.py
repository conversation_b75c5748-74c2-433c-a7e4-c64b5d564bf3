import json
from logging import Logger
from typing import Dict, List, Set
from urllib.parse import urljoin

import requests
from pydantic import BaseModel

from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat.exception import InternalServerError
from chat.metrics.model import AppMetrics
from chat.model import make_input

from .base import GapoBase, IWorkspaceClient


class SchemaGetUsersInImportedFileResp(BaseModel):
    data: List[str]


class SchemaIsAdmin(BaseModel):
    data: List[bool]


class SchemaWorkspace(BaseModel):
    workspace_id: int


class SchemaUserWorkspace(BaseModel):
    user_id: int
    workspaces: List[SchemaWorkspace]

    def get_workspaces(self) -> Set[str]:
        ws_ids = set([str(w.workspace_id) for w in self.workspaces])
        return ws_ids


class SchemaGetWorkspace(BaseModel):
    data: List[SchemaUserWorkspace]


class GapoWorkspaceCache:
    def __init__(self, config: Settings, log: Logger):

        con = RedisConnection(
            name="gapo_workspace_cache",
            host=config.redis_cache.host,
            port=config.redis_cache.port,
            db=config.redis_cache.db,
            password=config.redis_cache.password,
            log=log,
        )
        self.client = con.client
        self.log = log
        self.ttl = 5 * 60

    def get_joined_workspaces(self, user_id: str) -> Set[str]:
        data = self.client.get(f"gapo:workspace:{user_id}")
        if data:
            return set(json.loads(data))
        return set()

    def set_joined_workspaces(self, user_id: str, workspaces: Set[str]):
        self.client.set(
            f"gapo:workspace:{user_id}",
            json.dumps(list(workspaces)),
            nx=True,
            ex=self.ttl,
        )


class GapoWorkspace(GapoBase, IWorkspaceClient):
    def __init__(
        self,
        config: Settings,
        log: Logger,
        metrics: AppMetrics,
    ):
        super().__init__(config, log, "workspace-service", metrics)
        self.name = "gapo_workspace"
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.workspace_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.workspace_url
        self.session = requests.Session()
        self.cache = GapoWorkspaceCache(config, log)
        self.RETRY_COUNT = 10  # theo order cua a Tuan CTO

    def check_if_same_company(self, from_user_id: str, to_user_id: str):
        url = urljoin(self.root_url, "mutual-workspace/check")
        res = self.session.get(
            url=url,
            headers=self.header,
            timeout=self.REQ_TIMEOUT_SECS,
            params={"from_user_id": from_user_id, "to_user_id": to_user_id},
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        data = res.json()
        return data["data"]["status"] == 1

    def get_users_from_imported_file(
        self, workspace_id: str, file_id: str
    ) -> List[str]:
        """Get lists of users specified in an imported excel file.

        Docs: https://gapowork.redoc.ly/tag/Workspace-Service_Internal#operation/InternalApiController_exchangeUserByExcel_q4d58 # noqa
        """
        url = urljoin(self.root_url, "internal/users/exchanges-by-excel")
        headers = self._fill_workspace_id_to_headers(workspace_id)
        res = self.session.get(
            url=url,
            headers=headers,
            timeout=self.REQ_TIMEOUT_SECS,
            params={"file_id": file_id},
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        output, errors = make_input(
            SchemaGetUsersInImportedFileResp, res.json()
        )
        if errors:
            raise InternalServerError(error_details=errors)

        return output.data

    def _fill_workspace_id_to_headers(self, workspace_id: str):
        headers: Dict = {"x-gapo-workspace-id": workspace_id}
        for k, v in self.header.items():
            headers[k] = v

        return headers

    def is_admin(self, workspace_id: str, user_id: str) -> bool:
        """
        {
            "data": [
                false
            ]
        }

        {
            "data": [
                true
            ]
        }
        """

        url = urljoin(self.root_url, "internal/workspaces/check-user-admin-ws")
        headers = self._fill_workspace_id_to_headers(workspace_id)
        res = self.session.get(
            url=url,
            headers=headers,
            timeout=self.REQ_TIMEOUT_SECS,
            params={"user_ids": user_id},
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        output, errors = make_input(SchemaIsAdmin, res.json())
        if errors:
            raise InternalServerError(error_details=errors)

        return output.data[0]

    def get_workspaces(self, user_id: str) -> Set[str]:
        """
        {
        "data": [
            {
            "user_id": 395658491,
            "workspaces": [
                {
                    "is_main": false,
                    "role": 1,
                    "workspace_id": 581781330698384
                },
                {
                    "is_main": false,
                    "role": 2,
                    "workspace_id": 581791073629268
                },
                {
                    "is_main": false,
                    "role": 1,
                    "workspace_id": 582843675985602
                }
            }]
            }
        """
        url = urljoin(self.root_url, "internal/workspaces/joined")
        retry = 3
        workspaces = set()
        while retry > 0:
            try:
                retry -= 1
                res = self.session.get(
                    url=url,
                    headers=self.header,
                    timeout=self.REQ_TIMEOUT_SECS,
                    params={"user_ids": user_id},
                )
                if res.status_code != self.SUCCESS_CODE:
                    self.report_api_call_errors()
            except requests.exceptions.RequestException as e:
                self.log.error(
                    f"Error getting workspaces for user {user_id}: {e}"
                )
                workspaces = self.cache.get_joined_workspaces(user_id)
                if workspaces:
                    return workspaces
            except Exception as e:
                self.log.error(
                    f"Error getting workspaces for user {user_id}: {e}"
                )

        res.raise_for_status()
        output, errors = make_input(SchemaGetWorkspace, res.json())
        if errors:
            raise InternalServerError(error_details=errors)

        if not len(output.data):
            workspaces = set()
        else:
            workspaces = output.data[0].get_workspaces()

        self.cache.set_joined_workspaces(user_id, workspaces)
        return workspaces
