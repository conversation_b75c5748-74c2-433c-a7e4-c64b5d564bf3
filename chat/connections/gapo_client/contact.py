from logging import Logger
from urllib.parse import urljoin

import requests

from chat.config import Settings
from chat.metrics.model import AppMetrics

from .base import GapoBase, IContactClient


class GapoContact(GapoBase, IContactClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoContact, self).__init__(
            config, log, "contact-service", metrics
        )
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.contact_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.contact_url
        self.session = requests.Session()
        self.init_status_codes()

    def init_status_codes(self):
        # 0 => A & B k lưu nhau
        self.CODE_NONE_A_B = 0
        # 1 => A & B lưu nhau
        self.CODE_BOTH_A_B = 1
        # 2 => B lưu A && A không lưu B
        self.CODE_B_HAS_A = 2
        # 3 => A lưu B && B không lưu A
        self.CODE_A_HAS_B = 3

    def get_relationship_status(self, user_id: str, part_id):
        url = f"internal/check_have_contact/{user_id}/{part_id}"
        query_url = urljoin(self.root_url, url)
        code = self.CODE_NONE_A_B
        try:
            res = self.session.get(
                url=query_url,
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                res_code = int(res.text)
                if res_code not in (
                    self.CODE_A_HAS_B,
                    self.CODE_B_HAS_A,
                    self.CODE_BOTH_A_B,
                ):
                    self.log.error(
                        "Unexpected Contact Api response code", resp=res.text  # type: ignore # noqa
                    )
                code = res_code
            elif res.status_code != self.NO_CONTENT:
                self.log.error("Contact Api error http code", resp=res.text)  # type: ignore # noqa
        except requests.exceptions.Timeout:
            self.log.error("Contact Api Timeout ")

        self.log.info("Contact Api response code {}".format(code))
        return code
