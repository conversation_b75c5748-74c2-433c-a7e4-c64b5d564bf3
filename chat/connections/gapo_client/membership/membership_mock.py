from typing import Dict, List, Optional

from .membership_client import (
    DEFAULT_COLLAB_SETTINGS,
    CollabGroup,
    CollabGroupRole,
    CollabSettings,
    IMembershipClient,
    Member,
)


class MembershipMock(IMembershipClient):
    def __init__(
        self,
        collab_data: List[CollabGroup],
        member_data: Dict[str, List[Member]],
    ):
        self._collabs = collab_data
        self._members = member_data
        self.request_join_calls = 0
        self.join_collab_calls = 0

    def create_collab_group(
        self,
        caller_id: int,
        name: str,
        workspace_id: str,
        description="",
        avatar="",
        settings: CollabSettings = DEFAULT_COLLAB_SETTINGS,
    ) -> str:
        return ""

    def add_member(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        workspace_id: str,
        roles: List[CollabGroupRole],
        data_source: str,
    ) -> int:
        return user_id

    def remove_member(
        self, caller_id: int, collab_id: str, user_id: int, workspace_id: str
    ):
        pass

    def set_member_role(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        roles: List[CollabGroupRole],
        workspace_id: str,
    ):
        pass

    def list_members(self, caller_id: int, collab_id: str) -> List[Member]:
        return self._members.get(collab_id, [])

    def get_member_by_ids(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        user_ids: List[int],
    ) -> List[Member]:
        all_members = self.list_members(caller_id, collab_id)
        return [m for m in all_members if m.user_id in user_ids]

    def get_collab_group(
        self, caller_id: int, collab_id: str, workspace_id: str
    ) -> CollabGroup:
        for collab in self._collabs:
            if collab.collab_group_id == collab_id:
                return collab

        return CollabGroup(
            collab_group_id=collab_id,
            workspace_id=workspace_id,
            name="",
            avatar="",
            description="",
            settings=DEFAULT_COLLAB_SETTINGS,
        )

    # def check_access_permission(self, caller_id: int):
    #     pass

    # def generate_invite_link(self, caller_id: int):
    #     pass

    def join_collab(
        self,
        user_id: int,
        collab_id: str,
        workspace_id: str,
        data_source: str = "invite_mem",
    ):
        self.join_collab_calls += 1

    def update_collab_info(
        self,
        collab_id: str,
        caller_id: int,
        workspace_id: str,
        name: Optional[str] = None,
        avatar: Optional[str] = None,
        description: Optional[str] = None,
    ):
        pass

    def request_join(self, caller_id: int, collab_id: str, workspace_id: str):
        self.request_join_calls += 1

    def list_all_pending_members(
        self, caller_id: int, collab_id: str, workspace_id: str
    ) -> List[Member]:
        return []

    def list_pending_members(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        starting_after: str = "",
        limit: int = 50,
    ) -> List[Member]:
        return []

    def update_collab(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        name: Optional[str],
        settings: CollabSettings,
    ):
        pass
