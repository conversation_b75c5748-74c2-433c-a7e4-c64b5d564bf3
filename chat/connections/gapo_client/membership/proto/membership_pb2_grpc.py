# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from . import membership_pb2 as membership__pb2


class GapoMembershipStub(object):
    """Hiện tại có chat và bên task đang sử dụng
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateCollabGroup = channel.unary_unary(
                '/pb.membership.GapoMembership/CreateCollabGroup',
                request_serializer=membership__pb2.CreateCollabGroupRequest.SerializeToString,
                response_deserializer=membership__pb2.CreateCollabGroupResponse.FromString,
                )
        self.AddMember = channel.unary_unary(
                '/pb.membership.GapoMembership/AddMember',
                request_serializer=membership__pb2.AddMemberRequest.SerializeToString,
                response_deserializer=membership__pb2.AddMemberResponse.FromString,
                )
        self.RemoveMember = channel.unary_unary(
                '/pb.membership.GapoMembership/RemoveMember',
                request_serializer=membership__pb2.RemoveMemberRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SetMemberRole = channel.unary_unary(
                '/pb.membership.GapoMembership/SetMemberRole',
                request_serializer=membership__pb2.SetMemberRoleRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ListMembers = channel.unary_unary(
                '/pb.membership.GapoMembership/ListMembers',
                request_serializer=membership__pb2.ListMembersRequest.SerializeToString,
                response_deserializer=membership__pb2.ListMembersResponse.FromString,
                )
        self.GetCollabGroup = channel.unary_unary(
                '/pb.membership.GapoMembership/GetCollabGroup',
                request_serializer=membership__pb2.GetCollabGroupRequest.SerializeToString,
                response_deserializer=membership__pb2.GetCollabGroupResponse.FromString,
                )
        self.ListCollabGroups = channel.unary_unary(
                '/pb.membership.GapoMembership/ListCollabGroups',
                request_serializer=membership__pb2.ListCollabGroupsRequest.SerializeToString,
                response_deserializer=membership__pb2.ListCollabGroupsResponse.FromString,
                )
        self.RequestJoin = channel.unary_unary(
                '/pb.membership.GapoMembership/RequestJoin',
                request_serializer=membership__pb2.RequestJoinRequest.SerializeToString,
                response_deserializer=membership__pb2.RequestJoinResponse.FromString,
                )
        self.GetMemberByIDs = channel.unary_unary(
                '/pb.membership.GapoMembership/GetMemberByIDs',
                request_serializer=membership__pb2.GetMemberByIDsRequest.SerializeToString,
                response_deserializer=membership__pb2.ListMembersResponse.FromString,
                )
        self.UpdateCollabGroup = channel.unary_unary(
                '/pb.membership.GapoMembership/UpdateCollabGroup',
                request_serializer=membership__pb2.UpdateCollabGroupRequest.SerializeToString,
                response_deserializer=membership__pb2.UpdateCollabGroupResponse.FromString,
                )
        self.IsMember = channel.unary_unary(
                '/pb.membership.GapoMembership/IsMember',
                request_serializer=membership__pb2.IsMemberRequest.SerializeToString,
                response_deserializer=membership__pb2.IsMemberResponse.FromString,
                )
        self.ListAllMemberIDs = channel.unary_unary(
                '/pb.membership.GapoMembership/ListAllMemberIDs',
                request_serializer=membership__pb2.ListAllMemberIDsRequest.SerializeToString,
                response_deserializer=membership__pb2.ListAllMemberIDsResponse.FromString,
                )
        self.JoinDirectly = channel.unary_unary(
                '/pb.membership.GapoMembership/JoinDirectly',
                request_serializer=membership__pb2.JoinDirectlyRequest.SerializeToString,
                response_deserializer=membership__pb2.JoinDirectlyResponse.FromString,
                )


class GapoMembershipServicer(object):
    """Hiện tại có chat và bên task đang sử dụng
    """

    def CreateCollabGroup(self, request, context):
        """CreateCollabGroup creates a collaborator-group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddMember(self, request, context):
        """AddMember adds a member to the collaborator-group with specific roles
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveMember(self, request, context):
        """RemoveMember removes a member from a collab-group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetMemberRole(self, request, context):
        """// SetMemberRole sets member roles in a collab-group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListMembers(self, request, context):
        """ListMembers
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCollabGroup(self, request, context):
        """GetCollabGroup get the collab-group detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListCollabGroups(self, request, context):
        """ListCollabGroups
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RequestJoin(self, request, context):
        """rpc CheckAccessPermission(CheckAccessPermissionRequest) returns (CheckAccessPermissionResponse) {}

        // GenerateInviteLink
        rpc GenerateInviteLink(GenerateInviteLinkRequest) returns (GenerateInviteLinkResponse) {}

        // DeleteInviteLink
        rpc DeleteInviteLink(DeleteInviteLinkRequest) returns (google.protobuf.Empty) {}

        // ListInviteLink list of invite links in collab-group
        rpc ListInviteLink(ListInviteLinkRequest) returns (ListInviteLinkResponse) {}

        RequestJoin send request join to collab-group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMemberByIDs(self, request, context):
        """UpdatePrivacy update privacy of collab-group
        rpc UpdatePrivacy(UpdatePrivacyRequest) returns (google.protobuf.Empty) {}

        // UpdateAutoAccept update config auto_accept of collab-group
        rpc UpdateAutoAccept(UpdateAutoAcceptRequest) returns (google.protobuf.Empty) {}

        GetMemberByIDs get member in collab-group by user ids
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCollabGroup(self, request, context):
        """UpdateCollabGroup update name of collab-group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IsMember(self, request, context):
        """rpc UnlinkOrgChart(UnlinkOrgChartRequest) returns (google.protobuf.Empty) {}

        rpc ListPendingMembers(ListPendingMembersRequest) returns (ListPendingMembersResponse) {}

        rpc ReviewPendingMember(ReviewPendingMemberRequest) returns (google.protobuf.Empty) {}

        IsMember check user is member of collab group or not
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAllMemberIDs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def JoinDirectly(self, request, context):
        """// api check feature enable/disable
        rpc CheckFeature(CheckFeatureRequest) returns (CheckFeatureResponse) {}

        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GapoMembershipServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateCollabGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCollabGroup,
                    request_deserializer=membership__pb2.CreateCollabGroupRequest.FromString,
                    response_serializer=membership__pb2.CreateCollabGroupResponse.SerializeToString,
            ),
            'AddMember': grpc.unary_unary_rpc_method_handler(
                    servicer.AddMember,
                    request_deserializer=membership__pb2.AddMemberRequest.FromString,
                    response_serializer=membership__pb2.AddMemberResponse.SerializeToString,
            ),
            'RemoveMember': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveMember,
                    request_deserializer=membership__pb2.RemoveMemberRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SetMemberRole': grpc.unary_unary_rpc_method_handler(
                    servicer.SetMemberRole,
                    request_deserializer=membership__pb2.SetMemberRoleRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListMembers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListMembers,
                    request_deserializer=membership__pb2.ListMembersRequest.FromString,
                    response_serializer=membership__pb2.ListMembersResponse.SerializeToString,
            ),
            'GetCollabGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCollabGroup,
                    request_deserializer=membership__pb2.GetCollabGroupRequest.FromString,
                    response_serializer=membership__pb2.GetCollabGroupResponse.SerializeToString,
            ),
            'ListCollabGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCollabGroups,
                    request_deserializer=membership__pb2.ListCollabGroupsRequest.FromString,
                    response_serializer=membership__pb2.ListCollabGroupsResponse.SerializeToString,
            ),
            'RequestJoin': grpc.unary_unary_rpc_method_handler(
                    servicer.RequestJoin,
                    request_deserializer=membership__pb2.RequestJoinRequest.FromString,
                    response_serializer=membership__pb2.RequestJoinResponse.SerializeToString,
            ),
            'GetMemberByIDs': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMemberByIDs,
                    request_deserializer=membership__pb2.GetMemberByIDsRequest.FromString,
                    response_serializer=membership__pb2.ListMembersResponse.SerializeToString,
            ),
            'UpdateCollabGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCollabGroup,
                    request_deserializer=membership__pb2.UpdateCollabGroupRequest.FromString,
                    response_serializer=membership__pb2.UpdateCollabGroupResponse.SerializeToString,
            ),
            'IsMember': grpc.unary_unary_rpc_method_handler(
                    servicer.IsMember,
                    request_deserializer=membership__pb2.IsMemberRequest.FromString,
                    response_serializer=membership__pb2.IsMemberResponse.SerializeToString,
            ),
            'ListAllMemberIDs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAllMemberIDs,
                    request_deserializer=membership__pb2.ListAllMemberIDsRequest.FromString,
                    response_serializer=membership__pb2.ListAllMemberIDsResponse.SerializeToString,
            ),
            'JoinDirectly': grpc.unary_unary_rpc_method_handler(
                    servicer.JoinDirectly,
                    request_deserializer=membership__pb2.JoinDirectlyRequest.FromString,
                    response_serializer=membership__pb2.JoinDirectlyResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'pb.membership.GapoMembership', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class GapoMembership(object):
    """Hiện tại có chat và bên task đang sử dụng
    """

    @staticmethod
    def CreateCollabGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/CreateCollabGroup',
            membership__pb2.CreateCollabGroupRequest.SerializeToString,
            membership__pb2.CreateCollabGroupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddMember(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/AddMember',
            membership__pb2.AddMemberRequest.SerializeToString,
            membership__pb2.AddMemberResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemoveMember(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/RemoveMember',
            membership__pb2.RemoveMemberRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetMemberRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/SetMemberRole',
            membership__pb2.SetMemberRoleRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListMembers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/ListMembers',
            membership__pb2.ListMembersRequest.SerializeToString,
            membership__pb2.ListMembersResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCollabGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/GetCollabGroup',
            membership__pb2.GetCollabGroupRequest.SerializeToString,
            membership__pb2.GetCollabGroupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListCollabGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/ListCollabGroups',
            membership__pb2.ListCollabGroupsRequest.SerializeToString,
            membership__pb2.ListCollabGroupsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RequestJoin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/RequestJoin',
            membership__pb2.RequestJoinRequest.SerializeToString,
            membership__pb2.RequestJoinResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMemberByIDs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/GetMemberByIDs',
            membership__pb2.GetMemberByIDsRequest.SerializeToString,
            membership__pb2.ListMembersResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateCollabGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/UpdateCollabGroup',
            membership__pb2.UpdateCollabGroupRequest.SerializeToString,
            membership__pb2.UpdateCollabGroupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IsMember(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/IsMember',
            membership__pb2.IsMemberRequest.SerializeToString,
            membership__pb2.IsMemberResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListAllMemberIDs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/ListAllMemberIDs',
            membership__pb2.ListAllMemberIDsRequest.SerializeToString,
            membership__pb2.ListAllMemberIDsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def JoinDirectly(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/pb.membership.GapoMembership/JoinDirectly',
            membership__pb2.JoinDirectlyRequest.SerializeToString,
            membership__pb2.JoinDirectlyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
