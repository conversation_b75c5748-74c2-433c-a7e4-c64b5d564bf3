"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import abc
import google.protobuf.empty_pb2
import grpc
from . import membership_pb2

class GapoMembershipStub:
    """Hiện tại có chat và bên task đang sử dụng"""

    def __init__(self, channel: grpc.Channel) -> None: ...
    CreateCollabGroup: grpc.UnaryUnaryMultiCallable[
        membership_pb2.CreateCollabGroupRequest,
        membership_pb2.CreateCollabGroupResponse,
    ]
    """CreateCollabGroup creates a collaborator-group"""
    AddMember: grpc.UnaryUnaryMultiCallable[
        membership_pb2.AddMemberRequest,
        membership_pb2.AddMemberResponse,
    ]
    """AddMember adds a member to the collaborator-group with specific roles"""
    RemoveMember: grpc.UnaryUnaryMultiCallable[
        membership_pb2.RemoveMemberRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """RemoveMember removes a member from a collab-group"""
    SetMemberRole: grpc.UnaryUnaryMultiCallable[
        membership_pb2.SetMemberRoleRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """// SetMemberRole sets member roles in a collab-group"""
    ListMembers: grpc.UnaryUnaryMultiCallable[
        membership_pb2.ListMembersRequest,
        membership_pb2.ListMembersResponse,
    ]
    """ListMembers"""
    GetCollabGroup: grpc.UnaryUnaryMultiCallable[
        membership_pb2.GetCollabGroupRequest,
        membership_pb2.GetCollabGroupResponse,
    ]
    """GetCollabGroup get the collab-group detail"""
    ListCollabGroups: grpc.UnaryUnaryMultiCallable[
        membership_pb2.ListCollabGroupsRequest,
        membership_pb2.ListCollabGroupsResponse,
    ]
    """ListCollabGroups"""
    RequestJoin: grpc.UnaryUnaryMultiCallable[
        membership_pb2.RequestJoinRequest,
        membership_pb2.RequestJoinResponse,
    ]
    """// ListInviteLink list of invite links in collab-group
    rpc ListInviteLink(ListInviteLinkRequest) returns (ListInviteLinkResponse) {}

    RequestJoin send request join to collab-group
    """
    GetMemberByIDs: grpc.UnaryUnaryMultiCallable[
        membership_pb2.GetMemberByIDsRequest,
        membership_pb2.ListMembersResponse,
    ]
    """// UpdateAutoAccept update config auto_accept of collab-group
    rpc UpdateAutoAccept(UpdateAutoAcceptRequest) returns (google.protobuf.Empty) {}

    GetMemberByIDs get member in collab-group by user ids
    """
    UpdateCollabGroup: grpc.UnaryUnaryMultiCallable[
        membership_pb2.UpdateCollabGroupRequest,
        membership_pb2.UpdateCollabGroupResponse,
    ]
    """UpdateCollabGroup update name of collab-group"""
    IsMember: grpc.UnaryUnaryMultiCallable[
        membership_pb2.IsMemberRequest,
        membership_pb2.IsMemberResponse,
    ]
    """rpc ReviewPendingMember(ReviewPendingMemberRequest) returns (google.protobuf.Empty) {}

    IsMember check user is member of collab group or not
    """
    ListAllMemberIDs: grpc.UnaryUnaryMultiCallable[
        membership_pb2.ListAllMemberIDsRequest,
        membership_pb2.ListAllMemberIDsResponse,
    ]
    JoinDirectly: grpc.UnaryUnaryMultiCallable[
        membership_pb2.JoinDirectlyRequest,
        membership_pb2.JoinDirectlyResponse,
    ]
    """// api check feature enable/disable
    rpc CheckFeature(CheckFeatureRequest) returns (CheckFeatureResponse) {}
    """

class GapoMembershipServicer(metaclass=abc.ABCMeta):
    """Hiện tại có chat và bên task đang sử dụng"""

    @abc.abstractmethod
    def CreateCollabGroup(
        self,
        request: membership_pb2.CreateCollabGroupRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.CreateCollabGroupResponse:
        """CreateCollabGroup creates a collaborator-group"""
    @abc.abstractmethod
    def AddMember(
        self,
        request: membership_pb2.AddMemberRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.AddMemberResponse:
        """AddMember adds a member to the collaborator-group with specific roles"""
    @abc.abstractmethod
    def RemoveMember(
        self,
        request: membership_pb2.RemoveMemberRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """RemoveMember removes a member from a collab-group"""
    @abc.abstractmethod
    def SetMemberRole(
        self,
        request: membership_pb2.SetMemberRoleRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """// SetMemberRole sets member roles in a collab-group"""
    @abc.abstractmethod
    def ListMembers(
        self,
        request: membership_pb2.ListMembersRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.ListMembersResponse:
        """ListMembers"""
    @abc.abstractmethod
    def GetCollabGroup(
        self,
        request: membership_pb2.GetCollabGroupRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.GetCollabGroupResponse:
        """GetCollabGroup get the collab-group detail"""
    @abc.abstractmethod
    def ListCollabGroups(
        self,
        request: membership_pb2.ListCollabGroupsRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.ListCollabGroupsResponse:
        """ListCollabGroups"""
    @abc.abstractmethod
    def RequestJoin(
        self,
        request: membership_pb2.RequestJoinRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.RequestJoinResponse:
        """// ListInviteLink list of invite links in collab-group
        rpc ListInviteLink(ListInviteLinkRequest) returns (ListInviteLinkResponse) {}

        RequestJoin send request join to collab-group
        """
    @abc.abstractmethod
    def GetMemberByIDs(
        self,
        request: membership_pb2.GetMemberByIDsRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.ListMembersResponse:
        """// UpdateAutoAccept update config auto_accept of collab-group
        rpc UpdateAutoAccept(UpdateAutoAcceptRequest) returns (google.protobuf.Empty) {}

        GetMemberByIDs get member in collab-group by user ids
        """
    @abc.abstractmethod
    def UpdateCollabGroup(
        self,
        request: membership_pb2.UpdateCollabGroupRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.UpdateCollabGroupResponse:
        """UpdateCollabGroup update name of collab-group"""
    @abc.abstractmethod
    def IsMember(
        self,
        request: membership_pb2.IsMemberRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.IsMemberResponse:
        """rpc ReviewPendingMember(ReviewPendingMemberRequest) returns (google.protobuf.Empty) {}

        IsMember check user is member of collab group or not
        """
    @abc.abstractmethod
    def ListAllMemberIDs(
        self,
        request: membership_pb2.ListAllMemberIDsRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.ListAllMemberIDsResponse: ...
    @abc.abstractmethod
    def JoinDirectly(
        self,
        request: membership_pb2.JoinDirectlyRequest,
        context: grpc.ServicerContext,
    ) -> membership_pb2.JoinDirectlyResponse:
        """// api check feature enable/disable
        rpc CheckFeature(CheckFeatureRequest) returns (CheckFeatureResponse) {}
        """

def add_GapoMembershipServicer_to_server(servicer: GapoMembershipServicer, server: grpc.Server) -> None: ...
