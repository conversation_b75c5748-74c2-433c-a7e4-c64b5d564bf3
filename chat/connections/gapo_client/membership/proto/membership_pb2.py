# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: membership.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10membership.proto\x12\rpb.membership\x1a\x19google/protobuf/any.proto\x1a\x1bgoogle/protobuf/empty.proto\"^\n\x08Settings\x12\x11\n\tdiscovery\x18\x01 \x01(\x08\x12\x0e\n\x06public\x18\x02 \x01(\x08\x12\x14\n\x0cneed_approve\x18\x03 \x01(\x08\x12\x19\n\x11share_public_link\x18\x04 \x01(\x08\"\x89\x02\n\x18\x43reateCollabGroupRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12.\n\x0b\x64\x61ta_source\x18\x03 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x11\n\tcaller_id\x18\x04 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x05 \x01(\x04\x12\x15\n\rdepartment_id\x18\x06 \x01(\t\x12\n\n\x02id\x18\t \x01(\t\x12\x13\n\x0b\x65mpty_owner\x18\n \x01(\x08\x12)\n\x08Settings\x18\x0b \x01(\x0b\x32\x17.pb.membership.Settings\x12\x0e\n\x06\x61vatar\x18\x0c \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\r \x01(\t\"\'\n\x19\x43reateCollabGroupResponse\x12\n\n\x02id\x18\x01 \x01(\t\"\xa7\x01\n\x10\x41\x64\x64MemberRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\x04\x12\x10\n\x08role_ids\x18\x03 \x03(\x05\x12.\n\x0b\x64\x61ta_source\x18\x04 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x11\n\tcaller_id\x18\x05 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x06 \x01(\x04\"\x1f\n\x11\x41\x64\x64MemberResponse\x12\n\n\x02id\x18\x01 \x01(\x04\"\x98\x01\n\x13RemoveMemberRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\x04\x12.\n\x0b\x64\x61ta_source\x18\x03 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x11\n\tcaller_id\x18\x04 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x05 \x01(\x04\"\xab\x01\n\x14SetMemberRoleRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\x04\x12\x10\n\x08role_ids\x18\x03 \x03(\x05\x12.\n\x0b\x64\x61ta_source\x18\x04 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x11\n\tcaller_id\x18\x05 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x06 \x01(\x04\"\xf0\x01\n\x12ListMembersRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x16\n\x0estarting_after\x18\x04 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x05 \x01(\x04\x12\x12\n\nfeature_id\x18\x06 \x01(\x05\x12\x34\n\x04mode\x18\x07 \x01(\x0e\x32&.pb.membership.ListMembersRequest.Mode\"\'\n\x04Mode\x12\x06\n\x02ID\x10\x00\x12\x08\n\x04Name\x10\x01\x12\r\n\tWorkspace\x10\x02\"\xdd\x01\n\x06Member\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12\x0e\n\x06\x61vatar\x18\x03 \x01(\t\x12\x1c\n\x14\x61vatar_thumb_pattern\x18\x04 \x01(\t\x12\r\n\x05\x63over\x18\x05 \x01(\t\x12\x1b\n\x13\x63over_thumb_pattern\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x10\n\x08role_ids\x18\x08 \x03(\x05\x12\x12\n\ncreated_at\x18\t \x01(\x03\x12\x12\n\nupdated_at\x18\n \x01(\x03\x12\x0f\n\x07user_id\x18\x0b \x01(\x04\"\x14\n\x04Link\x12\x0c\n\x04next\x18\x01 \x01(\t\"]\n\x13ListMembersResponse\x12#\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x15.pb.membership.Member\x12!\n\x04link\x18\x02 \x01(\x0b\x32\x13.pb.membership.Link\"Y\n\x15GetCollabGroupRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\"\xe9\x01\n\x0b\x43ollabGroup\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x12\n\ncreated_at\x18\x04 \x01(\x03\x12\x12\n\nupdated_at\x18\x05 \x01(\x03\x12%\n\x07\x63ontext\x18\x06 \x01(\x0b\x32\x14.google.protobuf.Any\x12)\n\x08Settings\x18\t \x01(\x0b\x32\x17.pb.membership.Settings\x12\x0e\n\x06\x61vatar\x18\n \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0b \x01(\t\"B\n\x16GetCollabGroupResponse\x12(\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x1a.pb.membership.CollabGroup\"i\n\x17ListCollabGroupsRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x16\n\x0estarting_after\x18\x04 \x01(\t\"g\n\x18ListCollabGroupsResponse\x12(\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1a.pb.membership.CollabGroup\x12!\n\x04link\x18\x02 \x01(\x0b\x32\x13.pb.membership.Link\"}\n\x1c\x43heckAccessPermissionRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\x12\x10\n\x08resource\x18\x04 \x01(\t\x12\x11\n\tfeatureID\x18\x05 \x01(\x05\".\n\x1d\x43heckAccessPermissionResponse\x12\r\n\x05\x61llow\x18\x01 \x01(\x08\"\x7f\n\x19GenerateInviteLinkRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\r\n\x05roles\x18\x04 \x03(\x05\x12\x11\n\texpire_at\x18\x05 \x01(\x04\"0\n\x1aGenerateInviteLinkResponse\x12\x12\n\ninvite_key\x18\x01 \x01(\t\"\xab\x01\n\x12RequestJoinRequest\x12\x12\n\ninvite_key\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x04 \x01(\t\x12\x11\n\tcaller_id\x18\x05 \x01(\x04\x12.\n\x0b\x64\x61ta_source\x18\x06 \x01(\x0e\x32\x19.pb.membership.DataSource\"\x84\x01\n\x13RequestJoinResponse\x12\x39\n\x06status\x18\x01 \x01(\x0e\x32).pb.membership.RequestJoinResponse.Status\"2\n\x06Status\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07Pending\x10\x01\x12\n\n\x06Member\x10\x02\"\xb1\x01\n\x14UpdatePrivacyRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\'\n\x07privacy\x18\x04 \x01(\x0e\x32\x16.pb.membership.Privacy\x12.\n\x0b\x64\x61ta_source\x18\x05 \x01(\x0e\x32\x19.pb.membership.DataSource\"\xa0\x01\n\x17UpdateAutoAcceptRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x61uto_accept\x18\x04 \x01(\x08\x12.\n\x0b\x64\x61ta_source\x18\x05 \x01(\x0e\x32\x19.pb.membership.DataSource\"o\n\x17\x44\x65leteInviteLinkRequest\x12\x14\n\x0cinvite_token\x18\x01 \x01(\t\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x0f\n\x07\x63\x61ll_id\x18\x03 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x04 \x01(\x04\"\xb0\x01\n\x15ListInviteLinkRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x37\n\x04mode\x18\x04 \x01(\x0e\x32).pb.membership.ListInviteLinkRequest.Mode\"\x1c\n\x04Mode\x12\n\n\x06SIMPLE\x10\x00\x12\x08\n\x04\x46ULL\x10\x02\"\x96\x02\n\x12InviteLinkResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07\x63reator\x18\x02 \x01(\x04\x12\x17\n\x0f\x63ollab_group_id\x18\x03 \x01(\t\x12\r\n\x05roles\x18\x04 \x03(\x05\x12\x13\n\x0b\x65xpire_date\x18\x05 \x01(\x03\x12\x12\n\ncreated_at\x18\x06 \x01(\x03\x12\x14\n\x0cworkspace_id\x18\x07 \x01(\x04\x12\x14\n\x0c\x64isplay_name\x18\x08 \x01(\t\x12\x0e\n\x06\x61vatar\x18\t \x01(\t\x12\x1c\n\x14\x61vatar_thumb_pattern\x18\n \x01(\t\x12\r\n\x05\x63over\x18\x0b \x01(\t\x12\x1b\n\x13\x63over_thumb_pattern\x18\x0c \x01(\t\x12\x0c\n\x04link\x18\r \x01(\t\"I\n\x16ListInviteLinkResponse\x12/\n\x04\x64\x61te\x18\x01 \x03(\x0b\x32!.pb.membership.InviteLinkResponse\"k\n\x15GetMemberByIDsRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x10\n\x08user_ids\x18\x04 \x03(\x04\"\xa2\x02\n\x18UpdateCollabGroupRequest\x12\x11\n\x04name\x18\x01 \x01(\tH\x00\x88\x01\x01\x12.\n\x0b\x64\x61ta_source\x18\x02 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x11\n\tcaller_id\x18\x03 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x04 \x01(\x04\x12\n\n\x02id\x18\x05 \x01(\t\x12.\n\x08settings\x18\x06 \x01(\x0b\x32\x17.pb.membership.SettingsH\x01\x88\x01\x01\x12\x13\n\x06\x61vatar\x18\x07 \x01(\tH\x02\x88\x01\x01\x12\x18\n\x0b\x64\x65scription\x18\x08 \x01(\tH\x03\x88\x01\x01\x42\x07\n\x05_nameB\x0b\n\t_settingsB\t\n\x07_avatarB\x0e\n\x0c_description\"E\n\x19UpdateCollabGroupResponse\x12(\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x1a.pb.membership.CollabGroup\"\x89\x01\n\x15UnlinkOrgChartRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12.\n\x0b\x64\x61ta_source\x18\x04 \x01(\x0e\x32\x19.pb.membership.DataSource\"\xea\x01\n\x19ListPendingMembersRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12.\n\x0b\x64\x61ta_source\x18\x04 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x34\n\x04mode\x18\x05 \x01(\x0e\x32&.pb.membership.ListMembersRequest.Mode\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x16\n\x0estarting_after\x18\x07 \x01(\t\"d\n\x1aListPendingMembersResponse\x12#\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x15.pb.membership.Member\x12!\n\x04link\x18\x02 \x01(\x0b\x32\x13.pb.membership.Link\"\xaf\x01\n\x1aReviewPendingMemberRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\x05\".\n\x06\x41\x63tion\x12\x0b\n\x07Unknown\x10\x00\x12\x0b\n\x07\x41pprove\x10\x01\x12\n\n\x06Reject\x10\x02\"<\n\x0fIsMemberRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x10\n\x08user_ids\x18\x04 \x03(\x04\"x\n\x10IsMemberResponse\x12\x37\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32).pb.membership.IsMemberResponse.DataEntry\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"2\n\x17ListAllMemberIDsRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\"(\n\x18ListAllMemberIDsResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x04\"?\n\x13\x43heckFeatureRequest\x12\x0f\n\x07\x66\x65\x61ture\x18\x01 \x01(\x05\x12\x17\n\x0f\x63ollab_group_id\x18\x02 \x01(\t\"&\n\x14\x43heckFeatureResponse\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\"\x85\x01\n\x13JoinDirectlyRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\x04\x12.\n\x0b\x64\x61ta_source\x18\x04 \x01(\x0e\x32\x19.pb.membership.DataSource\x12\x14\n\x0cworkspace_id\x18\x06 \x01(\x04\"\"\n\x14JoinDirectlyResponse\x12\n\n\x02id\x18\x01 \x01(\x04\"^\n\x1a\x41rchivedCollabGroupRequest\x12\x17\n\x0f\x63ollab_group_id\x18\x01 \x01(\t\x12\x11\n\tcaller_id\x18\x02 \x01(\x04\x12\x14\n\x0cworkspace_id\x18\x03 \x01(\x04*\xf6\x03\n\nDataSource\x12\x1b\n\x17\x44\x41TA_SOURCE_UNSPECIFIED\x10\x00\x12\x16\n\x12\x44\x41TA_SOURCE_PC_WEB\x10\x01\x12\x19\n\x15\x44\x41TA_SOURCE_PC_MOBILE\x10\x02\x12\x13\n\x0f\x44\x41TA_SOURCE_IOS\x10\x03\x12\x17\n\x13\x44\x41TA_SOURCE_ANDROID\x10\x04\x12\x1b\n\x17\x44\x41TA_SOURCE_ORG_SERVICE\x10\x05\x12\x1c\n\x18\x44\x41TA_SOURCE_CHAT_SERVICE\x10\x06\x12\x1c\n\x18\x44\x41TA_SOURCE_TASK_SERVICE\x10\x07\x12\x1c\n\x18\x44\x41TA_SOURCE_MEET_SERVICE\x10\x08\x12\x1b\n\x17\x44\x41TA_SOURCE_CHAT_MANUAL\x10\t\x12\x19\n\x15\x44\x41TA_SOURCE_CHAT_FILE\x10\n\x12\x1b\n\x17\x44\x41TA_SOURCE_CHAT_THREAD\x10\x0b\x12\x1a\n\x16\x44\x41TA_SOURCE_CHAT_TITLE\x10\x0c\x12\x1f\n\x1b\x44\x41TA_SOURCE_CHAT_DEPARTMENT\x10\r\x12\x1a\n\x16\x44\x41TA_SOURCE_CHAT_GROUP\x10\x0e\x12$\n DATA_SOURCE_CHAT_JOIN_DEPARTMENT\x10\x0f\x12\x1f\n\x1b\x44\x41TA_SOURCE_CHAT_JOIN_GROUP\x10\x10*D\n\x07Privacy\x12\x12\n\x0ePRIVACY_PUBLIC\x10\x00\x12\x11\n\rPRIVACY_CLOSE\x10\x01\x12\x12\n\x0ePRIVACY_SECRET\x10\x02\x32\xbb\t\n\x0eGapoMembership\x12h\n\x11\x43reateCollabGroup\x12\'.pb.membership.CreateCollabGroupRequest\x1a(.pb.membership.CreateCollabGroupResponse\"\x00\x12P\n\tAddMember\x12\x1f.pb.membership.AddMemberRequest\x1a .pb.membership.AddMemberResponse\"\x00\x12L\n\x0cRemoveMember\x12\".pb.membership.RemoveMemberRequest\x1a\x16.google.protobuf.Empty\"\x00\x12N\n\rSetMemberRole\x12#.pb.membership.SetMemberRoleRequest\x1a\x16.google.protobuf.Empty\"\x00\x12V\n\x0bListMembers\x12!.pb.membership.ListMembersRequest\x1a\".pb.membership.ListMembersResponse\"\x00\x12_\n\x0eGetCollabGroup\x12$.pb.membership.GetCollabGroupRequest\x1a%.pb.membership.GetCollabGroupResponse\"\x00\x12\x65\n\x10ListCollabGroups\x12&.pb.membership.ListCollabGroupsRequest\x1a\'.pb.membership.ListCollabGroupsResponse\"\x00\x12V\n\x0bRequestJoin\x12!.pb.membership.RequestJoinRequest\x1a\".pb.membership.RequestJoinResponse\"\x00\x12\\\n\x0eGetMemberByIDs\x12$.pb.membership.GetMemberByIDsRequest\x1a\".pb.membership.ListMembersResponse\"\x00\x12h\n\x11UpdateCollabGroup\x12\'.pb.membership.UpdateCollabGroupRequest\x1a(.pb.membership.UpdateCollabGroupResponse\"\x00\x12M\n\x08IsMember\x12\x1e.pb.membership.IsMemberRequest\x1a\x1f.pb.membership.IsMemberResponse\"\x00\x12\x65\n\x10ListAllMemberIDs\x12&.pb.membership.ListAllMemberIDsRequest\x1a\'.pb.membership.ListAllMemberIDsResponse\"\x00\x12Y\n\x0cJoinDirectly\x12\".pb.membership.JoinDirectlyRequest\x1a#.pb.membership.JoinDirectlyResponse\"\x00\x42\x0fZ\rpb/membershipb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'membership_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\rpb/membership'
  _ISMEMBERRESPONSE_DATAENTRY._options = None
  _ISMEMBERRESPONSE_DATAENTRY._serialized_options = b'8\001'
  _DATASOURCE._serialized_start=5655
  _DATASOURCE._serialized_end=6157
  _PRIVACY._serialized_start=6159
  _PRIVACY._serialized_end=6227
  _SETTINGS._serialized_start=91
  _SETTINGS._serialized_end=185
  _CREATECOLLABGROUPREQUEST._serialized_start=188
  _CREATECOLLABGROUPREQUEST._serialized_end=453
  _CREATECOLLABGROUPRESPONSE._serialized_start=455
  _CREATECOLLABGROUPRESPONSE._serialized_end=494
  _ADDMEMBERREQUEST._serialized_start=497
  _ADDMEMBERREQUEST._serialized_end=664
  _ADDMEMBERRESPONSE._serialized_start=666
  _ADDMEMBERRESPONSE._serialized_end=697
  _REMOVEMEMBERREQUEST._serialized_start=700
  _REMOVEMEMBERREQUEST._serialized_end=852
  _SETMEMBERROLEREQUEST._serialized_start=855
  _SETMEMBERROLEREQUEST._serialized_end=1026
  _LISTMEMBERSREQUEST._serialized_start=1029
  _LISTMEMBERSREQUEST._serialized_end=1269
  _LISTMEMBERSREQUEST_MODE._serialized_start=1230
  _LISTMEMBERSREQUEST_MODE._serialized_end=1269
  _MEMBER._serialized_start=1272
  _MEMBER._serialized_end=1493
  _LINK._serialized_start=1495
  _LINK._serialized_end=1515
  _LISTMEMBERSRESPONSE._serialized_start=1517
  _LISTMEMBERSRESPONSE._serialized_end=1610
  _GETCOLLABGROUPREQUEST._serialized_start=1612
  _GETCOLLABGROUPREQUEST._serialized_end=1701
  _COLLABGROUP._serialized_start=1704
  _COLLABGROUP._serialized_end=1937
  _GETCOLLABGROUPRESPONSE._serialized_start=1939
  _GETCOLLABGROUPRESPONSE._serialized_end=2005
  _LISTCOLLABGROUPSREQUEST._serialized_start=2007
  _LISTCOLLABGROUPSREQUEST._serialized_end=2112
  _LISTCOLLABGROUPSRESPONSE._serialized_start=2114
  _LISTCOLLABGROUPSRESPONSE._serialized_end=2217
  _CHECKACCESSPERMISSIONREQUEST._serialized_start=2219
  _CHECKACCESSPERMISSIONREQUEST._serialized_end=2344
  _CHECKACCESSPERMISSIONRESPONSE._serialized_start=2346
  _CHECKACCESSPERMISSIONRESPONSE._serialized_end=2392
  _GENERATEINVITELINKREQUEST._serialized_start=2394
  _GENERATEINVITELINKREQUEST._serialized_end=2521
  _GENERATEINVITELINKRESPONSE._serialized_start=2523
  _GENERATEINVITELINKRESPONSE._serialized_end=2571
  _REQUESTJOINREQUEST._serialized_start=2574
  _REQUESTJOINREQUEST._serialized_end=2745
  _REQUESTJOINRESPONSE._serialized_start=2748
  _REQUESTJOINRESPONSE._serialized_end=2880
  _REQUESTJOINRESPONSE_STATUS._serialized_start=2830
  _REQUESTJOINRESPONSE_STATUS._serialized_end=2880
  _UPDATEPRIVACYREQUEST._serialized_start=2883
  _UPDATEPRIVACYREQUEST._serialized_end=3060
  _UPDATEAUTOACCEPTREQUEST._serialized_start=3063
  _UPDATEAUTOACCEPTREQUEST._serialized_end=3223
  _DELETEINVITELINKREQUEST._serialized_start=3225
  _DELETEINVITELINKREQUEST._serialized_end=3336
  _LISTINVITELINKREQUEST._serialized_start=3339
  _LISTINVITELINKREQUEST._serialized_end=3515
  _LISTINVITELINKREQUEST_MODE._serialized_start=3487
  _LISTINVITELINKREQUEST_MODE._serialized_end=3515
  _INVITELINKRESPONSE._serialized_start=3518
  _INVITELINKRESPONSE._serialized_end=3796
  _LISTINVITELINKRESPONSE._serialized_start=3798
  _LISTINVITELINKRESPONSE._serialized_end=3871
  _GETMEMBERBYIDSREQUEST._serialized_start=3873
  _GETMEMBERBYIDSREQUEST._serialized_end=3980
  _UPDATECOLLABGROUPREQUEST._serialized_start=3983
  _UPDATECOLLABGROUPREQUEST._serialized_end=4273
  _UPDATECOLLABGROUPRESPONSE._serialized_start=4275
  _UPDATECOLLABGROUPRESPONSE._serialized_end=4344
  _UNLINKORGCHARTREQUEST._serialized_start=4347
  _UNLINKORGCHARTREQUEST._serialized_end=4484
  _LISTPENDINGMEMBERSREQUEST._serialized_start=4487
  _LISTPENDINGMEMBERSREQUEST._serialized_end=4721
  _LISTPENDINGMEMBERSRESPONSE._serialized_start=4723
  _LISTPENDINGMEMBERSRESPONSE._serialized_end=4823
  _REVIEWPENDINGMEMBERREQUEST._serialized_start=4826
  _REVIEWPENDINGMEMBERREQUEST._serialized_end=5001
  _REVIEWPENDINGMEMBERREQUEST_ACTION._serialized_start=4955
  _REVIEWPENDINGMEMBERREQUEST_ACTION._serialized_end=5001
  _ISMEMBERREQUEST._serialized_start=5003
  _ISMEMBERREQUEST._serialized_end=5063
  _ISMEMBERRESPONSE._serialized_start=5065
  _ISMEMBERRESPONSE._serialized_end=5185
  _ISMEMBERRESPONSE_DATAENTRY._serialized_start=5142
  _ISMEMBERRESPONSE_DATAENTRY._serialized_end=5185
  _LISTALLMEMBERIDSREQUEST._serialized_start=5187
  _LISTALLMEMBERIDSREQUEST._serialized_end=5237
  _LISTALLMEMBERIDSRESPONSE._serialized_start=5239
  _LISTALLMEMBERIDSRESPONSE._serialized_end=5279
  _CHECKFEATUREREQUEST._serialized_start=5281
  _CHECKFEATUREREQUEST._serialized_end=5344
  _CHECKFEATURERESPONSE._serialized_start=5346
  _CHECKFEATURERESPONSE._serialized_end=5384
  _JOINDIRECTLYREQUEST._serialized_start=5387
  _JOINDIRECTLYREQUEST._serialized_end=5520
  _JOINDIRECTLYRESPONSE._serialized_start=5522
  _JOINDIRECTLYRESPONSE._serialized_end=5556
  _ARCHIVEDCOLLABGROUPREQUEST._serialized_start=5558
  _ARCHIVEDCOLLABGROUPREQUEST._serialized_end=5652
  _GAPOMEMBERSHIP._serialized_start=6230
  _GAPOMEMBERSHIP._serialized_end=7441
# @@protoc_insertion_point(module_scope)
