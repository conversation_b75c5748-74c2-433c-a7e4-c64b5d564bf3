"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import collections.abc
import google.protobuf.any_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _DataSource:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DataSourceEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DataSource.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DATA_SOURCE_UNSPECIFIED: _DataSource.ValueType  # 0
    DATA_SOURCE_PC_WEB: _DataSource.ValueType  # 1
    DATA_SOURCE_PC_MOBILE: _DataSource.ValueType  # 2
    DATA_SOURCE_IOS: _DataSource.ValueType  # 3
    DATA_SOURCE_ANDROID: _DataSource.ValueType  # 4
    DATA_SOURCE_ORG_SERVICE: _DataSource.ValueType  # 5
    DATA_SOURCE_CHAT_SERVICE: _DataSource.ValueType  # 6
    DATA_SOURCE_TASK_SERVICE: _DataSource.ValueType  # 7
    DATA_SOURCE_MEET_SERVICE: _DataSource.ValueType  # 8
    DATA_SOURCE_CHAT_MANUAL: _DataSource.ValueType  # 9
    DATA_SOURCE_CHAT_FILE: _DataSource.ValueType  # 10
    DATA_SOURCE_CHAT_THREAD: _DataSource.ValueType  # 11
    DATA_SOURCE_CHAT_TITLE: _DataSource.ValueType  # 12
    DATA_SOURCE_CHAT_DEPARTMENT: _DataSource.ValueType  # 13
    DATA_SOURCE_CHAT_GROUP: _DataSource.ValueType  # 14
    DATA_SOURCE_CHAT_JOIN_DEPARTMENT: _DataSource.ValueType  # 15
    DATA_SOURCE_CHAT_JOIN_GROUP: _DataSource.ValueType  # 16

class DataSource(_DataSource, metaclass=_DataSourceEnumTypeWrapper):
    """DataSource the source call to membership service"""

DATA_SOURCE_UNSPECIFIED: DataSource.ValueType  # 0
DATA_SOURCE_PC_WEB: DataSource.ValueType  # 1
DATA_SOURCE_PC_MOBILE: DataSource.ValueType  # 2
DATA_SOURCE_IOS: DataSource.ValueType  # 3
DATA_SOURCE_ANDROID: DataSource.ValueType  # 4
DATA_SOURCE_ORG_SERVICE: DataSource.ValueType  # 5
DATA_SOURCE_CHAT_SERVICE: DataSource.ValueType  # 6
DATA_SOURCE_TASK_SERVICE: DataSource.ValueType  # 7
DATA_SOURCE_MEET_SERVICE: DataSource.ValueType  # 8
DATA_SOURCE_CHAT_MANUAL: DataSource.ValueType  # 9
DATA_SOURCE_CHAT_FILE: DataSource.ValueType  # 10
DATA_SOURCE_CHAT_THREAD: DataSource.ValueType  # 11
DATA_SOURCE_CHAT_TITLE: DataSource.ValueType  # 12
DATA_SOURCE_CHAT_DEPARTMENT: DataSource.ValueType  # 13
DATA_SOURCE_CHAT_GROUP: DataSource.ValueType  # 14
DATA_SOURCE_CHAT_JOIN_DEPARTMENT: DataSource.ValueType  # 15
DATA_SOURCE_CHAT_JOIN_GROUP: DataSource.ValueType  # 16
global___DataSource = DataSource

class _Privacy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _PrivacyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_Privacy.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PRIVACY_PUBLIC: _Privacy.ValueType  # 0
    PRIVACY_CLOSE: _Privacy.ValueType  # 1
    PRIVACY_SECRET: _Privacy.ValueType  # 2

class Privacy(_Privacy, metaclass=_PrivacyEnumTypeWrapper):
    """Privacy DISCARD. privacy of collab group"""

PRIVACY_PUBLIC: Privacy.ValueType  # 0
PRIVACY_CLOSE: Privacy.ValueType  # 1
PRIVACY_SECRET: Privacy.ValueType  # 2
global___Privacy = Privacy

@typing_extensions.final
class Settings(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISCOVERY_FIELD_NUMBER: builtins.int
    PUBLIC_FIELD_NUMBER: builtins.int
    NEED_APPROVE_FIELD_NUMBER: builtins.int
    SHARE_PUBLIC_LINK_FIELD_NUMBER: builtins.int
    discovery: builtins.bool
    """discovery can find the collab-group, listed"""
    public: builtins.bool
    """public can view content"""
    need_approve: builtins.bool
    """need_approve the manager needs to approve the request to join"""
    share_public_link: builtins.bool
    def __init__(
        self,
        *,
        discovery: builtins.bool = ...,
        public: builtins.bool = ...,
        need_approve: builtins.bool = ...,
        share_public_link: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["discovery", b"discovery", "need_approve", b"need_approve", "public", b"public", "share_public_link", b"share_public_link"]) -> None: ...

global___Settings = Settings

@typing_extensions.final
class CreateCollabGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    DEPARTMENT_ID_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    EMPTY_OWNER_FIELD_NUMBER: builtins.int
    SETTINGS_FIELD_NUMBER: builtins.int
    AVATAR_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    name: builtins.str
    """name of collab-group"""
    data_source: global___DataSource.ValueType
    caller_id: builtins.int
    """caller_id the id of user create the collab-group"""
    workspace_id: builtins.int
    """workspace_id the id of workspace of user create the collab-group"""
    department_id: builtins.str
    """department_id the id of department for case create collab-group from orgChart."""
    id: builtins.str
    """privacy the privacy of collab-group. DISCARD
     Privacy privacy = 7;
    auto_accept the config allow auto accept the user join to collab-group. DISCARD
     bool auto_accept = 8;
    id the id of collab-group. this field to support for case use id from other service as collab-group id
    """
    empty_owner: builtins.bool
    """empty_owner if true will not add caller_id as owner of collab-group"""
    @property
    def Settings(self) -> global___Settings: ...
    avatar: builtins.str
    description: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        data_source: global___DataSource.ValueType = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        department_id: builtins.str = ...,
        id: builtins.str = ...,
        empty_owner: builtins.bool = ...,
        Settings: global___Settings | None = ...,
        avatar: builtins.str = ...,
        description: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["Settings", b"Settings"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["Settings", b"Settings", "avatar", b"avatar", "caller_id", b"caller_id", "data_source", b"data_source", "department_id", b"department_id", "description", b"description", "empty_owner", b"empty_owner", "id", b"id", "name", b"name", "workspace_id", b"workspace_id"]) -> None: ...

global___CreateCollabGroupRequest = CreateCollabGroupRequest

@typing_extensions.final
class CreateCollabGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    id: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["id", b"id"]) -> None: ...

global___CreateCollabGroupResponse = CreateCollabGroupResponse

@typing_extensions.final
class AddMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    ROLE_IDS_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    user_id: builtins.int
    @property
    def role_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    data_source: global___DataSource.ValueType
    caller_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        user_id: builtins.int = ...,
        role_ids: collections.abc.Iterable[builtins.int] | None = ...,
        data_source: global___DataSource.ValueType = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "role_ids", b"role_ids", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___AddMemberRequest = AddMemberRequest

@typing_extensions.final
class AddMemberResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    id: builtins.int
    def __init__(
        self,
        *,
        id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["id", b"id"]) -> None: ...

global___AddMemberResponse = AddMemberResponse

@typing_extensions.final
class RemoveMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    user_id: builtins.int
    data_source: global___DataSource.ValueType
    caller_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        user_id: builtins.int = ...,
        data_source: global___DataSource.ValueType = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___RemoveMemberRequest = RemoveMemberRequest

@typing_extensions.final
class SetMemberRoleRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    ROLE_IDS_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    user_id: builtins.int
    @property
    def role_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    data_source: global___DataSource.ValueType
    caller_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        user_id: builtins.int = ...,
        role_ids: collections.abc.Iterable[builtins.int] | None = ...,
        data_source: global___DataSource.ValueType = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "role_ids", b"role_ids", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___SetMemberRoleRequest = SetMemberRoleRequest

@typing_extensions.final
class ListMembersRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Mode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ListMembersRequest._Mode.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        ID: ListMembersRequest._Mode.ValueType  # 0
        Workspace: ListMembersRequest._Mode.ValueType  # 2

    class Mode(_Mode, metaclass=_ModeEnumTypeWrapper): ...
    ID: ListMembersRequest.Mode.ValueType  # 0
    Name: ListMembersRequest.Mode.ValueType  # 1
    Workspace: ListMembersRequest.Mode.ValueType  # 2

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    LIMIT_FIELD_NUMBER: builtins.int
    STARTING_AFTER_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    FEATURE_ID_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    limit: builtins.int
    starting_after: builtins.str
    workspace_id: builtins.int
    feature_id: builtins.int
    mode: global___ListMembersRequest.Mode.ValueType
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        limit: builtins.int = ...,
        starting_after: builtins.str = ...,
        workspace_id: builtins.int = ...,
        feature_id: builtins.int = ...,
        mode: global___ListMembersRequest.Mode.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "feature_id", b"feature_id", "limit", b"limit", "mode", b"mode", "starting_after", b"starting_after", "workspace_id", b"workspace_id"]) -> None: ...

global___ListMembersRequest = ListMembersRequest

@typing_extensions.final
class Member(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    DISPLAY_NAME_FIELD_NUMBER: builtins.int
    AVATAR_FIELD_NUMBER: builtins.int
    AVATAR_THUMB_PATTERN_FIELD_NUMBER: builtins.int
    COVER_FIELD_NUMBER: builtins.int
    COVER_THUMB_PATTERN_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    ROLE_IDS_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    UPDATED_AT_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    id: builtins.int
    display_name: builtins.str
    avatar: builtins.str
    avatar_thumb_pattern: builtins.str
    cover: builtins.str
    cover_thumb_pattern: builtins.str
    type: builtins.str
    @property
    def role_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    created_at: builtins.int
    updated_at: builtins.int
    user_id: builtins.int
    def __init__(
        self,
        *,
        id: builtins.int = ...,
        display_name: builtins.str = ...,
        avatar: builtins.str = ...,
        avatar_thumb_pattern: builtins.str = ...,
        cover: builtins.str = ...,
        cover_thumb_pattern: builtins.str = ...,
        type: builtins.str = ...,
        role_ids: collections.abc.Iterable[builtins.int] | None = ...,
        created_at: builtins.int = ...,
        updated_at: builtins.int = ...,
        user_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["avatar", b"avatar", "avatar_thumb_pattern", b"avatar_thumb_pattern", "cover", b"cover", "cover_thumb_pattern", b"cover_thumb_pattern", "created_at", b"created_at", "display_name", b"display_name", "id", b"id", "role_ids", b"role_ids", "type", b"type", "updated_at", b"updated_at", "user_id", b"user_id"]) -> None: ...

global___Member = Member

@typing_extensions.final
class Link(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NEXT_FIELD_NUMBER: builtins.int
    next: builtins.str
    def __init__(
        self,
        *,
        next: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["next", b"next"]) -> None: ...

global___Link = Link

@typing_extensions.final
class ListMembersResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    LINK_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Member]: ...
    @property
    def link(self) -> global___Link: ...
    def __init__(
        self,
        *,
        data: collections.abc.Iterable[global___Member] | None = ...,
        link: global___Link | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["link", b"link"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "link", b"link"]) -> None: ...

global___ListMembersResponse = ListMembersResponse

@typing_extensions.final
class GetCollabGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "workspace_id", b"workspace_id"]) -> None: ...

global___GetCollabGroupRequest = GetCollabGroupRequest

@typing_extensions.final
class CollabGroup(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    UPDATED_AT_FIELD_NUMBER: builtins.int
    CONTEXT_FIELD_NUMBER: builtins.int
    SETTINGS_FIELD_NUMBER: builtins.int
    AVATAR_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    name: builtins.str
    workspace_id: builtins.int
    created_at: builtins.int
    updated_at: builtins.int
    @property
    def context(self) -> google.protobuf.any_pb2.Any: ...
    @property
    def Settings(self) -> global___Settings:
        """ Privacy privacy = 7;
         bool auto_accept = 8;
        """
    avatar: builtins.str
    description: builtins.str
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        name: builtins.str = ...,
        workspace_id: builtins.int = ...,
        created_at: builtins.int = ...,
        updated_at: builtins.int = ...,
        context: google.protobuf.any_pb2.Any | None = ...,
        Settings: global___Settings | None = ...,
        avatar: builtins.str = ...,
        description: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["Settings", b"Settings", "context", b"context"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["Settings", b"Settings", "avatar", b"avatar", "collab_group_id", b"collab_group_id", "context", b"context", "created_at", b"created_at", "description", b"description", "name", b"name", "updated_at", b"updated_at", "workspace_id", b"workspace_id"]) -> None: ...

global___CollabGroup = CollabGroup

@typing_extensions.final
class GetCollabGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___CollabGroup: ...
    def __init__(
        self,
        *,
        data: global___CollabGroup | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data"]) -> None: ...

global___GetCollabGroupResponse = GetCollabGroupResponse

@typing_extensions.final
class ListCollabGroupsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    LIMIT_FIELD_NUMBER: builtins.int
    STARTING_AFTER_FIELD_NUMBER: builtins.int
    caller_id: builtins.int
    workspace_id: builtins.int
    limit: builtins.int
    starting_after: builtins.str
    def __init__(
        self,
        *,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        limit: builtins.int = ...,
        starting_after: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "limit", b"limit", "starting_after", b"starting_after", "workspace_id", b"workspace_id"]) -> None: ...

global___ListCollabGroupsRequest = ListCollabGroupsRequest

@typing_extensions.final
class ListCollabGroupsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    LINK_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CollabGroup]: ...
    @property
    def link(self) -> global___Link: ...
    def __init__(
        self,
        *,
        data: collections.abc.Iterable[global___CollabGroup] | None = ...,
        link: global___Link | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["link", b"link"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "link", b"link"]) -> None: ...

global___ListCollabGroupsResponse = ListCollabGroupsResponse

@typing_extensions.final
class CheckAccessPermissionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    ACTION_FIELD_NUMBER: builtins.int
    RESOURCE_FIELD_NUMBER: builtins.int
    FEATUREID_FIELD_NUMBER: builtins.int
    user_id: builtins.int
    collab_group_id: builtins.str
    action: builtins.str
    resource: builtins.str
    featureID: builtins.int
    def __init__(
        self,
        *,
        user_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        action: builtins.str = ...,
        resource: builtins.str = ...,
        featureID: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["action", b"action", "collab_group_id", b"collab_group_id", "featureID", b"featureID", "resource", b"resource", "user_id", b"user_id"]) -> None: ...

global___CheckAccessPermissionRequest = CheckAccessPermissionRequest

@typing_extensions.final
class CheckAccessPermissionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALLOW_FIELD_NUMBER: builtins.int
    allow: builtins.bool
    def __init__(
        self,
        *,
        allow: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["allow", b"allow"]) -> None: ...

global___CheckAccessPermissionResponse = CheckAccessPermissionResponse

@typing_extensions.final
class GenerateInviteLinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CALLER_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    ROLES_FIELD_NUMBER: builtins.int
    EXPIRE_AT_FIELD_NUMBER: builtins.int
    caller_id: builtins.int
    collab_group_id: builtins.str
    workspace_id: builtins.int
    @property
    def roles(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    expire_at: builtins.int
    def __init__(
        self,
        *,
        caller_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        workspace_id: builtins.int = ...,
        roles: collections.abc.Iterable[builtins.int] | None = ...,
        expire_at: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "expire_at", b"expire_at", "roles", b"roles", "workspace_id", b"workspace_id"]) -> None: ...

global___GenerateInviteLinkRequest = GenerateInviteLinkRequest

@typing_extensions.final
class GenerateInviteLinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INVITE_KEY_FIELD_NUMBER: builtins.int
    invite_key: builtins.str
    def __init__(
        self,
        *,
        invite_key: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["invite_key", b"invite_key"]) -> None: ...

global___GenerateInviteLinkResponse = GenerateInviteLinkResponse

@typing_extensions.final
class RequestJoinRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INVITE_KEY_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    invite_key: builtins.str
    user_id: builtins.int
    workspace_id: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    data_source: global___DataSource.ValueType
    def __init__(
        self,
        *,
        invite_key: builtins.str = ...,
        user_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        data_source: global___DataSource.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "invite_key", b"invite_key", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___RequestJoinRequest = RequestJoinRequest

@typing_extensions.final
class RequestJoinResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Status:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _StatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[RequestJoinResponse._Status.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSPECIFIED: RequestJoinResponse._Status.ValueType  # 0
        Pending: RequestJoinResponse._Status.ValueType  # 1
        Member: RequestJoinResponse._Status.ValueType  # 2

    class Status(_Status, metaclass=_StatusEnumTypeWrapper): ...
    UNSPECIFIED: RequestJoinResponse.Status.ValueType  # 0
    Pending: RequestJoinResponse.Status.ValueType  # 1
    Member: RequestJoinResponse.Status.ValueType  # 2

    STATUS_FIELD_NUMBER: builtins.int
    status: global___RequestJoinResponse.Status.ValueType
    def __init__(
        self,
        *,
        status: global___RequestJoinResponse.Status.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["status", b"status"]) -> None: ...

global___RequestJoinResponse = RequestJoinResponse

@typing_extensions.final
class UpdatePrivacyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CALLER_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    PRIVACY_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    caller_id: builtins.int
    collab_group_id: builtins.str
    workspace_id: builtins.int
    privacy: global___Privacy.ValueType
    data_source: global___DataSource.ValueType
    def __init__(
        self,
        *,
        caller_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        workspace_id: builtins.int = ...,
        privacy: global___Privacy.ValueType = ...,
        data_source: global___DataSource.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "privacy", b"privacy", "workspace_id", b"workspace_id"]) -> None: ...

global___UpdatePrivacyRequest = UpdatePrivacyRequest

@typing_extensions.final
class UpdateAutoAcceptRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CALLER_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    AUTO_ACCEPT_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    caller_id: builtins.int
    collab_group_id: builtins.str
    workspace_id: builtins.int
    auto_accept: builtins.bool
    data_source: global___DataSource.ValueType
    def __init__(
        self,
        *,
        caller_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        workspace_id: builtins.int = ...,
        auto_accept: builtins.bool = ...,
        data_source: global___DataSource.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["auto_accept", b"auto_accept", "caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "workspace_id", b"workspace_id"]) -> None: ...

global___UpdateAutoAcceptRequest = UpdateAutoAcceptRequest

@typing_extensions.final
class DeleteInviteLinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INVITE_TOKEN_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALL_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    invite_token: builtins.str
    collab_group_id: builtins.str
    call_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        invite_token: builtins.str = ...,
        collab_group_id: builtins.str = ...,
        call_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["call_id", b"call_id", "collab_group_id", b"collab_group_id", "invite_token", b"invite_token", "workspace_id", b"workspace_id"]) -> None: ...

global___DeleteInviteLinkRequest = DeleteInviteLinkRequest

@typing_extensions.final
class ListInviteLinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Mode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ListInviteLinkRequest._Mode.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SIMPLE: ListInviteLinkRequest._Mode.ValueType  # 0
        FULL: ListInviteLinkRequest._Mode.ValueType  # 2

    class Mode(_Mode, metaclass=_ModeEnumTypeWrapper): ...
    SIMPLE: ListInviteLinkRequest.Mode.ValueType  # 0
    FULL: ListInviteLinkRequest.Mode.ValueType  # 2

    CALLER_ID_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    caller_id: builtins.int
    collab_group_id: builtins.str
    workspace_id: builtins.int
    mode: global___ListInviteLinkRequest.Mode.ValueType
    def __init__(
        self,
        *,
        caller_id: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        workspace_id: builtins.int = ...,
        mode: global___ListInviteLinkRequest.Mode.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "mode", b"mode", "workspace_id", b"workspace_id"]) -> None: ...

global___ListInviteLinkRequest = ListInviteLinkRequest

@typing_extensions.final
class InviteLinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    CREATOR_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    ROLES_FIELD_NUMBER: builtins.int
    EXPIRE_DATE_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    DISPLAY_NAME_FIELD_NUMBER: builtins.int
    AVATAR_FIELD_NUMBER: builtins.int
    AVATAR_THUMB_PATTERN_FIELD_NUMBER: builtins.int
    COVER_FIELD_NUMBER: builtins.int
    COVER_THUMB_PATTERN_FIELD_NUMBER: builtins.int
    LINK_FIELD_NUMBER: builtins.int
    id: builtins.str
    creator: builtins.int
    collab_group_id: builtins.str
    @property
    def roles(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    expire_date: builtins.int
    created_at: builtins.int
    workspace_id: builtins.int
    display_name: builtins.str
    avatar: builtins.str
    avatar_thumb_pattern: builtins.str
    cover: builtins.str
    cover_thumb_pattern: builtins.str
    link: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        creator: builtins.int = ...,
        collab_group_id: builtins.str = ...,
        roles: collections.abc.Iterable[builtins.int] | None = ...,
        expire_date: builtins.int = ...,
        created_at: builtins.int = ...,
        workspace_id: builtins.int = ...,
        display_name: builtins.str = ...,
        avatar: builtins.str = ...,
        avatar_thumb_pattern: builtins.str = ...,
        cover: builtins.str = ...,
        cover_thumb_pattern: builtins.str = ...,
        link: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["avatar", b"avatar", "avatar_thumb_pattern", b"avatar_thumb_pattern", "collab_group_id", b"collab_group_id", "cover", b"cover", "cover_thumb_pattern", b"cover_thumb_pattern", "created_at", b"created_at", "creator", b"creator", "display_name", b"display_name", "expire_date", b"expire_date", "id", b"id", "link", b"link", "roles", b"roles", "workspace_id", b"workspace_id"]) -> None: ...

global___InviteLinkResponse = InviteLinkResponse

@typing_extensions.final
class ListInviteLinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATE_FIELD_NUMBER: builtins.int
    @property
    def date(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___InviteLinkResponse]: ...
    def __init__(
        self,
        *,
        date: collections.abc.Iterable[global___InviteLinkResponse] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["date", b"date"]) -> None: ...

global___ListInviteLinkResponse = ListInviteLinkResponse

@typing_extensions.final
class GetMemberByIDsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    USER_IDS_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    workspace_id: builtins.int
    @property
    def user_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        user_ids: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "user_ids", b"user_ids", "workspace_id", b"workspace_id"]) -> None: ...

global___GetMemberByIDsRequest = GetMemberByIDsRequest

@typing_extensions.final
class UpdateCollabGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    SETTINGS_FIELD_NUMBER: builtins.int
    AVATAR_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    name: builtins.str
    data_source: global___DataSource.ValueType
    caller_id: builtins.int
    workspace_id: builtins.int
    id: builtins.str
    @property
    def settings(self) -> global___Settings: ...
    avatar: builtins.str
    description: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str | None = ...,
        data_source: global___DataSource.ValueType = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        id: builtins.str = ...,
        settings: global___Settings | None = ...,
        avatar: builtins.str | None = ...,
        description: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_avatar", b"_avatar", "_description", b"_description", "_name", b"_name", "_settings", b"_settings", "avatar", b"avatar", "description", b"description", "name", b"name", "settings", b"settings"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_avatar", b"_avatar", "_description", b"_description", "_name", b"_name", "_settings", b"_settings", "avatar", b"avatar", "caller_id", b"caller_id", "data_source", b"data_source", "description", b"description", "id", b"id", "name", b"name", "settings", b"settings", "workspace_id", b"workspace_id"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_avatar", b"_avatar"]) -> typing_extensions.Literal["avatar"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_description", b"_description"]) -> typing_extensions.Literal["description"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_name", b"_name"]) -> typing_extensions.Literal["name"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_settings", b"_settings"]) -> typing_extensions.Literal["settings"] | None: ...

global___UpdateCollabGroupRequest = UpdateCollabGroupRequest

@typing_extensions.final
class UpdateCollabGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> global___CollabGroup: ...
    def __init__(
        self,
        *,
        data: global___CollabGroup | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data"]) -> None: ...

global___UpdateCollabGroupResponse = UpdateCollabGroupResponse

@typing_extensions.final
class UnlinkOrgChartRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    workspace_id: builtins.int
    data_source: global___DataSource.ValueType
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        data_source: global___DataSource.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "workspace_id", b"workspace_id"]) -> None: ...

global___UnlinkOrgChartRequest = UnlinkOrgChartRequest

@typing_extensions.final
class ListPendingMembersRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    LIMIT_FIELD_NUMBER: builtins.int
    STARTING_AFTER_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    """collab_group_id id of collab-group"""
    caller_id: builtins.int
    """caller_id id of user fetch list pending members"""
    workspace_id: builtins.int
    """workspace_id id of workspace of user fetch list pending members"""
    data_source: global___DataSource.ValueType
    mode: global___ListMembersRequest.Mode.ValueType
    limit: builtins.int
    starting_after: builtins.str
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        data_source: global___DataSource.ValueType = ...,
        mode: global___ListMembersRequest.Mode.ValueType = ...,
        limit: builtins.int = ...,
        starting_after: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "data_source", b"data_source", "limit", b"limit", "mode", b"mode", "starting_after", b"starting_after", "workspace_id", b"workspace_id"]) -> None: ...

global___ListPendingMembersRequest = ListPendingMembersRequest

@typing_extensions.final
class ListPendingMembersResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    LINK_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Member]: ...
    @property
    def link(self) -> global___Link: ...
    def __init__(
        self,
        *,
        data: collections.abc.Iterable[global___Member] | None = ...,
        link: global___Link | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["link", b"link"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "link", b"link"]) -> None: ...

global___ListPendingMembersResponse = ListPendingMembersResponse

@typing_extensions.final
class ReviewPendingMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Action:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ActionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ReviewPendingMemberRequest._Action.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        Unknown: ReviewPendingMemberRequest._Action.ValueType  # 0
        Approve: ReviewPendingMemberRequest._Action.ValueType  # 1
        Reject: ReviewPendingMemberRequest._Action.ValueType  # 2

    class Action(_Action, metaclass=_ActionEnumTypeWrapper): ...
    Unknown: ReviewPendingMemberRequest.Action.ValueType  # 0
    Approve: ReviewPendingMemberRequest.Action.ValueType  # 1
    Reject: ReviewPendingMemberRequest.Action.ValueType  # 2

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    ACTION_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    """collab_group_id id of collab-group"""
    caller_id: builtins.int
    """caller_id id of user fetch list pending members"""
    workspace_id: builtins.int
    """workspace_id id of workspace of user fetch list pending members"""
    user_id: builtins.int
    """user_id id of user want to review"""
    action: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
        user_id: builtins.int = ...,
        action: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["action", b"action", "caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___ReviewPendingMemberRequest = ReviewPendingMemberRequest

@typing_extensions.final
class IsMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    USER_IDS_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    """collab_group_id collab-group id"""
    @property
    def user_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """user_ids the list of user id want to check"""
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        user_ids: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["collab_group_id", b"collab_group_id", "user_ids", b"user_ids"]) -> None: ...

global___IsMemberRequest = IsMemberRequest

@typing_extensions.final
class IsMemberResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing_extensions.final
    class DataEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.bool
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.bool]:
        """data map[user_id][isExists]
        i.e: check list user_ids=[1,2,3]
        => response data = {
         1: true, // it's mean user 1 is member of collab group
         2: false, // user 2 is not member
         3:true
        }
        """
    def __init__(
        self,
        *,
        data: collections.abc.Mapping[builtins.str, builtins.bool] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data"]) -> None: ...

global___IsMemberResponse = IsMemberResponse

@typing_extensions.final
class ListAllMemberIDsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    """collab_group_id collab-group id"""
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["collab_group_id", b"collab_group_id"]) -> None: ...

global___ListAllMemberIDsRequest = ListAllMemberIDsRequest

@typing_extensions.final
class ListAllMemberIDsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    @property
    def data(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        data: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data"]) -> None: ...

global___ListAllMemberIDsResponse = ListAllMemberIDsResponse

@typing_extensions.final
class CheckFeatureRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FEATURE_FIELD_NUMBER: builtins.int
    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    feature: builtins.int
    collab_group_id: builtins.str
    """collab_group_id collab-group id"""
    def __init__(
        self,
        *,
        feature: builtins.int = ...,
        collab_group_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["collab_group_id", b"collab_group_id", "feature", b"feature"]) -> None: ...

global___CheckFeatureRequest = CheckFeatureRequest

@typing_extensions.final
class CheckFeatureResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENABLE_FIELD_NUMBER: builtins.int
    enable: builtins.bool
    def __init__(
        self,
        *,
        enable: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["enable", b"enable"]) -> None: ...

global___CheckFeatureResponse = CheckFeatureResponse

@typing_extensions.final
class JoinDirectlyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    DATA_SOURCE_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    user_id: builtins.int
    data_source: global___DataSource.ValueType
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        user_id: builtins.int = ...,
        data_source: global___DataSource.ValueType = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["collab_group_id", b"collab_group_id", "data_source", b"data_source", "user_id", b"user_id", "workspace_id", b"workspace_id"]) -> None: ...

global___JoinDirectlyRequest = JoinDirectlyRequest

@typing_extensions.final
class JoinDirectlyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    id: builtins.int
    def __init__(
        self,
        *,
        id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["id", b"id"]) -> None: ...

global___JoinDirectlyResponse = JoinDirectlyResponse

@typing_extensions.final
class ArchivedCollabGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COLLAB_GROUP_ID_FIELD_NUMBER: builtins.int
    CALLER_ID_FIELD_NUMBER: builtins.int
    WORKSPACE_ID_FIELD_NUMBER: builtins.int
    collab_group_id: builtins.str
    caller_id: builtins.int
    workspace_id: builtins.int
    def __init__(
        self,
        *,
        collab_group_id: builtins.str = ...,
        caller_id: builtins.int = ...,
        workspace_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["caller_id", b"caller_id", "collab_group_id", b"collab_group_id", "workspace_id", b"workspace_id"]) -> None: ...

global___ArchivedCollabGroupRequest = ArchivedCollabGroupRequest
