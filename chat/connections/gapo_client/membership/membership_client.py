from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from logging import Logger
from typing import Callable, List, Optional, Tuple, TypeVar

import grpc

from chat.config import Settings
from chat.metrics.model import HOSTNAME, AppMetrics
from chat.utils.json import json_loads
from chat import action_type

from .proto import membership_pb2 as membership
from .proto.membership_pb2_grpc import GapoMembershipStub

_ReqType = TypeVar("_ReqType")
_RespType = TypeVar("_RespType")


class CollabGroupRole(Enum):
    Owner = 1
    Admin = 2
    Member = 3


@dataclass(frozen=True)
class CollabSettings:
    discovery: bool
    public: bool
    need_approve: bool


class CollabFeature(Enum):
    Chat = 2
    Meet = 3
    Task = 4


class Privacy(Enum):
    Public = 0
    Close = 1
    Secret = 2


@dataclass(frozen=True)
class CollabGroup:
    collab_group_id: str
    name: str
    avatar: Optional[str]
    description: Optional[str]
    workspace_id: str
    settings: CollabSettings


@dataclass(frozen=True)
class Member:
    membership_id: int
    user_id: int
    roles: List[CollabGroupRole]


class CollabGroupNotFound(Exception):
    pass


class PermissionDenied(Exception):
    pass


class MemberNotExist(Exception):
    pass


class MemberAlreadyExist(Exception):
    pass


class GrpcError(Exception):
    def __init__(self, code: str, detail: str, logs) -> None:
        self.code = code
        self.detail = detail
        self.logs = logs

    def __repr__(self) -> str:
        return f"""<GrpcError code={self.code}
        detail='{self.detail}'
        logs={self.logs}>"""

    def __format__(self, __format_spec: str) -> str:
        return self.__repr__()

    def __str__(self) -> str:
        return self.__repr__()


DEFAULT_COLLAB_SETTINGS = CollabSettings(
    discovery=False, public=False, need_approve=False
)


class IMembershipClient(ABC):
    @abstractmethod
    def create_collab_group(
        self,
        caller_id: int,
        name: str,
        workspace_id: str,
        description="",
        avatar="",
        settings: CollabSettings = DEFAULT_COLLAB_SETTINGS,
    ) -> str:
        pass

    @abstractmethod
    def add_member(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        workspace_id: str,
        roles: List[CollabGroupRole],
        data_source: str,
    ) -> int:
        pass

    @abstractmethod
    def remove_member(
        self, caller_id: int, collab_id: str, user_id: int, workspace_id: str
    ):
        pass

    @abstractmethod
    def set_member_role(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        roles: List[CollabGroupRole],
        workspace_id: str,
    ):
        pass

    @abstractmethod
    def list_members(self, caller_id: int, collab_id: str) -> List[Member]:
        pass

    @abstractmethod
    def get_member_by_ids(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        user_ids: List[int],
    ) -> List[Member]:
        pass

    @abstractmethod
    def get_collab_group(
        self, caller_id: int, collab_id: str, workspace_id: str
    ) -> CollabGroup:
        pass

    # @abstractmethod
    # def check_access_permission(self, caller_id: int):
    #     pass

    # @abstractmethod
    # def generate_invite_link(self, caller_id: int):

    @abstractmethod
    def join_collab(
        self, user_id: int, collab_id: str, workspace_id: str, data_source: str
    ):
        pass

    @abstractmethod
    def update_collab_info(
        self,
        collab_id: str,
        caller_id: int,
        workspace_id: str,
        name: Optional[str] = None,
        avatar: Optional[str] = None,
        description: Optional[str] = None,
    ):
        pass

    # @abstractmethod
    # def list_all_pending_members(
    #     self, caller_id: int, collab_id: str, workspace_id: str
    # ) -> List[Member]:
    #     pass

    # @abstractmethod
    # def list_pending_members(
    #     self,
    #     caller_id: int,
    #     collab_id: str,
    #     workspace_id: str,
    #     starting_after: str = "",
    #     limit: int = 50,
    # ) -> List[Member]:
    #     pass

    @abstractmethod
    def request_join(self, caller_id: int, collab_id: str, workspace_id: str):
        """Adds a user to join request list."""

    @abstractmethod
    def update_collab(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        name: Optional[str],
        settings: CollabSettings,
    ):
        pass


class MembershipClient(IMembershipClient):
    def __init__(
        self,
        config: Settings,
        log: Optional[Logger] = None,
        metrics: Optional[AppMetrics] = None,
    ):
        self.log = log
        self.metrics = metrics
        self.service_name = "membership"

        channel = grpc.insecure_channel(config.gapo.membership_url)
        self.client = GapoMembershipStub(channel)

    def report_api_call_errors(self):
        if self.metrics:
            self.metrics.api_call_failures.labels(
                HOSTNAME, self.service_name
            ).inc()

    def create_collab_group(
        self,
        caller_id: int,
        name: str,
        workspace_id: str,
        description="",
        avatar="",
        settings: CollabSettings = DEFAULT_COLLAB_SETTINGS,
    ) -> str:
        """Creates new collab."""
        request = membership.CreateCollabGroupRequest(
            name=name,
            description=description,
            avatar=avatar,
            workspace_id=self._safe_int(workspace_id),
            caller_id=caller_id,
            Settings=self._to_grpc_settings(settings),
            data_source=membership.DATA_SOURCE_CHAT_SERVICE,
        )
        response = self._make_call(self.client.CreateCollabGroup, request)

        return response.id

    def update_collab(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        name: Optional[str],
        settings: CollabSettings,
    ):
        request = membership.UpdateCollabGroupRequest(
            caller_id=caller_id,
            id=collab_id,
            name=name,
            workspace_id=self._safe_int(workspace_id),
            data_source=membership.DATA_SOURCE_CHAT_SERVICE,
            settings=self._to_grpc_settings(settings),
        )

        self._make_call(self.client.UpdateCollabGroup, request)

    def add_member(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        workspace_id: str,
        roles: List[CollabGroupRole] = [CollabGroupRole.Member],
        data_source: str = "invite_mem",
    ) -> int:
        _source = membership.DATA_SOURCE_CHAT_MANUAL
        if data_source == action_type.INVITE_ORG_BY_DEPARTMENT:
            _source = membership.DATA_SOURCE_CHAT_DEPARTMENT
        elif data_source == action_type.INVITE_ORG_BY_TITLE:
            _source = membership.DATA_SOURCE_CHAT_TITLE
        elif data_source == action_type.INVITE_BY_FILE:
            _source = membership.DATA_SOURCE_CHAT_FILE
        elif data_source == action_type.INVITE_BY_THREAD_CHAT:
            _source = membership.DATA_SOURCE_CHAT_THREAD

        role_ids = [r.value for r in roles]
        request = membership.AddMemberRequest(
            collab_group_id=collab_id,
            user_id=user_id,
            role_ids=role_ids,
            caller_id=caller_id,
            workspace_id=self._safe_int(workspace_id),
            data_source=_source,
        )

        response = self._make_call(self.client.AddMember, request)
        return response.id

    def add_member_direct(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        workspace_id: str,
        roles: List[CollabGroupRole] = [CollabGroupRole.Member],
    ) -> int:
        role_ids = [r.value for r in roles]
        request = membership.AddMemberRequest(
            collab_group_id=collab_id,
            user_id=user_id,
            role_ids=role_ids,
            caller_id=caller_id,
            workspace_id=self._safe_int(workspace_id),
            data_source=membership.DATA_SOURCE_ORG_SERVICE,
        )

        response = self._make_call(self.client.AddMember, request)
        return response.id

    def remove_member(
        self, caller_id: int, collab_id: str, user_id: int, workspace_id: str
    ):
        request = membership.RemoveMemberRequest(
            collab_group_id=collab_id,
            user_id=user_id,
            caller_id=caller_id,
            data_source=membership.DATA_SOURCE_CHAT_SERVICE,
            workspace_id=self._safe_int(workspace_id),
        )

        self._make_call(self.client.RemoveMember, request)

    def request_join(self, caller_id: int, collab_id: str, workspace_id: str):
        request = membership.RequestJoinRequest(
            invite_key="",  # dummy value, membership service will ignore this
            user_id=caller_id,
            caller_id=caller_id,
            collab_group_id=collab_id,
            workspace_id=self._safe_int(workspace_id),
            data_source=membership.DATA_SOURCE_CHAT_SERVICE,
        )
        try:
            self._make_call(self.client.RequestJoin, request)
        except MemberAlreadyExist:
            pass

    # def list_all_pending_members(
    #     self, caller_id: int, collab_id: str, workspace_id: str
    # ):
    #     members: List[Member] = []
    #     next_link = ""
    #     while True:
    #         part, next_link = self._list_pending_members(
    #             caller_id,
    #             collab_id,
    #             workspace_id,
    #             starting_after=next_link,
    #             limit=100,
    #         )
    #         members.extend(part)
    #         if not next_link:
    #             break

    #     return members

    # def list_pending_members(
    #     self,
    #     caller_id: int,
    #     collab_id: str,
    #     workspace_id: str,
    #     starting_after: str = "",
    #     limit: int = 50,
    # ):
    #     return self._list_pending_members(
    #         caller_id,
    #         collab_id,
    #         workspace_id,
    #         starting_after=starting_after,
    #         limit=limit,
    #     )

    def set_member_role(
        self,
        caller_id: int,
        collab_id: str,
        user_id: int,
        roles: List[CollabGroupRole],
        workspace_id: str,
    ):
        role_ids = [r.value for r in roles]
        request = membership.SetMemberRoleRequest(
            collab_group_id=collab_id,
            user_id=user_id,
            role_ids=role_ids,
            caller_id=caller_id,
            data_source=membership.DATA_SOURCE_MEET_SERVICE,
            # data_source=membership.DATA_SOURCE_CHAT_SERVICE,
            workspace_id=self._safe_int(workspace_id),
        )

        self._make_call(self.client.SetMemberRole, request)

    def get_collab_group(
        self, caller_id: int, collab_id: str, workspace_id: str
    ) -> CollabGroup:
        request = membership.GetCollabGroupRequest(
            collab_group_id=collab_id,
            workspace_id=self._safe_int(workspace_id),
            caller_id=caller_id,
        )

        response = self._make_call(self.client.GetCollabGroup, request)
        return self._from_grpc_collab(response.data)

    def list_members(self, caller_id: int, collab_id: str) -> List[Member]:
        limit = 500
        next_link = ""
        members: List[Member] = []
        while True:
            member_parts, next_link = self._list_members(
                caller_id, collab_id, limit, next_link
            )
            members.extend(member_parts)
            if not next_link:
                break
        return members

    def _list_members(
        self,
        caller_id: int,
        collab_id: str,
        limit: int,
        next_link: str,
    ) -> Tuple[List[Member], str]:
        request = membership.ListMembersRequest(
            caller_id=caller_id,
            collab_group_id=collab_id,
            limit=limit,
            starting_after=next_link,
        )

        response = self._make_call(self.client.ListMembers, request)
        members = [self._from_grpc_member(m) for m in response.data]

        return members, response.link.next

    def get_member_by_ids(
        self,
        caller_id: int,
        collab_id: str,
        workspace_id: str,
        user_ids: List[int],
    ) -> List[Member]:
        request = membership.GetMemberByIDsRequest(
            collab_group_id=collab_id,
            workspace_id=self._safe_int(workspace_id),
            user_ids=user_ids,
            caller_id=caller_id,
        )

        response = self._make_call(self.client.GetMemberByIDs, request)
        members = [self._from_grpc_member(m) for m in response.data]

        return members

    def update_collab_info(
        self,
        collab_id: str,
        caller_id: int,
        workspace_id: str,
        name: Optional[str] = None,
        avatar: Optional[str] = None,
        description: Optional[str] = None,
    ):
        request = membership.UpdateCollabGroupRequest(
            id=collab_id,
            workspace_id=self._safe_int(workspace_id),
            caller_id=caller_id,
            name=name,
            avatar=avatar,
            description=description,
            data_source=membership.DATA_SOURCE_CHAT_SERVICE,
        )
        self._make_call(self.client.UpdateCollabGroup, request)

    # def check_access_permission(self, caller_id: int):
    #     pass

    # def generate_invite_link(self, caller_id: int):
    #     pass

    def join_collab(
        self,
        user_id: int,
        collab_id: str,
        workspace_id: str,
        data_source: str = "invite_mem",
    ):
        _source = membership.DATA_SOURCE_CHAT_SERVICE
        if data_source == action_type.JOIN_BY_DEPARTMENT:
            _source = membership.DATA_SOURCE_CHAT_JOIN_DEPARTMENT
        elif data_source == action_type.JOIN_BY_GROUP:
            _source = membership.DATA_SOURCE_CHAT_JOIN_GROUP
        request = membership.JoinDirectlyRequest(
            collab_group_id=collab_id,
            user_id=user_id,
            workspace_id=self._safe_int(workspace_id),
            data_source=_source,
        )
        self._make_call(self.client.JoinDirectly, request)

    def _make_call(
        self, rpc_call: Callable[[_ReqType], _RespType], request: _ReqType
    ) -> _RespType:
        try:
            return rpc_call(request, timeout=0.3)  # type: ignore
        except grpc.RpcError as e:
            self.report_api_call_errors()
            err_str = e.details()
            # if self.log and not "PermissionDenied" in err_str:
            #     self.log.critical(
            #         f"""Membership error: err:{str(e)}
            #           func_name:{func_name}
            #           request:{request}"""
            #     )
            # if e.code() == grpc.StatusCode.CANCELLED:
            #     pass
            # elif e.code() == grpc.StatusCode.UNAVAILABLE:
            #     pass

            # try to parse error to get more concrete error
            new_exc: Exception = e
            try:
                if err_str:
                    err_json = json_loads(err_str)
                    new_exc = GrpcError(
                        err_json.get("xcode") or err_json.get("code"),
                        err_json["msg"],
                        err_json.get("logs"),
                    )
            except Exception:
                pass

            if isinstance(new_exc, GrpcError):
                if new_exc.code in ("NotFoundCollabGroup"):
                    raise CollabGroupNotFound()
                elif new_exc.code == "PermissionDenied":
                    raise PermissionDenied()
                elif new_exc.code in ("MemberNotExist", "NotFoundMember"):
                    raise MemberNotExist()
                elif new_exc.code == "MemberAlreadyExist":
                    raise MemberAlreadyExist()

            raise new_exc

    # def _list_pending_members(
    #     self,
    #     caller_id: int,
    #     collab_id: str,
    #     workspace_id: str,
    #     starting_after: str,
    #     limit: int,
    # ):
    #     request = membership.ListPendingMembersRequest(
    #         collab_group_id=collab_id,
    #         caller_id=caller_id,
    #         data_source=membership.DATA_SOURCE_CHAT_SERVICE,
    #         workspace_id=self._safe_int(workspace_id),
    #         limit=limit,
    #         starting_after=starting_after,
    #     )
    #     resp = self._make_call(self.client.ListPendingMembers, request)
    #     members = [self._from_grpc_member(r) for r in resp.data]
    #     return members, resp.link.next

    def _from_grpc_member(self, member: membership.Member):
        return Member(
            member.id,
            member.user_id,
            [CollabGroupRole(id) for id in member.role_ids if id != 0],
        )

    def _from_grpc_settings(self, settings: membership.Settings):
        return CollabSettings(
            discovery=settings.discovery,
            public=settings.public,
            need_approve=settings.need_approve,
        )

    def _to_grpc_settings(self, settings: CollabSettings):
        return membership.Settings(
            discovery=settings.discovery,
            public=settings.public,
            need_approve=settings.need_approve,
        )

    def _from_grpc_collab(self, c: membership.CollabGroup) -> CollabGroup:
        return CollabGroup(
            collab_group_id=c.collab_group_id,
            name=c.name,
            description=c.description,
            avatar=c.avatar,
            workspace_id=str(c.workspace_id),
            settings=self._from_grpc_settings(c.Settings),
        )

    def _safe_int(self, val: str) -> int:
        return int(val or 0)
