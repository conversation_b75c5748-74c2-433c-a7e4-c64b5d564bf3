from logging import Logger
from typing import Any, Dict, List
from urllib.parse import parse_qsl, urljoin

import requests

from chat.config import Settings
from chat.metrics.model import AppMetrics
from chat.model import BaseModel

from .base import GapoBase, GetUserReactionListResp, IReactClient


class SchemaReactCount(BaseModel):
    react_type_1: int = 0
    react_type_2: int = 0
    react_type_3: int = 0
    react_type_4: int = 0
    react_type_5: int = 0
    react_type_6: int = 0
    react_type_7: int = 0
    react_type_8: int = 0
    react_type_9: int = 0
    react_type_10: int = 0


class SchemaReact(BaseModel):
    id: str
    react_count: SchemaReactCount = SchemaReactCount()
    react_yourself: int = 0
    react_count_total: int = 0


class GapoReact(GapoBase, IReactClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super(GapoReact, self).__init__(config, log, "react-service", metrics)
        self.name = "gapo_react"
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.react_api_key,
            "User-Agent": self._service_user_agent,
        }
        self.root_url = self.cfg.react_url
        self.session = requests.Session()

    def post_react(
        self,
        user_id: str,
        thread_id: int,
        msg_id: int,
        msg_created_at: int,
        react_type: int,
    ):
        header = dict(self.header)
        header["x-gapo-user-id"] = user_id
        chat_id = f"{thread_id}_{msg_id}_{msg_created_at}"
        post_fix = f"inner/chat/react/{chat_id}"
        url = urljoin(self.root_url, post_fix)
        data = [
            {
                "user_id": int(user_id),
                "react_type": react_type,
                "data_source": 6,
            }
        ]
        try:
            res = self.session.post(
                url=url,
                headers=header,
                timeout=self.REQ_TIMEOUT_SECS,
                json=data,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                return True
            else:
                self.log.error(f"{self.name} error http code {res.text}")  # type: ignore # noqa
                self.report_api_call_errors()
                return False
        except requests.exceptions.Timeout:
            self.log.error(f"{self.name} Timeout ")
            return False

    def get_react(
        self,
        user_id: str,
        thread_id: int,
        msg_ids: List[int],
        msg_created_ats: List[int],
    ) -> GetUserReactionListResp:
        header = dict(self.header)
        header["x-gapo-user-id"] = user_id
        chat_ids = [
            f"{thread_id}_{msg_id}_{msg_created_at}"
            for msg_id, msg_created_at in zip(msg_ids, msg_created_ats)
        ]
        param_ids = ",".join(chat_ids)
        post_fix = "inner/chat/get-data"
        url = urljoin(self.root_url, post_fix)
        responses: Dict[str, Any] = {
            chat_id.split("_")[1]: SchemaReact(id=chat_id).dict()
            for chat_id in chat_ids
        }
        try:
            res = self.session.get(
                url=url,
                headers=header,
                timeout=self.REQ_TIMEOUT_SECS,
                params={"ids": param_ids},
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                result = res.json()
                data = result["data"]
                responses.update(
                    {obj["id"].split("_")[1]: obj for obj in data}
                )
            else:
                self.report_api_call_errors()
                self.log.error(f"{self.name} error http code {res.text}")  # type: ignore # noqa
        except requests.exceptions.Timeout:
            self.report_api_call_errors()
            self.log.error(f"{self.name} Timeout ")

        return responses

    def get_react_users(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        message_created_at: int,
        react_type,
        last_time,
    ):
        header = {}
        header["x-gapo-user-id"] = user_id
        header["x-gapo-role"] = "user"
        chat_id = f"{thread_id}_{message_id}"
        url = urljoin(self.root_url, f"chat/list-user-react/{chat_id}")
        params = {"react_type": react_type, "last_time": last_time}
        response: Dict = {"data": []}
        try:
            res = self.session.get(
                url=url,
                params=params,
                headers=header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                response.update(res.json())
            else:
                self.report_api_call_errors()
                self.log.error(f"{self.name} error http code ", resp=res.text)  # type: ignore # noqa
        except requests.exceptions.Timeout:
            self.report_api_call_errors()
            self.log.error(f"{self.name} Timeout")

        has_next = response.get("links", {}).get("next")
        if has_next:
            query = dict(parse_qsl(has_next))
            response["last_time"] = query.get("last_time")
            response.pop("links", None)
        return response

    def get_default(
        self, thread_id: int, message_id: int, message_created_at: int
    ):
        _id = f"{thread_id}_{message_id}_{message_created_at}"
        return SchemaReact(id=_id).dict()

    def build_local(
        self,
        thread_id: int,
        message_id: int,
        message_created_at: int,
        react_types,
        react_yourself,
    ):
        _id = f"{thread_id}_{message_id}_{message_created_at}"
        react_o = SchemaReact(id=_id).dict()
        for re in react_types:
            react_type = f"react_type_{re}"
            count = react_o["react_count"].get(react_type, None)
            if count is None:
                continue
            count += 1
            react_o["react_count"][react_type] = count
            react_o["react_count_total"] += 1
        react_o["react_yourself"] = react_yourself
        return react_o
