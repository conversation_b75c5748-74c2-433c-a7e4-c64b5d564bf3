from logging import Logger
from typing import Any, Dict, Iterable
from urllib.parse import urljoin

import requests
from pydantic import ValidationError

from chat.bots.model import BotInfo
from chat.config import Settings
from chat.exception import InternalServerError
from chat.metrics.model import AppMetrics
from chat.model import BaseModel

from .base import GapoBase, IBotClient, SchemaBotProfile


class CheckOwnerBody(BaseModel):
    bot_id: str
    creator_id: str


class BaseResponse(BaseModel):
    code: int = 1
    message: str = "Thành công"
    data: Any = None


class CheckOwnerResponse(BaseResponse):
    is_owner: bool = True


class GapoBot(GapoBase, IBotClient):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "bot-service", metrics)
        self.header = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.bot_base_key,
            "User-Agent": self._service_user_agent,
        }
        self.base_url = self.cfg.bot_base_url
        self.session = requests.Session()

    def get_by_ids(self, bot_ids: Iterable[str]) -> Dict[str, BotInfo]:
        objects: Dict[str, BotInfo] = dict()
        url = urljoin(self.base_url, "internals/bots")
        params = {
            "ids": ",".join(bot_ids),
        }
        try:
            res = self.session.get(
                url=url,
                params=params,
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                result = res.json()
                users = result["data"]
                objects.update(
                    {str(u["id"]): SchemaBotProfile(**u).dict() for u in users}  # type: ignore
                )
            else:
                self.report_api_call_errors()
                self.log.error(res.text)
        except requests.exceptions.Timeout:
            self.report_api_call_errors()
            self.log.error("Bot API: Timeout error")
        missing_bots = set(bot_ids) - objects.keys()

        # fill missing ones with default info
        objects.update({u: SchemaBotProfile(id=u).dict() for u in missing_bots})  # type: ignore

        return objects

    def is_owner(self, user_id: str, bot_id) -> bool:
        """Checks if an user is the owner of a bot."""

        url = urljoin(self.base_url, "internals/check_owner")
        body = CheckOwnerBody(bot_id=bot_id, creator_id=user_id)
        try:
            res = self.session.post(
                url=url,
                json=body.dict(),
                headers=self.header,
                timeout=self.REQ_TIMEOUT_SECS,
                verify=False,
            )
            if res.status_code == self.SUCCESS_CODE:
                resp_json = res.json()
                response = CheckOwnerResponse(**resp_json)
                return response.is_owner
            else:
                self.report_api_call_errors()
                self.log.error("Bot API error:  {}".format(res.json()))

                raise InternalServerError()
        except ValidationError:
            # invalid response
            self.log.error("Bot API: invalid response")
            self.metrics.api_call_interface_errors.inc()
            raise InternalServerError()
        except requests.exceptions.Timeout:
            self.log.error("Bot API: Timeout error")
            raise InternalServerError()
