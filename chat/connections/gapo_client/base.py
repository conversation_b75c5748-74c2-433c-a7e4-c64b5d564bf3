from abc import ABC, abstractmethod
from http import HTT<PERSON>tatus
from logging import Logger
from typing import Any, Dict, Iterable, List, Set, Union

from pydantic import Field, root_validator, validator
from typing_extensions import TypedDict

import chat.utils.thumb as thumb
from chat.bots.model import BotInfo
from chat.config import Settings
from chat.metrics.model import H<PERSON><PERSON><PERSON><PERSON>, AppMetrics
from chat.model import BaseModel
from chat.users.model import UserInfo

DEFAULT_BOT = "Gapo Bot"


class SchemaVote(BaseModel):
    title: str
    image: str = ""
    id: str = ""
    user_id: str = ""
    last_user_update: int = 0
    count: int = 0
    user_votes: list = []


class SchemaPoll(BaseModel):
    id: str = ""
    title: str
    allow_add_choice: bool
    allow_multiple_choice: bool
    incognito: bool = False
    user_id: str
    end_at: int = 0
    created_at: int
    updated_at: int
    myself_votes: list = []
    votes: List[SchemaVote] = []
    total_users: int = 0


class SchemaBotProfile(BaseModel):
    should_cache: bool = True
    name: str = Field(DEFAULT_BOT, alias="name")
    status_verify: int = 0
    id: str
    avatar: Union[str, None] = None
    type: str = "bot"
    workspace_id: Union[str, None] = None

    creator_id: Union[str, None] = None
    creator_type: Union[str, None] = None

    @validator("avatar")
    def normalize_avatar(cls, v):
        return thumb.avatar(v) if v else v

    def can_create_thread(self, workspace_id: str) -> bool:
        if self.creator_type == "system":
            return True
        if self.workspace_id == workspace_id:
            return True
        return False  


class OrgcWithMembers(TypedDict):
    id: str
    """Department id"""
    name: str
    """Department name"""
    user_ids: List[str]
    """List of users in the deparment"""


class DepartmentInfo(TypedDict):
    id: str
    name: str
    tree_id: str
    thread_id: str
    fullpath_name: List[str]
    fullpath_ids: List[str]


class ThreadUserReactionList(TypedDict):
    react_count: Dict[str, int]
    react_count_total: int
    react_yourself: int


GetUserReactionListResp = Dict[str, ThreadUserReactionList]


DEFAULT_USER_NAME = "Gapo Member"


class SchemaUserProfile(BaseModel):
    should_cache: bool = True
    name: str = Field(DEFAULT_USER_NAME, alias="display_name")
    status_verify: int = 0
    id: str
    avatar: Union[None, str] = None
    type: str = "user"
    info: Union[None, dict]

    status: Union[None, int] = None

    company: Union[None, str] = ""
    title: Union[None, str] = ""
    department: Union[None, str] = ""
    departments: Union[None, list] = []

    @validator("avatar")
    def normalize_avatar(cls, v):
        return thumb.avatar(v) if v else v

    @validator("name", always=True)
    def check_name(cls, v, values, **kwargs):
        if v == DEFAULT_USER_NAME or not v:
            values["should_cache"] = False
        return v

    @root_validator(pre=True)
    def normalize_info(cls, values):
        info = values.get("info")
        if not info:
            return values

        work = info.get("work")
        if not work:
            return values

        try:
            values["company"] = work[0].get("company", "")
            values["title"] = work[0].get("title", "")
            values["department"] = work[0].get("department", "")
            values["departments"] = work[0].get("departments", [])
        except Exception as e:
            print("validator work:", work)
            raise (e)
        finally:
            return values


class IReactClient(ABC):
    @abstractmethod
    def post_react(
        self,
        user_id: str,
        thread_id: int,
        msg_id: int,
        msg_created_at: int,
        react_type: int,
    ):
        """Creates  new react to a message."""

    @abstractmethod
    def get_react(
        self,
        user_id: str,
        thread_id: int,
        msg_ids: List[int],
        msg_created_ats: List[int],
    ) -> GetUserReactionListResp:
        """Get list of reactions of a user to a list of messages."""

    def get_react_users(
        self,
        user_id: str,
        thread_id: int,
        message_id: int,
        message_created_at: int,
        react_type,
        last_time,
    ) -> Any:
        """Get list of users that react to a message_id"""

    @abstractmethod
    def get_default(
        self, thread_id: int, message_id: int, message_created_at: int
    ):
        pass

    @abstractmethod
    def build_local(
        self,
        thread_id: int,
        message_id: int,
        message_created_at: int,
        react_types,
        react_yourself,
    ):
        pass


class IRelationClient(ABC):
    @abstractmethod
    def is_friend(self, user_id: str, partner_id) -> bool:
        """Check if two users are friend."""

    @abstractmethod
    def get_friends_from_list(
        self, user_id: str, partner_ids: List[str]
    ) -> List[str]:
        """Returns list of friends to a user from a list."""


class IContactClient(ABC):
    @abstractmethod
    def get_relationship_status(self, user_id: str, part_id) -> int:
        pass


class IIAMClient(ABC):
    @abstractmethod
    def get_all_api_keys(self) -> Dict[str, str]:
        """Returns all api keys."""


class IPollClient(ABC):
    @abstractmethod
    def create_poll(self, object_payload):
        pass

    @abstractmethod
    def get_polls(
        self, user_id: str, poll_ids: Iterable[str]
    ) -> Dict[str, SchemaPoll]:
        pass

    @abstractmethod
    def update_poll(self, poll_id, update_d):
        pass

    @abstractmethod
    def create_vote(self, poll_id, vote_d):
        pass

    @abstractmethod
    def delete_vote(self, user_id: str, poll_id, vote_id):
        pass

    @abstractmethod
    def update_vote(self, poll_id, vote_id, update_d):
        pass

    @abstractmethod
    def vote_by_user(self, user_id: str, poll_id, vote_id):
        pass

    @abstractmethod
    def unvote_by_user(self, user_id: str, poll_id, vote_id):
        pass


class IBotClient(ABC):
    @abstractmethod
    def get_by_ids(self, bot_ids: List[Any]) -> Dict[str, BotInfo]:
        pass


class IUserClient(ABC):
    @abstractmethod
    def get_by_ids(self, user_ids: List[str]) -> Dict[str, UserInfo]:
        pass


class IFeatureClient(ABC):
    @abstractmethod
    def is_ws_enable(self, workspace_id: str) -> bool:
        pass


class IWorkspaceClient(ABC):
    @abstractmethod
    def check_if_same_company(self, from_user_id: str, to_user_id) -> bool:
        pass

    @abstractmethod
    def get_users_from_imported_file(
        self, workspace_id: str, file_id: str
    ) -> List[str]:
        """Returns list of users specified in a excel file."""

    @abstractmethod
    def is_admin(
        self, workspace_id: str, user_id: str
    ) -> bool:
        """Returns list of users specified in a excel file."""

    @abstractmethod
    def get_workspaces(
        self, user_id: str
    ) -> Set[str]:
        """Returns list of users specified in a excel file."""


class IOrgChart(ABC):
    @abstractmethod
    def get_members_in_departments(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        """Get members by department ids."""

    @abstractmethod
    def get_members_by_roles(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        """Get members by role in a workspace."""

    @abstractmethod
    def get_department_info(
        self, workspace_id, department_id
    ) -> DepartmentInfo:
        """Get department information."""

    @abstractmethod
    def unlink_thread_from_department(self, workspace_id, department_id):
        """Unlink current thread associated with a department"""


class IGroup(ABC):
    @abstractmethod
    def unlink_thread_from_group(self, group_id, thread_id):
        """Unlink thread from group."""


class GapoBase(object):
    def __init__(
        self,
        config: Settings,
        log: Logger,
        service_name: str,
        metrics: AppMetrics,
    ):
        self.cfg = config.gapo
        self.log = log
        self.metrics = metrics
        self.service_name = service_name
        self.SUCCESS_CODE = HTTPStatus.OK.value
        self.NO_CONTENT = HTTPStatus.NO_CONTENT.value
        self.NOT_FOUND_CODE = HTTPStatus.NOT_FOUND.value
        self.BAD_REQUEST_CODE = HTTPStatus.BAD_REQUEST.value
        self.REQ_TIMEOUT_SECS = 5.0
        self._service_user_agent = "chat-service"

    def report_api_call_errors(self):
        self.metrics.api_call_failures.labels(
            HOSTNAME, self.service_name
        ).inc()
