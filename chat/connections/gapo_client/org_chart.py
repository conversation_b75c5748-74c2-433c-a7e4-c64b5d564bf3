from logging import Logger
from typing import Dict, List
from urllib.parse import urljoin

import requests

from chat.config import Settings
from chat.metrics.model import AppMetrics

from .base import (
    DepartmentInfo,
    GapoBase,
    IOrgChart,
    OrgcWithMembers,
)


class GapoOrgChart(GapoBase, IOrgChart):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "orgc-chart", metrics)
        self.name = "gapo_org_chart"
        self.common_headers = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.orgc_api_key,
        }
        self.root_url = self.cfg.orgc_base_url
        self.session = requests.Session()
        self.log = log

    def get_members_in_departments(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        url = urljoin(self.root_url, "internal/users")
        res = self.session.get(
            url=url,
            headers=self.common_headers,
            timeout=self.REQ_TIMEOUT_SECS,
            params={
                "user_id": user_id,
                "workspace_id": workspace_id,
                "department_ids": ",".join(o_ids),
            },
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        data = res.json()
        return data["data"]  # type: ignore

    def get_members_by_roles(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        url = urljoin(self.root_url, "internal/users")
        res = self.session.get(
            url=url,
            headers=self.common_headers,
            timeout=self.REQ_TIMEOUT_SECS,
            params={
                "user_id": user_id,
                "workspace_id": workspace_id,
                "role_ids": ",".join(o_ids),
            },
            verify=False,
        )

        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        data = res.json()
        return data["data"]  # type: ignore

    def get_department_info(
        self, workspace_id, department_id
    ) -> DepartmentInfo:
        path = (
            f"internal/workspaces/{workspace_id}/departments/{department_id}"
        )
        url = urljoin(self.root_url, path)
        res = self.session.get(
            url=url,
            headers=self.common_headers,
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        # res.raise_for_status()
        data: Dict = {"data": {"fullpath_name": []}}
        if res.status_code == 200:
            data = res.json()
        else:
            self.report_api_call_errors()
            self.log.critical(f"Org chart error: path:{path} res:{res.json()}")
        return data["data"]  # type: ignore

    def unlink_thread_from_department(self, workspace_id, department_id):
        """Remove thread_id from department.

        No doc yet. just like orgc-chart-v3 API
        Docs: TODO: update docs
        """
        path = f"internal/department/{department_id}/remove-associate-chat"
        url = urljoin(self.root_url, path)
        res = self.session.post(
            url=url,
            headers=self._get_headers(workspace_id),
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code == 200:
            return True
        else:
            self.report_api_call_errors()
            self.log.critical(f"Org chart error: path:{path} res:{res.json()}")
            return False

    def _get_headers(self, workspace_id):
        headers = {"x-gapo-workspace-id": workspace_id}
        headers.update(self.common_headers)
        return headers


class GapoOrgChartV3(GapoBase, IOrgChart):
    def __init__(self, config: Settings, log: Logger, metrics: AppMetrics):
        super().__init__(config, log, "orgc-chart", metrics)
        self.name = "gapo_org_chart"
        self.common_headers = {
            "x-gapo-role": "service",
            "x-gapo-api-key": self.cfg.orgc_v3_api_key,
        }
        self.root_url = self.cfg.orgc_v3_base_url
        self.session = requests.Session()
        self.log = log

    def get_members_in_departments(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        """Get members by departments.

        docs: https://docs.contentcms.online/api.html?urls.primaryName=api.org-chart-v3#/internal/InternalApiController_getUserByDepartment # noqa
        """
        url = urljoin(self.root_url, "internal/user/user-ids-by-department")
        res = self.session.get(
            url=url,
            headers=self._get_headers(workspace_id),
            timeout=self.REQ_TIMEOUT_SECS,
            params={"department_ids": ",".join(o_ids)},
            verify=False,
        )
        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        data = res.json()
        return self._adapt_get_member_response(data["data"])  # type: ignore

    def get_members_by_roles(
        self, user_id: str, workspace_id: str, o_ids: List[str]
    ) -> List[OrgcWithMembers]:
        """Get members by roles.

        docs: https://docs.contentcms.online/api.html?urls.primaryName=api.org-chart-v3#/internal/InternalApiController_getUserByRole # noqa
        """
        url = urljoin(self.root_url, "internal/user/user-ids-by-role")
        res = self.session.get(
            url=url,
            headers=self._get_headers(workspace_id),
            timeout=self.REQ_TIMEOUT_SECS,
            params={"role_ids": ",".join(o_ids)},
            verify=False,
        )

        if res.status_code != self.SUCCESS_CODE:
            self.report_api_call_errors()

        res.raise_for_status()
        data = res.json()
        return self._adapt_get_member_response(data["data"])  # type: ignore

    def get_department_info(
        self, workspace_id, department_id
    ) -> DepartmentInfo:
        """Get department information.

        docs: https://docs.contentcms.online/api.html?urls.primaryName=api.org-chart-v3#/internal/InternalApiController_getOneDepartment # noqa
        """
        path = f"internal/department/{department_id}"
        url = urljoin(self.root_url, path)
        res = self.session.get(
            url=url,
            headers=self._get_headers(workspace_id),
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        # res.raise_for_status()
        data: Dict = {"data": {"fullpath_name": []}}
        if res.status_code == 200:
            data = res.json()
        else:
            self.report_api_call_errors()
            self.log.critical(f"Org chart error: path:{path} res:{res.json()}")
        return self._adapt_department_response(data["data"])  # type: ignore

    def unlink_thread_from_department(self, workspace_id, department_id):
        """Remove thread_id from department.

        Docs: https://docs.contentcms.online/api.html?urls.primaryName=api.org-chart-v3#/internal/InternalApiController_removeAssociateChat # noqa
        """
        path = f"internal/department/{department_id}/remove-associate-chat"
        url = urljoin(self.root_url, path)
        res = self.session.post(
            url=url,
            headers=self._get_headers(workspace_id),
            timeout=self.REQ_TIMEOUT_SECS,
            verify=False,
        )
        if res.status_code == 200:
            return True
        else:
            self.report_api_call_errors()
            self.log.critical(f"Org chart error: path:{path} res:{res.json()}")
            return False

    def _get_headers(self, workspace_id):
        headers = {"x-gapo-workspace-id": workspace_id}
        headers.update(self.common_headers)
        return headers

    def _adapt_get_member_response(self, response: List[Dict]):
        """Adapts output to match expected response.

        Input: [{"id": "", "name": "", "user_ids": [1,2,3]}]
        Output: [{"id": "", "name": "", "user_ids": ["1","2", "3"]}]

        """
        if not response:
            return response

        if not isinstance(response, list):
            response = [response]

        for obj in response:
            user_ids = obj.get("user_ids")
            if user_ids is None:
                user_ids = []
            user_ids = [str(user_id) for user_id in user_ids]  #
            obj["user_ids"] = user_ids
        return response

    def _adapt_department_response(self, response):
        """Adapt response to old response.
        Input: {"id": "", "name": "", "fullpath_names": []}
        Output: {"id": "", "name": "", "fullpath_name": []}
        """
        if "fullpath_names" in response:
            response["fullpath_name"] = response["fullpath_names"]
            del response["fullpath_names"]

        return response
