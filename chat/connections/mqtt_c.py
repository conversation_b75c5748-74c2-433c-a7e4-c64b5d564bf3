import uuid
from logging import Logger

import paho.mqtt.client as mqtt

from chat.utils.common import ticktock


class MQTTConnection(object):
    def __init__(
        self,
        name: str,
        host: str,
        port: int,
        username: str,
        password: str,
        log: Logger,
    ):
        self._name = name
        self._host = host
        self._port = port
        self._username = username
        self._password = password
        self.log = log
        self.prepare()
        self.connect()

    def prepare(self):
        self.client_id = str(uuid.uuid4())
        if not self._username:
            self.auth = {}
        else:
            self.auth = {
                "username": self._username,
                "password": self._password,
            }

    def connect(self):
        tick = ticktock()

        def on_connect(client, userdata, flags, rc):
            self.log.info(f"Connecting mqtt {self._name} {flags} {rc}")

        self.client = mqtt.Client(client_id=self.client_id, clean_session=True)
        if self.auth:
            self.client.username_pw_set(
                self.auth["username"], password=self.auth["password"]
            )
        self.client.enable_logger(logger=self.log)
        self.client.on_connect = on_connect
        self.client.connect(host=self._host, port=self._port, keepalive=30)
        self.client.loop_start()
        self.log.info(
            f"Connected mqtt {self._name} with client_id {self.client_id} took {tick()}"  # noqa
        )

    def close(self):
        self.log.info(f"Closing mqtt {self._name}")
        self.client.loop_stop(force=True)
        self.log.info(f"Closed mqtt {self._name}")
