import time
from logging import Logger
from typing import Optional

import requests
from falcon import Request, Response

from chat.config import Settings
from chat.connections.gapo_client import Gapo<PERSON>lient
from chat.constant import ADMIN_ROLE, GUEST_ROLE, SERVICE_ROLE, USER_ROLE
from chat.exception import Custom<PERSON><PERSON><PERSON>rror, PermissionDenied

DEFAULT_ADMIN_API_KEY = "RtK6oFO5CYsU7m1eSpLoSATqQGEIhV"


class AuthorizationMiddleware(object):
    def __init__(self, config: Settings, log: Logger, gapo_client: GapoClient):
        self.log = log
        self.config = config
        # self.api_key = self.config.iam_api_key
        self.api_expire = self.config.iam_api_expire_time
        self.key_expire_at: Optional[int] = None
        # self.gapo_iam = self.config.iam_base_url

        self.admin_key = self.config.admin_api_key
        self.iam_client = gapo_client.iam

    def is_authoz(self, token):
        url = self.config.url_authorization
        if url:
            r = requests.post(url, headers={"Authorization": token})
            return r.status_code == 200
        return True

    def check_admin_permmission(self, token: str) -> bool:
        if self.admin_key == "":
            return True
        return self.admin_key == token

    def get_api_key_infomation(self):
        now = int(time.time())
        if self.key_expire_at is None or self.key_expire_at < now:
            try:
                api_keys = self.iam_client.get_all_api_keys()
                self.key_pair = api_keys
                if self.key_pair:
                    self.key_expire_at = now + self.api_expire
            except requests.exceptions.RequestException as e:
                self.log.error(
                    f"Error getting api key failed: {e}"
                )
            # r = requests.get(
            #     self.gapo_iam,
            #     headers={
            #         "x-gapo-api-key": self.api_key,
            #         "x-gapo-role": "service",
            #     },
            # )
            # if r.status_code != 200:
            #     self.log.error(r.text)
            #     raise Exception("Error get key")

            # result = r.json()
            # self.key_pair = {}
            # for r in result["data"]:
            #     self.key_pair[r["apiKey"]] = r["scopes"]

        return self.key_pair

    # def admin_resource(self, req: Request, resp: Response, resource, params):
    #     req.context.auth_type = ADMIN_ROLE
    #     resource.role = ADMIN_ROLE

    #     api_key = req.get_header("x-gapo-api-key")
    #     if not self.check_admin_permmission(api_key):
    #         raise PermissionDenied()

    #     resource.user_id = ""
    #     resource.workspace_id = ""

    def internal_resource(
        self, req: Request, resp: Response, resource, params
    ):
        # if type(resource) not in (
        #     MessageResource,
        #     BlockUserResource,
        #     ParticipantMarkFriendResource,
        #     ParticipantAcceptFriendResource,
        #     MessageWalletResource,
        #     MessageBotResource,
        #     MessageCallServiceResource,
        #     ThreadInternalInfoResource,
        #     MessageAuthServiceResource,
        #     ThreadLeaveAllGroupResource,
        #     ThreadInternalAssociateResource,
        #     ThreadInternalAssociateItemResource,
        #     MessageZoomResource,
        #     MessageMeetingResource,
        #     MessageSaveMessage,
        #     ParticipantInternalResource,
        #     ParticipantInternalRoleResource,
        # ):
        #     raise CustomHTTPError(
        #         falcon.HTTP_403,
        #         error={"message": "not allowed resource", "code": 1},
        #     )

        if req.method not in ("POST", "GET", "PATCH", "DELETE", "PUT"):
            raise CustomHTTPError(
                403,
                error={"message": "Method not allowed"},
            )

        api_key = req.get_header("x-gapo-api-key", required=True)
        key_pair = self.get_api_key_infomation()

        if api_key not in key_pair.keys():
            self.log.info("apiKey not allowed")
            raise CustomHTTPError(
                403,
                error={"message": "api-key wrong", "code": 2},
            )

        user_id = req.get_header("x-gapo-user-id") or "system"
        lang = req.get_header("x-gapo-lang") or "vi"
        gapo_workspace_id = req.get_header(
            "x-gapo-workspace-id", required=False, default=""
        )

        resource.user_id = user_id
        resource.workspace_id = gapo_workspace_id
        resource.role = SERVICE_ROLE
        resource.lang = lang

        # set some fields to req.context
        req.context.workspace_id = gapo_workspace_id
        req.context.user_id = user_id
        req.context.role = SERVICE_ROLE
        req.context.auth_type = "service"
        req.context.lang = lang

    def guest_resource(self, req: Request, resp: Response, resource, params):
        req.context.auth_type = "guess"
        # if type(resource) not in (
        #     HealthCheckResource,
        #     MemoryTraceResource,
        #     ThreadJoinByLinkResource,
        # ):
        #     raise CustomHTTPError(
        #         falcon.HTTP_401,
        #         error={"message": "Token is wrong or not found"},
        #     )

        resource.user_id = ""
        resource.role = GUEST_ROLE
        resource.workspace_id = ""
        resource.lang = "vi"

    def user_resource(self, req: Request, resp: Response, resource, params):
        # if type(resource) in (
        #     ParticipantMarkFriendResource,
        #     ParticipantAcceptFriendResource,
        #     MessageWalletResource,
        #     MessageBotResource,
        #     MessageCallServiceResource,
        #     ThreadLeaveAllGroupResource,
        #     ThreadInternalAssociateResource,
        #     ThreadInternalAssociateItemResource,
        #     MessageZoomResource,
        #     MessageMeetingResource,
        #     MessageSaveMessage,
        #     ParticipantInternalResource,
        #     ParticipantInternalRoleResource
        # ):
        #     raise CustomHTTPError(
        #         falcon.HTTP_403,
        #         error={"message": "This is not resource for user"},
        #     )
        gapo_user_id = req.get_header("x-gapo-user-id", required=True)
        gapo_workspace_id = req.get_header(
            "x-gapo-workspace-id", required=False, default=""
        )

        resource.user_id = str(gapo_user_id)
        resource.role = USER_ROLE
        resource.workspace_id = gapo_workspace_id

        # set some field to req.context
        req.context.user_id = resource.user_id
        req.context.workspace_id = gapo_workspace_id
        req.context.auth_type = "user"

        # For block API relation
        gapo_bearer_token = req.get_header("Authorization")
        resource.token = gapo_bearer_token
        lang = req.get_header("x-gapo-lang") or "vi"
        resource.lang = lang

    def process_resource(self, req: Request, resp: Response, resource, params):
        try:
            # add fallback parametrs if devs forget to
            # extends BaseResource class
            if not hasattr(resource, "auth_types"):
                resource.auth_types = (USER_ROLE, SERVICE_ROLE)

            gapo_role = req.get_header("x-gapo-role", default=GUEST_ROLE)
            if gapo_role == USER_ROLE and gapo_role in resource.auth_types:
                return self.user_resource(req, resp, resource, params)
            elif gapo_role == GUEST_ROLE and gapo_role in resource.auth_types:
                return self.guest_resource(req, resp, resource, params)
            elif (
                gapo_role == SERVICE_ROLE and gapo_role in resource.auth_types
            ):
                return self.internal_resource(req, resp, resource, params)
            # elif (
            #     ADMIN_ROLE in resource.auth_types
            # ):  # cannot pass x-gapo-role from the outside
            #     return self.admin_resource(req, resp, resource, params)
            else:
                raise CustomHTTPError(
                    401,
                    error={"message": "Token is wrong or not found"},
                )

        except CustomHTTPError as e:
            self.log.info(e)
            raise e
        except Exception as e:
            self.log.info(e)
            raise CustomHTTPError(
                401,
                error={"message": "Token is wrong or not found"},
            )
