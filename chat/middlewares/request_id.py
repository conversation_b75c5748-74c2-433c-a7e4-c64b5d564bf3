import falcon
import structlog
from nanoid import generate
from structlog.contextvars import bind_contextvars, unbind_contextvars


class RequestIDMiddleware(object):
    def __init__(self):
        pass

    def _generate_id(self):
        return "cx-" + generate()

    def process_request(self, req: falcon.Request, resp: falcon.Response):
        request_id = req.get_header("x-request-id")
        if not request_id:
            request_id = self._generate_id()

        req.context.log = structlog.get_logger(request_id=request_id)

        req.context.request_id = request_id

    def process_response(
        self,
        req: falcon.Request,
        resp: falcon.Response,
        resource,
        req_succeeded,
    ):
        resp.set_header("X-Request-ID", req.context.request_id)

        # unbind request context
        unbind_contextvars("request_id")
        unbind_contextvars("ua")

    def process_resource(
        self, req: falcon.Request, resp: falcon.Response, resource, params
    ):
        # bind request_id to log context
        bind_contextvars(
            request_id=req.context.request_id, ua=req.get_header("user-agent")
        )

        # overwrite resource log with new instance
        if hasattr(resource, "log"):
            resource.log = req.context.log
