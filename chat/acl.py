# PB for public
# PV for private
# OW for owner
# AD for admin
# ME for member

# {group_type}_{user_role}_{name_permission}

# permission list

# chuyển quyền owner


from chat import constant
from chat.threads.model import ThreadSettings

ADMIN_ROLES = [constant.ADMIN_ROLE, constant.OWNER_ROLE]

acl = {
    "PB_OW_AD_AUTHORIZE_TO_OWNER": 1,
    "PV_OW_AD_AUTHORIZE_TO_OWNER": 1,
    "PB_OW_ME_AUTHORIZE_TO_OWNER": 1,
    "PV_OW_ME_AUTHORIZE_TO_OWNER": 1,
}

_acl_authorize_revoke_admin = {
    "PB_OW_AD_REVOKE_TO_MEMBER": 1,
    "PV_OW_AD_REVOKE_TO_MEMBER": 1,
    "PV_AD_AD_REVOKE_TO_MEMBER": 0,
    "PB_AD_AD_REVOKE_TO_MEMBER": 0,
    "PB_OW_ME_AUTHORIZE_TO_ADMIN": 1,
    "PV_OW_ME_AUTHORIZE_TO_ADMIN": 1,
    "PB_AD_ME_AUTHORIZE_TO_ADMIN": 1,
    "PV_AD_ME_AUTHORIZE_TO_ADMIN": 1,
}

_acl_ban_remove_member = {
    "PV_OW_ME_REMOVE": 1,
    "PV_OW_AD_REMOVE": 1,
    "PV_AD_ME_REMOVE": 1,
    "PB_OW_ME_REMOVE": 1,
    "PB_OW_AD_REMOVE": 1,
    "PB_AD_ME_REMOVE": 1,
    "PB_OW_ME_BAN": 1,
    "PV_OW_ME_BAN": 1,
    "PB_AD_ME_BAN": 1,
    "PV_AD_ME_BAN": 1,
    "PV_OW_AD_BAN": 1,
    "PB_OW_AD_BAN": 1,
}

_acl_unban_member = {
    "PV_OW_UNBAN": 1,
    "PV_AD_UNBAN": 1,
    "PB_OW_UNBAN": 1,
    "PB_AD_UNBAN": 1,
}

_acl_view_ban_member = {
    "PV_OW_VIEW_BAN_LIST": 1,
    "PV_AD_VIEW_BAN_LIST": 1,
    "PB_OW_VIEW_BAN_LIST": 1,
    "PB_AD_VIEW_BAN_LIST": 1,
}

_acl_add_member = {
    "PB_OW_ADD": 1,
    "PV_OW_ADD": 1,
    "PB_AD_ADD": 1,
    "PV_AD_ADD": 1,
    "PV_ME_ADD": 1,
    "PB_ME_ADD": 1,
}


_acl_leave = {
    "PB_OW_LEAVE": 1,
    "PV_OW_LEAVE": 1,
    "PB_AD_LEAVE": 1,
    "PV_AD_LEAVE": 1,
    "PB_ME_LEAVE": 1,
    "PV_ME_LEAVE": 1,
}

_acl_edit_group = {
    "PB_OW_EDIT_GROUP_INFO": 1,
    "PV_OW_EDIT_GROUP_INFO": 1,
    "PB_AD_EDIT_GROUP_INFO": 1,
    "PV_AD_EDIT_GROUP_INFO": 1,
    "PB_ME_EDIT_GROUP_INFO": 0,
    "PV_ME_EDIT_GROUP_INFO": 1,
}

_acl_share_link = {
    "PB_OW_TOGGLE_LINK": 1,
    "PV_OW_TOGGLE_LINK": 1,
    "PB_AD_TOGGLE_LINK": 1,
    "PV_AD_TOGGLE_LINK": 1,
}

_acl_auto_delete_message = {
    "PB_OW_AUTO_DELETE_MESSAGE": 1,
    "PV_OW_AUTO_DELETE_MESSAGE": 1,
    "PB_AD_AUTO_DELETE_MESSAGE": 1,
    "PV_AD_AUTO_DELETE_MESSAGE": 1,
}

_acl_disband_group = {
    # public group
    "PB_OW_DISBAND": 1,
    "PV_OW_DISBAND": 1,
}

_acl_pin_message = {
    "PB_OW_PIN_MESSAGE": 1,
    "PV_OW_PIN_MESSAGE": 1,
    "PB_AD_PIN_MESSAGE": 1,
    "PV_AD_PIN_MESSAGE": 1,
    "PV_ME_PIN_MESSAGE": 1,
}

_acl_delete_message = {
    "PB_OW_ME_DELETE_MESSAGE": 1,
    "PV_OW_ME_DELETE_MESSAGE": 1,
    "PB_OW_AD_DELETE_MESSAGE": 1,
    "PV_OW_AD_DELETE_MESSAGE": 1,
    "PB_AD_ME_DELETE_MESSAGE": 1,
    "PV_AD_ME_DELETE_MESSAGE": 1,
    "PV_AD_AD_DELETE_MESSAGE": 0,
}

_acl_toggle_mute_all = {
    "PV_OW_TOGGLE_MUTE_ALL": 1,
    "PV_AD_TOGGLE_MUTE_ALL": 1,
    "PB_OW_TOGGLE_MUTE_ALL": 1,
    "PB_AD_TOGGLE_MUTE_ALL": 1,
}

_acl_toggle_allow_member_add_member = {
    "PV_OW_TOGGLE_ADD_MEM": 1,
    "PV_AD_TOGGLE_ADD_MEM": 1,
    "PB_OW_TOGGLE_ADD_MEM": 1,
    "PB_AD_TOGGLE_ADD_MEM": 1,
}

_acl_toggle_only_admin_update_info = {
    "PV_OW_TOGGLE_UPDATE_INFO": 1,
    "PV_AD_TOGGLE_UPDATE_INFO": 1,
    "PB_OW_TOGGLE_UPDATE_INFO": 1,
    "PB_AD_TOGGLE_UPDATE_INFO": 1,
}

acl.update(_acl_authorize_revoke_admin)
acl.update(_acl_ban_remove_member)
acl.update(_acl_add_member)
acl.update(_acl_unban_member)
acl.update(_acl_view_ban_member)
acl.update(_acl_leave)
acl.update(_acl_edit_group)
acl.update(_acl_share_link)
acl.update(_acl_pin_message)
acl.update(_acl_delete_message)
acl.update(_acl_toggle_mute_all)
acl.update(_acl_disband_group)
acl.update(_acl_toggle_allow_member_add_member)
acl.update(_acl_toggle_only_admin_update_info)
acl.update(_acl_auto_delete_message)


def get_permission(group_type: int, actor_role: str, permission_name: str):
    """
    @deprecated

    Should use check_permission function instead.
    """
    acl_group = _encode_acl_group(group_type)
    acl_actor = _encode_acl_user(actor_role)

    p_name = f"{acl_group}_{acl_actor}_{permission_name.upper()}"
    return acl.get(p_name, 0), p_name


def check_permission(
    setting: ThreadSettings, actor_role: str, permission_name: str
):
    acl_group = _encode_acl_group(1 if setting.is_public else 0)
    acl_actor = _encode_acl_user(actor_role)

    permission_key = f"{acl_group}_{acl_actor}_{permission_name.upper()}"

    if (
        permission_name == constant.PERMISSION_ADD_MEMBER
        and setting.only_admin_can_add_member
    ):
        return (
            actor_role in ADMIN_ROLES,
            permission_name,
        )
    elif (
        permission_name == constant.PERMISSION_EDIT_GROUP
        and setting.only_admin_can_update_info
    ):
        return (
            actor_role in ADMIN_ROLES,
            permission_key,
        )
    elif (
        permission_name == constant.PERMISSION_EDIT_GROUP
        and setting.only_admin_can_update_info
    ):
        return (
            actor_role in ADMIN_ROLES,
            permission_key,
        )

    return acl.get(permission_key, 0) > 0, permission_key


def get_permission_to_user(
    group_type,
    actor_role,
    receiver_role,
    permission_name,
):
    acl_group = _encode_acl_group(group_type)
    acl_actor = _encode_acl_user(actor_role)
    acl_receiver = _encode_acl_user(receiver_role)

    pm = permission_name.upper()

    p_name = f"{acl_group}_{acl_actor}_{acl_receiver}_{pm}"
    return acl.get(p_name, 0), p_name


def _encode_acl_user(decoded):
    decoded_acl_user = {"owner": "OW", "admin": "AD", "member": "ME"}
    return decoded_acl_user[decoded]


def _encode_acl_group(decoded):
    decoded_acl_group = {1: "PB", 0: "PV"}
    return decoded_acl_group[decoded]


def _decode_acl_user(encoded):
    encoded_acl_user = {"OW": "OWNER", "AD": "ADMIN", "ME": "MEMBER"}
    return encoded_acl_user[encoded]


def _decode_acl_group(encoded):
    encoded_acl_group = {
        "PB": "PUBLIC",
        "PV": "PRIVATE",
    }
    return encoded_acl_group[encoded]


def print_all():
    for k, v in acl.items():
        parse_key = k.split("_")
        acl_group = parse_key[0]
        acl_user = parse_key[1]
        acl_postfix = parse_key[2:]

        decoded_acl_group = _decode_acl_group(acl_group)
        decoded_acl_user = _decode_acl_user(acl_user)

        if len(acl_postfix) > 2:
            try:
                decoded_receiver_user = _decode_acl_user(acl_postfix[0])
                acl_name = "_".join(acl_postfix[1:])
                print(
                    "=========================\n"
                    f"group type {decoded_acl_group} "
                    f"actor role {decoded_acl_user} "
                    f"receiver role {decoded_receiver_user} "
                    f"permission {acl_name} "
                    f"enable: {v}"
                )
            except KeyError:
                acl_name = "_".join(acl_postfix)
        else:
            acl_name = "_".join(acl_postfix)
        print(
            "=========================\n"
            f"group type {decoded_acl_group} "
            f"actor role {decoded_acl_user} "
            f"permission {acl_name} "
            f"enable: {v}"
        )


if __name__ == "__main__":
    print_all()
