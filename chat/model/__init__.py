from typing import Any, Dict, List, Optional, Tuple, Type, TypeVar, Union

import pydantic
from pydantic import ValidationError
from typing_extensions import TypedDict

from chat.utils.json import json_dumps, json_loads

Loc = Tuple[Union[int, str], ...]

__all__ = ["ErrorDict", "BaseModel", "make_input", "make_output"]


class _ErrorDictRequired(TypedDict):
    loc: Loc
    msg: str
    type: str


class ErrorDict(_ErrorDictRequired, total=False):
    ctx: Dict[str, Any]


T = TypeVar("T")


class BaseModel(pydantic.BaseModel):
    """Base model for all pydantic model."""

    class Config:
        json_loads = json_loads
        json_dumps = json_dumps


def make_input(schema: Type[T], obj) -> Tuple[T, Optional[List[ErrorDict]]]:
    try:
        return schema(**obj), None
    except ValidationError as e:
        return None, e.errors()  # type: ignore


def make_output(schema: Type[T], obj) -> Tuple[T, Optional[List[ErrorDict]]]:
    try:
        return schema(**obj), None
    except ValidationError as e:
        return None, e.errors()  # type: ignore
