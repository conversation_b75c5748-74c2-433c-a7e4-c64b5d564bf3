from typing import Union

from chat.model import BaseModel

from pydantic import root_validator
from chat.constant import (
    BOT_TYPE,
    BOT_SPECIAL_VARPART,
    SYSTEM_ID,
    SYSTEM_TYPE,
    USER_TYPE,
)


def get_user_type(user_id: str):
    if user_id == SYSTEM_ID:
        return SYSTEM_TYPE
    elif str.isnumeric(user_id):
        checkID = int(user_id)
        if 0 < checkID < 2147483648:
            return USER_TYPE
        elif not checkID >> 59 ^ 10:
            return BOT_TYPE
    return None


class SchemaUser(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str = "user"


class SchemaPage(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str = "page"


class SchemaPartner(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str
    read_count: int = 0

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        if get_user_type(values["id"]) == BOT_TYPE:
            values["bot_id"] = values["id"]
        return values
