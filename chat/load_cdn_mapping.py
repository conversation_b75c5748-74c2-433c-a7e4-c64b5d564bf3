import json
from typing import Dict, List

PREFIX_THUMB_IMAGE: Dict[str, str] = {}
"""Map from image domain to thumb domain.
Example:
{'https://gapo-image.s3-ap-southeast-1.amazonaws.com/images': 'https://cdn-thumb-image-1.gapo.vn', # noqa
 'https://image-5a.gapo.vn/images': 'https://cdn-thumb-image-5a.gapo.vn'}
"""
PREFIX_IMAGE_URLS: List[str] = []
PREFIX_FILE_URLS: List[str] = []
PREFIX_STICKER_URLS: List[str] = []
PREFIX_VIDEO_URLS: List[str] = []

PREFIX_FILE_CDN_URLS: Dict[str, str] = {}
PREFIX_IMAGE_CDN_URLS: Dict[str, str] = {}
PREFIX_ANIMATED_STICKER_CDN_URLS: Dict[str, str] = {}


def load(path: str):
    """Loads CDN mapping.

    Example mapping file:
    {
      "UploadMapping": {
        "gapo-image.s3-ap-southeast-1.amazonaws.com": "image-1.gapo.vn",
        "gapo-video.s3-ap-southeast-1.amazonaws.com": "video-1.gapo.vn",
        "gapo-image.s3.storage.com.vn": "image-2.gapo.vn",
        "gapo-video.s3.storage.com.vn": "video-2.gapo.vn",
        "gapo-image.s3south.storage.com.vn": "image-3.gapo.vn",
        "gapo-video.s3south.storage.com.vn": "video-3.gapo.vn",
        "gapo-files.s3south.storage.com.vn": "files-3.gapo.vn",
        "gapo-image.statics.pancake.vn": "image-5.gapo.vn",
        "gapo-video.statics.pancake.vn": "video-5.gapo.vn",
        "gapo-files.statics.pancake.vn": "files-5.gapo.vn"
      },
      "ThumbMapping": {
        "image-1.gapo.vn": "cdn-thumb-image-1.gapo.vn",
        "image-2.gapo.vn": "cdn-thumb-image-2.gapo.vn",
        "image-3.gapo.vn": "cdn-thumb-image-3.gapo.vn",
        "image-5.gapo.vn": "cdn-thumb-image-5.gapo.vn"
      }
    }

    """
    image_prefix_format = "https://{host}/images"
    thumb_prefix_format = "https://{host}"

    file_prefix_format = "https://{host}/files/origin"
    video_prefix_format = "https://{host}/videos/origin"
    sticker_prefix_format = "https://{host}/sticker"
    with open(path) as json_file:
        cdn_mapping = json.load(json_file)

    upload_mapping = cdn_mapping["UploadMapping"]
    thumb_mapping = cdn_mapping["ThumbMapping"]

    prefix_thumb_image = {}

    prefix_image_urls = []
    prefix_file_urls = []
    prefix_sticker_urls = []

    prefix_file_cdn_urls = {}
    prefix_image_cdn_urls = {}
    prefix_sticker_cdn_urls = {}

    for k, v in upload_mapping.items():
        if "-image" in k:
            prefix_image_url = image_prefix_format.format(host=k)
            prefix_cdn_url = image_prefix_format.format(host=v)
            prefix_image_urls.append(prefix_image_url)
            prefix_image_urls.append(prefix_cdn_url)

            prefix_image_cdn_urls[prefix_image_url] = prefix_cdn_url
            thumb_url = thumb_mapping[v]
            prefix_thumb_url = thumb_prefix_format.format(host=thumb_url)

            prefix_thumb_image[prefix_image_url] = prefix_thumb_url
            prefix_thumb_image[prefix_cdn_url] = prefix_thumb_url

        elif "files" in k:
            prefix_file_url = file_prefix_format.format(host=k)
            prefix_sticker_url = sticker_prefix_format.format(host=k)

            prefix_cdn_url = file_prefix_format.format(host=v)
            prefix_cdn_sticker_url = sticker_prefix_format.format(host=v)
            prefix_file_urls.append(prefix_file_url)
            prefix_file_urls.append(prefix_cdn_url)

            prefix_sticker_urls.append(prefix_sticker_url)
            prefix_sticker_urls.append(prefix_cdn_sticker_url)

            prefix_sticker_cdn_urls[
                prefix_sticker_url
            ] = prefix_cdn_sticker_url
            prefix_file_cdn_urls[prefix_file_url] = prefix_cdn_url

            prefix_file_url = video_prefix_format.format(host=k)
            prefix_cdn_url = video_prefix_format.format(host=v)
            prefix_file_urls.append(prefix_file_url)
            prefix_file_urls.append(prefix_cdn_url)
            prefix_file_cdn_urls[prefix_file_url] = prefix_cdn_url
        elif "-video" in k:
            # add -video* as valid file domain
            prefix_file_url = video_prefix_format.format(host=k)
            prefix_cdn_url = video_prefix_format.format(host=v)
            prefix_file_urls.append(prefix_file_url)
            prefix_file_urls.append(prefix_cdn_url)
            prefix_file_cdn_urls[prefix_file_url] = prefix_cdn_url

    global PREFIX_THUMB_IMAGE
    PREFIX_THUMB_IMAGE.update(prefix_thumb_image)

    global PREFIX_IMAGE_URLS
    PREFIX_IMAGE_URLS.extend(set(prefix_image_urls))

    global PREFIX_FILE_URLS
    PREFIX_FILE_URLS.extend(set(prefix_file_urls))

    global PREFIX_STICKER_URLS
    PREFIX_STICKER_URLS.extend(set(prefix_sticker_urls))

    global PREFIX_IMAGE_CDN_URLS
    PREFIX_IMAGE_CDN_URLS.update(prefix_image_cdn_urls)

    global PREFIX_FILE_CDN_URLS
    PREFIX_FILE_CDN_URLS.update(prefix_file_cdn_urls)

    global PREFIX_ANIMATED_STICKER_CDN_URLS
    PREFIX_ANIMATED_STICKER_CDN_URLS.update(prefix_sticker_cdn_urls)
