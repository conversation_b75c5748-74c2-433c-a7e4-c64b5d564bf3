from abc import ABC, abstractmethod
from typing import List

from sqlalchemy.orm import Session

from chat.quick_message.model import QuickMessageModel


class QuickMessageRepository(ABC):
    @abstractmethod
    def create(
        self,
        session: Session,
        object: QuickMessageModel,
    ):
        pass

    @abstractmethod
    def update(
        self,
        session: Session,
        object: QuickMessageModel,
    ):
        pass

    @abstractmethod
    def delete(
        self,
        session: Session,
        user_id: str,
        command: str,
    ):
        pass

    @abstractmethod
    def get_by_user_id(
        self,
        session: Session,
        user_id: str,
        limit: int = 10,
        offset: int = 0,
    ) -> List[QuickMessageModel]:
        pass