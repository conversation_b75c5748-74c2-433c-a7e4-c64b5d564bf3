from typing import List, Optional

from sqlalchemy.orm import Session

from chat.messages.model import MessageBody
from chat.quick_message.model import QuickMessageModel
from chat.quick_message.repository.interface import QuickMessageRepository
from chat.utils.json import json_loads


class QuickMessageRepositoryMySQL(QuickMessageRepository):
    t_name = "quick_messages"

    def __init__(self):
        super().__init__()

    def _q_get_item(
        self, session: Session, query, params
    ) -> Optional[QuickMessageModel]:
        record = session.execute(query, params).fetchone()
        if record is None:
            return None
        return QuickMessageModel(**dict(record))

    def _q_get_items(
        self, session: Session, query, params
    ) -> List[QuickMessageModel]:
        records = session.execute(query, params).fetchall()
        items: List[QuickMessageModel] = []
        for record in records:
            if not record:
                continue
            record_d = dict(record)
            body = record_d.pop("body")
            items.append(
                QuickMessageModel(
                    **record_d, body=MessageBody(**json_loads(body))
                )
            )
        return items

    def _q_insert(self, session: Session, query, payload):
        session.execute(query, payload)

    def _q_update(self, session: Session, query, params):
        session.execute(query, params)

    def create(self, session: Session, obj: QuickMessageModel):
        payload = {
            "user_id": obj.user_id,
            "command": obj.command,
            "body": obj.body.json(),
        }
        query = f"""
        INSERT INTO {self.t_name} (user_id, command, body)
        VALUES (:user_id, :command, :body)
        """
        self._q_insert(session, query, payload)

    def update(self, session: Session, obj: QuickMessageModel):
        payload = {
            "user_id": obj.user_id,
            "command": obj.command,
            "body": obj.body.json(),
        }
        query = f"""
        UPDATE {self.t_name}
        SET body=:body
        WHERE user_id=:user_id AND command=:command
        """
        self._q_update(session, query, payload)

    def delete(self, session: Session, user_id: str, command: str):
        payload = {
            "user_id": user_id,
            "command": command,
        }
        query = f"""
        DELETE FROM {self.t_name}
        WHERE user_id=:user_id AND command=:command
        """
        self._q_update(session, query, payload)

    def get_by_user_id(
        self,
        session: Session,
        user_id: str,
        limit: int = 10,
        offset: int = 0,
    ) -> List[QuickMessageModel]:
        params = {"user_id": user_id, "limit": limit, "offset": offset}
        query = f"""
        SELECT * FROM {self.t_name}
        WHERE user_id=:user_id
        ORDER BY command DESC
        LIMIT :limit OFFSET :offset
        """
        return self._q_get_items(session, query, params)
