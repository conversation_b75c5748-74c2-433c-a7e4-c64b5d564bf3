from pydantic import BaseModel, Field, validator

from chat.messages.model.api import validate_body_message


def validate_command(v: str) -> str:
    if not v.startswith("/"):
        raise ValueError("Invalid command not start with /")

    if not v[1:].isprintable():
        raise ValueError("Invalid command must contain only printable characters")
    
    if "," in v[1:]:
        raise ValueError("Invalid command must not contain commas")
    
    if " " in v[1:]:
        raise ValueError("Invalid command must not contain spaces")
    
    return v.lower().strip()

class SchemaCommand(BaseModel):
    command: str = Field(..., max_length=20)

    _normalize_command = validator("command", allow_reuse=True)(validate_command)

class SchemaBody(BaseModel):
    body: dict

    _normalize_body = validator("body", allow_reuse=True)(validate_body_message)


class SchemaCreateQuickMessage(SchemaCommand, SchemaBody):
    pass


class SchemaEditQuickMessage(SchemaCommand, SchemaBody):
    pass



class SchemaGetAllQuickMessages(BaseModel):
    limit: int = Field(default=10, ge=1, le=100)
    offset: int = Field(default=0, ge=0, le=20000)


class SchemaDeleteQuickMessage(SchemaCommand):
    pass