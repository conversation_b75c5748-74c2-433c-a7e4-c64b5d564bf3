from dependency_injector import containers, providers

from chat.app import MyApp
from chat.quick_message.deliver.quick_message import QuickMessageResource
from chat.quick_message.usecase.all import QuickMessageUsecaseAll


class quickMessageResourcesContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(QuickMessageUsecaseAll)

    quick_message_resource = providers.Singleton(
        QuickMessageResource, app, usecases
    )




class QuickMessageContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Singleton(
        QuickMessageUsecaseAll, app=app
    )
    resources = providers.Singleton(
        quickMessageResourcesContainer, app=app, usecases=usecases
    )
