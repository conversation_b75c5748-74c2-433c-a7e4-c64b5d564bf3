import falcon

from chat import codes, response_messages
from chat.exception import ChatError


class QuickMessageError(ChatError):
    pass


class QuickMessageAlreadyExists(QuickMessageError):
    http_code = 409
    i18n_message = response_messages.QUICK_MESSAGE_ALREADY_EXISTS
    code = codes.QUICK_MESSAGE_ALREADY_EXISTS
    error = "quick_message_already_exists"

    def __init__(self, command):
        self.status = falcon.HTTP_409
        self.command = command
        super().__init__(self.status)

    def to_dict(self):
        return {
            "message": self.message,
            "code": self.code,
            "error": self.error,
            "command": self.command,
        }