from typing import Any, Dict

import sqlalchemy

from chat.messages.model import MessageBody
from chat.quick_message.exception import QuickMessageAlreadyExists
from chat.quick_message.model import QuickMessageModel
from chat.quick_message.usecase.base import QuickMessageUsecase


class QuickMessageUsecaseAll(QuickMessageUsecase):
    def create(self, user_id: str, command: str, body: Dict[Any, Any]):
        quick_message = QuickMessageModel(
            user_id=user_id, command=command, body=MessageBody(**body)
        )

        try:
            with self.session_m.get_session() as session:
                self.quick_message_repo.create(session, quick_message)
                session.commit()
        except sqlalchemy.exc.IntegrityError:
            raise QuickMessageAlreadyExists(command)
        return quick_message

    def get_all(self, user_id: str, limit: int, offset: int):
        with self.session_m.get_session() as session:
            return self.quick_message_repo.get_by_user_id(
                session, user_id, limit, offset
            )

    def update(self, user_id: str, command: str, body: Dict[Any, Any]):
        quick_message = QuickMessageModel(
            user_id=user_id, command=command, body=MessageBody(**body)
        )

        with self.session_m.get_session() as session:
            self.quick_message_repo.update(session, quick_message)
            session.commit()

        return quick_message

    def delete(self, user_id: str, command: str):
        with self.session_m.get_session() as session:
            self.quick_message_repo.delete(session, user_id, command)
            session.commit()