
from chat.app import MyApp
from chat.quick_message.repository.interface import QuickMessageRepository


class QuickMessageUsecase(object):
    def __init__(
        self,
        app: MyApp,
    ):
        self.config = app.conf
        self.log = app.log
        self.session_m = app.conns.mysql
        self.metrics = app.app_metrics
        self.quick_message_repo: QuickMessageRepository = (
            app.repos.quick_message_repo
        )
