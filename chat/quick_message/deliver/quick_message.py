import falcon

from chat.app.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.exception import InvalidParameters
from chat.hooks import making_quick_message_hook
from chat.model import make_input
from chat.quick_message.model.api import (
    SchemaCreateQuickMessage,
    SchemaDeleteQuickMessage,
    SchemaEditQuickMessage,
)
from chat.quick_message.usecase.all import QuickMessageUsecaseAll


class QuickMessageResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: QuickMessageUsecaseAll):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    @falcon.before(making_quick_message_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        raw["user_id"] = user_id
        body, errors = make_input(SchemaCreateQuickMessage, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        quick_message = self.usecase.create(
            user_id, body.command, body.body
        )
        resp.media = {"data": quick_message.dict()}
        resp.status = falcon.HTTP_200
        return

    @falcon.before(making_quick_message_hook)
    def on_patch(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        raw["user_id"] = user_id
        body, errors = make_input(SchemaEditQuickMessage, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        # TOD: quên chưa check exist, nếu mà k bị exist thì kệ tạm thời    
        quick_message = self.usecase.update(
            user_id, body.command, body.body
        )
        resp.media = {"data": quick_message.dict()}
        resp.status = falcon.HTTP_200
        return

    @falcon.before(making_quick_message_hook)
    def on_delete(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        raw["user_id"] = user_id
        body, errors = make_input(SchemaDeleteQuickMessage, raw)
        if errors:
            raise InvalidParameters(error_details=errors)
        self.usecase.delete(user_id, body.command)
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
        return