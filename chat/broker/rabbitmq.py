from logging import Logger
from typing import Dict
from uuid import uuid4

import pika

from chat import constant
from chat.config import Settings
from chat.publishers.rabbitmq import AmqpProducer
from chat.utils.json import json_dumps
from chat.utils import common
from chat.messages.model import MessageModel


class BaseRabbitmqBroker(object):
    def __init__(
        self, config: Settings, log: Logger, engine: AmqpProducer
    ) -> None:
        self.mcfg = config.rabbitmq.event_route["MESSAGE"]
        self.msg_routing_key = self.mcfg.routing_key
        self.config = config
        self.log = log
        self.engine = engine

    def get_sleep(self, jobs: int = 1) -> float:
        if jobs < constant.MAX_INVITE_PER_JOB:
            return 0

        if self.config.is_staging():
            return 0.1
        return 0.05

    def publish_background_task(
        self,
        event_type: str,
        body: Dict,
        priority: int = constant.AMQP_PRIORITY_MEDIUM,
        is_heavy=False,
    ):
        """Submits a background task."""

        new_uuid = str(uuid4())
        event_at = common.now()
        message = {
            "event_type": event_type,
            "body": body,
            "uuid": new_uuid,
            "version": "3",
            "event_at": event_at,
        }
        self.log.info(f"[AMQP] Publisher background task {message}")

        key = constant.TASK_ROUTING_KEY

        if is_heavy:
            key = constant.TASK_HEAVY_ROUTING_KEY

        if self.engine.publish(
            self.mcfg.exchange_name,
            key,
            json_dumps(message),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
                priority=priority,
            ),
            mandatory=True,
        ):
            return new_uuid

    def publish_message(
        self,
        event_type: str,
        body: Dict,
        priority: int = constant.AMQP_PRIORITY_MEDIUM,
        api_version="3",
        skip_message=False,
        event_at=0,
    ):
        """Publishes a message event.

        Params:
            skip_message: if this is enable,
                        message worker won't create new message.
        """

        new_uuid = str(uuid4())
        if not event_at:
            event_at = common.now()
        message = {
            "event_type": event_type,
            "skip_message": skip_message,
            "body": body,
            "uuid": new_uuid,
            "version": api_version,
            "event_at": event_at,
        }
        if self.engine.publish(
            self.mcfg.exchange_name,
            self.msg_routing_key,
            json_dumps(message),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
                priority=priority,
            ),
            mandatory=True,
        ):
            self.log.info(f"[AMQP] Publisher message event {new_uuid}")
            return new_uuid

    def publish_status(
        self,
        event_type: str,
        body: Dict,
        priority: int = constant.AMQP_PRIORITY_MEDIUM,
        api_version="3",
    ):
        """Publishes a change event related to thread."""

        new_uuid = str(uuid4())
        event_at = common.now()
        message = {
            "event_type": event_type,
            "body": body,
            "uuid": new_uuid,
            "event_at": event_at,
            "version": api_version,
        }
        if self.engine.publish(
            self.mcfg.exchange_name,
            constant.STATUS_ROUTING_KEY,
            json_dumps(message),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
                priority=priority,
            ),
            mandatory=False,
        ):
            self.log.info(f"[AMQP] Publisher status {new_uuid}")
            return new_uuid

    def publish_delete(self, m: MessageModel):
        data = m.to_delete_event().dict()
        if self.engine.publish(
            constant.DELETE_EX,
            constant.DELETE_RK,
            json_dumps(data),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
            ),
            mandatory=False,
        ):
            self.log.info(f"[AMQP] Publisher delete m.id {m.id}")
            return m.id

    def publish_action_note(
        self,
        event_type: str,
        body: Dict,
        priority: int = constant.AMQP_PRIORITY_MEDIUM,
        skip_message: bool = False,
        api_version="3",
    ):
        """Publishes an action note."""

        new_uuid = str(uuid4())
        event_at = common.now()
        message = {
            "event_type": event_type,
            "skip_message": skip_message,
            "body": body,
            "uuid": new_uuid,
            "version": api_version,
            "event_at": event_at,
        }
        if self.engine.publish(
            self.mcfg.exchange_name,
            self.msg_routing_key,
            json_dumps(message),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
                priority=priority,
            ),
            mandatory=True,
        ):
            self.log.info(f"[AMQP] Publisher action_note {new_uuid}")
            return new_uuid

    def is_queue_full(self):
        return self.engine._queue.full()
