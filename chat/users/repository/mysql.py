from typing import Optional

from sqlalchemy.orm import Session

from .interface import UserRepository


class UserRepositoryMySQL(UserRepository):
    t_name = "users"

    def __init__(self, log):
        self.log = log

    def get_pass_code(self, session: Session, user_id: str):
        get_by_id_q = f"SELECT pass_code FROM {self.t_name} WHERE id=:id"
        result = session.execute(get_by_id_q, {"id": user_id}).fetchone()
        if result:
            return dict(result)
        return result

    def create_pass_code(self, session: Session, user_id: str, pass_code):
        insert_pass_code_q = (
            f"INSERT INTO {self.t_name}(id, pass_code) "
            "VALUES(:id, :pass_code) "
            "ON DUPLICATE KEY UPDATE pass_code=:pass_code"
        )

        session.execute(
            insert_pass_code_q,
            {"id": user_id, "pass_code": pass_code},
        )

    def update_pass_code(
        self, session: Session, user_id: str, pass_code: Optional[str]
    ):
        update_pass_code_q = (
            f"UPDATE {self.t_name} SET pass_code=:pass_code WHERE id=:id"
        )

        session.execute(
            update_pass_code_q,
            {"id": user_id, "pass_code": pass_code},
        )
