from logging import Logger
from typing import List

from chat.config import Settings
from chat.connections.cassandra_c import CassandraConnection


class UserRepositoryCassandra(object):
    """NOTE: please don't use this."""

    def __init__(
        self, config: Settings, log: Logger, engine: CassandraConnection
    ):
        self.cc = config.cassandra
        self.log = log
        self.session = engine
        self.BUCKET_FACTOR = config.bucket_message_factor

    def get_by_id(self, user_id: str):
        result = self.session.execute(
            "SELECT * FROM users WHERE id=%s LIMIT 1", (user_id,)  # noqa
        )
        return result.one()

    def get_in(self, uids):
        if len(uids) == 0:
            return []

        params = "(" + ",".join(["%s"] * len(uids)) + ")"
        query = "SELECT * FROM users WHERE id IN {}".format(params)
        result = self.session.execute(query, uids)
        return result.current_rows

    def update(self, user_id: str, seen_at):
        query = "UPDATE users SET seen_at=%s WHERE id=%s"
        self.session.execute(
            query,
            (
                seen_at,
                user_id,
            ),
        )

    def get_all(self, user_ids: List[str]):
        if len(user_ids) == 0:
            return []

        if len(user_ids) == 1:
            query = "SELECT * FROM users WHERE id=%s LIMIT 1"
        else:
            limit = len(user_ids)
            params = "(" + ",".join(["%s"] * len(user_ids)) + ")"
            query = "SELECT * FROM users WHERE id IN {} LIMIT {}".format(
                params, limit
            )
        result = self.session.execute(query, user_ids)
        return result.current_rows
