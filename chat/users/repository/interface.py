from abc import ABC, abstractmethod
from typing import Optional

from sqlalchemy.orm import Session

from chat.users.model import UserPasscode


class UserRepository(ABC):
    @abstractmethod
    def get_pass_code(
        self, session: Session, user_id: str
    ) -> Optional[UserPasscode]:
        pass

    @abstractmethod
    def create_pass_code(self, session: Session, user_id: str, pass_code: str):
        pass

    @abstractmethod
    def update_pass_code(
        self, session: Session, user_id: str, pass_code: Optional[str]
    ):
        pass
