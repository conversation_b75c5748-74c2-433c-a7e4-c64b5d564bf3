from typing import Union

from typing_extensions import TypedDict

from chat import constant
from chat.app import <PERSON><PERSON><PERSON>
from chat.exception import MissingPasscode, WrongPasscode
from chat.security import pass_code as PassCode


class PassCodeResp(TypedDict):
    pass_code: Union[None, str]


class UserUsecase(object):
    def __init__(self, app: MyApp):
        self.session_m = app.conns.mysql
        self.user_repo = app.repos.user
        self.config = app.conf
        self.log = app.log
        self.redis_rate_limit = app.repos.rate_limit
        self.gapo_client = app.repos.gapo
        self.pt_repo = app.repos.pt
        self.thread_broker = app.brokers.thread_rd
        self.mqtt_broker = app.brokers.user_mqtt

    def get_pass_code(self, user_id: str) -> PassCodeResp:
        with self.session_m.get_session() as session:
            data = self.user_repo.get_pass_code(session, user_id)
            if not data or data["pass_code"] is None:
                return {"pass_code": None}
            pass_code = data["pass_code"]
            return {"pass_code": pass_code.split("|")[1]}

    def update(self, user_id: str, old_pass_code: str, new_pass_code: str):
        """Updates user pass-code (for secret threads)."""

        with self.session_m.get_session() as session:
            if self.redis_rate_limit.is_pass_code_limit(user_id):
                raise WrongPasscode("Error wrong old passcode")

            data = self.user_repo.get_pass_code(session, user_id)
            if not data or not data["pass_code"]:
                raise MissingPasscode("Error no passcode")

            hashed_pass_code = data["pass_code"]
            if not PassCode.validate(old_pass_code, hashed_pass_code):
                self.redis_rate_limit.set_pass_code_limit(user_id)
                raise WrongPasscode("Error wrong old passcode")
            self.redis_rate_limit.del_pass_code_limit(user_id)
            hashed_new_pass_code = PassCode.generate(new_pass_code)
            self.user_repo.update_pass_code(
                session, user_id, hashed_new_pass_code
            )
            session.commit()

    def verify(self, user_id: str, pass_code: str):
        """Verifies passcode."""

        with self.session_m.get_session() as session:
            if self.redis_rate_limit.is_pass_code_limit(user_id):
                raise WrongPasscode("Error wrong old passcode")

            data = self.user_repo.get_pass_code(session, user_id)
            if not data or not data["pass_code"]:
                raise MissingPasscode("Error no passcode")

            hashed_pass_code = data["pass_code"]
            if not PassCode.validate(pass_code, hashed_pass_code):
                self.redis_rate_limit.set_pass_code_limit(user_id)
                raise WrongPasscode("Error wrong old passcode")
            self.redis_rate_limit.del_pass_code_limit(user_id)
            session.commit()
            return True

    def create(self, user_id: str, pass_code: str):
        """Creates pass code for user."""

        with self.session_m.get_session() as session:
            data = self.user_repo.get_pass_code(session, user_id)
            if not data or not data["pass_code"]:
                hashed_pass_code = PassCode.generate(pass_code)
                self.user_repo.create_pass_code(
                    session, user_id, hashed_pass_code
                )
                session.commit()
            else:
                raise WrongPasscode("Error alreay created passcode")

    def reset(self, user_id: str):
        """Reset pass-code."""

        folder_name = "secret"
        with self.session_m.get_session() as session:
            self.user_repo.update_pass_code(session, user_id, None)
            thread_ids = self.thread_broker.get_ordered_thread_list(
                user_id=user_id, folder=folder_name
            )
            self.pt_repo.clear_and_move_folder_user_threads(
                session,
                user_id,
                constant.FOLDER_SECRET,
                constant.FOLDER_DEFAULT,
            )
            if thread_ids:
                self.thread_broker.remove_folder(user_id, folder_name)
                self.mqtt_broker.publish_thread_delete([user_id], thread_ids)
            session.commit()
        self.redis_rate_limit.del_pass_code_limit(user_id)
