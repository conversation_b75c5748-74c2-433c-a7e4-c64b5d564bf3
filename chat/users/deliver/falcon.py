# import falcon

# from chat.app import UserAuthResource
# from chat.app.app import MyApp
# from chat.exception import InvalidParameters
# from chat.hooks import making_user_hook
# from chat.model import make_input
# from chat.users.model.api import <PERSON>hem<PERSON><PERSON><PERSON>, SchemaEdit, SchemaVerify
# from chat.users.usecase import UserUsecase


# class UserPassCodeResource(UserAuthResource):
#     def __init__(self, app: MyApp, usecases: UserUsecase):
#         self.config = app.conf
#         self.log = app.log
#         self.usecase = usecases

#     def on_get(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         message = self.usecase.get_pass_code(user_id)
#         resp.media = {"data": message}
#         resp.status = falcon.HTTP_200

#     @falcon.before(making_user_hook)
#     def on_patch(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         raw = req.context.body
#         body, errors = make_input(SchemaEdit, raw)
#         if errors:
#             self.log.error(errors)
#             raise InvalidParameters(error_details=errors)

#         self.usecase.update(user_id, body.pass_code, body.new_pass_code)
#         resp.media = {"data": {"message": "ok"}}
#         resp.status = falcon.HTTP_200

#     @falcon.before(making_user_hook)
#     def on_post(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         raw = req.context.body
#         body, errors = make_input(SchemaCreate, raw)
#         if errors:
#             raise InvalidParameters(error_details=errors)

#         self.usecase.create(user_id, body.pass_code)
#         resp.media = {"data": {"message": "ok"}}
#         resp.status = falcon.HTTP_200

#     def on_delete(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         try:
#             self.usecase.reset(user_id)
#             resp.media = {"data": {"message": "ok"}}
#             resp.status = falcon.HTTP_200
#         except Exception as e:
#             raise e
#             resp.status = falcon.HTTP_403


# class UserPassCodeVerifyResource(UserAuthResource):
#     def __init__(self, app: MyApp, usecases: UserUsecase):
#         self.config = app.conf
#         self.log = app.log
#         self.usecase = usecases

#     @falcon.before(making_user_hook)
#     def on_post(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         raw = req.context.body
#         body, errors = make_input(SchemaVerify, raw)
#         if errors:
#             raise InvalidParameters(error_details=errors)

#         self.usecase.verify(user_id, body.pass_code)
#         resp.status = falcon.HTTP_200
#         resp.media = {"data": {"message": "ok"}}
