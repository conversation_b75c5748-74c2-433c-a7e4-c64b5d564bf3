# from logging import Logger

# from chat.config import Settings
# from chat.connections.redis_c import RedisConnection


# class UserRedisBroker(object):
#     def __init__(self, config: Settings, log: Logger, engine: RedisConnection):
#         self.config = config.redis
#         self.log = log
#         self.engine = engine
#         self.tsf = self.config.thread_score_format
#         self.tfsf = self.tsf + "_{folder}"
#         self.dnfrf = self.config.direct_n_f_rate_format
#         self.SECONDS_PER_DAY = 86400
#         self.GMT = 7

#     # def get_all_thread_score_list(self, user_id: str, folder):
#     #     key = self.tfsf.format(user_id=user_id, folder=folder)
#     #     timestamp = "+inf"
#     #     result = [
#     #         int(r) for r in self.engine.client.zrevrangebyscore(key, timestamp, "-inf")
#     #     ]
#     #     return result

#     # def del_folder(self, user_id: str, folder):
#     #     c = self.engine.client
#     #     key = self.tfsf.format(user_id=user_id, folder=folder)
#     #     c.delete(key)
