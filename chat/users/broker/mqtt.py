from logging import Logger
from typing import List

from chat.config import Settings
from chat.publishers.mqtt import MqttPublisher


class UserMQTTBroker(object):
    def __init__(self, config: Settings, log: Logger, engine: MqttPublisher):
        self.config = config.mqtt
        self.log = log
        self.engine = engine

    def publish_thread_delete(
        self, member_ids: List[str], thread_ids: List[int]
    ):
        for thread_id in thread_ids:
            msg = {
                "thread_id": thread_id,
            }
            event = {"event_type": "thread_deleted", "body": msg}
            self.engine.publish_status(member_ids, event)
