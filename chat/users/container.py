from dependency_injector import containers, providers

from chat.app import MyApp
from chat.users.usecase import UserUsecase


# class UserResourcesContainer(containers.DeclarativeContainer):
#     app = providers.Dependency(MyApp)
#     usecases = providers.Dependency(UserUsecase)

#     pass_code = providers.Singleton(UserPassCodeResource, app, usecases)
#     verify = providers.Singleton(UserPassCodeVerifyResource, app, usecases)


class UserContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Singleton(UserUsecase, app=app)

    # resources = providers.Container(
    #     UserResourcesContainer, app=app, usecases=usecases
    # )
