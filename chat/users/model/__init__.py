from typing import Dict

from typing_extensions import NotRequired, TypedDict


class UserPasscode(TypedDict):
    _created_at: int
    _deleted: NotRequired[int]
    id: str
    name: str
    seen_at: int
    enable_notify: int
    channels: str
    pass_code: str


class PublicProfile(TypedDict):
    name: str
    avatar: NotRequired[str]
    id: str
    type: str
    status_verify: NotRequired[int]

    workspace_id: NotRequired[str]
    creator_id: NotRequired[str]
    creator_type: NotRequired[str]



class UserInfo(PublicProfile):
    should_cache: bool
    info: NotRequired[Dict]

    status: NotRequired[int]
    """User status. 1 -> active. 2 -> deactivated."""

    company: NotRequired[str]
    title: NotRequired[str]
    department: NotRequired[str]
