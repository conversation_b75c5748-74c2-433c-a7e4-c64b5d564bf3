from pydantic import root_validator, validator

from chat.model import BaseModel

MAX_PASSCODE = 4


class SchemaCreate(BaseModel):
    pass_code: str

    @validator("pass_code")
    def type_check(cls, v):
        if not v.isdigit():
            raise ValueError(f"Type invalid {v}")
        if len(v) != MAX_PASSCODE:
            raise ValueError(f"Length invalid {v}")

        return v


class SchemaVerify(BaseModel):
    pass_code: str

    @validator("pass_code")
    def type_check(cls, v):
        if not v.isdigit():
            raise ValueError(f"Type invalid {v}")
        if len(v) != MAX_PASSCODE:
            raise ValueError(f"Length invalid {v}")

        return v


class SchemaEdit(BaseModel):
    pass_code: str
    new_pass_code: str

    @validator("pass_code")
    def type_check_pass(cls, v):
        if not v.isdigit():
            raise ValueError(f"Type invalid {v}")
        if len(v) != MAX_PASSCODE:
            raise ValueError(f"Length invalid {v}")

        return v

    @validator("new_pass_code")
    def type_check_new_pass(cls, v):
        if not v.isdigit():
            raise ValueError(f"Type invalid {v}")
        if len(v) != MAX_PASSCODE:
            raise ValueError(f"Length invalid {v}")

        return v

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        pass_code = values["pass_code"]
        new_pass_code = values["new_pass_code"]
        if pass_code == new_pass_code:
            raise ValueError("Pass and new-pass should not the same")
        return values


class SchemaOutput(BaseModel):
    pass_code: str
