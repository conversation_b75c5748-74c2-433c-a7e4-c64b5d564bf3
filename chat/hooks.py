import falcon

from chat.exception import CustomHT<PERSON><PERSON>rror
from chat.utils.json import json_loads


def load_request_body(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST", "PATCH"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )


def making_message_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST", "PATCH", "PUT"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSO<PERSON>."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )

def making_quick_message_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST", "PATCH", "DELETE"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )



def making_participant_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )


def making_block_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )


def making_admin_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )


def making_user_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST", "PATCH"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )


def making_folder_hook(
    req: falcon.Request, resp: falcon.Response, resource, params
):
    if req.method in ("POST", "PATCH"):
        if req.content_type is None:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        if "application/json" not in req.content_type:
            raise CustomHTTPError(
                400,
                error={
                    "message": "This API only supports requests encoded as JSON."  # noqa
                },
            )
        try:
            body = json_loads(req.bounded_stream.read())
            req.context.body = body
        except Exception:
            raise CustomHTTPError(
                400,
                error={"message": "Body required"},
            )
