# import falcon

# from chat.app.app import MyApp
# from chat.app.resource import UserAndServiceResource
# from chat.block_users.model.api import SchemaCreate, SchemaOutput
# from chat.block_users.usecase import BlockUserUsecase
# from chat.exception import (
#     InternalServerError,
#     PermissionDenied,
#     UserBlockingFeatureDisabled,
# )
# from chat.hooks import making_block_hook
# from chat.model import make_input, make_output

# # from core.utils import validate_schema, normalize_schema
# # from core.block_users.model.cerberus import (
# #     INPUT_BLOCK_USER, OUTPUT_BLOCK_USER, OUTPUT_RENAME_BLOCK_USER,
# #     INPUT_PAGE_ID, INPUT_USER_ID)


# class BlockUserResource(UserAndServiceResource):
#     def __init__(self, app: MyApp, usecases: BlockUserUsecase):
#         self.config = app.conf
#         self.log = app.log
#         self.usecase = usecases

#     def on_get(self, req: falcon.Request, resp: falcon.Response):
#         user_id = self.user_id
#         block_list = self.usecase.get_by_id(user_id)
#         results = []
#         for m in block_list:
#             result, errors = make_output(SchemaOutput, m)
#             if errors:
#                 raise InternalServerError(error_details=errors)
#             results.append(result.dict())
#         resp.media = {"data": results}
#         resp.status = falcon.HTTP_200

#     @falcon.before(making_block_hook)
#     def on_post(self, req: falcon.Request, resp: falcon.Response):
#         if self.config.features.disable_blocking_users:
#             raise UserBlockingFeatureDisabled()

#         # body, errors = validate_schema(INPUT_BLOCK_USER, req.context.body)
#         # if errors:
#         #     self.log.error(errors)
#         #     resp.status = falcon.HTTP_400
#         #     resp.media = errors
#         #     return

#         body, errors = make_input(SchemaCreate, req.context.body)
#         if errors:
#             self.log.warning(errors)
#             resp.status = falcon.HTTP_400
#             resp.media = errors
#             return
#         user_id = self.user_id
#         block_id = body.id
#         block_type = body.type
#         action_type = body.action

#         # if block_type == "user":
#         #     _, errors = validate_schema(INPUT_USER_ID, body, True)

#         # if errors:
#         #     self.log.error(errors)
#         #     resp.status = falcon.HTTP_400
#         #     resp.media = errors
#         #     return
#         try:
#             block_list = getattr(self.usecase, action_type)(
#                 user_id, block_id, block_type
#             )
#             # results = [normalize_schema(
#             #     OUTPUT_RENAME_BLOCK_USER,
#             #     normalize_schema(OUTPUT_BLOCK_USER, m, True)) for m in messages # noqa
#             #     if m is not None
#             # ]
#             results = []
#             for b in block_list:
#                 result, errors = make_output(SchemaOutput, b)
#                 if errors:
#                     raise InternalServerError(error_details=errors)
#                 results.append(result.dict())
#             resp.media = {"data": results}
#             resp.status = falcon.HTTP_200

#         except PermissionDenied:
#             resp.media = {"message": "Already block"}
#             resp.status = falcon.HTTP_403
