from pydantic import Field, root_validator, validator

from chat import constant
from chat.model import BaseModel
from chat.utils.validator_helper import is_user_id


class SchemaCreate(BaseModel):
    id: str
    action: str
    type: str

    @validator("type", pre=True)
    def type_check(cls, v):
        if v not in (constant.USER_TYPE, constant.PAGE_TYPE):
            raise ValueError(f"Type invalid {v}")
        return v

    @validator("action")
    def action_check(cls, v):
        if v not in ("add", "remove"):
            raise ValueError(f"Action invalid {v}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        block_id = values["id"]
        block_type = values["type"]

        if block_type == constant.USER_TYPE:
            if not is_user_id(block_id):
                raise ValueError(
                    f"BlockID invalid {block_id} with BlockType {block_type}"
                )
        else:
            raise ValueError(f"Invalid block type {block_type}")

        return values


class SchemaOutput(BaseModel):
    id: str = Field(..., alias="blocked_id")
    type: str
