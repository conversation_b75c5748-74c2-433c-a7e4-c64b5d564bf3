from abc import ABC, abstractmethod
from typing import Any, List, Optional

from sqlalchemy.orm import Session
from typing_extensions import TypedDict

from chat.block_users.model import BlockedUser


class IDOnly(TypedDict):
    id: str


class BlockUserRepository(ABC):
    @abstractmethod
    def get_in(
        self, session: Session, user_id: str, part_ids: List[Any]
    ) -> List[IDOnly]:
        pass

    @abstractmethod
    def get_all(self, session: Session, user_id: str) -> List[BlockedUser]:
        pass

    @abstractmethod
    def get_by_id(
        self, session: Session, user_id: str, blocked_id
    ) -> Optional[BlockedUser]:
        pass

    @abstractmethod
    def check_block(
        self, session: Session, user_id: str, blocked_id
    ) -> Optional[BlockedUser]:
        pass

    @abstractmethod
    def add(self, session: Session, user_id: str, blocked_id=None):
        pass

    @abstractmethod
    def remove(self, session: Session, user_id: str, blocked_id):
        pass
