from typing import Any, List

from sqlalchemy.orm import Session

from chat.utils.common import make_pair_ids

from .interface import BlockUserRepository


class BlockUserRepositoryMySQL(BlockUserRepository):
    t_name = "block_users"

    def __init__(self, log):
        self.log = log
        # self.INSERT_PAGE_BLOCK = (
        #     f"INSERT INTO {self.t_name}"
        #     "(user_id, blocked_id, pair_ids, type) "
        #     "VALUES(:user_id, :blocked_id, :pair_ids, :type) "
        #     "ON DUPLICATE KEY UPDATE "
        #     "is_removed=0"
        # )

    def get_in(self, session: Session, user_id: str, part_ids: List[Any]):
        if len(part_ids) == 0:
            return []

        get_in = (
            "SELECT blocked_id AS id "
            f"FROM {self.t_name} "
            "WHERE user_id=:user_id "
            "AND blocked_id IN :blocked_ids "
            "AND is_removed=False "
            "UNION "
            "SELECT user_id AS id "
            f"FROM {self.t_name} "
            "WHERE user_id IN :blocked_ids "
            "AND blocked_id=:user_id "
            "AND is_removed=False"
        )

        result = session.execute(
            get_in, {"user_id": user_id, "blocked_ids": part_ids}
        ).fetchall()
        if result:
            return [r["id"] for r in result]
        return []

    def get_all(self, session: Session, user_id: str):
        get_block_list = (
            "SELECT * "
            f"FROM {self.t_name} "
            "WHERE user_id=:user_id "
            "AND is_removed=False"
        )

        result = session.execute(
            get_block_list, {"user_id": user_id}
        ).fetchall()
        if result:
            return [dict(r) for r in result]
        return result

    def get_by_id(self, session: Session, user_id: str, blocked_id):
        get_block = (
            "SELECT * "
            f"FROM {self.t_name} "
            "WHERE user_id=:user_id "
            "AND blocked_id=:blocked_id "
            "AND is_removed=False "
            "LIMIT 1"
        )

        result = session.execute(
            get_block, {"user_id": user_id, "blocked_id": blocked_id}
        )
        return result.fetchone()

    def check_block(self, session: Session, user_id: str, blocked_id):
        check_block = (
            "SELECT * "
            f"FROM {self.t_name} "
            "WHERE pair_ids=:pair_ids "
            "AND is_removed=False "
            "LIMIT 1"
        )
        pair_ids = make_pair_ids(user_id, user_b=blocked_id)
        result = session.execute(check_block, {"pair_ids": pair_ids})
        return result.fetchone()

    def add(self, session: Session, user_id: str, blocked_id=None):
        insert_block = (
            f"INSERT INTO {self.t_name}"
            "(user_id, blocked_id, pair_ids) "
            "VALUES(:user_id, :blocked_id, :pair_ids) "
            "ON DUPLICATE KEY UPDATE "
            "is_removed=0"
        )

        session.execute(
            insert_block,
            {
                "user_id": user_id,
                "blocked_id": blocked_id,
                "pair_ids": make_pair_ids(user_id, user_b=blocked_id),
            },
        )

    def remove(self, session: Session, user_id: str, blocked_id):
        remove_block = (
            f"UPDATE {self.t_name} "
            "SET is_removed=True "
            "WHERE user_id=:user_id "
            "AND blocked_id=:blocked_id"
        )

        session.execute(
            remove_block, {"user_id": user_id, "blocked_id": blocked_id}
        )
