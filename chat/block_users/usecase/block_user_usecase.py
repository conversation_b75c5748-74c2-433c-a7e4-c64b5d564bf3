from chat.app import <PERSON><PERSON><PERSON>
from chat.exception import PermissionDenied
from chat.utils import common


class BlockUserUsecase(object):
    def __init__(self, app: MyApp):
        self.thread_repo = app.repos.thread
        self.gapo_client = app.repos.gapo
        self.block_repo = app.repos.block
        self.config = app.conf
        self.log = app.log
        self.mqtt_broker = app.brokers.block_user_mqtt

        self.session_m = app.conns.mysql

    def get_by_id(self, user_id: str):
        with self.session_m.get_session() as session:
            block_list = self.block_repo.get_all(session, user_id)
            return block_list

    def add(self, user_id: str, block_id, block_type):
        maker_id = None
        maker_type = None

        receiver_id = None
        receiver_type = None

        action_success = False

        with self.session_m.get_session() as session:
            if self.block_repo.get_by_id(session, block_id, user_id):
                raise PermissionDenied("Already block")

            maker_id = user_id
            maker_type = "user"
            receiver_type = block_type
            receiver_id = block_id

            if not self.block_repo.get_by_id(session, user_id, block_id):
                if block_type == "user":
                    self.block_repo.add(session, user_id, block_id)
                    pair_ids = common.make_pair_ids(user_id, block_id)
                    self.thread_repo.block_thread_by_partner(
                        session, user_id, pair_ids
                    )

                action_success = True
            session.commit()

            block_list = self.block_repo.get_all(session, user_id)
        if action_success:
            self._publish_sync(
                "block", maker_id, maker_type, receiver_id, receiver_type
            )
        return block_list

    def remove(self, user_id: str, block_id, block_type):

        maker_id = None
        maker_type = None

        receiver_id = None
        receiver_type = None

        action_success = False

        with self.session_m.get_session() as session:
            if self.block_repo.get_by_id(session, user_id, block_id):
                maker_id = user_id
                maker_type = "user"
                receiver_id = block_id
                receiver_type = block_type

                self.block_repo.remove(session, user_id, block_id)

                pair_ids = common.make_pair_ids(user_id, block_id)
                self.thread_repo.unblock_thread_by_partner(session, pair_ids)
                action_success = True
            session.commit()
            block_list = self.block_repo.get_all(session, user_id)
            if action_success:
                self._publish_sync(
                    "unblock", maker_id, maker_type, receiver_id, receiver_type
                )
            return block_list

    def _publish_sync(
        self, action, maker_id, maker_type, receiver_id, receiver_type
    ):
        members = (
            {"id": maker_id, "type": maker_type},
            {"id": receiver_id, "type": receiver_type},
        )
        sync_block_message = {
            "event_type": action,
            "event_at": common.now(),
            "event_id": common.generate_event_id(),
            "body": {"maker": members[0], "receiver": members[1]},
        }
        self.mqtt_broker.publish_message(members, sync_block_message)  # type: ignore # noqa
