from logging import Logger
from typing import List

from chat.config import Settings
from chat.publishers.mqtt import MqttPublisher
from chat.users.model import PublicProfile
from chat.utils.common import parse_member_id


class BlockUserMQTTBroker(object):
    def __init__(self, config: Settings, log: Logger, engine: MqttPublisher):
        self.config = config.mqtt
        self.log = log
        self.engine = engine

    def publish_message(self, members: List[PublicProfile], event):
        id_collection = parse_member_id(members)
        self.engine.publish_status(id_collection["user_ids"], event)
