# pylint: skip-file
import datetime
import subprocess
import json
import os
import random
import re
import signal
import string
import time
import uuid
from contextlib import contextmanager
from functools import wraps
from math import ceil, floor
from typing import TYPE_CHECKING, Dict, List, Optional
from urllib.parse import urlparse

import phonenumbers
from cassandra.util import SortedSet
from urlquote import quote

from chat.constant import (
    BOT_TYPE,
    LENGTH_FOLDER_ALIAS,
    SYSTEM_ID,
    SYSTEM_TYPE,
    USER_TYPE,
)

if TYPE_CHECKING:
    from chat.users.model import PublicProfile

EPOCH = datetime.datetime(1970, 1, 1)

# example: v3/topics/1584586572145
channel_regex = re.compile(
    "^v3/channels/[1-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]$"  # noqa
)  # noqa


def utcnow_timestamp():
    return int((datetime.datetime.utcnow() - EPOCH).total_seconds())


FILE_CACHE_SAMPLE = "/tmp/gapo_cache_health.txt"


def get_git_revision_short_hash() -> Optional[str]:
    try:
        return (
            subprocess.check_output(["git", "rev-parse", "--short", "HEAD"])
            .decode("ascii")
            .strip()
        )
    except Exception:
        return None


GIT_COMMIT_VERSION = get_git_revision_short_hash()


def cache_health(handler):
    @wraps(handler)
    def wrap(*args, **kwargs):
        now = int(time.time())
        modified = None
        try:
            modified = os.path.getmtime(FILE_CACHE_SAMPLE)
            if int(now - modified) < 2:
                with open(FILE_CACHE_SAMPLE) as json_file:
                    return json.load(json_file)
        except (FileNotFoundError, json.JSONDecodeError):
            pass

        result = handler(*args, **kwargs)
        result["cache_at"] = modified
        result["check_at"] = now
        with open(FILE_CACHE_SAMPLE, "w") as outfile:
            json.dump(result, outfile)
        return result

    return wrap


def cassandra_json(o):
    if isinstance(o, datetime.datetime):
        return int(datetime.datetime.timestamp(o) * 1000)
    elif isinstance(o, SortedSet):
        return list(o)


def get_pagination(from_, to, max_page, begin=0):
    if from_ < 0 or to < 0:
        raise ValueError("Must be positive number")
    if from_ >= to:
        raise ValueError("From must be smaller than to")
    if begin == 0:
        round_alg = floor
    else:
        round_alg = ceil  # type: ignore

    result = []
    while True:
        f_p = (floor(from_ / max_page) + 1) * max_page
        if f_p >= to:
            result.append((from_, to, round_alg(from_ / max_page)))
            return result
        else:
            result.append((from_, f_p, round_alg(from_ / max_page)))
            from_ = f_p


def order_with_page(ids, max_page, begin=0) -> Dict[int, List[int]]:
    if not isinstance(ids, list) and not isinstance(ids, set):
        raise ValueError("Must be list or set")

    if isinstance(ids, set):
        ids = list(ids)
    ids.sort()

    if begin == 0:
        round_alg = floor
    else:
        round_alg = ceil  # type: ignore
    result: Dict = {}
    for i in ids:
        bucket = round_alg(i / max_page)
        if result.get(bucket, None) is None:
            result[bucket] = [
                i,
            ]
        else:
            result[bucket].append(i)

    return result


@contextmanager
def timeout_function(time):
    # Timeout for function
    signal.signal(signal.SIGALRM, raise_timeout)
    signal.setitimer(signal.ITIMER_REAL, time)

    try:
        yield
    except TimeoutError:
        raise
    finally:
        signal.signal(signal.SIGALRM, signal.SIG_IGN)


def raise_timeout(signum, frame):
    raise TimeoutError


def parse_member_id(members: "List[PublicProfile]"):
    result: Dict = {"user_ids": []}
    for m in members:
        if m["type"] == USER_TYPE:
            result["user_ids"].append(m["id"])
    return result


def parse_member_id_from_object(members):
    result: Dict = {"user_ids": []}
    for m in members:
        if m["type"] == USER_TYPE:
            result["user_ids"].append(m["user_id"])
    return result


def get_partner_id(pair_ids, user_id):
    user_a, user_b = pair_ids.split("|")
    if user_a == user_id:
        return user_b
    elif user_b == user_id:
        return user_a

    return None


def make_pair_ids(user_a, user_b=None):
    if user_a == "system":
        return "|".join(map(str, ["system", user_b]))
    elif user_b == "system":
        return "|".join(map(str, ["system", user_a]))
    elif user_b is not None:
        return "|".join(map(str, sorted([int(user_a), int(user_b)])))


def get_position(user_p, pair_ids):
    user_p = str(user_p)
    pair_users = pair_ids.split("|")
    for i, user in enumerate(pair_users):
        if user == user_p:
            return i
    # default position for page
    return 0


def _parse_int(val):
    try:
        return int(val)
    except Exception:
        return None


def get_account_type(id: Optional[str]):
    if id is None:
        return None
    if id == SYSTEM_ID:
        return SYSTEM_TYPE

    int_id = _parse_int(id)
    if not int_id:
        return None
    if 0 < int_id < **********:
        return USER_TYPE
    elif not int_id >> 59 ^ 10:
        return BOT_TYPE
    # elif not checkID >> 59 ^ 3:
    #     return PAGE_TYPE
    # elif not str.isnumeric(user_id):
    #     return PAGE_TYPE
    return None


CHOICES = string.ascii_letters + string.digits


def generate_random_string(length=22):
    # NOTE: https://github.com/nebula-chat/chatengine/blob/5f7c270e39d83fef4c90e08bbcd4784bfef4a48e/messenger/biz_server/biz/core/chat/chat_data.go#L634 # noqa
    return "".join(random.choices(CHOICES, k=length))


def now():
    return int(time.time() * 1000)


def from_now(day_num: int = 0):
    milliseconds = day_num * 24 * 60 * 60 * 1000
    return now() + milliseconds


def count_unicode(string):
    # ios using utf-16 with little endian
    # https://www.bignerdranch.com/blog/its-the-endian-of-the-world-as-we-know-it/#:~:text=It%20can%20be%20run%20in,endianness%20for%20sections%20of%20code. # noqa
    unicode_bytes = string.encode("utf-16-le")

    # python encode both code points and code units
    # https://stackoverflow.com/questions/30775689/python-length-of-unicode-string-confusion?noredirect=1&lq=1
    return len(unicode_bytes) // 2


def vn_money(number):
    return "{:,}".format(number).replace(",", ".")


def generate_event_id():
    return str(uuid.uuid4())


def is_valid_uuid(val):
    try:
        uuid.UUID(str(val))
        return True
    except ValueError:
        return False


def ticktock():
    start = time.time()

    def pick():
        return time.time() - start

    return pick


def parse_created_at(nano_timestamp):
    date_o = datetime.datetime.fromtimestamp(
        nano_timestamp / 1000,
        tz=datetime.timezone(datetime.timedelta(hours=+7)),
    )
    return date_o.strftime("%H:%M, %d tháng %m, %Y")


from bs4 import BeautifulSoup  # noqa
from markdown import markdown  # noqa


def markdown_to_text(markdown_string):
    """Converts a markdown string to plaintext"""

    # md -> html -> text since BeautifulSoup can extract text cleanly
    html = markdown(markdown_string, extensions=["pymdownx.tilde"])

    # extract text
    soup = BeautifulSoup(html, features="html.parser")
    text = "".join(soup.findAll(text=True))

    return text


def _is_hex(val):
    try:
        int(val, 16)
        return True
    except ValueError:
        return False


def is_folder_created_by_user(alias):
    return _is_hex(alias) and len(alias) == LENGTH_FOLDER_ALIAS


def get_conf(conf, key, default):
    if key in conf:
        return conf[key]
    return default


def get_link_code_from_link(link: str):
    if not link:
        return link
    parts = link.split("/")
    return parts[-1]


def encode_url(url: str) -> str:
    """Encodes url."""
    try:
        return quote(url).decode()  # type: ignore
    except Exception:
        return url


def is_valid_http_url(url: str):
    try:
        result = urlparse(url)
        return result.scheme in ("http", "https") and result.netloc
    except Exception:
        return False


def normalize_and_valid_phone_number(number: str):
    try:
        phone = phonenumbers.parse(number, region="VN")
        if not phonenumbers.is_valid_number(phone):
            return None

        # format to +84xxxxxx
        return phonenumbers.format_number(
            phone, phonenumbers.PhoneNumberFormat.E164
        )
    except Exception:
        return None


def is_valid_image_url(url: str):
    if not is_valid_http_url(url):
        return False

    file_ext = url.split(".")[-1]
    return file_ext in ("png", "jpg", "jpeg")
