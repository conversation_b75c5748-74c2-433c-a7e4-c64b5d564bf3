from functools import lru_cache

from chat.load_cdn_mapping import (
    PREFIX_ANIMATED_STICKER_CDN_URLS,
    PREFIX_FILE_CDN_URLS,
    PREFIX_IMAGE_CDN_URLS,
)

from .common import encode_url, is_valid_uuid


def is_protected_download_link(link: str):
    return isinstance(link, str) and "/download/v1.0/" in link


@lru_cache(maxsize=128)
def video(original: str):
    """Generates cdn link for video."""

    if isinstance(original, str):
        for k, v in PREFIX_FILE_CDN_URLS.items():
            if original.startswith(k):
                prefix_thumb_url = v
                video_id, video_name = original.split("/")[-2:]
                cdn_link = "/".join([prefix_thumb_url, video_id, video_name])
                if is_protected_download_link(cdn_link):
                    # we can't play link in protected download link
                    # without custom player (request authorization header). So we will use original link
                    # to play video here.
                    return encode_url(original)
                else:
                    return encode_url(cdn_link)
    return encode_url(original)


@lru_cache(maxsize=128)
def image(original: str):
    """Generates CDN link for image."""

    if isinstance(original, str):
        for k, v in PREFIX_IMAGE_CDN_URLS.items():
            if original.startswith(k):
                prefix_thumb_url = v
                image_id, image_name = original.split("/")[-2:]
                if is_valid_uuid(image_id):
                    return encode_url(
                        "/".join([prefix_thumb_url, image_id, image_name])
                    )
                return encode_url("/".join([prefix_thumb_url, image_name]))

    return original
