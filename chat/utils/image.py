from functools import lru_cache

from chat.load_cdn_mapping import PREFIX_IMAGE_URLS


@lru_cache(maxsize=128)
def validate(original: str) -> bool:
    """Validates if a link is valid image link."""

    try:
        if isinstance(original, str):
            for url in PREFIX_IMAGE_URLS:
                if original.startswith(url):
                    return True
        return False
    except Exception as e:
        raise e
