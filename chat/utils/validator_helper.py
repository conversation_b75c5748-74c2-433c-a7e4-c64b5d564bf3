import datetime
from uuid import UUID

from bson import ObjectId

import chat.utils.file as gapoFile
import chat.utils.image as gapoImage
import chat.utils.sticker as gapoSticker
from chat import constant
from chat.constant import SAVE_MESSAGE_VARPART


def is_timestamp_nano(timestamp: int) -> bool:
    try:
        datetime.datetime.fromtimestamp(timestamp / 1000)
        return True
    except Exception:
        return False


def is_object_id(value: str) -> bool:
    try:
        return ObjectId.is_valid(value)  # type: ignore
    except Exception:
        return False


def is_gapo_file(value: str) -> bool:
    try:
        return gapoFile.validate(value)
    except Exception:
        return False


def is_user_id(value: str) -> bool:
    try:
        return 0 < int(value) < 2147483648
    except Exception:
        return False


def is_save_bot_id(id: str) -> bool:
    """Checks whether a id is the ID of Save message bot."""
    try:
        if not is_bot_id(id):
            return False
        variant_part = (int(id) >> 0) & 0x3FFFF
        return variant_part == SAVE_MESSAGE_VARPART
    except:
        return False


def is_bot_id(value: str) -> bool:
    try:
        return not int(value) >> 59 ^ 10
    except Exception:
        return False


def get_id_type(value: str) -> str:
    if is_system(value):
        return constant.SYSTEM_ID
    elif is_bot_id(value):
        return constant.BOT_TYPE
    elif is_user_id(value):
        return constant.USER_TYPE
    else:
        return "unknown"


def is_group_id(value: str) -> bool:
    try:
        if str.isnumeric(value):
            return not int(value) >> 59 ^ 4
        return False
    except:
        return False


def is_department_id(value: str) -> bool:
    return is_uuid(value)


def is_system(value: str) -> bool:
    try:
        return value == "system"
    except ValueError:
        return False


def is_gapo_video(value: str) -> bool:
    # file cung voi video chung duong link
    try:
        return gapoFile.validate(value)
    except Exception:
        return False


def is_gapo_image(value: str) -> bool:
    try:
        return gapoImage.validate(value)
    except Exception:
        return False


def is_gapo_animated_sticker(value: str) -> bool:
    try:
        return gapoSticker.validate(value) and value.endswith(".json")
    except Exception:
        return False


def is_gapo_sticker(value: str) -> bool:
    try:
        return gapoImage.validate(value) or gapoSticker.validate(value)
    except Exception:
        return False


def is_uuid(value: str) -> bool:
    try:
        UUID(value)
        return True
    except ValueError:
        return False


def check_thread_id(v: int) -> int:
    if v > 0 and is_timestamp_nano(v):
        return v
    raise ValueError(f"Invalid thread_id {v}")


def is_valid_thread_id(v: int) -> bool:
    try:
        v = int(v)
        return v > 0 and is_timestamp_nano(v)
    except:
        return False
