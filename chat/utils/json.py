from typing import Any, Callable, Union

try:
    import orjson

    USE_ORJSON = True

    def _json_dumps_impl(*a, **kw):
        return orjson.dumps(*a, **kw).decode()

    _json_loads_impl = orjson.loads
except:
    # fall back to normal json
    # we will use normal json package
    # i.e. if we want to deploy service with PyPy
    USE_ORJSON = False
    import json

    _json_dumps_impl = json.dumps
    _json_loads_impl = json.loads

json_dumps: Callable[..., str] = _json_dumps_impl
json_loads: Callable[[Union[bytes, str]], Any] = _json_loads_impl
