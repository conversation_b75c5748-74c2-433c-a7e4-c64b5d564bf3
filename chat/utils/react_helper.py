import bitstring

BIT_LENGTH = 8
FIRST_4_BIT_MASK = bitstring.Bits(bin="0b11110000")
SECOND_4_BIT_MASK = bitstring.Bits(bin="0b00001111")


def decode(react_binary: int):
    r_sender = r_receiver = 0
    if not react_binary:
        return r_sender, r_receiver

    # tiny int cassandra is signed
    react_binary = bitstring.Bits(int=react_binary, length=BIT_LENGTH)
    # first 4 bits
    r_sender = react_binary >> 4

    # next 4 bits
    r_receiver = react_binary << 4 >> 4
    return r_sender.uint, r_receiver.uint  # type: ignore


def encode(r_binary, r_type, position):
    if not r_binary:
        r_binary = bitstring.Bits(int=0, length=BIT_LENGTH)
    else:
        r_binary = bitstring.Bits(int=r_binary, length=BIT_LENGTH)

    r_type = bitstring.Bits(int=r_type, length=BIT_LENGTH)
    if not position:
        r_binary = (r_type << 4) | (r_binary & SECOND_4_BIT_MASK)
    else:
        r_binary = r_type | (r_binary & FIRST_4_BIT_MASK)
    return r_binary.int
