# from chat.utils.cache import lru_cache
from functools import lru_cache

from chat.load_cdn_mapping import PREFIX_FILE_URLS


@lru_cache(maxsize=128)
def validate(original: str) -> bool:
    try:
        if isinstance(original, str):
            for url in PREFIX_FILE_URLS:
                if original.startswith(url):
                    return True
        return False
    except Exception as e:
        raise e
