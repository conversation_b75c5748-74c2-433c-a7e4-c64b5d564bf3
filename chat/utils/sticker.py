from functools import lru_cache

from chat.load_cdn_mapping import PREFIX_STICKER_URLS


@lru_cache(maxsize=128)
def validate(original: str):
    """Validates if a link is a valid sticker link."""

    try:
        if isinstance(original, str):
            for url in PREFIX_STICKER_URLS:
                if original.startswith(url):
                    return True
        return False
    except Exception as e:
        raise e
