from typing import Any, List, Union

from chat.action_format import ACTION_NOTES
from chat.constant import SYSTEM_OBJECT
from chat.message_event.model import MessageEvent
from chat.threads.model import ThreadModel
from chat.users.model import UserInfo


def _notify_builder(a_event, m_name, default_text, r_ids, ignore_ids):
    notify = ACTION_NOTES[a_event]["notify"]
    if not notify["enable"]:
        return {}

    if notify.get("format"):
        text = notify["format"].format(maker=m_name)
    else:
        text = default_text
    return {"text": text, "receiver_ids": r_ids, "ignore_ids": ignore_ids}


def _action_build(
    a_event, text, note, m_id, m_name, r_ids, msg_id, thread, maker=None
):
    notify = _notify_builder(a_event, m_name, text, r_ids, [m_id])

    message_o = MessageEvent(
        body=note,
        user=maker if maker else SYSTEM_OBJECT,  # type: ignore
        page=None,
        message_id=msg_id,
        thread=thread,
        sender_type="user" if maker else "system",
        notify=notify,
    )
    message_o.after_parse()

    return message_o.dict()


def action_note_builder(
    a_event: str,
    msg_id: int,
    maker: UserInfo,
    thread: ThreadModel,
    option=None,
    receiver=None,
    tmp_text: str = "",
    extra_data: Union[dict, None] = None,
):
    # default string should ""
    m_name = ""
    m_id = ""
    notify_text = ""

    if not extra_data:
        extra_data = {}

    if maker:
        m_name = maker["name"]
        m_id = maker["id"]

    extra_data["maker"] = m_name

    text = ACTION_NOTES[a_event]["format"].format(**extra_data)  # type: ignore
    if ACTION_NOTES[a_event].get("notify_format"):
        notify_text = ACTION_NOTES[a_event]["notify_format"].format(  # type: ignore # noqa
            **extra_data
        )  # type: ignore

    note = {
        "type": "action_note",
        "text": text,
        "notify_text": notify_text,
        "tmp_text": tmp_text,
        "metadata": {
            "action_note_receivers": receiver or [],
            "action_note_maker": m_id or "",
            "action_note_type": ACTION_NOTES[a_event]["code"],
            "action_note_data_change": option or {},
        },
    }

    return _action_build(a_event, text, note, m_id, m_name, [], msg_id, thread)


def action_note_lines(
    a_event: str,
    msg_id: int,
    maker: UserInfo,
    receivers: List[Any],
    thread: ThreadModel,
):
    m_name = maker["name"]
    m_id = maker["id"]

    text = "\n".join(
        [
            ACTION_NOTES[a_event]["format"].format(  # type: ignore
                maker=m_name, receiver=r["name"]
            )  # type: ignore
            for r in receivers
        ]
    )
    r_ids = [r["id"] for r in receivers]
    note = {
        "type": "action_note",
        "text": text,
        "metadata": {
            "action_note_receivers": r_ids,
            "action_note_maker": m_id,
            "action_note_type": ACTION_NOTES[a_event]["code"],
            "action_note_data_change": {},
        },
    }
    return _action_build(
        a_event,
        text,
        note,
        m_id,
        m_name,
        r_ids,
        msg_id,
        thread,
    )


def action_note_batch(
    a_event: str,
    msg_id: int,
    maker: UserInfo,
    r_names: List[Any],
    receivers: List[Any],
    thread,
):
    m_name = maker["name"]
    m_id = maker["id"]

    text = ACTION_NOTES[a_event]["format"].format(  # type: ignore
        maker=m_name, receiver=",".join(r_names)
    )
    r_ids = [r["id"] for r in receivers]
    note = {
        "type": "action_note",
        "text": text,
        "metadata": {
            "action_note_receivers": r_ids,
            "action_note_maker": m_id,
            "action_note_type": ACTION_NOTES[a_event]["code"],
            "action_note_data_change": {},
        },
    }
    return _action_build(
        a_event,
        text,
        note,
        m_id,
        m_name,
        r_ids,
        msg_id,
        thread,
    )


def action_data_change(
    a_event: str,
    msg_id: int,
    maker: UserInfo,
    data_changes,
    thread,
):
    m_name = maker["name"]
    m_id = maker["id"]
    text = "\n".join(
        [
            ACTION_NOTES[a_event]["format"][k].format(
                maker=m_name, dataChange=v
            )
            for k, v in data_changes.items()
        ]
    )
    note = {
        "type": "action_note",
        "text": text,
        "metadata": {
            "action_note_receivers": [],
            "action_note_maker": m_id,
            "action_note_type": ACTION_NOTES[a_event]["code"],
            "action_note_data_change": data_changes,
        },
    }
    return _action_build(
        a_event, text, note, m_id, m_name, [], msg_id, thread, maker=maker
    )
