from functools import lru_cache
from typing import Optional

from chat.constant import THUMB_SIZE
from chat.load_cdn_mapping import (
    PREFIX_FILE_CDN_URLS,
    PREFIX_IMAGE_URLS,
    PREFIX_THUMB_IMAGE,
)

from .common import encode_url, is_valid_uuid


def _get_file_path_from_url(url: str):
    """Map from https://xxxx/images/A/B.../C -> /A/B.../C"""
    return url.rpartition("/images/")[-1]


@lru_cache(maxsize=128)
def avatar(original: str) -> str:
    """Generates avatar link from a url."""

    if isinstance(original, str):
        for prefix_url in PREFIX_IMAGE_URLS:
            if original.startswith(prefix_url):
                prefix_thumb_url = PREFIX_THUMB_IMAGE[prefix_url]
                image_path = _get_file_path_from_url(original)
                return encode_url(
                    "/".join(
                        [prefix_thumb_url, THUMB_SIZE, "smart", image_path]
                    )
                )
    return encode_url(original)


@lru_cache(maxsize=128)
def image(original) -> str:
    if isinstance(original, str):
        for prefix_url in PREFIX_IMAGE_URLS:
            if original.startswith(prefix_url):
                prefix_thumb_url = PREFIX_THUMB_IMAGE[prefix_url]
                image_id, image_name = original.split("/")[-2:]
                if is_valid_uuid(image_id):
                    return encode_url(
                        "/".join(
                            [
                                prefix_thumb_url,
                                "$size$",
                                "smart",
                                image_id,
                                image_name,
                            ]
                        )
                    )
                return encode_url(
                    "/".join([prefix_thumb_url, "$size$", "smart", image_name])
                )
    return encode_url(original)


@lru_cache(maxsize=128)
def gif(original: str) -> str:
    if isinstance(original, str):
        for url in PREFIX_IMAGE_URLS:
            if original.startswith(url):
                prefix_thumb_url = PREFIX_THUMB_IMAGE[url]
                image_uid = original.rpartition("/")[2]
                return encode_url(
                    "/".join([prefix_thumb_url, "$size$", image_uid])
                )
    return encode_url(original)


@lru_cache(maxsize=128)
def video_thumb(original: str) -> Optional[str]:
    """Generates a video thumb from a link."""

    if isinstance(original, str):
        for k, v in PREFIX_FILE_CDN_URLS.items():
            if original.startswith(k):
                # replace "https://.../file_id/name.mp4" to
                # "https://../file_id/thumb.jpeg"
                file_id, file_name = original.split("/")[-2:]
                if is_valid_uuid(file_id):
                    return f"{v}/{file_id}/thumb.jpeg"

    return None
