from typing import Dict, List, Optional, Union

from pydantic import BaseModel, BaseSettings, Extra, Field, root_validator


class GunicornApp(BaseSettings):
    app_bind: str = Field("0.0.0.0:5000", env="GUNICORN_APP_BIND")
    health_bind: str = Field("0.0.0.0:5001", env="GUNICORN_HEALTH_BIND")
    workers: int = Field(3, env="GUNICORN_WORKERS")
    max_requests: int = Field(1000000, env="GUNICORN_MAX_REQUESTS")
    max_requests_jitter: int = Field(
        1000000, env="GUNICORN_MAX_REQUESTS_JITTER"
    )
    timeout: int = Field(60, env="GUNICORN_TIMEOUT")
    graceful_timeout: int = Field(10, env="GUNICORN_GRACEFUL_TIMEOUT")
    keepalive: int = Field(600, env="GUNICORN_KEEPALIVE")
    backlog: int = Field(128, env="GUNICORN_BACKLOG")
    chdir: str = Field(".", env="GUNICORN_CHDIR")
    loglevel: str = Field("error", env="GUNICORN_LOGLEVEL")
    limit_request_line: int = Field(4094, env="GUNICORN_LIMIT_REQUEST_LINE")


class CassandraConf(BaseSettings):
    """Cassandra config under CASSANDRA_* keys."""

    username: str = Field(..., env="CASSANDRA_USERNAME")
    password: str = Field(..., env="CASSANDRA_PASSWORD")
    keyspace: str = Field(..., env="CASSANDRA_KEYSPACE")
    host: str = Field(..., env="CASSANDRA_HOST")

    # @root_validator(pre=True)
    # def root(cls, values):
    #     print("Values: {}".format(values))
    #     return values


class MessageQueueInfo(BaseModel):
    queue_name: str
    exchange_name: str
    routing_key: str
    exchange_type: str = "direct"


class BackgroundRoute(BaseModel):
    queue_name: str
    exchange_routings: str


class RabbitMQConf(BaseSettings):
    host: str = Field(..., env="RABBITMQ_HOST")
    port: int = Field(default=5672, env="RABBITMQ_PORT")
    vhost: str = Field(..., env="RABBITMQ_VHOST")
    urls: str = Field(..., env="RABBITMQ_URLS")
    username: str = Field(..., env="RABBITMQ_USERNAME")
    password: str = Field(..., env="RABBITMQ_PASSWORD")

    message_queue_name: str = Field(
        default="message_queue", env="RABBITMQ_MESSAGE_QUEUE_NAME"
    )
    message_exchange_name: str = Field(
        default="message_exchange", env="RABBITMQ_MESSAGE_EXCHANGE_NAME"
    )
    message_routing_key: str = Field(
        default="message_routing_key", env="RABBITMQ_MESSAGE_ROUTING_KEY"
    )

    event_route: Dict[str, MessageQueueInfo]
    background_route: BackgroundRoute
    background_heavy_route: BackgroundRoute
    thread_event_queue: MessageQueueInfo

    @root_validator(pre=True)
    def root(cls, values):
        event_route = {
            "MESSAGE": MessageQueueInfo(
                queue_name=values["message_queue_name"],
                exchange_name=values["message_exchange_name"],
                routing_key=values["message_routing_key"],
            )
        }
        values["event_route"] = event_route
        values["thread_event_queue"] = MessageQueueInfo(
            queue_name="thread_queue",
            exchange_name=values[
                "message_exchange_name"
            ],  # just same as message exchange
            routing_key="thread_events",
        )

        m_ex_name = values["message_exchange_name"]

        values["background_route"] = BackgroundRoute(
            queue_name="chat:background:queue",
            exchange_routings=f"{m_ex_name}|background_task",
        )

        values["background_heavy_route"] = BackgroundRoute(
            queue_name="chat:background:heavy:queue",
            exchange_routings=f"{m_ex_name}|background_heavy_task",  # noqa
        )
        return values


class GapoConf(BaseSettings):
    """Config unser GAPO_*"""

    is_friend_url: str = Field(..., env="GAPO_IS_FRIEND_URL")
    is_friends_url: str = Field(..., env="GAPO_IS_FRIENDS_URL")
    white_list_is_friend: List[str] = ["49415", "**********", "**********"]

    user_info_url: str = Field(..., env="GAPO_USER_INFO_URL")
    user_api_key: str = Field(..., env="GAPO_USER_API_KEY")

    # page related api
    page_collection_url: str = Field(..., env="GAPO_PAGE_COLLECTION_URL")
    page_permission_url: str = Field(..., env="GAPO_PAGE_PERMISSION_URL")
    page_item_url: str = Field(..., env="GAPO_PAGE_ITEM_URL")

    internal_register_topic_notify_url: str = Field(
        ..., env="GAPO_INTERNAL_REGISTER_TOPIC_NOTIFY_URL"
    )
    internal_unregister_topic_notify_url: str = Field(
        ..., env="GAPO_INTERNAL_UNREGISTER_TOPIC_NOTIFY_URL"
    )

    mobile_push_api_key: str = Field(..., env="MOBILE_PUSH_API_KEY")

    block_user_url: str = Field(..., env="GAPO_BLOCK_USER_URL")
    unblock_user_url: str = Field(..., env="GAPO_UNBLOCK_USER_URL")

    relation_v2_api_key: str = Field(..., env="GAPO_RELATION_V2_API_KEY")
    relation_v2_url: str = Field(..., env="GAPO_RELATION_V2_URL")

    contact_url: str = Field(..., env="GAPO_CONTACT_URL")
    contact_api_key: str = Field(..., env="GAPO_CONTACT_API_KEY")

    react_url: str = Field(..., env="GAPO_REACT_URL")
    react_api_key: str = Field(..., env="GAPO_REACT_API_KEY")

    feature_url: str = Field(..., env="GAPO_FEATURE_URL")
    feature_api_key: str = Field(..., env="GAPO_FEATURE_API_KEY")

    workspace_url: str = Field(..., env="GAPO_WORKSPACE_URL")
    workspace_api_key: str = Field(..., env="GAPO_WORKSPACE_API_KEY")

    bot_base_url: str = Field(..., env="BOT_BASE_URL")
    bot_base_key: str = Field(..., env="BOT_API_KEY")

    poll_base_url: str = Field(..., env="POLL_BASE_URL")
    poll_api_key: str = Field(..., env="POLL_API_KEY")

    orgc_base_url: str = Field(..., env="ORGC_BASE_URL")
    orgc_api_key: str = Field(..., env="ORGC_API_KEY")

    group_base_url: str = Field(..., env="GROUP_BASE_URL")
    group_api_key: str = Field(..., env="GROUP_API_KEY")

    orgc_v3_base_url: str = Field(..., env="ORGC_V3_BASE_URL")
    orgc_v3_api_key: str = Field(..., env="ORGC_V3_API_KEY")
    use_orgc_chart_v3: bool = Field(default=False, env="USE_ORGC_CHART_V3")

    membership_url: str = Field(..., env="MEMBERSHIP_URL")

    call_bot_id: str = Field(..., env="GAPO_CALL_BOT_ID")


class RedisPersistentChatConf(BaseSettings):
    host: str = Field(..., env="REDIS__PERSISTENT_CHAT_HOST")
    password: str = Field(default="", env="REDIS__PERSISTENT_CHAT_PASSWORD")
    db: int = Field(default=0, env="REDIS__PERSISTENT_CHAT_DB")
    port: int = Field(default=6379, env="REDIS__PERSISTENT_CHAT_PORT")

    bad_comment_key: str = Field(default="bad_comments", env="BAD_COMMENT_KEY")

    limit_pin_thread: int = Field(default=10, env="LIMIT_PIN_THREAD")

    message_score_format: str = "{user_id}_{thread_id}_messages"
    thread_score_format: str = "{user_id}_threads"
    thread_member_format: str = "participant|thread:{thread_id}"
    super_group_list_key: str = "super_group_set"
    direct_n_f_rate_format: str = "{user_id}_DIRECT_N_F_RATE_LIMIT"
    limit_max_count: int = 1000
    pin_key_format: str = "user:{user_id}:thread:pin:count"
    thread_counter_name: str = "THREAD_COUNTER_NAME"
    thread_counter_key_format: str = "thread:{thread_id}"
    member_read_key_format: str = "thread:{thread_id}:member:read"


class RedisThreadChatConf(BaseSettings):
    host: str = Field(..., env="REDIS__THREAD_CHAT_HOST")
    password: str = Field(default="", env="REDIS__THREAD_CHAT_PASSWORD")
    db: int = Field(default=0, env="REDIS__THREAD_CHAT_DB")
    port: int = Field(default=6379, env="REDIS__THREAD_CHAT_PORT")


class RedisMessageChatConf(BaseSettings):
    host: str = Field(..., env="REDIS__MESSAGE_CHAT_HOST")
    password: str = Field(default="", env="REDIS__MESSAGE_CHAT_PASSWORD")
    db: int = Field(default=0, env="REDIS__MESSAGE_CHAT_DB")
    port: int = Field(default=6379, env="REDIS__MESSAGE_CHAT_PORT")


class RedisCacheConf(BaseSettings):
    host: str = Field(..., env="REDIS_CACHE_HOST")
    password: str = Field(default="", env="REDIS_CACHE_PASSWORD")
    port: int = Field(default=6379, env="REDIS_CACHE_PORT")
    db: int = Field(default=0, env="REDIS_CACHE_DB")
    redis_refresh_key: str = "day_la_refresh_cache"
    cache_timeout: int = 10 * 60 * 60  # 1 hour
    cache_hash_key: str = "HSET_CACHE_INFO"


class Miscs(BaseSettings):
    droppi_workspace_id: Optional[str] = Field(
        None, env="MISC__DROPPI_WORKSPACE_ID"
    )
    droppi_limit_members_creating_chat: int = Field(
        10, env="MISC__DROPPI_LIMIT_MEMBERS_CREATING_CHAT"
    )


class MySqlConf(BaseSettings):
    url: str = Field(..., env="MYSQL_URL")


class MQTTConf(BaseSettings):
    host: str = Field(..., env="MQTT_HOST")
    port: int = Field(default=1883, env="MQTT_PORT")
    username: str = Field(default="", env="MQTT_ADMIN_USERNAME")
    password: str = Field(default="", env="MQTT_ADMIN_PASSWORD")
    postfix_status_channel: str = "status"
    prefix_version: str = "v3"

    @root_validator()
    def root(cls, values):
        return values


class KafkaConf(BaseSettings):
    bootstrap_servers: str = Field(default="", env="KAFKA_BOOTSTRAP_SERVERS")
    username: str = Field(default="", env="KAFKA_USERNAME")
    password: str = Field(default="", env="KAFKA_PASSWORD")
    protocol: str = Field(default="SASL_PLAINTEXT", env="KAFKA_PROTOCOL")
    mechanisms: str = Field(default="PLAIN", env="KAFKA_MECHANISM")

    membership_topic: str = Field(
        default="collaborator_membership", env="MEMBERSHIP_KAFKA_TOPIC"
    )
    membership_consumer_group: str = Field(
        default="chat_service_membership", env="MEMBERSHIP_CONSUMER_GROUP"
    )


class Features(BaseSettings):
    """Feature flag settings."""

    disable_blocking_users: bool = Field(
        default=False, env="FEATURE__DISABLE_USER_BLOCKING"
    )
    """Disable user blocking. If this feature is enabled, you can't
    block your coworkers to chat with you."""


class Settings(BaseSettings):
    cassandra: CassandraConf
    rabbitmq: RabbitMQConf
    mysql: MySqlConf
    mqtt: MQTTConf
    redis_pcc: RedisPersistentChatConf
    redis_tcc: RedisThreadChatConf
    redis_mcc: RedisMessageChatConf
    redis_cache: RedisCacheConf
    gapo: GapoConf
    kafka: KafkaConf
    features: Features
    miscs: Miscs
    gunicorn_app: GunicornApp

    sentry_dsn: str = ""

    # Whether we should use JSON logging format
    # Logging using json format is faster. However, for development
    # purpose, you should set to False
    # See: https://www.structlog.org/en/stable/performance.html
    use_json_log: bool = Field(default=True, env="USE_JSON_LOG")

    api_version: str = "3"
    prefix_image_urls: str = (
        "https://image-1.gapo.vn,https://gapo-image.s3-ap-southeast-1.amazonaws.com"  # noqa
    )
    prefix_thumb_url: str = "https://cdn-thumb-image-1.gapo.vn"
    thumb_size: str = "168x168"
    expire_message_delete_level_2: int = 3600 * 1000  # in milliseconds
    maximum_pin_thread: int = 5

    # NONE(0),
    # LOVE(1),
    # HAHA(2),
    # PARTY(3),
    # SAD(4),
    # ANGRY(5),
    # HELO(6),
    # KISS(7),
    # SOCK(8)
    story_react_list: List[int] = [0, 1, 2, 3, 4, 5, 6, 7, 8]

    flirt_countdown: int = 5
    flirt_max_length: int = 1000

    chat_domain: str = Field(default="https://gapowork.vn", env="CHAT_DOMAIN")

    iam_api_key: str = Field(..., env="IAM_API_KEY")
    iam_api_expire_time: int = 300  # 5 min
    iam_base_url: str = Field(..., env="GAPO_IAM_URL")

    admin_api_key: str = "RtK6oFO5CYsU7m1eSpLoSATqQGEIhV"

    url_authorization: Union[str, None] = None

    cdn_mapping_filepath: str
    max_member_sync_status: int = 200
    max_member_tag_all: int = 100
    workspace_thread_id: str = Field(default="", env="WORKSPACE_THREAD_ID")
    admin_api: List[str] = []

    # The period that user can edit his/her message since its creation time.
    ttl_edit_message: int = 60 * 60 * 1000  # 1 hour

    # Period that user can delete his/her message.
    # If this value is set to 0, we will disable time checking.
    ttl_message_deletion: int = 0

    bucket_message_factor: int = Field(
        default=10000, env="BUCKET_MESSAGE_FACTOR"
    )

    page_size: int = 100

    direct_n_f_rate_limit: int = 100

    include_server_error_trace: bool = Field(
        default=False, env="INCLUDE_SERVER_ERROR_TRACE"
    )

    app_deploy_env: str = Field(default="staging", env="APP_DEPLOY_ENV")

    private_workspace: str = Field(default="", env="PRIVATE_WORKSPACE")

    class Config:
        extra = Extra.allow

    def is_staging(self):
        return self.app_deploy_env == "staging"

    def is_prod(self):
        return self.app_deploy_env == "prod"

    def is_uat(self):
        return self.app_deploy_env == "uat"

    def get_private_workspaces(self) -> List:
        private_workspaces: List = list()

        if not self.private_workspace:
            private_workspaces

        for workspaces in self.private_workspace.split(","):
            private_workspaces.append(set(workspaces.split("|")))

        return private_workspaces


def load_config(env_file=".env.prod") -> Settings:
    """Loads app config from env file."""

    print("Loading config from file {}".format(env_file))
    cassandra: CassandraConf = CassandraConf(_env_file=env_file)  # type: ignore # noqa
    rabbitmq: RabbitMQConf = RabbitMQConf(_env_file=env_file)  # type: ignore
    mysql: MySqlConf = MySqlConf(_env_file=env_file)  # type: ignore
    mqtt: MQTTConf = MQTTConf(_env_file=env_file)  # type: ignore
    redis_pcc: RedisPersistentChatConf = RedisPersistentChatConf(_env_file=env_file)  # type: ignore # noqa
    redis_tcc: RedisThreadChatConf = RedisThreadChatConf(_env_file=env_file)  # type: ignore # noqa
    redis_mcc: RedisMessageChatConf = RedisMessageChatConf(_env_file=env_file)  # type: ignore # noqa
    redis: RedisPersistentChatConf = RedisPersistentChatConf(_env_file=env_file)  # type: ignore # noqa
    redis_cache: RedisCacheConf = RedisCacheConf(_env_file=env_file)  # type: ignore # noqa
    gapo: GapoConf = GapoConf(_env_file=env_file)  # type: ignore
    kafka: KafkaConf = KafkaConf(_env_file=env_file)  # type: ignore
    features: Features = Features(_env_file=env_file)  # type: ignore
    miscs: Miscs = Miscs(_env_file=env_file)  # type: ignore
    gunicorn_app: GunicornApp = GunicornApp(_env_file=env_file)  # type: ignore

    return Settings(  # type: ignore
        _env_file=env_file,
        gapo=gapo,
        cassandra=cassandra,
        rabbitmq=rabbitmq,
        mysql=mysql,
        mqtt=mqtt,
        redis_pcc=redis_pcc,
        redis_tcc=redis_tcc,
        redis_mcc=redis_mcc,
        redis_cache=redis_cache,
        kafka=kafka,
        features=features,
        miscs=miscs,
        gunicorn_app=gunicorn_app,
    )
