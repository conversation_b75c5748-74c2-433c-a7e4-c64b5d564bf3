from typing import Dict, List, Set, Union

from pydantic import Field, root_validator, validator

from chat.model import BaseModel
from chat import dto
from chat.utils.validator_helper import check_thread_id, is_bot_id, is_user_id

MAX_SOURCE_THREADS = 10


class SchemaAction(BaseModel):
    creator: str
    type: str
    thread_id: int
    participant_ids: Union[None, Set[str]] = set([])

    source_thread_ids: List[int] = []

    member_file_id: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @validator("source_thread_ids")
    def validate_source_thread_ids(cls, v, values):

        target_thread_id = values.get("thread_id")

        if len(v) >= MAX_SOURCE_THREADS:
            raise ValueError("Maximum 10 source_thread_ids are allowed")

        # unique value
        unique_vals = set([])
        for thread_id in v:
            if thread_id == target_thread_id:
                raise ValueError(
                    "The source thread cannot be the same as the target thread"
                )

            check_thread_id(thread_id)
            unique_vals.add(thread_id)

        return list(unique_vals)

    @validator("participant_ids")
    def remove_creator(cls, v, values, **kwargs):
        v = v or set([])
        creator = values["creator"]
        v.discard(creator)
        if len(v) > 5000:
            raise ValueError(f"Invite participants{v}")

        for p in v:
            if is_user_id(p) or is_bot_id(p):
                continue
            raise ValueError("Invite people only support user")
        return list(v)

    @validator("type")
    def valid_type(cls, v):
        if v not in ("add", "remove", "ban", "unban", "leave"):
            raise ValueError("Type invalid")
        return v

    @root_validator(skip_on_failure=True)
    def validate_action(cls, values):
        p_ids = values.get("participant_ids") or []
        member_file_id = values.get("member_file_id")
        action_type = values["type"]
        if action_type in ("add",):
            num_source_threads = 0
            try:
                num_source_threads = len(values.get("source_thread_ids"))
            except Exception:
                pass
            if (
                len(p_ids) < 1
                and num_source_threads == 0
                and not member_file_id
            ):
                raise ValueError(
                    """Requires 'participant_ids' or
                    'source_thread_ids' or
                    'member_file_id' for add action"""
                )

        elif action_type in ("remove", "ban", "unban"):
            if len(p_ids) != 1:
                raise ValueError("Need or allow single participant_ids")
        else:
            values.pop("participant_ids", None)
        return values


class SchemaInternalParticipantsAction(BaseModel):
    actor_id: str
    action: str
    thread_id: int
    participant_ids: Union[None, List[str]] = []

    source_thread_ids: List[int] = []

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @validator("source_thread_ids")
    def validate_source_thread_ids(cls, v, values):

        target_thread_id = values.get("thread_id")

        if len(v) >= MAX_SOURCE_THREADS:
            raise ValueError("Maximum 10 source_thread_ids are allowed")

        # unique value
        unique_vals = set([])
        for thread_id in v:
            if thread_id == target_thread_id:
                raise ValueError(
                    "The source thread cannot be the same as the target thread"
                )

            check_thread_id(thread_id)
            unique_vals.add(thread_id)

        return list(unique_vals)

    @validator("participant_ids")
    def unique_members(cls, v, values, **kwargs):
        v = set(v or [])
        # actor_id = values["actor_id"]
        return list(v)

    @validator("action")
    def validate_action_type(cls, v):
        if v not in ("add", "remove"):
            raise ValueError(
                "Invalid action. Only 'add' and 'remove' are valid values"
            )
        return v

    @root_validator(skip_on_failure=True)
    def validate_action(cls, values):
        p_ids = values.get("participant_ids") or []
        action_type = values["action"]
        if action_type in ("add",):
            num_source_threads = 0
            try:
                num_source_threads = len(values.get("source_thread_ids"))
            except Exception:
                pass
            if len(p_ids) < 1 and num_source_threads == 0:
                raise ValueError(
                    """Required 'participant_ids' or
                    'source_thread_ids' for add action"""
                )

        return values


class SchemaAddBot(BaseModel):
    thread_id: int
    bot_id: str

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @validator("bot_id")
    def valid_bot_id(cls, v):
        if not is_bot_id(v):
            raise ValueError("Bot di invalid")
        return v


class SchemaAddOrgc(BaseModel):
    thread_id: int
    org_c_ids: Set[str]
    org_type: str

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    org_cs: Dict = {}  # filled later in validate_type_other_field

    @validator("org_c_ids")
    def remove_creator(cls, v, values, **kwargs):
        len_v = len(v)
        if not len_v:
            raise ValueError(f"Need orgc_ids{v}")
        elif len_v > 200:
            raise ValueError(f"Too much orgc_ids{len(v)}")
        return list(v)

    @validator("org_type")
    def check_org_type(cls, v, values, **kwargs):
        if v not in ("department", "title"):
            raise ValueError(f"Invalid org type {v}")
        return v

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        org_c_ids = values["org_c_ids"]

        org_cs: Dict = {}

        for org_c_id in org_c_ids:
            w_id, d_id = org_c_id.split(":")
            d_ids = org_cs.get(w_id, [])
            d_ids.append(d_id)
            org_cs[w_id] = d_ids
        values["org_cs"] = org_cs
        return values


class SchemaTyping(BaseModel):
    thread_id: int
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaChangeRole(BaseModel):
    thread_id: int
    receiver_id: str
    new_role: str

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @validator("receiver_id")
    def check_receiver_id(cls, v):
        if v and not is_user_id(v):
            raise ValueError(f"Invalid user_id {v}")
        return v

    @validator("new_role")
    def check_new_role(cls, v):
        if v not in ("member", "admin", "owner"):
            raise ValueError(f"New role not valid {v}")
        return v


class SchemaReading(BaseModel):
    thread_id: int
    message_id: Union[int, None] = Field(default=None, ge=1)

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaMarkUnread(BaseModel):
    thread_id: int
    message_id: int = Field(ge=1)

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )


class SchemaOutput(BaseModel):
    id: str = Field(..., alias="user_id")
    status_verify: int = 0
    avatar: Union[None, str] = None
    name: Union[None, str] = None
    type: str
    read_count: int
    role: str

    department: Union[None, str] = ""
    company: Union[None, str] = ""
    title: Union[None, str] = ""


class SchemaMarkFriend(BaseModel):
    id: str


class SchemaAcceptFriend(BaseModel):
    id: str
    accept_type: Union[None, str] = None


class SchemaOutputBan(BaseModel):
    id: str = Field(..., alias="user_id")
    status_verify: int = 0
    avatar: Union[None, str] = None
    name: Union[None, str] = None
    type: str
    read_count: int
    role: str
    by: str = Field(..., alias="banned_by")
    from_: int = Field(..., alias="banned_from")
    level: int = Field(..., alias="banned_level")

    @root_validator(skip_on_failure=True)
    def change_field(cls, values):
        values["from"] = values["from_"]
        values.pop("from_", None)
        return values


class SchemaOrgcObject(dto.OrgcObject):
    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        if not values["title_ids"] and not values["department_ids"]:
            raise ValueError("Create from orgc need title or department")
        return values


class SchemaAdd(BaseModel):
    thread_id: int
    participant_ids: Set[str] = set()
    member_file_id: str = ""
    source_thread_ids: Set[int] = set()
    orgcs: List[SchemaOrgcObject] = []

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(
        check_thread_id
    )

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        has_ids = 0
        has_ids += len(values["participant_ids"])
        has_ids += len(values["member_file_id"])
        has_ids += len(values["source_thread_ids"])
        has_ids += len(values["orgcs"])

        if not has_ids:
            raise ValueError("Add require at least from a source")
        return values
