from __future__ import annotations

import random
import time
from typing import Any, Dict, Mapping, Optional, Union

from pydantic import root_validator, validator
from typing_extensions import NotRequired, TypedDict

from chat import constant
from chat.constant import FOLDER_DEFAULT
from chat.messages.model import Last<PERSON>essageModel
from chat.model import BaseModel
from chat.profile.model import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from chat.threads.model import ChatThread
from chat.utils.json import json_loads


class ThreadMemberWithFolder(TypedDict):
    user_id: str
    folder: str


class ChatThreadMember(ThreadMemberWithFolder):
    partner_id: NotRequired[str]
    thread_id: int
    type: str
    last_message: Any
    delete_to: str

    is_removed: int
    """Whether user is removed or not."""

    is_deactivated: bool
    """Whether user is deactivated or not."""

    alias: str
    """Folder id. """

    tags: str
    read_count: int
    video_count: int
    image_count: int
    file_count: int
    role: str

    banned_level: int
    banned_by: NotRequired[str]

    pin_default_pos: int
    """Pinned timestamp in default folder. """

    pin_pos: int
    """Pinned timestamp in thread's folder. """

    mark_unread: bool
    """thread is marked as unread."""
    # just a flag for user
    # not to check if an user has read all messages in thread


# Fields to fill to sub-threads
# Because we don't add every members in to sub-thread (performance concern)
# so need to check it manually
# PLEASE TO fill this fields to make sure the model is up to date
# TODO: any better way to do this?
DEFAULT_SUB_THREAD_PARTICIPANT_THREAD_FIELDS = {
    "role": "member",
    "folder": "subthread",
    "read_count": 0,
    "is_removed": False,
    "delete_to": 0,
    "tags": "",
    "pin_pos": 0,
    "pin_default_pos": 0,
    "mark_unread": False,
    "can_call": False,
    "enable_notify": 1,
    "image_count": 0,
    "video_count": 0,
    "file_count": 0,
    "_created": 0,
    "_deleted": 0,
}


class ChatThreadWithMemberInfo(ChatThread, ThreadMemberWithFolder):
    """Mixed result from threads folder with some ChatThreadMember fields."""

    role: str
    alias: str
    enable_notify: bool
    last_message: Any
    read_count: int
    partner_id: str
    delete_to: int

    pin_pos: int
    """Pin time of thread in its folder."""

    pin_default_pos: int
    """Pin time of thread in default folder."""

    mark_unread: bool
    """thread is marked as unread."""
    # just a flag for user
    # not to check if an user has read all messages in thread

    is_deactivated: bool
    """Whether the user is deactivated or not"""

    tags: str

    # not a db field, will filled later
    departments: Optional[str]
    can_call: Optional[bool]
    referenced_message: NotRequired[Dict]
    partner: Optional[Mapping]


class ThreadWithPartner(TypedDict):
    thread_id: int
    partner_id: str


class UserWithRole(TypedDict):
    user_id: str
    role: str


class ParticipantModel(BaseModel):
    role: str
    alias: Union[None, str]
    enable_notify: bool = False
    last_message: Union[None, LastMessageModel] = None
    read_count: int = 0
    thread_id: int
    type: str
    partner_id: Union[str, None]
    partner: Union[SchemaPartner, None] = None
    delete_to: int = 0
    tags: Union[None, str] = ""
    pin_pos: int = 0
    pin_default_pos: int = 0
    mark_unread: bool = False
    folder: str
    user_id: str
    id: str = ""
    can_call: Union[None, bool] = True
    is_removed: Union[None, bool] = False
    is_deactivated: bool = False

    banned_level: int = 0
    banned_by: Union[None, str] = ""

    def is_banned(self) -> bool:
        return self.banned_level != 0

    def is_admin(self) -> bool:
        return self.role == constant.ADMIN_ROLE

    def is_owner(self) -> bool:
        return self.role == constant.OWNER_ROLE

    def is_member(self) -> bool:
        return self.role == constant.MEMBER_ROLE

    def is_user(self) -> bool:
        return self.type == constant.USER_TYPE

    def is_bot(self) -> bool:
        return self.type == constant.BOT_TYPE

    @validator("last_message", pre=True)
    def parse_lm(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    def update_partner(self, p_info):
        self.partner = SchemaPartner(**p_info)

    def update_pin_pos(self):
        if self.folder == FOLDER_DEFAULT:
            if self.pin_default_pos:
                self.pin_pos = self.pin_default_pos

    @classmethod
    def new_sub_participant(
        cls, user_id: str, thread_id: int
    ) -> ParticipantModel:
        return cls(
            user_id=user_id,
            role=constant.GUEST_ROLE,
            folder=constant.SUB_THREAD_TYPE,
            can_call=False,
            type=constant.USER_TYPE,
            thread_id=thread_id,
        )

    @classmethod
    def new_participant(
        cls, user_id: str, thread_id: int
    ) -> ParticipantModel:
        return cls(
            user_id=user_id,
            role=constant.MEMBER_ROLE,
            folder=constant.FOLDER_DEFAULT,
            can_call=False,
            type=constant.USER_TYPE,
            thread_id=thread_id,
        )

    def get_score(self):
        mul = 1
        if self.role == "admin":
            mul += 1
        elif self.role == "owner":
            mul += 2

        rand = random.randint(0, 1000)
        score = int(time.time() + rand) * mul
        return score

    @root_validator(skip_on_failure=True)
    def fill(cls, values):
        values["id"] = values["user_id"]
        return values

    def __eq__(self, other):
        if not isinstance(other, ParticipantModel):
            raise NotImplementedError
        if not self.id or not other.id:
            raise Exception("id not found")

        return (
            self.role == other.role
            and self.enable_notify == other.enable_notify
            and self.thread_id == other.thread_id
            and self.read_count == other.read_count
            and self.partner_id == other.partner_id
            and self.folder == other.folder
            and self.user_id == other.user_id
            and self.id == other.id
        )
