from chat.model import BaseModel
from chat.threads.model import ThreadModel
from chat.users.model import UserInfo


class TaskRemoveParticipant(BaseModel):
    thread_id: int
    user_id: str
    to_remove_id: str


class TaskJoinCollab(BaseModel):
    user_id: str
    collab_id: str
    data_source: str = "join_by_link"


class UserSeenThreadEvent(BaseModel):
    thread_id: int
    user_id: str
    message_id: int
    user: UserInfo
    thread: ThreadModel
