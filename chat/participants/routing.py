import falcon

from chat.participants.container import ParticipantContainer


def routing(router: falcon.API, container: ParticipantContainer):
    resources = container.resources()

    router.add_route(
        "/participants",
        resources.participants(),
    )

    router.add_route(
        "/participants/add_bot",
        resources.bot(),
    )

    router.add_route("/participants/add_orgc", resources.orgc())  # 1
    router.add_route("/participants/add", resources.add_member())  # 1

    router.add_route("/participants/mark_friend", resources.mark_friend())

    router.add_route("/participants/accept_friend", resources.accept_friend())

    router.add_route("/participants/change_role", resources.change_role())

    router.add_route(
        "/internal/participants", resources.internal_participants()
    )

    # router.add_route(
    #     "/internal/participants/member_role", resources.internal_role()
    # )

    # MYSQL ACTION ON ME
    router.add_route("/me/read", resources.me_read())

    router.add_route("/me/unread", resources.me_mark_unread())

    router.add_route("/me/typing", resources.me_typing())
