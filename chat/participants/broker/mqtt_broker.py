from logging import Logger
from typing import Dict, List

from chat.config import Settings
from chat.publishers.mqtt import MqttPublisher
from chat.threads.model import ThreadModel
from chat.users.model import UserInfo
from chat.utils.common import parse_member_id_from_object


class ParticipantMQTTBroker(object):
    def __init__(self, config: Settings, log: Logger, engine: MqttPublisher):
        self.config = config.mqtt
        self.log = log
        self.engine = engine

    def publish_status(self, members, event):
        id_collection = parse_member_id_from_object(members)
        self.engine.publish_status(id_collection["user_ids"], event)

    def publish_typing(
        self,
        members: List[Dict],
        thread_id: int,
        actor_id: str,
        actor_info: UserInfo,
    ):
        event = self._make_typing_event(thread_id, actor_id, actor_info)
        self.publish_status(members, event)

    def publish_role_changed_event(self, members, body):
        event = {"event_type": "authorize", "body": body}
        self.publish_status(members, event)

    def publish_message_seen_event(
        self,
        members,
        user: UserInfo,
        thread: ThreadModel,
        message_id: int,
    ):
        try:
            event = self._make_seen_event(user, thread, message_id)
            self.publish_status(members, event)
        except TimeoutError:
            pass

    def publish_thread_deleted_event(
        self, member_ids: List[str], thread_id: int, message_to: int
    ):
        msg = {
            "thread_id": thread_id,
            "message_to": message_to,
        }
        event = {"event_type": "thread_deleted", "body": msg}
        self.engine.publish_status(member_ids, event)

    def _make_typing_event(
        self, thread_id: int, typing_user_id: str, actor_info: UserInfo
    ):
        msg = {
            "thread_id": thread_id,
            "user_id": typing_user_id,
            "user": actor_info,
        }
        event = {"event_type": "typing", "body": msg}
        return event

    def _make_seen_event(
        self,
        user: UserInfo,
        thread: ThreadModel,
        message_id: int,
    ):
        msg = {
            "thread_id": thread.id,
            "user_id": user["id"],
            "message_id": message_id,
            "user": user,
            "thread": thread.dict(),  # type: ignore
        }

        event = {"event_type": "read_at", "body": msg}
        return event
