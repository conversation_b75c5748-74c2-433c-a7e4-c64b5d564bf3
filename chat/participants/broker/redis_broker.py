# from logging import Logger

# from chat.config import Settings
# from chat.connections.redis_c import RedisConnection


# class ParticipantRedisBroker(object):
#     def __init__(self, config: Settings, log: Logger, engine: RedisConnection):
#         self.config = config.redis
#         self.log = log
#         self.engine = engine
#         self.tsf = self.config.thread_score_format
#         self.tfsf = self.tsf + "_{folder}"
#         self.limit_max_count = self.config.limit_max_count
#         self.member_key = self.config.thread_member_format

#     # def get_thread_score_list(self, user_id: str, folder=None):
#     #     if folder:
#     #         key = self.tfsf.format(user_id=user_id, folder=folder)
#     #     else:
#     #         key = self.tsf.format(user_id=user_id)
#     #     timestamp = "+inf"
#     #     result = [
#     #         int(r)
#     #         for r in self.engine.client.zrevrangebyscore(
#     #             key, timestamp, "-inf", start=0, num=self.limit_max_count
#     #         )
#     #     ]
#     #     return result

#     # def update_group_score(
#     #     self, members: List[Dict], thread_id: int, start_time=None
#     # ):
#     #     if not start_time:
#     #         start_time = now()
#     #     pipe = self.engine.client.pipeline()
#     #     for member in members:
#     #         folder = member.get("folder", constant.FOLDER_DEFAULT)
#     #         user_id = member["user_id"]
#     #         pipe.zadd(
#     #             self.folder_score_key(user_id),
#     #             self.tsf.format(user_id=member["user_id"]),
#     #             {str(thread_id): start_time},
#     #         )
#     #         pipe.zadd(
#     #             self.folder_score_key(user_id, folder),
#     #             {str(thread_id): start_time},
#     #         )
#     #     pipe.execute()

#     # def remove_group_score(self, uids, thread_id):
#     #     pipe = self.engine.client.pipeline()
#     #     for u in uids:
#     #         pipe.zrem(self.tsf.format(user_id=u), thread_id)
#     #         pipe.zrem(
#     #             self.tfsf.format(user_id=u, folder=constant.FOLDER_DEFAULT),
#     #             thread_id,
#     #         )
#     #         pipe.zrem(
#     #             self.tfsf.format(user_id=u, folder=constant.FOLDER_PIN),
#     #             thread_id,
#     #         )
#     #     pipe.execute()

#     # def del_thread(self, thread_id: int, members):
#     #     c = self.engine.client
#     #     thread_score_formats = []
#     #     for m in members:
#     #         thread_score_formats.append(
#     #             self.tsf.format(user_id=m["user_id"]),
#     #         )
#     #         thread_score_formats.append(
#     #             self.tfsf.format(user_id=m["user_id"], folder=m["folder"]),
#     #         )
#     #     p = c.pipeline()
#     #     for score_format in thread_score_formats:
#     #         p.zrem(score_format, thread_id)
#     #     p.execute()

#     # def remove_member(self, thread_id: int, user_ids):
#     #     key = self.member_key.format(thread_id=thread_id)
#     #     for user_id in user_ids:
#     #         self.engine.client.zrem(key, user_id)

#     # def folder_score_key(
#     #     self, user_id: str, folder: str = constant.FOLDER_DEFAULT
#     # ):
#     #     """Returns the redis key used to rank thread in a folder."""
#     #     if folder == constant.FOLDER_DEFAULT:
#     #         return self.tsf.format(user_id=user_id)

#     #     return self.tfsf.format(user_id=user_id, folder=folder)
