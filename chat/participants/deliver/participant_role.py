import falcon

from chat.app import User<PERSON>uthResource
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.exception import InvalidParameters
from chat.hooks import making_participant_hook
from chat.model import make_input
from chat.participants.model.api import SchemaChangeRole
from chat.participants.usecase import ParticipantUseCases


class ParticipantChangeRoleResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaChangeRole, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        self.usecase.update_role(
            user_id, body.receiver_id, body.thread_id, body.new_role
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
