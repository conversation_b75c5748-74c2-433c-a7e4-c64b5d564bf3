from typing import List

import falcon

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import load_request_body
from chat.model import make_input
from chat.participants.model.api import SchemaInternalParticipantsAction
from chat.participants.usecase import ParticipantUseCases


class ParticipantInternalResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecases = usecases

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaInternalParticipantsAction, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        user_id = body.actor_id
        thread_id = body.thread_id
        action_type = body.action
        source_thread_ids: List[int] = body.source_thread_ids
        participant_ids = body.participant_ids or []

        # if action_type == "leave":
        #     self.usecase.leave_thread(user_id, thread_id)
        #     resp.status = falcon.HTTP_200
        #     return
        if action_type == "add":
            self.usecases.add_participant.add_members(
                user_id,
                thread_id,
                participant_ids,
                source_thread_ids,
                skip_action_notes=True,
            )
            resp.media = {"data": {"message": "ok"}}
            resp.status = falcon.HTTP_200
            return
        elif action_type == "remove":
            self.usecases.ban_participant.remove_members_internal(
                thread_id,
                participant_ids,
            )
            resp.media = {"data": {"message": "ok"}}
            resp.status = falcon.HTTP_200
            return
