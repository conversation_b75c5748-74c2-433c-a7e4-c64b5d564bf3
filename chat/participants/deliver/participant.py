from typing import List

import falcon

from chat import constant
from chat.app import UserAuthResource
from chat.app.app import <PERSON><PERSON><PERSON>
from chat import dto
from chat.exception import InvalidParameters
from chat.hooks import making_participant_hook
from chat.model import make_input
from chat.participants.model.api import SchemaAction, SchemaAdd
from chat.participants.usecase import ParticipantUseCases


class ParticipantResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases
        self.PAGE_SIZE = self.config.page_size

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        raw["creator"] = user_id
        body, errors = make_input(SchemaAction, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        member_file_id = body.member_file_id

        body = body.dict()
        thread_id = body["thread_id"]
        action_type = body["type"]
        user_id = self.user_id
        source_thread_ids: List[int] = body["source_thread_ids"]

        # add to logging context
        req.context.thread_id = thread_id

        # if action_type == "leave":
        #     self.usecase.leave_thread(user_id, thread_id)
        #     resp.status = falcon.HTTP_200
        #     return
        if action_type == "add":
            message = self.usecase.add_participant.add_members(
                user_id,
                thread_id,
                body["participant_ids"],
                source_thread_ids=source_thread_ids,
                user_workspace_id=self.workspace_id,
                member_file_id=member_file_id,
                call_source=constant.CALL_SOURCE_API,
            )
            resp.media = {"data": message}
            resp.status = falcon.HTTP_200
            return
        elif action_type == "remove":
            message = self.usecase.ban_participant.remove_member(
                user_id, thread_id, body["participant_ids"][0],
            )
            resp.media = {"data": message}
            resp.status = falcon.HTTP_200
            return
        elif action_type == "ban":
            message = self.usecase.ban_participant.ban_member(
                user_id, thread_id, body["participant_ids"][0],
            )
            resp.media = {"data": message}
            resp.status = falcon.HTTP_200
            return
        elif action_type == "unban":
            message = self.usecase.ban_participant.unban_member(
                user_id, thread_id, body["participant_ids"][0],
            )
            resp.media = {"data": message}
            resp.status = falcon.HTTP_200
            return


class AddParticipantResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases
        self.PAGE_SIZE = self.config.page_size

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        body, errors = make_input(SchemaAdd, req.context.body)
        if errors:
            raise InvalidParameters(error_details=errors)

        # add to logging context
        req.context.thread_id = body.thread_id

        message = self.usecase.add_participant.add_members(
            self.user_id,
            body.thread_id,
            list(body.participant_ids),
            user_workspace_id=self.workspace_id,
            source_thread_ids=list(body.source_thread_ids),
            member_file_id=body.member_file_id,
            orgcs=[dto.OrgcObject(**orgc.dict()) for orgc in body.orgcs],
            call_source=constant.CALL_SOURCE_API,
        )
        resp.media = {"data": message}
        resp.status = falcon.HTTP_200
        return
