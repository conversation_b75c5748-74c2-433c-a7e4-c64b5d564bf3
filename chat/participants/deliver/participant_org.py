import falcon

from chat.app import User<PERSON>uthResource
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.exception import InvalidParameters
from chat.hooks import making_participant_hook
from chat.model import make_input
from chat.participants.model.api import SchemaAddOrgc
from chat.participants.usecase import ParticipantUseCases


class ParticipantOrgcResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.add_participant
        self.PAGE_SIZE = self.config.page_size

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaAddOrgc, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id

        message = self.usecase.add_members_from_orgc(
            user_id, body.thread_id, body.org_cs, body.org_type
        )
        resp.media = {"data": message}
        resp.status = falcon.HTTP_200
