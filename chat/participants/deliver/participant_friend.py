import falcon

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import making_participant_hook
from chat.model import make_input
from chat.participants.model.api import SchemaAcceptFriend, SchemaMarkFriend
from chat.participants.usecase import ParticipantUseCases


class ParticipantMarkFriendResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaMarkFriend, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        body = body.dict()

        self.usecase.mark_as_friend(user_id, body["id"])
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class ParticipantAcceptFriendResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaAcceptFriend, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)
        body = body.dict()

        self.usecase.accept_friend_request(
            user_id, body["id"], body["accept_type"]
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
