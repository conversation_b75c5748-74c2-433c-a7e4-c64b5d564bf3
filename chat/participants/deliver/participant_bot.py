import falcon

from chat.app import UserAuthResource
from chat.app.app import My<PERSON><PERSON>
from chat.exception import InvalidParameters, PermissionDenied
from chat.hooks import making_participant_hook
from chat.model import make_input
from chat.participants.model.api import SchemaAddBot
from chat.participants.usecase import ParticipantUseCases


class ParticipantBotResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.add_participant
        self.PAGE_SIZE = self.config.page_size

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raise PermissionDenied(error_details=None)
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaAddBot, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        body = body.dict()
        thread_id = body["thread_id"]
        bot_id = body["bot_id"]
        user_id = self.user_id

        message = self.usecase.add_bot(user_id, thread_id, bot_id)
        resp.media = {"data": message}
        resp.status = falcon.HTTP_200
