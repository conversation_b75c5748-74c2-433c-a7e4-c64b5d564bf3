import falcon

from chat.app import UserAuthResource
from chat.app.app import <PERSON><PERSON><PERSON>
from chat.exception import InternalServerError, InvalidParameters
from chat.hooks import making_participant_hook
from chat.model import make_input, make_output
from chat.participants.model.api import (
    SchemaOutputBan,
    SchemaReading,
    SchemaMarkUnread,
    SchemaTyping,
)
from chat.participants.usecase import ParticipantUseCases


class ParticipantReadResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaReading, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        thread_id = body.thread_id

        # add to logging
        req.context.thread_id = thread_id

        self.usecase.mark_as_read(user_id, thread_id, body.message_id)
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class ParticipantMarkUnReadResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaMarkUnread, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        thread_id = body.thread_id

        # add to logging
        req.context.thread_id = thread_id

        self.usecase.mark_unread(user_id, thread_id, body.message_id)
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class ParticipantTypingResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ParticipantUseCases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.participant_action

    @falcon.before(making_participant_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaTyping, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        thread_id = body.thread_id
        user_id = self.user_id

        # add to logging context
        req.context.thread_id = thread_id

        user_id = self.user_id
        self.usecase.update_typing(user_id, thread_id)

        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
