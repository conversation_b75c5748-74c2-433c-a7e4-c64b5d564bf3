import falcon

from chat import codes, response_messages
from chat.exception import ChatError


class ParticipantError(ChatError):
    pass


class ViewBanListNotAllowed(ParticipantError):
    http_code = 403
    i18n_message = response_messages.PERMISSION_DENIED
    code = codes.CANT_VIEW_BAN_LIST
    error = "cant_view_ban_list"

    def __init__(self, permission_name):
        self.status = falcon.HTTP_403
        self.permission_name = permission_name
        super().__init__(self.status)

    def to_dict(self):
        return {
            "message": self.message,
            "code": self.code,
            "error": self.error,
            "permission_name": self.permission_name,
        }


class MaximumMemberReachedError(ParticipantError):
    http_code = 403
    i18n_message = response_messages.LIMIT_ADD_MEMBER
    error = "maximum_member_reached"


class MoreThanOneBot(ParticipantError):
    http_code = 422
    error = "more_than_one_bot"
    i18n_message = response_messages.MORE_THAN_ONE_BOT


class NoMoreMember(ParticipantError):
    http_code = 403
    i18n_message = response_messages.NO_MORE_MEMBER
    error = "not_friend"


class AddMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_ADD_MEMBER
    error = "access_denied"
    i18n_message = response_messages.DENIED_ADD_MEMBER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class AddBotPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_ADD_BOT
    error = "access_denied"
    i18n_message = response_messages.DENIED_ADD_BOT

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class RemoveMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_REMOVE_MEMBER
    error = "access_denied"
    i18n_message = response_messages.DENIED_REMOVE_MEMBER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class UnbanMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_UNBAN_MEMBER
    error = "access_denied"
    i18n_message = response_messages.DENIED_UNBAN_MEMBER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "code": self.code,
            "error": self.error,
            "permission": self.permission_name,
        }


class BanMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_UNBAN_MEMBER
    error = "access_denied"
    i18n_message = response_messages.DENIED_UNBAN_MEMBER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class RevokeToMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_REVOKE_TO_MEMBER
    error = "access_denied"
    i18n_message = response_messages.DENIED_REVOKE_TO_MEMBER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class AuthorizeToMemberPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_AUTHORIZE_TO_ADMIN
    error = "access_denied"
    i18n_message = response_messages.DENIED_AUTHORIZE_TO_ADMIN

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }


class AuthorizeToOwnerPermissionError(ParticipantError):
    http_code = 403
    code = codes.CANT_AUTHORIZE_TO_OWNER
    error = "access_denied"
    i18n_message = response_messages.DENIED_AUTHORIZE_TO_OWNER

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "error": self.error,
            "code": self.code,
            "permission": self.permission_name,
        }
