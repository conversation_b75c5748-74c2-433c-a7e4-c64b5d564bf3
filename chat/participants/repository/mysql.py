from logging import Logger
from typing import Any, Dict, List, Optional, Sequence, Tuple

from sqlalchemy.orm import Session

from chat import constant
from chat.participants.model import ParticipantModel
from chat.participants.repository.interface import ParticipantThreadRepository
from chat.utils import common
from chat.utils.json import json_dumps

from .cache import ParticipantCache


class BaseController(object):
    def __init__(self, log: Logger):
        self.log = log

    t_name = "participant_threads"
    thread_participant_fields = ",".join(
        [
            "p.user_id",
            "p.role",
            "p.alias",
            "p.enable_notify",
            "p.last_message",
            "p.read_count",
            "p.folder",
            "p.partner_id",
            "p.delete_to",
            "p.pin_pos",
            "p.pin_default_pos",
            "p.tags",
            "p.mark_unread",
        ]
    )


class ParticipantListController(BaseController):
    def __init__(self, log: Logger):
        super().__init__(log)

    def _q_items(
        self, session: Session, query, params
    ) -> List[ParticipantModel]:
        records = session.execute(query, params).fetchall()
        return [ParticipantModel(**dict(r)) for r in records] if records else []

    def get_threads_by_pair_ids(
        self, session: Session, user_id: str, partner_ids: List[Any]
    ) -> List[ParticipantModel]:
        # TODO: recheck
        if len(partner_ids) == 0:
            return []
        params = {"user_id": user_id, "partner_ids": partner_ids}
        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id=:user_id
            AND partner_id IN :partner_ids"""
        return self._q_items(session, query, params)

    def get_all_sub_threads_for_user(
        self, session: Session, user_id: str, thread_id: int
    ) -> List[ParticipantModel]:
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name} AS p
            JOIN threads AS t
            ON p.thread_id=t.id
            WHERE p.user_id=:user_id AND
            t.parent_id=:thread_id AND
            p.is_removed= false AND
            t.type = 'subthread' """
        return self._q_items(session, query, params)

    def _get_thread_first_page(
        self, session: Session, user_id: str, page_size
    ) -> List[ParticipantModel]:
        params = {"user_id": user_id, "limit": page_size}
        get_first_list_q = f"""SELECT * FROM {self.t_name}
        WHERE user_id=:user_id
        AND is_removed=False
        LIMIT :limit"""

        return self._q_items(session, get_first_list_q, params)

    def _get_thread_next_page(
        self, session: Session, user_id: str, last_id, page_size
    ) -> List[ParticipantModel]:
        params = {"user_id": user_id, "last_id": last_id, "limit": page_size}
        get_next_list_q = f"""SELECT * FROM {self.t_name}
            WHERE user_id=:user_id
            AND is_removed=False
            AND thread_id > :last_id
            LIMIT :limit"""

        return self._q_items(session, get_next_list_q, params)

    def get_folders_by_thread_ids(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0:
            return []

        params = {"ids": thread_ids, "user_id": user_id}
        get_folder_q = f"""SELECT * FROM {self.t_name}
            WHERE thread_id IN :ids
            AND user_id=:user_id """
        return self._q_items(session, get_folder_q, params)

    def get_threads_by_user_id(
        self, session: Session, user_id: str, last_id=None, page_size=10
    ) -> List[ParticipantModel]:
        if last_id is None:
            return self._get_thread_first_page(session, user_id, page_size)
        return self._get_thread_next_page(session, user_id, last_id, page_size)

    def get_members_in_list_threads(
        self,
        session: Session,
        thread_ids: List[int],
        roles: List[str] = [],
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0:
            return []

        if roles:
            # filter by roles
            params = {"thread_ids": thread_ids, "roles": roles}
            query = f"""SELECT * FROM {self.t_name}
                WHERE thread_id IN :thread_ids
                AND role in :roles
                AND is_removed=False
                ORDER BY user_id"""
        else:
            params = {"thread_ids": thread_ids}
            query = f"""SELECT * FROM {self.t_name}
                WHERE thread_id IN :thread_ids
                AND is_removed=False
                ORDER BY user_id"""

        return self._q_items(session, query, params)

    def get_members_in_thread(
        self, session: Session, thread_id: int, last_user_id: int, page_size
    ) -> List[ParticipantModel]:
        params = {
            "role": "member",
            "thread_id": thread_id,
            "last_user_id": last_user_id,
            "limit": page_size,
        }
        query = f"""SELECT * FROM {self.t_name}
            WHERE thread_id=:thread_id
            AND user_id > :last_user_id
            AND is_removed=False
            AND role=:role
            ORDER BY user_id
            LIMIT :limit"""

        return self._q_items(session, query, params)

    def get_user_roles_in_thread(
        self, session: Session, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        if len(user_ids) == 0:
            return []

        params = {"thread_id": thread_id, "user_ids": user_ids}
        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id IN :user_ids
            AND thread_id=:thread_id """
        return self._q_items(session, query, params)

    def get_members_by_ids(
        self, session: Session, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        if len(user_ids) == 0:
            return []

        params = {"thread_id": thread_id, "user_ids": user_ids}
        query = f"""SELECT * from {self.t_name}
            WHERE thread_id = :thread_id
            AND user_id IN :user_ids
            AND is_removed=False """

        return self._q_items(session, query, params)

    def get_user_role_in_threads(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0:
            return []

        params = {"thread_ids": thread_ids, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name}
        WHERE thread_id IN :thread_ids
        AND user_id=:user_id """

        return self._q_items(session, query, params)

    def get_owners(
        self, session: Session, thread_id: int
    ) -> List[ParticipantModel]:
        params = {
            "roles": ("owner", "admin"),
            "thread_id": thread_id,
        }
        get_members_in_roles_q = f"""SELECT * FROM {self.t_name}
            WHERE thread_id=:thread_id
            AND is_removed=False
            AND role IN :roles
            ORDER BY user_id """

        return self._q_items(session, get_members_in_roles_q, params)

    def get_members_by_roles(
        self,
        session: Session,
        thread_id: int,
        roles: Sequence[Any],
        limit=2,
        last_id=0,
    ) -> List[ParticipantModel]:
        params = {
            "roles": roles,
            "thread_id": thread_id,
            "limit": limit,
            "last_id": last_id,
            "member_type": "user",
        }

        query = f"""SELECT * FROM {self.t_name}
            WHERE thread_id=:thread_id
            AND is_removed=False
            AND role IN :roles
            AND user_id > :last_id
            AND type=:member_type
            ORDER BY role DESC, user_id ASC
            LIMIT :limit """

        return self._q_items(session, query, params)

    def get_list_threads_in_folders(
        self,
        session: Session,
        user_id: str,
        folders: List[str],
        thread_ids: List[int],
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0 or len(folders) == 0:
            return []

        params = {
            "user_id": user_id,
            "folders": folders,
            "thread_ids": thread_ids,
            "length": len(thread_ids),
        }

        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id=:user_id
            AND thread_id IN :thread_ids
            AND is_removed=False
            AND folder IN :folders
            LIMIT :length"""

        return self._q_items(session, query, params)

    def get_thread_members_in_list(
        self, session: Session, user_thread_ids: List[Tuple[str, int]]
    ) -> List[ParticipantModel]:
        params = {"user_thread_ids": user_thread_ids}

        query = f"""SELECT * FROM {self.t_name}
        WHERE (user_id, thread_id) IN :user_thread_ids """
        return self._q_items(session, query, params)

    def get_exist_records(
        self, session: Session, thread_id: int, user_ids: Sequence[str]
    ) -> List[ParticipantModel]:
        if len(user_ids) == 0:
            return []

        params = {"thread_id": thread_id, "user_ids": user_ids}
        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id IN :user_ids
            AND thread_id=:thread_id """

        return self._q_items(session, query, params)

    def get_banned_members(
        self, session: Session, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        if len(user_ids) == 0:
            return []

        params = {
            "thread_id": thread_id,
            "user_ids": user_ids,
            "ban_level": 1,
        }

        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id IN :user_ids
            AND thread_id=:thread_id
            AND banned_level=:ban_level """

        return self._q_items(session, query, params)

    def get_banned_members_limit(
        self, session: Session, thread_id: int, last_user_id: str, page_size
    ) -> List[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "last_user_id": last_user_id,
            "limit": page_size,
            "ban_level": 1,
        }

        query = f"""SELECT * FROM {self.t_name}
            WHERE thread_id=:thread_id
            AND user_id > :last_user_id
            AND banned_level=:ban_level
            ORDER BY user_id
            LIMIT :limit"""

        return self._q_items(session, query, params)

    def get_threads_by_ids(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0:
            return []

        params = {
            "user_id": user_id,
            "thread_ids": thread_ids,
            "length": len(thread_ids),
        }

        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id=:user_id
            AND thread_id IN :thread_ids
            AND is_removed=False
            LIMIT :length"""

        return self._q_items(session, query, params)

    def get_all_threads_in_folder(
        self, session: Session, user_id: str, folder
    ) -> List[ParticipantModel]:
        params = {"user_id": user_id, "folder": folder}
        query = f"""SELECT * FROM {self.t_name}
            WHERE user_id=:user_id
            AND is_removed=False
            AND folder = :folder"""

        return self._q_items(session, query, params)

    def get_all_group_ids(
        self, session: Session, user_id: str, workspace_id
    ) -> List[ParticipantModel]:
        params = {
            "user_id": user_id,
            "workspace_id": workspace_id,
            "thread_type": "group",
        }

        query = f"""SELECT p.* FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id=:user_id
        AND p.is_removed=False
        AND t.type=:thread_type
        AND t.workspace_id=:workspace_id"""

        return self._q_items(session, query, params)

    def get_all_by_workspace(
        self, session: Session, user_id: str, workspace_id
    ) -> List[ParticipantModel]:
        params = {
            "user_id": user_id,
            "workspace_id": workspace_id,
            "thread_types": ("group", "subthread"),
        }

        query = f"""SELECT * FROM {self.t_name} AS p
            JOIN threads AS t
            ON p.thread_id=t.id
            WHERE p.user_id=:user_id
            AND t.type in :thread_types
            AND p.is_deactivated=True
            AND t.workspace_id=:workspace_id"""

        return self._q_items(session, query, params)

    def get_all_group_ids_without_associate(
        self, session: Session, user_id: str
    ) -> List[ParticipantModel]:
        params = {
            "user_id": user_id,
            "thread_type": "group",
        }

        query = f"""SELECT p.* FROM {self.t_name} AS p
            JOIN threads AS t
            ON p.thread_id=t.id
            WHERE p.user_id=:user_id
            AND t.type=:thread_type
            AND (t.associate_link is NULL OR t.associate_link = '')
            AND p.is_removed=False """

        return self._q_items(session, query, params)

    def get_all_thread_ids_in_folders(
        self, session: Session, user_id: str, folders: List[Any]
    ) -> List[ParticipantModel]:
        if len(folders) == 0:
            return []

        params = {"user_id": user_id, "folders": folders}
        query = f"""SELECT * FROM {self.t_name}
            WHERE is_removed=False
            AND user_id=:user_id
            AND folder IN :folders """

        return self._q_items(session, query, params)

    def get_in_folder(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        if len(thread_ids) == 0:
            return []

        params = {"user_id": user_id, "thread_ids": thread_ids}
        query = f"""SELECT * FROM {self.t_name}
            WHERE is_removed=False
            AND user_id=:user_id
            AND thread_id IN :thread_ids """

        return self._q_items(session, query, params)

    def get_pinned_thread_ids(
        self, session, user_id, thread_ids, folder
    ) -> List[ParticipantModel]:
        query = f"""SELECT * FROM {self.t_name}
        WHERE user_id=:user_id AND thread_id IN :thread_ids
        """
        params = {"user_id": user_id, "thread_ids": thread_ids}
        if folder == constant.FOLDER_DEFAULT:
            query += " AND pin_default_pos != 0"
        else:
            query += " AND pin_pos != 0"
        return self._q_items(session, query, params)

    def get_all_thread_by_user(
        self, session: Session, user_id: str
    ) -> List[ParticipantModel]:
        last_id = 0
        limit = 100
        pthreads: List[ParticipantModel] = []
        while True:
            self.log.info(
                f"get_all_thread_by_user: {user_id}, last_id: {last_id}"
            )
            params = {"user_id": user_id, "last_id": last_id, "limit": limit}
            query = f"""SELECT * FROM {self.t_name} AS p
                WHERE p.user_id=:user_id
                AND p.thread_id > :last_id
                ORDER BY p.thread_id ASC
                LIMIT :limit
                """
            db_pthreads: List[ParticipantModel] = self._q_items(
                session, query, params
            )
            self.log.info(
                f"get_all_thread_by_user: {user_id}, last_id: {last_id}, found: {len(db_pthreads)}"
            )
            if len(db_pthreads) == 0:
                break
            last_id = db_pthreads[-1].thread_id
            pthreads.extend(db_pthreads)
        return pthreads


class ParticipantUpdateController(BaseController):
    t_name = "participant_threads"

    def __init__(self, log: Logger):
        super().__init__(log)

        self._basic_clean_field = ", ".join(
            [
                "p.url_count=0",
                "p.video_count=0",
                "p.file_count=0",
                "p.image_count=0",
                "p.last_message=null",
                "p.pin_pos=0",
                "p.pin_default_pos=0",
                "p.tags=null",
            ]
        )

    def host_group(self, thread_id: int) -> str:
        # return f" /* hostgroup={thread_id % 3 + 1} */ "
        return ""

    def _update_item(self, session: Session, query, payload):
        session.execute(query, payload)

    def set_unread_flag(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        unread_status: bool,
    ):
        params = {
            "status": unread_status,
            "thread_id": thread_id,
            "user_id": user_id,
        }
        query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
            SET mark_unread=:status
            WHERE thread_id=:thread_id
            AND user_id=:user_id"""
        self._update_item(session, query, params)

    def update_role(
        self, session: Session, user_id: str, thread_id: int, new_role
    ):
        params = {"user_id": user_id, "thread_id": thread_id, "role": new_role}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET p.role=:role
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def reactivate_groups_for_user(self, session: Session, user_id: str):
        params = {"user_id": user_id}
        query = f"""UPDATE {self.t_name}
        SET is_removed=False, is_deactivated=False, role='member'
        WHERE is_deactivated=True
        AND user_id=:user_id"""
        return self._update_item(session, query, params)

    def disable_reactivate(
        self, session: Session, user_id: str
    ):
        params = {"user_id": user_id}
        query = f"""UPDATE {self.t_name}
        SET is_removed=True, is_deactivated=False, role='member'
        WHERE is_deactivated=True
        AND user_id=:user_id"""
        return self._update_item(session, query, params)

    def toggle_notify(self, session: Session, thread_id: int, user_id: str):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
        SET enable_notify=1 - enable_notify
        WHERE user_id=:user_id
        AND thread_id=:thread_id"""

        self._update_item(session, query, params)

    def edit_info(
        self, session: Session, thread_id: int, user_id: str, edit_fields
    ):
        if not edit_fields:
            return {}
        params = {"thread_id": thread_id, "user_id": user_id}
        params.update(edit_fields)

        query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
        SET {{field}}
        WHERE user_id=:user_id
        AND thread_id=:thread_id"""

        query = query.format(
            field=", ".join(
                ["{key}=:{key}".format(key=key) for key in edit_fields.keys()]
            )
        )
        self._update_item(session, query, params)
        return edit_fields

    def pin(
        self,
        session,
        user_id,
        thread_id,
        pinned_at: int,
        folder: str,
    ):
        """Update"""
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "pin_pos": pinned_at,
        }
        if folder == constant.FOLDER_DEFAULT:
            query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
            SET pin_default_pos=:pin_pos
            WHERE user_id=:user_id
            AND thread_id=:thread_id"""
        else:
            query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
            SET pin_pos=:pin_pos
            WHERE user_id=:user_id
            AND thread_id=:thread_id"""

        self._update_item(session, query, params)

    def move_folder(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        to_folder="default",
    ):
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "to_folder": to_folder,
        }
        query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
            SET folder=:to_folder
            WHERE user_id=:user_id AND thread_id=:thread_id"""

        self._update_item(session, query, params)

    def clear_and_move_folder_user_threads(
        self, session: Session, user_id: str, from_folder, to_folder
    ):
        params = {
            "user_id": user_id,
            "from_folder": from_folder,
            "to_folder": to_folder,
        }
        query = f"""UPDATE {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        SET {self._basic_clean_field},
        p.folder=:to_folder,
        p.delete_to=t.message_count
        WHERE p.user_id=:user_id
        AND p.folder=:from_folder"""

        self._update_item(session, query, params)

    def clear_thread_users(
        self, session: Session, thread_id: int, message_id: int
    ):
        params = {"thread_id": thread_id, "message_id": message_id}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET {self._basic_clean_field},
        p.delete_to=:message_id
        WHERE p.thread_id=:thread_id """
        self._update_item(session, query, params)

    def clear_user(self, session: Session, thread_id: int, user_id: str):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        JOIN threads as t
        ON p.thread_id=t.id
        SET {self._basic_clean_field},
        p.delete_to=t.message_count
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""
        self._update_item(session, query, params)

    def update_media_count_users(
        self,
        session,
        thread_id,
        user_ids: List,
        media_infos: List[Tuple[str, int]],
        is_decrease: bool,
    ):
        operator = -1 if is_decrease else 1
        media_updates = " AND ".join(
            [
                "{k}_count = greatest(cast({k}_count as signed) + {v}, 0)".format(  # noqa
                    k=k, v=v * operator
                )
                for k, v in media_infos
            ]
        )
        update_media_count_user_f = f"""UPDATE {self.t_name} AS p
        SET {{media_types}}
        WHERE p.user_id IN :user_ids
        AND p.thread_id=:thread_id"""

        query = update_media_count_user_f.format(media_types=media_updates)

        params = {"thread_id": thread_id, "user_ids": user_ids}
        self._update_item(session, query, params)

    def leave_thread(self, session: Session, thread_id: int, user_id: str):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
            SET {self._basic_clean_field},
            p.is_removed=True,
            p.role='member'
            WHERE p.user_id=:user_id
            AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def set_deactivate_status(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        is_deactivated: bool,
    ):
        """Mark an user is deactivated or not"""
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "is_deactivated": is_deactivated,
            "is_removed": is_deactivated,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET {self._basic_clean_field},
        p.is_deactivated=:is_deactivated,
        p.is_removed=:is_removed,
        p.role='member'
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def update_last_message(
        self, session: Session, user_id: str, thread_id: int, message
    ):
        params = {
            "last_message": message,
            "user_id": user_id,
            "thread_id": thread_id,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET p.last_message=:last_message
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def update_all_last_message(
        self, session: Session, thread_id: int, message_id: int, message
    ):
        params = {
            "last_message": message,
            "thread_id": thread_id,
            "message_id": message_id,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        JOIN threads AS t
        ON p.thread_id=t.id
        SET p.last_message=:last_message
        WHERE p.thread_id=:thread_id
        AND p.is_removed=False
        AND p.read_count=t.message_count
        AND JSON_EXTRACT(p.last_message, "$.id")=:message_id
        """

        self._update_item(session, query, params)

    def update_read_count(
        self, session: Session, thread_id: int, user_id: str, read_count=0
    ):
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "read_count": read_count,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET p.read_count=:read_count,
        p.last_message=NULL,
        p.tags=NULL
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""
        self._update_item(session, query, params)

    def update_max_read_count(
        self, session: Session, thread_id: int, user_id: str
    ):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        JOIN threads as t
        ON p.thread_id=t.id
        SET p.read_count=t.message_count,
        p.last_message=NULL,
        p.tags=NULL
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""
        self._update_item(session, query, params)

    def unban(self, session: Session, thread_id: int, user_id: str):
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET p.banned_by=null,
        p.banned_from=null,
        p.banned_level=0
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def rejoin(
        self, session: Session, thread_id: int, user_id: str, read_count
    ):
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "read_count": read_count,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        JOIN threads AS t
        ON p.thread_id=t.id
        SET p.is_removed=False,
        p.read_count=t.message_count,
        p.role='member',
        p.banned_by=null,
        p.banned_from=null,
        p.banned_level=0
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def rejoins(self, session: Session, thread_id: int, user_ids: List[str]):
        params = {
            "thread_id": thread_id,
            "user_ids": user_ids,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        JOIN threads AS t
        ON p.thread_id=t.id
        SET p.is_removed=False,
        p.role='member'
        WHERE p.user_id IN :user_ids
        AND p.thread_id=:thread_id"""

        self._update_item(session, query, params)

    def update_folder(
        self, session: Session, user_id: str, thread_id: int, folder
    ):
        params = {"user_id": user_id, "thread_id": thread_id, "folder": folder}
        query = f"""UPDATE {self.t_name}
            {self.host_group(thread_id)}
        SET folder=:folder
        WHERE user_id=:user_id
        AND thread_id=:thread_id"""

        self._update_item(session, query, params)

    def set_ban_and_remove(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        will_banned_id: str,
    ):
        params = {
            "thread_id": thread_id,
            "banned_by": user_id,
            "banned_id": will_banned_id,
            "banned_from": common.now(),
            "banned_level": 1,
        }
        query = f"""UPDATE {self.t_name} AS p
            {self.host_group(thread_id)}
        SET p.is_removed=True,
        p.banned_from=:banned_from,
        p.banned_by=:banned_by,
        p.banned_level=:banned_level
        WHERE p.user_id=:banned_id
        AND p.thread_id=:thread_id """

        self._update_item(session, query, params)


class ParticipantItemController(BaseController):
    def __init__(self, log: Logger):
        super().__init__(log)

        self.GET_BANNED_MEMBER_Q = (
            f"SELECT * FROM {self.t_name} "
            "WHERE user_id=:user_id "
            "AND thread_id=:thread_id "
            "AND banned_level=:ban_level "
        )

    def _q_item(
        self, session: Session, query, params
    ) -> Optional[ParticipantModel]:
        record = session.execute(query, params).fetchone()
        return ParticipantModel(**dict(record)) if record else None

    def get_thread_for_user(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name}
        WHERE user_id=:user_id
        AND thread_id=:thread_id
        AND is_removed=False """
        return self._q_item(session, query, params)

    def get_partner(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name}
        WHERE thread_id=:thread_id
        AND user_id!=:user_id """
        return self._q_item(session, query, params)

    def get_banned_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "ban_level": 1,
        }
        query = f"""SELECT * FROM {self.t_name}
        WHERE user_id=:user_id
        AND thread_id=:thread_id
        AND banned_level=:ban_level """
        return self._q_item(session, query, params)

    def get_thread_for_user_by_collab_id(
        self, session: Session, user_id: str, collab_id: str
    ) -> Optional[ParticipantModel]:
        params = {"collab_id": collab_id, "user_id": user_id}

        query = f"""SELECT * FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id= :user_id
        AND t.collab_id = :collab_id
        AND p.is_removed=False """
        return self._q_item(session, query, params)

    def get_by_id_lock_thread_type(
        self, session: Session, thread_id: int, user_id: str, thread_types
    ) -> Optional[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "thread_types": thread_types,
        }
        query = f"""SELECT * FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id
        AND p.is_removed=False
        AND t.type IN :thread_types """
        return self._q_item(session, query, params)

    def get_group_for_owner(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "role": "owner",
            "is_removed": False,
            "thread_type": ["group", "super_group"],
        }
        query = f"""SELECT * FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id
        AND p.is_removed=:is_removed
        AND p.role=:role
        AND t.type IN :thread_type"""
        return self._q_item(session, query, params)

    def get_group_for_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "is_removed": False,
            "thread_type": ["group", "super_group"],
        }
        query = f"""SELECT * FROM {self.t_name} AS p JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id
        AND p.is_removed=:is_removed
        AND t.type IN :thread_type
        """
        return self._q_item(session, query, params)

    def get_exist_record(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {"thread_id": thread_id, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name}
        WHERE user_id=:user_id
        AND thread_id=:thread_id
        LIMIT 1"""

        return self._q_item(session, query, params)

    def get_thread_by_pair_ids(
        self, session: Session, user_id: str, pair_ids
    ) -> Optional[ParticipantModel]:
        params = {"pair_ids": pair_ids, "user_id": user_id}
        query = f"""SELECT * FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE t.pair_ids=:pair_ids AND p.user_id=:user_id"""
        return self._q_item(session, query, params)

    def get_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "is_removed": False,
            "thread_type": ["group", "super_group", "direct"],
        }
        query = f"""SELECT * FROM {self.t_name} AS p
        JOIN threads AS t
        ON p.thread_id=t.id
        WHERE p.user_id=:user_id
        AND p.thread_id=:thread_id
        AND p.is_removed=:is_removed
        AND t.type IN :thread_type"""
        return self._q_item(session, query, params)


class ParticipantInsertController(BaseController):
    def __init__(self, log: Logger):
        super().__init__(log)

        self._insert_member_q = (
            f"INSERT INTO {self.t_name}"
            "(user_id, thread_id, "
            "partner_id, read_count, "
            "role, folder) "
            "VALUES "
            "(:user_id, :thread_id, "
            ":partner_id, :read_count, "
            ":role, :folder)"
        )

        self._insert_q = (
            f"INSERT INTO {self.t_name}"
            "(user_id, thread_id, "
            "partner_id, read_count, role, type, folder, is_removed) "
            "VALUES "
            "(:user_id, :thread_id, "
            ":partner_id, :read_count, "
            ":role, :type, :folder, :is_removed) "
            "ON DUPLICATE KEY UPDATE "
            "is_removed=:is_removed, "
            "read_count=:read_count, "
            "type=:type"
        )

    def _insert_item(self, session: Session, query, payload):
        session.execute(query, payload)

    def create_bot_member(
        self, session: Session, thread_id: int, page_id, partner_id, read_count
    ):
        payload = {
            "thread_id": thread_id,
            "user_id": page_id,
            "role": "owner",
            "partner_id": partner_id,
            "read_count": read_count,
            "is_removed": False,
            "type": "bot",
            "folder": constant.FOLDER_DEFAULT,
        }
        self._insert_item(session, self._insert_q, payload)

    def create_group_bot(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id=None,
        read_count=0,
        folder=constant.FOLDER_DEFAULT,
    ):
        payload = {
            "user_id": user_id,
            "thread_id": thread_id,
            "partner_id": partner_id,
            "read_count": read_count,
            "role": "member",
            "is_removed": False,
            "type": "bot",
            "folder": folder,
        }
        self._insert_item(session, self._insert_q, payload)

    def create_member(
        self,
        session,
        thread_id,
        user_id,
        partner_id=None,
        read_count=0,
        folder: str = constant.FOLDER_DEFAULT,
    ):
        payload = {
            "user_id": user_id,
            "thread_id": thread_id,
            "partner_id": partner_id,
            "read_count": read_count,
            "role": "member",
            "folder": folder,
        }
        self._insert_item(session, self._insert_member_q, payload)

    def create_members(
        self,
        session,
        thread_id,
        member_ids,
        owner_id=None,
        folder=constant.FOLDER_DEFAULT,
    ):
        payloads = [
            {
                "user_id": member_id,
                "thread_id": thread_id,
                "partner_id": None,
                "read_count": 0,
                "role": "member",
                "folder": folder,
            }
            for member_id in member_ids
        ]
        if owner_id is not None:
            payloads.append(
                {
                    "user_id": owner_id,
                    "thread_id": thread_id,
                    "partner_id": None,
                    "read_count": 0,
                    "role": "owner",
                    "folder": folder,
                }
            )

        self._insert_item(session, self._insert_member_q, payloads)

    def create_owner(
        self,
        session,
        thread_id,
        user_id,
        partner_id=None,
        read_count=0,
        folder=constant.FOLDER_DEFAULT,
        last_message=None,
    ):
        payload = {
            "user_id": user_id,
            "thread_id": thread_id,
            "partner_id": partner_id,
            "read_count": read_count,
            "role": "owner",
            "folder": folder,
            "last_message": json_dumps(last_message) if last_message else None,
            "is_removed": False,
        }

        insert_owner_q = f"""INSERT INTO {self.t_name}
            (user_id, thread_id,
            partner_id, read_count, role,
            folder, last_message)
            VALUES
            (:user_id, :thread_id,
            :partner_id, :read_count,
            :role, :folder, :last_message)
            ON DUPLICATE KEY UPDATE
            is_removed=:is_removed,
            read_count=:read_count,
            folder=:folder,
            last_message=:last_message,
            role=:role
            """

        self._insert_item(session, insert_owner_q, payload)

    def create_leave_member(self, session, thread_id, user_id, folder):
        payload = {
            "thread_id": thread_id,
            "user_id": user_id,
            "role": "member",
            "partner_id": None,
            "read_count": 0,
            "is_removed": True,
            "type": "member",
            "folder": folder,
        }
        self._insert_item(session, self._insert_q, payload)


class ParticipantThreadRepositoryMySQL(
    ParticipantListController,
    ParticipantItemController,
    ParticipantUpdateController,
    ParticipantInsertController,
    ParticipantThreadRepository,
):
    def __init__(self, log: Logger, cache: ParticipantCache):
        super().__init__(log)
        self.log = log
        self.cache = cache

    def unpin(self, session: Session, user_id: str, thread_id: int, folder):
        # 0 mean remove
        self.pin(
            session,
            user_id,
            thread_id,
            0,
            folder,
        )

    def get_bots_in_thread(
        self, session: Session, thread_id: int
    ) -> List[ParticipantModel]:
        params = {"thread_id": thread_id, "type": "bot"}
        query = (
            f"SELECT * FROM {self.t_name} "
            "WHERE thread_id=:thread_id "
            "AND type=:type "
            "AND is_removed=False"
        )

        return self._q_items(session, query, params)

    def update_cache(self, part_d: Dict):
        try:
            part_o = ParticipantModel(**part_d)
            self.cache.update(part_o)
        except Exception as e:
            self.log.error(e)

    def remove_cache(self, thread_id: int, user_id: str):
        try:
            self.cache.remove(thread_id, user_id)
        except Exception as e:
            self.log.error(e)
