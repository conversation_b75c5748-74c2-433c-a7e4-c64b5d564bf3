from logging import Logger
from typing import Optional

from chat.connections.redis_c import RedisConnection
from chat.utils.json import json_loads

from chat.participants.model import ParticipantModel
from redis import Redis


class ParticipantCache(object):
    """
    Updates thread scores in folders and tracks active members.
    Each thread will have a score in a folder. Thread with higher score will be
    positioned at higher position.

    A pinned thread has much higher score, so it will stay on top:
        score = timestamp * 1000
    """

    # FIXME: will we have overflow problem ? timestamp + 1000 years
    def __init__(
        self,
        log: Logger,
        engine: RedisConnection,
    ):
        self.log = log
        self.client: Redis = engine.client
        self.chat_part_key = "chat:cache:participants:{thread_id}:{user_id}"
        self.chat_thread_part_key = "chat:cache:participants:{thread_id}"
        self.lock_key = "chat:lock:participants:{thread_id}:{user_id}"

    def update(self, part: ParticipantModel):
        key_info = self.chat_part_key.format(
            thread_id=part.thread_id, user_id=part.user_id
        )
        key_idx = self.chat_thread_part_key.format(thread_id=part.thread_id)
        self.client.set(key_info, part.json())
        if part.is_removed:
            self.client.zrem(key_idx, part.user_id)
        else:
            self.client.zadd(key_idx, {part.user_id: part.get_score()})

    def get(self, thread_id: int, user_id: str) -> Optional[ParticipantModel]:
        key = self.chat_part_key.format(thread_id=thread_id, user_id=user_id)
        part = self.client.get(key)
        if not part:
            return None

        return ParticipantModel(**json_loads(part))

    def remove(self, thread_id: int, user_id: str):
        key_info = self.chat_part_key.format(
            thread_id=thread_id, user_id=user_id
        )
        key_idx = self.chat_thread_part_key.format(thread_id=thread_id)

        self.client.delete(key_info)
        self.client.zrem(key_idx, user_id)
