from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Sequence, Tuple

from sqlalchemy.orm import Session
from typing_extensions import TypedDict

from chat import constant
from chat.participants.model import (
    ParticipantModel,
)


class IDOnly(TypedDict):
    id: str


class ThreadOnly(TypedDict):
    thread_id: int


class ThreadAndFolder(TypedDict):
    thread_id: int
    folder: str


class ThreadId(TypedDict):
    id: int


class UserRole(TypedDict):
    role: str


ThreadIdList = List[ThreadId]


class ParticipantThreadRepository(ABC):
    @abstractmethod
    def get_all_thread_ids_in_folders(
        self, session: Session, user_id: str, folders: List[str]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_in_folder(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_all_sub_threads_for_user(
        self, session: Session, user_id: str, thread_id: int
    ) -> List[ParticipantModel]:
        """Returns id of all sub-threads that user has joined."""

    @abstractmethod
    def set_unread_flag(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        unread_status: bool,
    ):
        """Set unread flag to thread."""

    @abstractmethod
    def get_folders_by_thread_ids(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_threads_by_user_id(
        self, session: Session, user_id: str, last_id=None, page_size=10
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_members_in_list_threads(
        self,
        session: Session,
        thread_ids: List[int],
        roles: List[str] = [],
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_members_in_thread(
        self,
        session: Session,
        thread_id: int,
        last_user_id: int,
        page_size: int,
    ) -> List[ParticipantModel]:
        """Gets all members in a thread (only with member role)."""

    @abstractmethod
    def get_user_roles_in_thread(
        self, session: Session, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        """Get user with roles in thread.

        Returns: a map from user_id to role of the user in thread.
        """

    @abstractmethod
    def get_members_by_ids(
        self, session: Session, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_user_role_in_threads(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        """Get user role in list thread

        Returns a map from thread_id to role of the user in thread.
        """

    @abstractmethod
    def get_owners(
        self, session: Session, thread_id: int
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_members_by_roles(
        self,
        session: Session,
        thread_id: int,
        roles: Sequence[str],
        limit=2,
        last_id=0,
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_all_threads_in_folder(
        self, session: Session, user_id: str, folder: str
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_list_threads_in_folders(
        self,
        session: Session,
        user_id: str,
        folders: List[str],
        thread_ids: List[int],
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_thread_members_in_list(
        self, session: Session, user_thread_ids: List[Tuple[str, int]]
    ) -> List[ParticipantModel]:
        """Returns list of thread members from list user-thread."""

    @abstractmethod
    def get_exist_records(
        self, session: Session, thread_id: int, user_ids: Sequence[str]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_banned_members_limit(
        self,
        session: Session,
        thread_id: int,
        last_user_id: str,
        page_size: int,
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_threads_by_ids(
        self, session: Session, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_all_group_ids(
        self, session: Session, user_id: str, workspace_id
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def get_all_by_workspace(
        self, session: Session, user_id: str, workspace_id
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def reactivate_groups_for_user(self, session: Session, user_id: str):
        """Reactivate user in groups for a deactivated member.

        User role will be set to member if he/she was admin/owner of group.
        """

    @abstractmethod
    def get_all_group_ids_without_associate(
        self, session: Session, user_id: str
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def update_role(
        self, session: Session, user_id: str, thread_id: int, new_role: str
    ):
        pass

    @abstractmethod
    def toggle_notify(self, session: Session, thread_id: int, user_id: str):
        pass

    @abstractmethod
    def edit_info(
        self, session: Session, thread_id: int, user_id: str, edit_fields
    ) -> Any:
        pass

    @abstractmethod
    def pin(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        pinned_at: int,
        folder: str,
    ):
        pass

    @abstractmethod
    def unpin(
        self, session: Session, user_id: str, thread_id: int, folder: str
    ):
        pass

    @abstractmethod
    def get_pinned_thread_ids(
        self,
        session: Session,
        user_id: str,
        thread_ids: List[int],
        folder: str,
    ):
        pass

    @abstractmethod
    def move_folder(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        to_folder="default",
    ):
        pass

    @abstractmethod
    def clear_and_move_folder_user_threads(
        self, session: Session, user_id: str, from_folder: str, to_folder: str
    ):
        pass

    @abstractmethod
    def clear_thread_users(
        self, session: Session, thread_id: int, message_id: int
    ):
        pass

    @abstractmethod
    def clear_user(self, session: Session, thread_id: int, user_id: str):
        pass

    @abstractmethod
    def update_media_count_users(
        self,
        session: Session,
        thread_id: int,
        user_ids: List[str],
        media_infos,
        is_decrease,
    ):
        pass

    @abstractmethod
    def leave_thread(self, session: Session, thread_id: int, user_id: str):
        """Remove an user from thread."""

    @abstractmethod
    def set_deactivate_status(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        is_deactivated: bool,
    ):
        """Mark an user is deactivated or not"""

    @abstractmethod
    def update_last_message(
        self, session: Session, user_id: str, thread_id: int, message
    ):
        pass

    @abstractmethod
    def update_all_last_message(
        self, session: Session, thread_id: int, message_id: int, message
    ):
        pass

    @abstractmethod
    def update_read_count(
        self, session: Session, thread_id: int, user_id: str, read_count=0
    ):
        pass

    @abstractmethod
    def update_max_read_count(
        self, session: Session, thread_id: int, user_id: str
    ):
        pass

    @abstractmethod
    def unban(self, session: Session, thread_id: int, user_id: str):
        pass

    @abstractmethod
    def rejoin(
        self, session: Session, thread_id: int, user_id: str, read_count: int
    ):
        pass

    @abstractmethod
    def rejoins(self, session: Session, thread_id: int, user_ids: List[str]):
        pass

    @abstractmethod
    def update_folder(
        self, session: Session, user_id: str, thread_id: int, folder: str
    ):
        pass

    @abstractmethod
    def set_ban_and_remove(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        will_banned_id: str,
    ):
        pass

    @abstractmethod
    def get_thread_for_user(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        """Gets thread for user, None if user is not in the thread."""

    @abstractmethod
    def get_partner(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_banned_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_thread_for_user_by_collab_id(
        self, session: Session, user_id: str, collab_id: str
    ) -> Optional[ParticipantModel]:
        """Gets thread info for user if user is
        a member of a thread, None otherwise."""

    @abstractmethod
    def get_by_id_lock_thread_type(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        thread_types: Sequence[Any],
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_group_for_owner(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        """Returns thread info if user is the owner of a group chat, None if
        the user isn't the owner of thread."""

    @abstractmethod
    def get_group_for_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        """Returns thread info if user is
        a member of group chat, None otherwise."""

    @abstractmethod
    def get_exist_record(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_thread_by_pair_ids(
        self, session: Session, user_id: str, pair_ids
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_member(
        self, session: Session, thread_id: int, user_id: str
    ) -> Optional[ParticipantModel]:
        pass

    @abstractmethod
    def get_threads_by_pair_ids(
        self, session: Session, user_id: str, partner_ids: List[Any]
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def create_bot_member(
        self, session: Session, thread_id: int, page_id, partner_id, read_count
    ):
        pass

    @abstractmethod
    def create_group_bot(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id=None,
        read_count=0,
        folder: str = constant.FOLDER_DEFAULT,
    ):
        pass

    @abstractmethod
    def create_member(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id=None,
        read_count=0,
        folder: str = constant.FOLDER_DEFAULT,
    ):
        pass

    @abstractmethod
    def create_members(
        self,
        session: Session,
        thread_id: int,
        member_ids: Sequence[str],
        owner_id=None,
        folder=constant.FOLDER_DEFAULT,
    ):
        pass

    @abstractmethod
    def create_owner(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id=None,
        read_count=0,
        folder=constant.FOLDER_DEFAULT,
        last_message=None,
    ):
        pass

    @abstractmethod
    def create_leave_member(
        self, session: Session, thread_id: int, user_id, folder
    ):
        pass

    @abstractmethod
    def get_bots_in_thread(
        self, session: Session, thread_id: int
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def update_cache(self, part_d: Dict):
        """Update cache"""

    @abstractmethod
    def remove_cache(self, thread_id: int, user_id: str):
        """Update cache"""

    @abstractmethod
    def get_all_thread_by_user(
        self, session: Session, user_id: str
    ) -> List[ParticipantModel]:
        pass

    @abstractmethod
    def disable_reactivate(
        self, session: Session, user_id: str
    ):
        pass
