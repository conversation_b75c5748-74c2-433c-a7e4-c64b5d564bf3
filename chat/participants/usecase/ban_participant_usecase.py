import time
from typing import Any, Dict, List

from sqlalchemy.orm import Session

from chat import acl, action_type, constant
from chat.connections.gapo_client.membership import (
    PermissionDenied as MembershipPermissionError,
)
from chat.exception import UserNotInThread
from chat.participants import exception as PartError
from chat.participants.model import ParticipantModel
from chat.participants.model.task import TaskRemoveParticipant
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.model.task import TaskLeaveGroup
from chat.threads.model import ThreadModel
from chat.utils import common

from .get_participant_usecase import GetParticipantUseCase


class BanParticipantUseCase(GetParticipantUseCase):
    def ban_member(self, user_id: str, thread_id: int, will_remove_id: str):
        with self.session_m.get_session() as session:
            actor_p_thread = self.get_member_group(session, thread_id, user_id)
            actor_role = actor_p_thread.participant.role
            settings = actor_p_thread.thread.settings

            to_ban_p_thread = self.get_member_group(
                session, thread_id, will_remove_id
            )
            to_ban_user_role = to_ban_p_thread.participant.role
            has_permission, permission_name = acl.get_permission_to_user(
                settings.is_public,
                actor_role,
                to_ban_user_role,
                constant.PERMISSION_BAN_MEMBER,
            )
            if not has_permission:
                raise PartError.RemoveMemberPermissionError(permission_name)

            # self.addBanHandler(session, user_id, thread_id, will_remove_id)
            self.pt_repo.set_ban_and_remove(
                session, user_id, thread_id, will_remove_id
            )
            self._membership_remove_member(
                creator_id=user_id,
                remove_user_id=will_remove_id,
                thread=actor_p_thread.thread,
            )
            self.pt_repo.remove_cache(thread_id, will_remove_id)

            to_remove_ids = [will_remove_id]
            self.thread_broker.remove_thread_scores_for_users(
                thread_id, [to_ban_p_thread.participant]
            )

            self.thread_broker.remove_members(thread_id, to_remove_ids)
            self._update_thread_member_count(
                session, thread_id, -len(to_remove_ids)
            )
            self._update_thread_banned_count(
                session, thread_id, len(to_remove_ids)
            )
            session.commit()

            thread = self.thread_repo.get_by_id(session, thread_id)
            if not thread:
                return

            if thread.is_group():
                self._publish_action_note(
                    thread.id,
                    user_id,
                    to_remove_ids,
                    action_type.KICK_MEM,
                    None,
                    skip_all_action_notes=True,
                )
            session.commit()

        notify_thread_message: Dict[str, Any] = {
            "thread_id": thread_id,
            "message_to": thread.message_count,
            "member_ids": to_remove_ids,
        }
        self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)

    def unban_member(self, user_id: str, thread_id: int, will_unban_id: str):
        with self.session_m.get_session() as session:
            actor_p_thread = self.get_member_group(session, thread_id, user_id)
            actor_role = actor_p_thread.participant.role
            settings = actor_p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                actor_role,
                constant.PERMISSION_UNBAN_MEMBER,
            )
            if not has_permission:
                raise PartError.UnbanMemberPermissionError(permission_name)

            self.get_banned_member_group(session, thread_id, will_unban_id)
            self.pt_repo.unban(session, thread_id, will_unban_id)
            self._update_thread_banned_count(session, thread_id, -1)
            session.commit()

    def remove_member(self, user_id: str, thread_id: int, will_remove_id: str):
        with self.session_m.get_session() as session:
            actor_p_thread = self.get_member_group(session, thread_id, user_id)
            if not actor_p_thread.thread.is_group():
                raise UserNotInThread()

            remove_p_thread = self.get_member_group(
                session, thread_id, will_remove_id
            )
            self._remove_member(
                session, actor_p_thread, thread_id, remove_p_thread
            )
            session.commit()

    def remove_members(
        self, user_id: str, thread_id: int, to_remove_ids: List[str]
    ):
        with self.session_m.get_session() as session:
            # make sure the current user is a member of thread
            self.get_member_group(session, thread_id, user_id)
            for to_remove_id in to_remove_ids:
                self.rb_broker.publish_background_task(
                    constant.TASK_REMOVE_MEMBER,
                    TaskRemoveParticipant(
                        user_id=user_id,
                        thread_id=thread_id,
                        to_remove_id=to_remove_id,
                    ).dict(),
                )

    def remove_members_internal(
        self, thread_id: int, to_remove_ids: List[str]
    ):
        """Removes all members internal.

        This will treat all actions as leave_group actions."""
        for user_id in to_remove_ids:
            self.rb_broker.publish_background_task(
                constant.TASK_LEAVE_GROUP,
                TaskLeaveGroup(
                    user_id=user_id,
                    # don't notify other members about this event
                    send_action_note=True,
                    thread_id=thread_id,
                    clear_msg=False,
                    promote_new_owner_if_needed=True,
                ).dict(),
            )

    def remove_members_bg(
        self, user_id: str, thread_id: int, to_remove_ids: List[str]
    ):
        with self.session_m.get_session() as session:
            p_thread = self.get_member_group(session, thread_id, user_id)
            for to_remove_id in to_remove_ids:
                remove_p_thread = self.get_member_group(
                    session, thread_id, to_remove_id
                )
                self._remove_member(
                    session, p_thread, thread_id, remove_p_thread
                )
                session.commit()

    def _remove_member(
        self,
        session: Session,
        actor_p_thread: ParticipantThreadModel,
        thread_id: int,
        remove_p_thread: ParticipantThreadModel,
    ):
        actor_role = actor_p_thread.participant.role
        settings = actor_p_thread.thread.settings

        remove_role = remove_p_thread.participant.role
        has_permission, permission_name = acl.get_permission_to_user(
            settings.is_public,
            actor_role,
            remove_role,
            constant.PERMISSION_REMOVE_MEMBER,
        )

        actor_id = actor_p_thread.participant.user_id
        remove_id = remove_p_thread.participant.user_id

        if not has_permission:
            raise PartError.RemoveMemberPermissionError(permission_name)

        self._remove_mem_handler(session, thread_id, remove_id)
        self._membership_remove_member(
            creator_id=actor_id,
            remove_user_id=remove_id,
            thread=actor_p_thread.thread,
        )

        to_remove_ids = [remove_id]
        self.thread_broker.remove_thread_scores_for_users(
            thread_id, [remove_p_thread.participant]
        )
        self.thread_broker.remove_members(thread_id, to_remove_ids)

        self._update_thread_member_count(
            session, thread_id, -len(to_remove_ids)
        )
        session.commit()

        thread = actor_p_thread.thread
        if thread.is_group():
            self._publish_action_note(
                thread.id,
                actor_id,
                to_remove_ids,
                action_type.KICK_MEM,
                None,
                skip_all_action_notes=True,
            )

        notify_thread_message: Dict[str, Any] = {
            "thread_id": thread_id,
            "message_to": thread.message_count,
            "member_ids": to_remove_ids,
        }
        self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)

        # remove members from sub-threads of that thread
        # that user is in.
        self._remove_member_from_sub_threads(session, remove_id, thread_id)

    def _remove_member_from_sub_threads(
        self, session: Session, user_id: str, parent_thread_id: int
    ):
        parts = self.pt_repo.get_all_sub_threads_for_user(
            session, user_id, parent_thread_id
        )
        for part in parts:
            self._remove_member_from_sub_thread(session, part)

    def _remove_member_from_sub_thread(
        self,
        session: Session,
        part: ParticipantModel,
    ):
        # remove thread from database
        self.pt_repo.leave_thread(session, part.thread_id, part.user_id)
        self.pt_repo.remove_cache(part.thread_id, part.user_id)

        # remove thread score and data in redis
        self._remove_thread_scores(
            part.thread_id, [part.user_id], constant.FOLDER_SUB_THREAD
        )
        self.thread_broker.remove_members(part.thread_id, [part.user_id])

        # update member count
        self._update_thread_member_count(session, part.thread_id, -1)
        session.commit()

        # FIXME: update the order in the list of commenters ?

        # send mqtt message to delete thread in client
        notify_thread_message: Dict[str, Any] = {
            "thread_id": part.thread_id,
            "message_to": 1,  # FIXME: what is this for ?
            "member_ids": [part.user_id],
        }
        self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)

    def _remove_mem_handler(
        self, session: Session, thread_id: int, member_id: str
    ):
        self._reset_folder(session, member_id, thread_id)
        self.pt_repo.leave_thread(session, thread_id, member_id)
        self.pt_repo.remove_cache(thread_id, member_id)

    def _reset_folder(self, session: Session, user_id: str, thread_id: int):
        part = self.pt_repo.get_by_id_lock_thread_type(
            session,
            thread_id,
            user_id,
            ["group", "super_group"],
        )
        if not part:
            raise UserNotInThread()

        if common.is_folder_created_by_user(part.folder):
            self.pt_repo.update_folder(
                session, user_id, thread_id, constant.FOLDER_DEFAULT
            )

    def _update_thread_banned_count(
        self, session: Session, thread_id: int, delta: int
    ):
        self.thread_repo.update_thread_banned_count(session, thread_id, delta)

    def _membership_remove_member(
        self, creator_id, remove_user_id: str, thread: ThreadModel
    ):
        collab_id = thread.collab_id
        if not collab_id:
            return
        try:
            workspace_id = thread.workspace_id

            self.gapo_client.membership.remove_member(
                caller_id=int(creator_id),
                collab_id=collab_id,
                user_id=int(remove_user_id),
                workspace_id=workspace_id or "",
            )
        except Exception as e:
            # maybe due to slow syncing between chat-service and
            # membership, we may got permission error
            # we will reduce this by retry again after some small duration
            if not isinstance(e, MembershipPermissionError):
                self.log.critical(
                    f"[Membership] Remove Member error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {collab_id}, "
                    f"caller_id: {creator_id}, "
                    f"user_id: {remove_user_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )
                return

            # retry
            try:
                time.sleep(0.2)
                workspace_id = ""
                if thread.workspace_id:
                    workspace_id = thread.workspace_id

                self.gapo_client.membership.remove_member(
                    caller_id=int(creator_id),
                    collab_id=collab_id,
                    user_id=int(remove_user_id),
                    workspace_id=workspace_id,
                )
            except Exception as e:
                self.log.critical(
                    f"[Membership] Remove Member error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {collab_id}, "
                    f"caller_id: {creator_id}, "
                    f"user_id: {remove_user_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )

    def _remove_thread_scores(
        self,
        thread_id: int,
        user_ids: List[str],
        folder: str = constant.FOLDER_DEFAULT,
    ):
        members: List[ParticipantModel] = [
            ParticipantModel.new_participant(
                user_id=user_id, thread_id=thread_id
            )
            for user_id in user_ids
        ]
        for m in members:
            m.folder = folder
        self.thread_broker.remove_thread_scores_for_users(thread_id, members)
