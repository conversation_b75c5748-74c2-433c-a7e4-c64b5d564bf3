from typing import Any, List, Sequence

from sqlalchemy.orm import Session

from chat import action_type as ACTION_TYPES
from chat import constant
from chat.app.app import MyApp
from chat.participants import exception as PartError
from chat.threads.model import ThreadModel
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.usecase import ThreadUsecases
from chat.utils import message_helpers as mh
from chat.utils.validator_helper import is_bot_id


class ParticipantMySQLUsecase(object):
    def __init__(
        self,
        app: MyApp,
        thread_usc: ThreadUsecases,
    ):
        self.i_storage = app.repos.info
        self.mqtt_broker = app.brokers.pt_mqtt
        self.thread_broker = app.brokers.thread_rd
        self.info_repo = app.repos.info
        self.rb_broker = app.brokers.pt_rb
        self.thread_repo = app.repos.thread
        self.thread_usc = thread_usc
        self.redis_counter = app.repos.counter
        self.pt_repo = app.repos.pt
        self.user_repo = app.repos.user
        self.gapo_client = app.repos.gapo
        self.block_repo = app.repos.block
        self.config = app.conf
        self.log = app.log
        self.read_repo = app.repos.thread_read
        self._max_member_status = self.config.max_member_sync_status
        self.session_m = app.conns.mysql
        self.metrics = app.app_metrics

    def _update_thread_member_count(
        self, session: Session, thread_id: int, delta: int
    ):
        return

    def _get_users(self, user_ids: List[str]):
        return self.i_storage.get_users(user_ids)

    def _get_bots(self, bot_ids: List[Any]):
        return self.i_storage.get_bots(bot_ids)

    def _get_object_infos(self, receiver_ids: Sequence[Any]):
        user_ids, bot_ids = [], []
        for i in receiver_ids:
            if is_bot_id(i):
                bot_ids.append(i)
            else:
                user_ids.append(i)
        bots = self._get_bots(bot_ids)
        receivers = self._get_users(user_ids)
        receivers.update(bots)
        return receivers

    def _incr_message_counter(self, session: Session, thread: ThreadModel):
        thread_id = thread.id
        current_counter = thread.message_count
        counter = self.redis_counter.inc_counter(thread_id)
        if counter <= current_counter:
            counter = self.thread_repo.incr_thread_msg_counter(
                session, thread_id
            )
            self.redis_counter.set_counter(thread_id, counter)
        return counter

    def _decr_message_counter(self, session: Session, thread: ThreadModel):
        self.redis_counter.dcr_counter(thread.id)

    def get_max_msg_id(self, thread: ThreadModel):
        thread_id = thread.id
        counter = self.redis_counter.get_counter(thread_id)
        current_counter = thread.message_count
        if counter <= current_counter:
            return current_counter
        return counter

    def _skip_creating_message_from_action_note(
        self,
        action_type: str,
        skip_all_action_notes: bool = False,
    ):
        if skip_all_action_notes:
            return True

        # don't create new message if user is leaving group
        if action_type == ACTION_TYPES.LEAVE_GROUP:
            return True

        return False

    def _publish_action_note(
        self,
        thread_id: int,
        maker_id: str,
        receiver_ids: Sequence[Any],
        action_type,
        batch_receivers,
        skip_all_action_notes: bool = False,
    ):
        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_id(session, thread_id)
            if not thread:
                return

            self.thread_repo.update_cache(thread.dict())

        if self._skip_creating_message_from_action_note(
            action_type, skip_all_action_notes
        ):
            skip_message = True
            message_id = thread.message_count
        else:
            # increase message counter
            skip_message = False

        maker = self._get_users([maker_id])[maker_id]
        receivers = self._get_object_infos(receiver_ids)

        if not skip_message:
            message_id = self._incr_message_counter(session, thread)
        try:
            if not batch_receivers:
                action_note = mh.action_note_lines(
                    action_type,
                    message_id,
                    maker,
                    [receivers[new_id] for new_id in receiver_ids],
                    thread,
                )
            else:
                action_note = mh.action_note_batch(
                    action_type,
                    message_id,
                    maker,
                    batch_receivers,
                    [receivers[new_id] for new_id in receiver_ids],
                    thread,
                )
        except Exception as e:
            if not skip_message:
                self._decr_message_counter(session, thread)
            raise e
        self.rb_broker.publish_action_note(
            constant.TASK_CREATE_MESSAGE,
            action_note,
            skip_message=skip_message,
        )

    def _update_thread_score_in_folders(
        self,
        user_id: str,
        thread_id: int,
        p_thread: ParticipantThreadModel,
        thread_score: int,
    ):
        folders = self._get_unpinned_folders(p_thread)
        for folder in folders:
            self.thread_broker.set_thread_score(
                user_id,
                thread_id,
                folder,
                thread_score,
                set_default_folder=False,
            )

    def _get_unpinned_folders(self, p_thread: ParticipantThreadModel):
        """Gets list of folders where a thread isn't pinned."""
        folders = []
        if p_thread.participant.pin_pos == 0:
            folders.append(p_thread.participant.folder)

        if p_thread.participant.pin_default_pos == 0:
            folders.append(constant.FOLDER_DEFAULT)

        return folders

    def _get_pinned_folders(self, p_thread: ParticipantThreadModel):
        folders = []
        if p_thread.participant.pin_pos > 0:
            folders.append(p_thread.participant.folder)

        if p_thread.participant.pin_default_pos > 0:
            folders.append(constant.FOLDER_DEFAULT)

        return folders

    def get_thread_for_user(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        return self.thread_usc.get_thread.get_thread_for_user(
            session, thread_id, user_id
        )

    def get_member_group(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        p_thread = self.get_thread_for_user(
            session,
            thread_id,
            user_id,
        )
        if p_thread.thread.is_direct():
            raise PartError.ParticipantError()
        return p_thread
