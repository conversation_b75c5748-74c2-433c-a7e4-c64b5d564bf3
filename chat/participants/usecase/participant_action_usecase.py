import time
from typing import Optional

from sqlalchemy.orm import Session

from chat import acl, constant
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.connections.gapo_client.membership.membership_client import (
    PermissionDenied as MembershipPermissionError,
)
from chat.exception import DataNotFound, UserNotInThread
from chat.participants import exception as PartError
from chat.participants.model.task import UserSeen<PERSON>hreadEvent
from chat.participant_threads.model import ParticipantThreadModel
from chat.threads.model import ThreadModel
from chat.utils import common
from chat.utils.json import json_loads

from .get_participant_usecase import GetParticipantUseCase


class ParticipantActionUseCase(GetParticipantUseCase):
    def mark_as_read(
        self, user_id_read, thread_id: int, to_msg_id: Optional[int] = None
    ):
        # always read lastest
        with self.session_m.get_session() as session:
            try:
                self._update_read_from_user(
                    session, user_id_read, thread_id, to_msg_id
                )
            except UserNotInThread:
                self._update_read_from_user_not_in_thread(
                    session, user_id_read, thread_id, to_msg_id
                )
            session.commit()
            self.after_update(session, thread_id, [user_id_read])

    def mark_unread(self, user_id: str, thread_id: int, msg_id: int):
        with self.session_m.get_session() as session:
            self._update_unread(session, user_id, thread_id, msg_id - 1)
            session.commit()
            self.after_update(session, thread_id, [user_id])

    def update_typing(self, user_id: str, thread_id: int):
        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_id(session, thread_id)
            if not thread:
                raise DataNotFound()
            member_ids = self._get_active_members(
                thread_id, thread.member_count
            )

            user_info = self._get_users([user_id])[user_id]
            member_objects = [
                {"user_id": member_id, "type": constant.USER_TYPE}
                for member_id in member_ids
            ]

            try:
                self.mqtt_broker.publish_typing(
                    member_objects, thread_id, user_id, user_info
                )
            except TimeoutError:
                pass
            session.commit()

    def mark_as_friend(self, user_id: str, part_id: str):
        with self.session_m.get_session() as session:
            session.commit()

    def accept_friend_request(
        self, user_id: str, part_id: str, accept_type: str
    ):
        with self.session_m.get_session() as session:
            session.commit()

    def update_role(
        self, actor_id: str, receiver_id: str, thread_id: int, new_role: str
    ):
        with self.session_m.get_session() as session:
            a_p_thread = self.get_member_group(session, thread_id, actor_id)

            actor_role = a_p_thread.participant.role
            settings = a_p_thread.thread.settings

            r_p_thread = self.get_member_group(session, thread_id, receiver_id)
            receiver_role = r_p_thread.participant.role

            params = (
                session,
                settings.is_public,
                actor_id,
                actor_role,
                receiver_id,
                receiver_role,
                thread_id,
            )

            if new_role == constant.MEMBER_ROLE:
                events = self._change_role_to_member(*params)
                self._membership_set_role(
                    actor_id=actor_id,
                    receiver_id=receiver_id,
                    thread=a_p_thread.thread,
                    role=CollabGroupRole.Member,
                )
            elif new_role == constant.ADMIN_ROLE:
                events = self._change_role_to_admin(*params)
                self._membership_set_role(
                    actor_id=actor_id,
                    receiver_id=receiver_id,
                    thread=a_p_thread.thread,
                    role=CollabGroupRole.Admin,
                )
            elif new_role == constant.OWNER_ROLE:
                events = self._change_role_to_owner(*params)
                self._membership_set_role(
                    actor_id=actor_id,
                    receiver_id=receiver_id,
                    thread=a_p_thread.thread,
                    role=CollabGroupRole.Owner,
                )
                self._membership_set_role(
                    actor_id=actor_id,
                    receiver_id=actor_id,
                    thread=a_p_thread.thread,
                    role=CollabGroupRole.Admin,
                )

            session.commit()
            self.after_update(session, thread_id, [actor_id, receiver_id])

        members = self._user_seen_to_group(
            actor_id, thread_id, a_p_thread.thread.member_count
        )

        for event in events:
            event["thread_id"] = thread_id
            self.mqtt_broker.publish_role_changed_event(members, event)

    def _change_role_to_member(
        self,
        session: Session,
        is_g_pub,
        actor_id: str,
        actor_role: str,
        receiver_id: str,
        receiver_role: str,
        thread_id: int,
    ):
        has_permission, permission_name = acl.get_permission_to_user(
            is_g_pub,
            actor_role,
            receiver_role,
            constant.PERMISSION_REVOKE_TO_MEMBER,
        )
        if not has_permission:
            raise PartError.RevokeToMemberPermissionError(permission_name)

        self.pt_repo.update_role(
            session, receiver_id, thread_id, constant.MEMBER_ROLE
        )
        event = {"user_id": receiver_id, "role": constant.MEMBER_ROLE}
        return [event]

    def _change_role_to_admin(
        self,
        session: Session,
        is_g_pub,
        actor_id: str,
        actor_role: str,
        receiver_id: str,
        receiver_role: str,
        thread_id: int,
    ):
        has_permission, permission_name = acl.get_permission_to_user(
            is_g_pub,
            actor_role,
            receiver_role,
            constant.PERMISSION_AUTHORIZE_TO_ADMIN,
        )
        if not has_permission:
            raise PartError.AuthorizeToMemberPermissionError(permission_name)
        self.pt_repo.update_role(
            session, receiver_id, thread_id, constant.ADMIN_ROLE
        )
        event = {"user_id": receiver_id, "role": constant.ADMIN_ROLE}
        return [event]

    def _change_role_to_owner(
        self,
        session: Session,
        is_g_pub,
        actor_id: str,
        actor_role: str,
        receiver_id: str,
        receiver_role: str,
        thread_id: int,
    ):
        has_permission, permission_name = acl.get_permission_to_user(
            is_g_pub,
            actor_role,
            receiver_role,
            constant.PERMISSION_AUTHORIZE_TO_OWNER,
        )
        if not has_permission:
            raise PartError.AuthorizeToOwnerPermissionError(permission_name)
        events = []
        self.pt_repo.update_role(
            session, receiver_id, thread_id, constant.OWNER_ROLE
        )
        events.append({"user_id": receiver_id, "role": constant.OWNER_ROLE})
        self.pt_repo.update_role(
            session, actor_id, thread_id, constant.ADMIN_ROLE
        )
        events.append({"user_id": actor_id, "role": constant.ADMIN_ROLE})
        return events

    def _update_read_from_user(
        self,
        session: Session,
        user_id,
        thread_id: int,
        to_msg_id: Optional[int] = None,
    ):
        p_thread = self.get_thread_for_user(session, thread_id, user_id)
        # message
        latest_message_id = self.get_max_msg_id(p_thread.thread)
        read_count = p_thread.participant.read_count
        if to_msg_id:
            if to_msg_id < 0:
                return

            if read_count > latest_message_id:
                # BUG: wrong data already in db
                # TODO: remove it after some period
                latest_message_id = min(latest_message_id, to_msg_id)
            elif to_msg_id <= read_count:
                # cannot unread a message
                return

            # cannot read a message after the current message
            if to_msg_id > latest_message_id:
                return

            latest_message_id = to_msg_id

        self._update_read_repo(session, thread_id, user_id, latest_message_id)
        session.commit()  # unlock block

        if latest_message_id == read_count:
            return

        if p_thread.thread.is_direct():
            members = self._user_seen_to_direct(user_id, p_thread)
        else:
            members = self._user_seen_to_group(
                user_id, thread_id, p_thread.thread.member_count
            )

        user = self._get_users([user_id])[user_id]
        self.mqtt_broker.publish_message_seen_event(
            members,
            user,
            p_thread.thread,
            latest_message_id,
        )

        # publish rabbitmq event as "message"
        # so notification worker can deal with this
        # This is workaround/temporary solution because
        # right now, notification workers don't receive messages from MQTT
        # TODO: optimize this
        self.rb_broker.publish_message(
            constant.EVENT_USER_SEEN_THREAD,
            UserSeenThreadEvent(
                thread_id=thread_id,
                message_id=latest_message_id,
                user_id=user_id,
                user=user,
                thread=p_thread.thread,
            ).dict(),
            skip_message=True,  # avoid mqtt
        )

    def _update_read_from_user_not_in_thread(
        self,
        session: Session,
        user_id,
        thread_id: int,
        to_msg_id: Optional[int] = None,
    ):
        p_thread = self.get_thread_for_user(
            session, thread_id, user_id
        )
        if p_thread.thread.is_subthread():
            raise UserNotInThread()

        exist_member = self.pt_repo.get_exist_record(
            session, thread_id, user_id
        )
        if not exist_member:
            self.pt_repo.create_leave_member(
                session, thread_id, user_id, constant.FOLDER_SUB_THREAD
            )
        latest_message_id = self.get_max_msg_id(p_thread.thread)
        self.pt_repo.update_read_count(
            session,
            thread_id,
            user_id,
            latest_message_id,
        )

    def _update_unread(
        self, session: Session, user_id: str, thread_id: int, message_id: int
    ):
        p_thread = self.get_thread_for_user(session, thread_id, user_id)
        latest_message_id = p_thread.thread.message_count
        if message_id > latest_message_id:
            return
        self.pt_repo.update_read_count(session, thread_id, user_id, message_id)
        user = self._get_users([user_id])[user_id]
        self.mqtt_broker.publish_message_seen_event(
            [{"user_id": user_id, "type": constant.USER_TYPE}],
            user,
            p_thread.thread,
            latest_message_id,
        )

    def _update_read_from_partner(
        self, session: Session, thread_id: int, user_id: str, message_count
    ):  # use for save_message
        partner = self.get_partner(session, thread_id, user_id)
        self._update_read_repo(
            session, thread_id, partner.user_id, message_count
        )

    def _user_seen_to_group(self, user_id: str, thread_id: int, member_count):
        member_ids = self._get_active_members(thread_id, member_count)
        member_ids.append(user_id)
        return [
            {"user_id": member_id, "type": constant.USER_TYPE}
            for member_id in set(member_ids)
        ]

    def _user_seen_to_direct(
        self, user_id_read, p_thread: ParticipantThreadModel
    ):
        partner_id = common.get_partner_id(
            p_thread.thread.pair_ids, user_id_read
        )
        if partner_id is None:
            raise UserNotInThread("User not in thread")
        partner = {"user_id": partner_id}
        partner["type"] = constant.USER_TYPE
        return [partner, {"user_id": user_id_read, "type": constant.USER_TYPE}]

    def _user_seen_to_unsupported(self, user_id_read):
        return [{"user_id": user_id_read, "type": constant.USER_TYPE}]

    def _update_read_repo(
        self, session: Session, thread_id: int, user_id_read, message_id: int
    ):
        self.pt_repo.update_read_count(
            session, thread_id, user_id_read, message_id
        )
        self.read_repo.update_read(thread_id, user_id_read, message_id)

    def _membership_set_role(
        self,
        actor_id,
        receiver_id,
        thread: ThreadModel,
        role: CollabGroupRole,
    ):
        if not thread.collab_id:
            # not a collab group, i.e direct chat
            return

        try:
            self.gapo_client.membership.set_member_role(
                caller_id=int(actor_id),
                collab_id=thread.collab_id,
                user_id=int(receiver_id),
                roles=[role],
                workspace_id=thread.workspace_id or "",
            )
        except Exception as e:
            # maybe due to slow syncing between chat-service and
            # membership, we may got permission error
            # we will reduce these errors by retry again after some
            # small duration

            if not isinstance(e, MembershipPermissionError):
                self.log.critical(
                    f"[Membership] Set Member Role error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {actor_id}, "
                    f"user_id: {receiver_id}, "
                    f"role: {role}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )
                return

            # retry
            try:
                time.sleep(0.2)  # sleep small time until data is synced
                self.gapo_client.membership.set_member_role(
                    caller_id=int(actor_id),
                    collab_id=thread.collab_id,
                    user_id=int(receiver_id),
                    roles=[role],
                    workspace_id=thread.workspace_id or "",
                )
            except Exception as e:
                self.log.critical(
                    f"[Membership] Set Member Role error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {actor_id}, "
                    f"user_id: {receiver_id}, "
                    f"role: {role}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )
