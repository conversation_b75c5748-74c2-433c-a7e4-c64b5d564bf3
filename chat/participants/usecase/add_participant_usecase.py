import time
from typing import Dict, List, Optional, Set, Tuple

from sqlalchemy.orm import Session

from chat import acl, action_type, constant, dto
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.connections.gapo_client.membership.membership_client import (
    PermissionDenied as MembershipPermissionError,
)
from chat.exception import (
    AddingMembersDisabled,
    InvalidParameters,
    OnlyAdminCanCreate,
    PermissionDenied,
    UserNotInThread,
)
from chat.participant_threads.model import ParticipantThreadModel
from chat.participants import exception as PartError
from chat.participants.model import ParticipantModel
from chat.utils.validator_helper import is_bot_id

from .get_participant_usecase import GetParticipantUseCase


class AddParticipantUseCase(GetParticipantUseCase):
    def add_bot(self, user_id: str, thread_id: int, bot_id: str):
        with self.session_m.get_session() as session:
            p_thread = self.get_member_group(session, thread_id, user_id)
            if p_thread.thread.member_count + 1 >= constant.MAX_MEMBER:
                raise PartError.MaximumMemberReachedError()

            settings = p_thread.thread.settings
            member_role = p_thread.participant.role
            has_permission, permission_name = acl.get_permission(
                settings.is_public, member_role, constant.PERMISSION_ADD_MEMBER
            )
            if not has_permission:
                raise PartError.AddMemberPermissionError(permission_name)

            if not self.gapo_client.bot.is_owner(user_id, bot_id):
                raise PermissionDenied()

            bot_exist = self.pt_repo.get_exist_record(
                session, thread_id, bot_id
            )

            if bot_exist:
                if self._is_banned(bot_exist) or not self._is_removed(
                    bot_exist
                ):
                    raise PartError.NoMoreMember()
                self.pt_repo.rejoin(
                    session, thread_id, bot_id, p_thread.thread.message_count
                )
            else:
                self.pt_repo.create_group_bot(
                    session,
                    thread_id,
                    bot_id,
                    read_count=p_thread.thread.message_count,
                )

            self._update_thread_member_count(session, thread_id, 1)
            session.commit()
            self._publish_action_note(
                thread_id, user_id, (user_id,), action_type.INVITE_MEM, None
            )

    def add_members_from_orgc(
        self, user_id: str, thread_id: int, org_cs: Dict, org_type: str
    ) -> Tuple[List[str], List[str]]:
        members: Set[str] = set()
        org_infos: List[str] = []
        for w_id, o_ids in org_cs.items():
            if org_type == "department":
                o_mems = self.gapo_client.orgc.get_members_in_departments(
                    user_id, w_id, o_ids
                )
            else:
                o_mems = self.gapo_client.orgc.get_members_by_roles(
                    user_id, w_id, o_ids
                )

            for o_mem in o_mems:
                members.update(o_mem["user_ids"])
                org_infos.append(o_mem["name"])
        return list(members), org_infos

    def add_members_from_thread(
        self, user_id: str, thread_id: int, source_thread_id: int
    ) -> List[str]:
        with self.session_m.get_session() as session:
            if str(source_thread_id) == str(thread_id):
                raise InvalidParameters(
                    error_details=[
                        {
                            "msg": """The source thread cannot be
                            the same as the target thread"""
                        }
                    ]
                )

            self.get_member_group(
                session, thread_id=source_thread_id, user_id=user_id
            )
            members = self.get_all_members([source_thread_id])
            return [str(m.user_id) for m in members]

    def add_members_from_file(
        self,
        user_id: str,
        thread_id: int,
        user_workspace_id: Optional[str],
        file_id: str,
    ) -> List[str]:
        with self.session_m.get_session() as session:
            p_thread = self.get_member_group(session, thread_id, user_id)
            workspace_id = (
                p_thread.thread.workspace_id or user_workspace_id or ""
            )
            members = self.gapo_client.workspace.get_users_from_imported_file(
                workspace_id, file_id
            )
            self.log.info(
                "Chat-api: add member user: %s, ws: %s, file_id: %s, member: %s",  # noqa
                user_id,
                workspace_id,
                file_id,
                members,
            )

            return [str(m) for m in members]

    def add_members(
        self,
        user_id: str,
        thread_id: int,
        invite_ids: List[str],
        source_thread_ids: List[int] = [],
        member_file_id: Optional[str] = None,
        skip_action_notes=False,
        user_workspace_id: Optional[str] = None,
        orgcs: List[dto.OrgcObject] = [],
        call_source: Optional[str] = None,
    ):
        with self.session_m.get_session() as session:
            bot_num = 0
            p_thread = self.get_member_group(session, thread_id, user_id)
            if not p_thread.thread.is_group():
                raise UserNotInThread()

            workspace_id = (
                p_thread.thread.workspace_id or user_workspace_id or ""
            )

            self._special_add_mem_checks_for_gapo_partner(
                call_source, workspace_id
            )
            settings = p_thread.thread.settings
            user_role = p_thread.participant.role
            has_permission, permission_key = acl.check_permission(
                settings, user_role, constant.PERMISSION_ADD_MEMBER
            )
            if not has_permission:
                raise PartError.AddMemberPermissionError(permission_key)

            # make sure they are in source thread
            members_to_add = self._get_all_members_to_invite(
                session,
                user_id,
                invite_ids=invite_ids,
            )
            receiver = []
            if member_file_id:
                receiver.append("file excel")
            if source_thread_ids:
                receiver.append("nhóm chat")
            if orgcs:
                receiver.append("sơ đồ tổ chức")

            if receiver:
                info = " qua " + ", ".join(receiver)
            else:
                info = ""

            need_approve = False
            try:
                if p_thread.thread.collab_id:
                    collab = self.gapo_client.membership.get_collab_group(
                        int(user_id), p_thread.thread.collab_id, workspace_id
                    )
                if collab is not None:
                    # tranh anh huong logic khac tim collab k ra
                    need_approve = collab.settings.need_approve
                    if (
                        p_thread.participant.is_owner()
                        or p_thread.participant.is_admin()
                    ):
                        need_approve = False
            except Exception as e:
                self.log.error(e)

            if need_approve:
                self.thread_usc.create_thread.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.INVITE_ANNOUNCE_APPROVED,
                    extra_data={"info": info},
                    skip_all_action_notes=False,
                )
            else:
                self.thread_usc.create_thread.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.INVITE_ANNOUNCE,
                    extra_data={"info": info},
                    skip_all_action_notes=False,
                )

            can_add_bot = (
                p_thread.participant.is_owner()
                or p_thread.participant.is_admin()
            )
            for id in members_to_add:
                is_bot = is_bot_id(id)
                if not can_add_bot and is_bot:
                    raise PartError.AddBotPermissionError("Can't add bot")
                if is_bot:
                    bot_num += 1

            member_from_files: List[str] = []
            if member_file_id:
                member_from_files = self.add_members_from_file(
                    user_id, thread_id, user_workspace_id, member_file_id
                )

            member_from_threads: List[str] = []
            for source_thread_id in source_thread_ids:
                member_from_threads.extend(
                    self.add_members_from_thread(
                        user_id, thread_id, source_thread_id
                    )
                )

            member_from_titles: List[str] = []
            member_from_department: List[str] = []
            title_infos: List[str] = []
            department_infos: List[str] = []
            for orgc in orgcs:
                if orgc.title_ids:
                    members, infos = self.add_members_from_orgc(
                        user_id,
                        thread_id,
                        {orgc.workspace_id: orgc.title_ids},
                        "title",
                    )
                    member_from_titles.extend(members)
                    title_infos.extend(infos)

                if orgc.department_ids:
                    members, infos = self.add_members_from_orgc(
                        user_id,
                        thread_id,
                        {orgc.workspace_id: orgc.department_ids},
                        "department",
                    )
                    member_from_department.extend(members)
                    department_infos.extend(infos)

            total_members = len(
                set(
                    member_from_titles
                    + member_from_files
                    + member_from_threads
                    + member_from_department
                    + members_to_add
                )
            )

            if total_members > constant.MAX_CREATE_THREAD_FOR_USER:
                if not self.gapo_client.workspace.is_admin(
                    workspace_id, user_id
                ):
                    raise OnlyAdminCanCreate()

            if member_from_department:
                self.publish_task(
                    {
                        "user_id": user_id,
                        "thread_id": thread_id,
                        "invite_ids": list(set(member_from_department)),
                        "receiver_infos": department_infos,
                        "type": "invite_org_department",
                    },
                )

            if members_to_add:
                self.publish_task(
                    {
                        "user_id": user_id,
                        "thread_id": thread_id,
                        "invite_ids": members_to_add,
                        "type": "invite_mem",
                        "skip_action_notes": skip_action_notes,
                    },
                )

            if member_from_titles:
                self.publish_task(
                    {
                        "user_id": user_id,
                        "thread_id": thread_id,
                        "invite_ids": list(set(member_from_titles)),
                        "receiver_infos": title_infos,
                        "type": "invite_org_title",
                    },
                )

            if member_from_threads:
                self.publish_task(
                    {
                        "user_id": user_id,
                        "thread_id": thread_id,
                        "invite_ids": list(set(member_from_threads)),
                        "type": action_type.INVITE_BY_THREAD_CHAT,
                    },
                )

            if member_from_files:
                self.publish_task(
                    {
                        "user_id": user_id,
                        "thread_id": thread_id,
                        "invite_ids": member_from_files,
                        "type": action_type.INVITE_BY_FILE,
                    },
                )

    def add_mention_to_subthread(
        self,
        sender_id: str,
        thread_id: int,
        thread_parent_id: int,
        mentions: List[str],
    ):
        # no need check valid member, only check member is already in thread or
        # logic check member is in threads or not
        #   if hash member in thread
        #       check member is in subthread
        #           if hasn't member in subthread -> add
        with self.session_m.get_session() as session:
            if "all" in mentions:
                # add all members to
                # NOTE !!!  nếu thread này quá to sẽ bị chậm
                new_member_ids: List[str] = [
                    u.user_id
                    for u in self.pt_repo.get_members_in_list_threads(
                        session, [thread_parent_id]
                    )
                ]
            else:
                review_member_ids = list(set([sender_id] + mentions))
                new_member_ids = [
                    u.user_id
                    for u in self.pt_repo.get_members_by_ids(
                        session, thread_parent_id, review_member_ids
                    )
                ]

            # only members of threads
            # FIXED BUG khi out ra khoi thread roi vao lai - check commit
            exist_members = self.pt_repo.get_exist_records(
                session, thread_id, new_member_ids
            )
            existing_member_ids = set(
                [
                    member_info.user_id
                    for member_info in exist_members
                    if member_info.is_removed == 0  # is False but 0 not work
                ]
            )
            rejoin_member_ids = set(
                [
                    member_info.user_id
                    for member_info in exist_members
                    if member_info.is_removed == 1  # is False but 0 not work
                ]
            )
            # TODO duplicate kha nhieu
            create_member_ids = (
                set(new_member_ids)
                - set(existing_member_ids)
                - set(rejoin_member_ids)
            )
            if not create_member_ids and not rejoin_member_ids:
                return

            if rejoin_member_ids:
                self.pt_repo.rejoins(
                    session, thread_id, list(rejoin_member_ids)
                )
            for member_id in create_member_ids:
                self.pt_repo.create_member(
                    session,
                    thread_id,
                    member_id,
                    read_count=0,
                    folder=constant.FOLDER_SUB_THREAD,
                )

            total_ids = list(rejoin_member_ids) + list(create_member_ids)
            total_count = len(total_ids)
            self._update_thread_member_count(session, thread_id, total_count)
            self.after_update(session, thread_id, total_ids)
            session.commit()

    def add_members_bg(
        self,
        user_id: str,
        thread_id: int,
        invite_ids: List[str],
        add_type: str,
        receiver_infos,
        skip_action_notes: bool = False,
        folder: str = constant.FOLDER_DEFAULT,
    ):
        with self.session_m.get_session() as session:
            if add_type == action_type.JOIN_BUT_IN_REVIEW:
                self._publish_action_note(
                    thread_id,
                    user_id,
                    invite_ids,
                    add_type,
                    receiver_infos,
                    skip_all_action_notes=skip_action_notes,
                )
                return

            members_to_add = self._get_all_members_to_invite(
                session,
                user_id,
                invite_ids,
            )

            join_ids = self._add_members(
                session, user_id, thread_id, members_to_add, folder=folder
            )
            if not join_ids:
                raise PartError.NoMoreMember()

            # only send action note if join_ids is not empty
            # NOTE: web will throw errors if join_ids is not empty
            if join_ids:
                self._update_thread_member_count(
                    session, thread_id, len(join_ids)
                )
                session.commit()
                self._publish_action_note(
                    thread_id,
                    user_id,
                    join_ids,
                    add_type,
                    receiver_infos,
                    skip_all_action_notes=skip_action_notes,
                )

    def _special_add_mem_checks_for_gapo_partner(
        self, create_soure: Optional[str], workspace_id: str
    ):
        if not self.config.miscs.droppi_workspace_id:
            return

        if (
            str(workspace_id) == self.config.miscs.droppi_workspace_id
            and create_soure == constant.CALL_SOURCE_API
        ):
            raise AddingMembersDisabled()

    def _is_removed(self, member):
        return member["is_removed"]

    def _is_banned(self, member):
        return member["banned_level"] != 0

    def _membership_add_member(
        self, p_thread: ParticipantThreadModel, user_id: str, invite_type: str
    ):
        collab_id = p_thread.thread.collab_id
        if not collab_id:
            return

        try:
            self.gapo_client.membership.add_member(
                caller_id=int(p_thread.participant.user_id),
                collab_id=collab_id,
                user_id=int(user_id),
                workspace_id=p_thread.thread.workspace_id or "",
                roles=[CollabGroupRole.Member],
                data_source=invite_type,
            )
        except Exception as e:
            # maybe due to slow syncing between chat-service and
            # membership, we may got permission error
            # we will reduce these errors by retry again after some
            # small duration
            if not isinstance(e, MembershipPermissionError):
                self.log.critical(
                    f"[Membership] Add Member error, "
                    f"workspace_id: {p_thread.thread.workspace_id}, "
                    f"collab_id: {collab_id}, "
                    f"caller_id: {p_thread.participant.user_id}, "
                    f"user_id: {user_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )
                return

            # retry
            try:
                time.sleep(0.2)
                self.gapo_client.membership.add_member(
                    caller_id=int(p_thread.participant.user_id),
                    collab_id=collab_id,
                    user_id=int(user_id),
                    workspace_id=p_thread.thread.workspace_id or "",
                    roles=[CollabGroupRole.Member],
                    data_source=invite_type,
                )
            except Exception as e:
                self.log.critical(
                    f"[Membership] Add Member error, "
                    f"workspace_id: {p_thread.thread.workspace_id}, "
                    f"collab_id: {collab_id}, "
                    f"caller_id: {p_thread.participant.user_id}, "
                    f"user_id: {user_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )

    def _add_mem_handler(
        self,
        session: Session,
        p_thread: ParticipantThreadModel,
        thread_id: int,
        add_ids,
        members: List[ParticipantModel],
        folder: str = constant.FOLDER_DEFAULT,
    ):
        still_remtain_ids = []
        rejoin_ids: List = []
        banned_ids: List = []
        join_ids: List = []
        ban_count = 0

        for m in members:
            if not m.is_removed:
                still_remtain_ids.append(m.user_id)
            else:
                rejoin_ids.append(m.user_id)

        insert_ids = (
            set(add_ids)
            - set(still_remtain_ids)
            - set(rejoin_ids)
            - set(banned_ids)
        )
        # TODO: HERE
        # for id in set(still_remtain_ids).union(set(rejoin_ids)):
        #     self._membership_add_member(member, id)

        for insert_id in insert_ids:
            if is_bot_id(insert_id):
                self.pt_repo.create_group_bot(
                    session,
                    thread_id,
                    insert_id,
                    read_count=p_thread.thread.message_count,
                )
            else:
                self.pt_repo.create_member(
                    session,
                    thread_id,
                    insert_id,
                    read_count=p_thread.thread.message_count,
                    folder=folder,
                )

            # self._membership_add_member(member, insert_id)

        join_ids.extend(insert_ids)

        for rejoin_id in rejoin_ids:
            self.pt_repo.rejoin(
                session, thread_id, rejoin_id, p_thread.thread.message_count
            )
        if ban_count:
            self.thread_repo.update_thread_banned_count(
                session, thread_id, ban_count * -1
            )

        join_ids.extend(rejoin_ids)
        return join_ids

    def _add_members(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        invite_ids: List[str],
        folder: str = constant.FOLDER_DEFAULT,
    ):
        """Adds list of member to a thread.

        It will return a tupple of two values:
        - List of members to be added
        - A boolean indicates if these members need to be approved by admin.
        Note that if this flag is true, the returned list will be empty, too.
        """
        p_thread = self.get_member_group(session, thread_id, user_id)

        if (
            p_thread.thread.member_count + len(invite_ids)
            >= constant.MAX_MEMBER
        ):
            raise PartError.MaximumMemberReachedError()

        settings = p_thread.thread.settings
        member_role = p_thread.participant.role
        # is_admin = member_role in (constant.ADMIN_ROLE, constant.OWNER_ROLE)
        has_permission, permission_name = acl.check_permission(
            settings, member_role, constant.PERMISSION_ADD_MEMBER
        )
        if not has_permission:
            raise PartError.AddMemberPermissionError(permission_name)

        # blockIDs = self.getBlockList(session, user_id, invite_ids)
        # new_ids = list(set(invite_ids) - blockIDs)
        new_ids = invite_ids
        if not new_ids:
            raise PartError.NoMoreMember()

        existing_members = self.pt_repo.get_exist_records(
            session, thread_id, new_ids
        )

        join_ids = self._add_mem_handler(
            session,
            p_thread,
            thread_id,
            new_ids,
            existing_members,
            folder=folder,
        )

        new_members: List[ParticipantModel] = []
        for m in existing_members:
            if m.user_id in join_ids:
                new_members.append(
                    ParticipantModel.new_participant(
                        user_id=m.user_id, thread_id=thread_id
                    )
                )
        for join_id in join_ids:
            new_members.append(
                ParticipantModel.new_participant(
                    user_id=join_id, thread_id=thread_id
                )
            )

        set_default_folder = folder not in (
            constant.FOLDER_SECRET,
            constant.FOLDER_SUB_THREAD,
        )
        self.thread_broker.set_thread_scores_for_users(
            thread_id, new_members, set_default_folder=set_default_folder
        )

        self.after_update(session, thread_id, join_ids)
        return join_ids

    def after_update(self, session: Session, thread_id: int, p_ids: List[str]):
        members = self.pt_repo.get_exist_records(session, thread_id, p_ids)
        for m in members:
            self.pt_repo.update_cache(m.dict())

    def _get_all_members_to_invite(
        self,
        session: Session,
        user_id: str,
        invite_ids: List,
    ):
        unique_members: Set[str] = set([])
        for invite_id in invite_ids:
            unique_members.add(str(invite_id))

        # make sure we don't add user_id again
        unique_members.discard(str(user_id))
        return list(unique_members)

    def add_members_to_collab_bg(
        self,
        user_id,
        thread_id,
        member_ids,
        payload,
    ):
        invite_type = payload.get("type", "invite_mem")
        with self.session_m.get_session() as session:
            p_thread = self.get_member_group(session, thread_id, user_id)
            time_sleep = self.rb_broker.get_sleep(jobs=len(member_ids))
            for member_id in member_ids:
                self._membership_add_member(p_thread, member_id, invite_type)
                time.sleep(time_sleep)  # 5000 * 0.1 = 500

    def publish_task(self, params):
        invite_ids = params["invite_ids"]
        len_iids = len(invite_ids)
        num_parts = len_iids // constant.MAX_INVITE_PER_TASK + 1
        for i in range(num_parts):
            start = i * constant.MAX_INVITE_PER_TASK
            end = start + constant.MAX_INVITE_PER_TASK

            p_invite_ids = invite_ids[start:end]
            if not p_invite_ids:
                continue

            is_heavy = self.redis_counter.set_backpressure(
                params["thread_id"], len(p_invite_ids)
            )

            self.rb_broker.publish_background_task(
                constant.TASK_ADD_MEMBER_COLLAB,
                {
                    "user_id": params["user_id"],
                    "thread_id": params["thread_id"],
                    "invite_ids": p_invite_ids,
                    "type": params["type"],
                    "skip_action_notes": params.get("skip_action_notes", False),
                    "receiver_infos": params.get("receiver_infos", None),
                },
                is_heavy=is_heavy,
            )

    def publish_add_task(
        self, user_id, thread_id, invite_id, data_source: str, skip=True
    ):
        invite_ids = [invite_id]
        is_heavy = self.redis_counter.set_backpressure(
            thread_id, len(invite_ids)
        )
        self.rb_broker.publish_background_task(
            constant.TASK_ADD_MEMBER,
            {
                "user_id": user_id,
                "thread_id": thread_id,
                "invite_ids": invite_ids,
                "type": data_source,
                "data_source": data_source,
                "skip_action_notes": skip,
            },
            is_heavy=is_heavy,
        )
