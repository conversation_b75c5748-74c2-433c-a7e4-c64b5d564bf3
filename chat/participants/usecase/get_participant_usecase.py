from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from chat.exception import PermissionDenied, UserNotInThread
from chat.participants.model import ParticipantModel
from chat.utils import common

from .base import ParticipantMySQLUsecase


class GetParticipantUseCase(ParticipantMySQLUsecase):
    def _require_member(
        self, participant: Optional[ParticipantModel]
    ) -> ParticipantModel:
        if not participant:
            raise UserNotInThread()
        return participant

    def get_all_members(
        self, thread_ids: List[int], roles: List[str] = []
    ) -> List[ParticipantModel]:
        with self.session_m.get_session() as session:
            members = self.pt_repo.get_members_in_list_threads(
                session, thread_ids, roles
            )
            return members

    def _get_active_members(self, thread_id: int, member_count):
        # TODO: just make it run first
        # if member_count > self._max_member_status:
        #     return []
        return self.read_repo.get_most_recent_thread_viewers(
            thread_id, self._max_member_status
        )

    def get_partner(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantModel:
        part = self.pt_repo.get_partner(session, thread_id, user_id)
        return self._require_member(part)

    def get_participant_role_member(
        self, thread_id: int, user_ids: List[str]
    ) -> List[ParticipantModel]:
        with self.session_m.get_session() as session:
            return self.pt_repo.get_user_roles_in_thread(
                session, thread_id, user_ids
            )

    def get_banned_member_group(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantModel:
        part = self.pt_repo.get_banned_member(session, thread_id, user_id)
        return self._require_member(part)

    def get_all_threads_in_folders(
        self, user_id: str, folders: List[Any]
    ) -> List[int]:
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_all_thread_ids_in_folders(
                session, user_id, folders
            )
            return [part.thread_id for part in parts]

    def get_in_folder(
        self, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_in_folder(session, user_id, thread_ids)
            return parts

    def _filter_existing_threads_in_folder(
        self,
        user_id: str,
        thread_ids: List[int],
        thread_to_partner: Dict[int, str],
    ):
        existing_threads = []
        new_thread_ids = []
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_folders_by_thread_ids(
                session, user_id, thread_ids
            )

        for part in parts:
            if common.is_folder_created_by_user(part.folder):
                existing_threads.append(
                    {
                        "thread_id": part.thread_id,
                        "folder_alias": part.folder,
                        "user_id": thread_to_partner.get(part.thread_id, 0),
                    }
                )
            else:
                new_thread_ids.append(part.thread_id)

        return new_thread_ids, existing_threads

    def get_threads_by_partner_ids(self, user_id: str, partner_ids: List[str]):
        partner_info: Dict[int, str] = dict()
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_threads_by_pair_ids(
                session, user_id, partner_ids
            )

            for part in parts:
                if not part.partner_id:
                    continue
                thread_id = part.thread_id
                partner_info[thread_id] = part.partner_id

        return partner_info

    def verify_admin_permission_to_threads(
        self, user_id: str, thread_ids: List[int]
    ):
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_user_role_in_threads(
                session, user_id, thread_ids
            )

        if not parts:
            raise UserNotInThread()

        for part in parts:
            if part.is_member():
                raise PermissionDenied()

    def after_update(self, session: Session, thread_id: int, p_ids: List[str]):
        members = self.pt_repo.get_exist_records(session, thread_id, p_ids)
        for m in members:
            self.pt_repo.update_cache(m.dict())
