from chat.app import <PERSON>App
from chat.threads.usecase import ThreadUsecases

from .add_participant_usecase import AddParticipantUseCase
from .ban_participant_usecase import BanParticipantUseCase
from .get_participant_usecase import GetParticipantUseCase
from .participant_action_usecase import ParticipantActionUseCase


class ParticipantUseCases(object):
    def __init__(self, app: MyApp, thread_usc: ThreadUsecases):
        self.app = app
        self.thread_usc = thread_usc

        self._init_add_participant()
        self._init_ban_participant()
        self._init_get_participant()
        self._init_action_participant()

    def _init_add_participant(self):
        self.add_participant = AddParticipantUseCase(
            self.app,
            self.thread_usc,
        )

    def _init_ban_participant(self):
        self.ban_participant = BanParticipantUseCase(
            self.app,
            self.thread_usc,
        )

    def _init_get_participant(self):
        self.get_participant = GetParticipantUseCase(
            self.app,
            self.thread_usc,
        )

    def _init_action_participant(self):
        self.participant_action = ParticipantActionUseCase(
            self.app,
            self.thread_usc,
        )


__all__ = [
    "ParticipantUseCases",
    "MyApp",
    "ThreadUsecases",
    "AddParticipantUseCase",
    "BanParticipantUseCase",
    "GetParticipantUseCase",
    "ParticipantActionUseCase",
]
