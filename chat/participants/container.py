from dependency_injector import containers, providers
from dependency_injector.providers import Singleton

from chat.app import MyApp
from chat.participants.deliver import (
    ParticipantAcceptFriendResource,
    ParticipantBotResource,
    ParticipantChangeRoleResource,
    ParticipantInternalResource,
    ParticipantMarkFriendResource,
    ParticipantOrgcResource,
    ParticipantReadResource,
    ParticipantMarkUnReadResource,
    ParticipantResource,
    ParticipantTypingResource,
    AddParticipantResource,
)
from chat.participants.usecase import ParticipantUseCases
from chat.threads.usecase import ThreadUsecases


class participantResourcesContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(ParticipantUseCases)

    participants: providers.Singleton[
        ParticipantResource
    ] = providers.Singleton(ParticipantResource, app, usecases)
    bot: providers.Singleton[ParticipantBotResource] = providers.Singleton(
        ParticipantBotResource, app, usecases
    )
    orgc: providers.Singleton[ParticipantOrgcResource] = providers.Singleton(
        ParticipantOrgcResource, app, usecases
    )
    mark_friend: providers.Singleton[
        ParticipantMarkFriendResource
    ] = providers.Singleton(ParticipantMarkFriendResource, app, usecases)
    accept_friend: providers.Singleton[
        ParticipantAcceptFriendResource
    ] = providers.Singleton(ParticipantAcceptFriendResource, app, usecases)
    change_role: providers.Singleton[
        ParticipantChangeRoleResource
    ] = providers.Singleton(ParticipantChangeRoleResource, app, usecases)
    internal_participants: providers.Singleton[
        ParticipantInternalResource
    ] = providers.Singleton(ParticipantInternalResource, app, usecases)
    me_read: Singleton[ParticipantReadResource] = providers.Singleton(
        ParticipantReadResource, app, usecases
    )

    me_mark_unread: Singleton[
        ParticipantMarkUnReadResource
    ] = providers.Singleton(ParticipantMarkUnReadResource, app, usecases)
    me_typing: Singleton[ParticipantTypingResource] = providers.Singleton(
        ParticipantTypingResource, app, usecases
    )
    add_member: Singleton[AddParticipantResource] = providers.Singleton(
        AddParticipantResource, app, usecases
    )


class ParticipantContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    thread_usc = providers.Dependency(ThreadUsecases)
    usecases = providers.Singleton(
        ParticipantUseCases, app=app, thread_usc=thread_usc
    )
    resources = providers.Singleton(
        participantResourcesContainer, app=app, usecases=usecases
    )
