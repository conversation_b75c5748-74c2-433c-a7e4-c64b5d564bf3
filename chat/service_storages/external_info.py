from typing import Any, Dict, List

import chat.utils.thumb as thumb
from chat.bots.model import BotInfo
from chat.cache import RedisCache
from chat.connections.gapo_client import GapoClient
from chat.constant import SYSTEM_OBJECT
from chat.users.model import PublicProfile, UserInfo

SYSTEM_ID: str = SYSTEM_OBJECT["id"]  # type: ignore


class InfoStorage(object):
    def __init__(self, gapo_client: GapoClient, cache_service: RedisCache):
        self.gapo_client = gapo_client
        self.cache_service = cache_service

    def _update_thumb(self, objects):
        # NOTE: gapo refresh chat missed update thumb
        for o in objects.values():
            o["avatar"] = thumb.avatar(o["avatar"])

    def _update_data(self, data):
        existing_info = data.get("info", None)
        if existing_info:
            existing_work = existing_info.get("work", None)
            if existing_work:
                new_work = dict()
                for w in existing_work:
                    wid = w["workspace_id"]
                    new_work[wid] = {
                        "company": w.get("company", ""),
                        "department": w.get("department", ""),
                        "departments": w.get("departments", []),
                        "title": w.get("title", ""),
                    }
                data["work"] = new_work
                del data["info"]

        return data

    def get_system_user(self) -> PublicProfile:
        return dict(SYSTEM_OBJECT)  # type: ignore

    def get_users(self, user_ids: List[str]) -> Dict[str, UserInfo]:
        # make sure the input is a string list
        user_ids: List[str] = [str(user_id) for user_id in user_ids]

        objects: Dict[str, UserInfo] = dict()
        if SYSTEM_ID in user_ids:
            objects[SYSTEM_ID] = SYSTEM_OBJECT  # type: ignore
            user_ids.remove(SYSTEM_ID)
        if not user_ids:
            return objects

        missed_ids, users = self.cache_service.get_users(user_ids)
        self._update_thumb(users)
        objects.update(users)
        if missed_ids:
            missed_users = self.gapo_client.user.get_by_ids(missed_ids)
            objects.update(missed_users)
            to_update = {}
            for k, v in missed_users.items():
                if v["should_cache"]:
                    to_update[k] = self._update_data(v)
            self.cache_service.update_users(to_update)
        return objects

    def get_bots(self, bot_ids: List[Any]) -> Dict[str, BotInfo]:
        objects: Dict[str, BotInfo] = dict()
        if not bot_ids:
            return objects

        missed_ids, bots = self.cache_service.get_bots(bot_ids)
        self._update_thumb(bots)
        if missed_ids:
            missed_bots = self.gapo_client.bot.get_by_ids(missed_ids)
            bots.update(missed_bots)
            self.cache_service.update_bots(missed_bots)
        return bots
