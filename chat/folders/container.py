from dependency_injector import containers, providers

from chat.app import MyApp
from chat.folders.deliver import (
    FolderItemResource,
    FolderMoveResource,
    FolderResource,
    FolderSortedResource,
)
from chat.folders.usecase import FolderUsecase
from chat.participants.usecase import ParticipantUseCases
from chat.threads.usecase import ThreadUsecases


class FolderResourcesContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(FolderUsecase)

    folder = providers.Singleton(FolderResource, app, usecases)
    item = providers.Singleton(FolderItemResource, app, usecases)
    sort = providers.Singleton(FolderSortedResource, app, usecases)
    move = providers.Singleton(FolderMoveResource, app, usecases)


class FolderContainer(containers.DeclarativeContainer):
    app = providers.Dependency(MyApp)
    thread_usc = providers.Dependency(ThreadUsecases)
    pt_usc = providers.Dependency(ParticipantUseCases)
    usecases = providers.Singleton(
        FolderUsecase, app=app, thread_usc=thread_usc, pt_usc=pt_usc
    )

    resources = providers.Singleton(
        FolderResourcesContainer, app=app, usecases=usecases
    )
