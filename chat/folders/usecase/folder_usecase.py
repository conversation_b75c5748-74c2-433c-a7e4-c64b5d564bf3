import time
import uuid
from datetime import datetime
from typing import Any, List, Optional

from chat.constant import (
    FOLDER_DEFAULT,
    FOLDER_NOT_FRIEND,
    FOLDER_SECRET,
    FOLDER_SUB_THREAD,
    TASK_MOVE_FOLDER,
    TASK_SORT_FOLDER,
)
from chat.exception import PermissionDenied

from .base import BaseUsecase


class FolderUsecase(BaseUsecase):
    def move_to_folder(
        self,
        new_folder: str,
        user_id: str,
        thread_ids: List[int],
        partner_ids: List[str],
    ):
        """Moves list of threads to new folder.

        Params:
            new_folder: target folder
            user_id: actor
            thread_ids: List of thread that user is in
            partner_ids: List of partners (direct threads)
        """
        with self.session_m.get_session() as session:
            if new_folder == FOLDER_SUB_THREAD:
                is_valid_folder = False
            elif new_folder in [
                FOLDER_NOT_FRIEND,
                FOLDER_DEFAULT,
                FOLDER_SECRET,
            ]:
                is_valid_folder = True
            else:
                is_valid_folder = self._folder_exists(
                    session, user_id, new_folder
                )

            if not is_valid_folder:
                raise PermissionDenied()

            new_thread_ids = []
            usc = self.pt_usecase.get_participant

            if len(partner_ids) > 0:
                direct_threads = usc.get_threads_by_partner_ids(
                    user_id, partner_ids
                )
                new_thread_ids.extend(direct_threads.keys())

            if len(thread_ids) > 0:
                new_thread_ids.extend(thread_ids)

            existing_parts = self.thread_usc.get_thread.get_all_by_ids(
                user_id, new_thread_ids
            )
            for part in existing_parts:
                if part.folder == FOLDER_SUB_THREAD:
                    raise PermissionDenied()

            return self._move_threads_to_folder(
                user_id=user_id,
                new_folder=new_folder,
                thread_ids=new_thread_ids,
            )

    def create(
        self,
        user_id: str,
        name: str,
        avatar: str,
        thread_ids: List[int],
        partner_ids: List[Any],
        position: Optional[int] = None,
    ):
        """Creates new folder with threads."""

        if position is None:
            position = int(time.time())
        usc = self.pt_usecase.get_participant
        direct_threads = dict()
        with self.session_m.get_session() as session:
            # existed_name = self._existed_name(session, user_id, name)
            # if existed_name:
            #     raise exceptions.ExistedNameFolder()

            alias = uuid.uuid4().hex
            self.pf_repos.create(
                session, user_id, name, alias, position, avatar
            )
            session.commit()

            folder = self._get_item(session, user_id, alias)
            folder["existed_threads"] = []

            if len(partner_ids) > 0:
                direct_threads = usc.get_threads_by_partner_ids(
                    user_id, partner_ids
                )
                thread_ids.extend(direct_threads.keys())

            if len(thread_ids) == 0:
                return folder

            (
                new_thread_ids,
                existing_threads,
            ) = usc._filter_existing_threads_in_folder(
                user_id, thread_ids, direct_threads
            )
            folder["existed_threads"] = existing_threads

            self._move_threads_to_folder(
                user_id=user_id, new_folder=alias, thread_ids=new_thread_ids
            )

            session.commit()

            return folder

    def update(self, user_id: str, alias: str, name: str, avatar: str):
        """Updates folder."""

        with self.session_m.get_session() as session:
            item_exists = self._folder_exists(session, user_id, alias)
            if item_exists is False:
                raise PermissionDenied()

            self.pf_repos.update(session, user_id, name, alias, avatar)
            session.commit()
            return self._get_item(session, user_id, alias)

    def delete(self, user_id: str, alias: str):
        """Deletes a folder."""

        with self.session_m.get_session() as session:
            exists = self._folder_exists(session, user_id, alias)
            if exists is False:
                raise PermissionDenied()

            deleted_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.pf_repos.delete(session, user_id, alias, deleted_at)
            session.commit()

            self._move_threads_to_folder(
                user_id=user_id, remove_folder=alias, new_folder=FOLDER_DEFAULT
            )

    def move_to_folder_bg(
        self,
        user_id: str,
        new_folder: str,
        thread_ids: Optional[List[int]] = None,
        remove_folder=None,
    ):
        with self.session_m.get_session() as session:  # noqa
            usc = self.pt_usecase.get_participant
            if remove_folder:
                thread_ids = usc.get_all_threads_in_folders(
                    user_id, [remove_folder]
                )
            if thread_ids:
                parts = usc.get_in_folder(user_id, thread_ids)
                for part in parts:
                    part.folder = new_folder
                    usc.pt_repo.update_cache(part.dict())

                for t_id in thread_ids:
                    self.thread_usc.setup_thread.move_to_folder(
                        t_id, user_id, new_folder
                    )

    def sort_folders(self, user_id: str, ordered_aliases: List[str]):
        """Sorts list of folders."""

        sync_folder = {
            "user_id": user_id,
            "aliases": ordered_aliases,
        }
        self.rb_broker.publish_background_task(
            event_type=TASK_SORT_FOLDER, body=sync_folder
        )

    def sort_folders_bg(self, user_id: str, ordered_aliases: List[str]):

        with self.session_m.get_session() as session:
            for idx, alias in enumerate(ordered_aliases):
                position = int(time.time()) + idx
                self.pf_repos.set_position(session, user_id, alias, position)

            session.commit()

    def _move_threads_to_folder(
        self,
        user_id: str,
        new_folder,
        thread_ids: Optional[List[int]] = None,
        remove_folder=None,
    ):
        sync_folder = {"user_id": user_id, "new_folder": new_folder}

        if remove_folder:
            sync_folder["remove_folder"] = remove_folder
        else:
            sync_folder["thread_ids"] = thread_ids

        return self.rb_broker.publish_background_task(
            event_type=TASK_MOVE_FOLDER, body=sync_folder
        )
