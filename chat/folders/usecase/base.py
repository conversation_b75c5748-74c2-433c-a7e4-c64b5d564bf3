from sqlalchemy.orm import Session

from chat.app import MyApp
from chat.participants.usecase import ParticipantUseCases
from chat.threads.usecase import ThreadUsecases


class BaseUsecase(object):
    def __init__(
        self,
        app: MyApp,
        thread_usc: ThreadUsecases,
        pt_usc: ParticipantUseCases,
    ):
        self.config = app.conf
        self.log = app.log
        self.pf_repos = app.repos.folder
        self.pt_usecase = pt_usc

        self.session_m = app.conns.mysql
        self.rb_broker = app.brokers.thread_rb
        self.thread_usc = thread_usc

    def _check_exists(self, session: Session, user_id: str, name: str):
        item = self.pf_repos.get_by_name(session, user_id, name)
        if item:
            return True
        return False

    def _folder_exists(self, session: Session, user_id: str, alias: str):
        item = self.pf_repos.get_by_alias(session, user_id, alias)
        if item:
            return True
        return False

    def _get_item(self, session: Session, user_id: str, alias: str):
        folder = self.pf_repos.get_by_alias(session, user_id, alias)
        return folder
