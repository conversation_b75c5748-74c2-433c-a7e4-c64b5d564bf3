import falcon

from chat.app.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.exception import InvalidParameters
from chat.folders.model.api import (
    SchemaCreateFolder,
    SchemaEditFolder,
    SchemaMoveFolder,
    SchemaSortFolder,
)
from chat.folders.usecase import FolderUsecase
from chat.hooks import making_folder_hook
from chat.model import make_input


class FolderResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: FolderUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    @falcon.before(making_folder_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        raw["user_id"] = user_id
        body, errors = make_input(SchemaCreateFolder, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        folder = self.usecase.create(
            user_id, body.name, body.avatar, body.thread_ids, body.user_ids
        )
        resp.media = {"data": folder}
        resp.status = falcon.HTTP_200
        return


class FolderItemResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: FolderUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    @falcon.before(making_folder_hook)
    def on_patch(self, req: falcon.Request, resp: falcon.Response, alias):
        user_id = self.user_id
        raw = req.context.body
        raw["user_id"] = user_id
        body, errors = make_input(SchemaEditFolder, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        folder = self.usecase.update(user_id, alias, body.name, body.avatar)
        resp.media = {"data": folder}
        resp.status = falcon.HTTP_200

    def on_delete(self, req: falcon.Request, resp: falcon.Response, alias):
        user_id = self.user_id
        self.usecase.delete(user_id, alias)


class FolderSortedResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: FolderUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    @falcon.before(making_folder_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaSortFolder, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        self.usecase.sort_folders(user_id, body.aliases)

        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200


class FolderMoveResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: FolderUsecase):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases

    @falcon.before(making_folder_hook)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaMoveFolder, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        self.usecase.move_to_folder(
            user_id=user_id,
            new_folder=body.new_folder,
            thread_ids=body.thread_ids,
            partner_ids=body.user_ids,
        )
        resp.media = {"data": {"message": "ok"}}
        resp.status = falcon.HTTP_200
        return
