from abc import ABC, abstractmethod
from typing import List, Optional

from sqlalchemy.orm import Session

from chat.folders.model import ChatFolder


class FolderRepository(ABC):
    @abstractmethod
    def create(
        self,
        session: Session,
        user_id: str,
        name: str,
        alias: str,
        position: int,
        avatar: Optional[str] = None,
    ):
        """Creates new folder."""

    @abstractmethod
    def update(
        self,
        session: Session,
        user_id: str,
        name: str,
        alias: str,
        avatar: Optional[str] = None,
    ):
        """Updates folder"""

    @abstractmethod
    def delete(self, session: Session, user_id: str, alias: str, deleted_at):
        """Delete folder by alias"""

    @abstractmethod
    def get_by_alias(
        self, session: Session, user_id: str, alias: str
    ) -> ChatFolder:
        """Gets folder by alias"""

    @abstractmethod
    def get_by_name(
        self, session: Session, user_id: str, name: str
    ) -> ChatFolder:
        """Gets folder by name."""

    @abstractmethod
    def get_folders_by_user(
        self, session: Session, user_id: str, limit=100
    ) -> List[ChatFolder]:
        """Get list of folders created by a given user."""

    @abstractmethod
    def set_position(
        self, session: Session, user_id: str, alias: str, position: int
    ):
        """Set position for a folder."""

    @abstractmethod
    def check_exists(self, session: Session, user_id: str, alias: str) -> bool:
        """Checks if a folder exists."""
