from logging import Logger
from typing import Op<PERSON>

from sqlalchemy.orm import Session

from chat.repositories import BaseRepository

from .interface import FolderRepository


class FolderRepositoryMySQL(BaseRepository, FolderRepository):

    t_name = "participant_folders"

    def __init__(self, log: Logger):
        self.log = log

    def create(
        self,
        session: Session,
        user_id: str,
        name: str,
        alias: str,
        position: int,
        avatar: Optional[str] = None,
    ):
        params = {
            "user_id": user_id,
            "name": name,
            "alias": alias,
            "avatar": avatar,
            "position": position,
        }
        query = (
            f"""INSERT INTO {self.t_name}(
            user_id,
            name,
            alias,
            avatar,
            position
            ) """
            """VALUES(:user_id, :name, :alias, :avatar, :position) """
        )
        self._q_insert(session, query, params)

    def update(
        self,
        session: Session,
        user_id: str,
        name: str,
        alias: str,
        avatar: Optional[str] = None,
    ):
        params = {
            "user_id": user_id,
            "name": name,
            "alias": alias,
            "avatar": avatar,
        }
        query = (
            f"""UPDATE {self.t_name} SET name=:name, avatar=:avatar """
            """WHERE user_id=:user_id AND alias=:alias """
        )
        self._q_update(session, query, params)

    def delete(self, session: Session, user_id: str, alias: str, deleted_at):
        params = {"user_id": user_id, "alias": alias, "deleted_at": deleted_at}
        query = (
            f"""UPDATE {self.t_name} SET deleted_at=:deleted_at """
            """WHERE user_id=:user_id AND alias=:alias """
        )
        self._q_delete(session, query, params)

    def get_by_alias(self, session: Session, user_id: str, alias):
        params = {"user_id": user_id, "alias": alias}
        query = (
            f"""SELECT * FROM {self.t_name} """
            """WHERE user_id=:user_id AND alias=:alias """
        )
        return self._q_item(session, query, params)

    def get_by_name(self, session: Session, user_id: str, name: str):
        params = {"user_id": user_id, "name": name}
        query = f"""SELECT * FROM {self.t_name}
                        WHERE user_id=:user_id AND name=:name"""
        return self._q_item(session, query, params)

    def get_folders_by_user(self, session: Session, user_id: str, limit=100):
        params = {"user_id": user_id, "limit": limit}
        query = f"""SELECT * FROM {self.t_name}
                        WHERE user_id=:user_id AND limit=:limit"""
        return self._q_items(session, query, params)

    def set_position(
        self, session: Session, user_id: str, alias: str, position: int
    ):
        params = {"alias": alias, "user_id": user_id, "position": position}
        query = f"""UPDATE {self.t_name} SET position=:position
                    WHERE alias=:alias AND user_id=:user_id"""
        return self._q_update(session, query, params)

    def check_exists(self, session: Session, user_id: str, alias: str):
        params = {"user_id": user_id, "alias": alias}
        query = (
            f"SELECT * FROM {self.t_name} "
            "WHERE user_id=:user_id AND alias=:alias "
            "AND deleted_at IS NULL"
        )
        return self._q_item(session, query, params) is not None
