from typing import List

from pydantic import BaseModel, conint, constr, root_validator

from chat.utils.validator_helper import check_thread_id, is_bot_id, is_user_id

PAGE_SIZE = 100

__all__ = [
    "SchemaCreateFolder",
    "SchemaEditFolder",
    "SchemaMoveFolder",
    "SchemaFolder",
    "SchemaSortFolder",
]


class SchemaCreateFolder(BaseModel):
    user_id: int
    name: constr(min_length=1, max_length=20)  # type: ignore
    avatar: str
    page_size: conint(ge=1, le=PAGE_SIZE) = PAGE_SIZE  # type: ignore
    thread_ids: List[int] = []
    user_ids: List[str] = []

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        len_t = len(values["thread_ids"])
        len_u = len(values["user_ids"])
        total_len = len_t + len_u

        if total_len < 1:
            raise ValueError(f"Invalid length ids {total_len}")

        for t_id in values["thread_ids"]:
            check_thread_id(t_id)

        for u_id in values["user_ids"]:
            if not is_bot_id(u_id):
                is_user_id(u_id)

        return values


class SchemaEditFolder(BaseModel):
    name: constr(min_length=1, max_length=20)  # type: ignore
    avatar: str


class SchemaFolder(SchemaCreateFolder):
    alias: str


class SchemaSortFolder(BaseModel):
    aliases: List[str] = []


class SchemaMoveFolder(BaseModel):
    thread_ids: List[int]
    user_ids: List[str]
    new_folder: str

    @root_validator(skip_on_failure=True)
    def add_default_field(cls, values):
        len_t = len(values["thread_ids"])
        len_u = len(values["user_ids"])
        total_len = len_t + len_u

        if total_len < 1:
            raise ValueError(f"Invalid length ids {total_len}")

        for t_id in values["thread_ids"]:
            check_thread_id(t_id)

        for u_id in values["user_ids"]:
            is_user_id(u_id)

        return values
