from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

from chat.constant import GROUP_LEVEL_PRIVATE
from chat.threads.model import ThreadModel


class ThreadRepository(ABC):
    @abstractmethod
    def create_direct(
        self,
        session,
        pair_ids,
        creator,
        banner=None,
        message_count=0,
        init_last_message=None,
        associate_link: str = "",
    ) -> int:
        pass

    @abstractmethod
    def create_page(self, session: Session, pair_ids, creator) -> int:
        pass

    @abstractmethod
    def create_group(
        self,
        session: Session,
        name: str,
        member_count: int,
        avatar: Optional[str] = None,
        link_code=None,
        associate_link="",
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
        workspace_id="",
        collab_id: Optional[str] = None,
        parent_id: Optional[int] = None,
        parent_thread_type: Optional[str] = None,
        root_message_id: Optional[int] = None,
    ) -> int:
        pass

    @abstractmethod
    def set_collab_id(self, session: Session, thread_id: int, collab_id: str):
        """Set collab id for a thread."""

    @abstractmethod
    def clear_collab_id(self, session: Session, thread_id: int):
        """Unlinks current thread with its collab."""

    @abstractmethod
    def decrease_pin_count(
        self, session: Session, thread_id: int, message_id: int
    ):
        pass

    @abstractmethod
    def increase_pin_count(
        self, session: Session, thread_id: int, message_id: int
    ):
        pass

    @abstractmethod
    def unblock_thread(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def block_thread(self, session: Session, user_id: str, thread_id: int):
        pass

    @abstractmethod
    def block_thread_by_partner(
        self, session: Session, user_id: str, pair_ids
    ):
        pass

    @abstractmethod
    def unblock_thread_by_partner(self, session: Session, pair_ids):
        pass

    @abstractmethod
    def update_msg_count(
        self, session: Session, thread_id: int, msg_count: int
    ):
        pass

    @abstractmethod
    def update_last_message(
        self, session: Session, thread_id: int, message_id: int, message
    ):
        pass

    @abstractmethod
    def update_sub_threads_info(
        self, session: Session, parent_thread_id: int, edited_fields: Dict
    ):
        pass

    @abstractmethod
    def update_last_message_when_edit(
        self, session: Session, thread_id: int, message_id: int, body
    ):
        pass

    @abstractmethod
    def update_thread_media_count(
        self, session: Session, thread_id: int, media_infos, decrease=True
    ):
        pass

    @abstractmethod
    def clear_thread(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def remove_banner(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def pin_message(self, session: Session, thread_id: int, message_id=0):
        pass

    @abstractmethod
    def update_thread_banned_count(
        self, session: Session, thread_id: int, delta: int
    ):
        pass

    @abstractmethod
    def update_thread_member_count(
        self, session: Session, thread_id: int, delta: int
    ):
        pass

    @abstractmethod
    def update_thread_group_level(
        self, session: Session, thread_id: int, group_level: int
    ):
        pass

    @abstractmethod
    def update_threads_member_count(
        self, session: Session, thread_ids: List[int], delta: int
    ):
        pass

    @abstractmethod
    def edit_info(self, session: Session, thread_id: int, edit_infos) -> Any:
        pass

    @abstractmethod
    def toggle_privacy(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def toggle_mute_all(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def toggle_mute_all_sub(self, session: Session, thread_id: int):
        pass

    @abstractmethod
    def toggle_only_admin_can_add_member(
        self, session: Session, thread_id: int
    ):
        """Toggles only admin can add member setting."""

    @abstractmethod
    def toggle_only_admin_can_update_info(
        self, session: Session, thread_id: int
    ):
        """Toggles only admin can update info."""

    @abstractmethod
    def update_thread_link(
        self, session: Session, thread_id: int, link, level
    ):
        pass

    @abstractmethod
    def update_thread_and_member_count(
        self, session: Session, thread_id: int, information, number
    ):
        pass

    @abstractmethod
    def update_thread_workspace_id(
        self, session: Session, thread_id: int, workspace_id
    ):
        pass

    @abstractmethod
    def clear_associate_link(
        self, session: Session, thread_id: int, workspace_id
    ):
        pass

    @abstractmethod
    def get_by_id(
        self, session: Session, thread_id: int
    ) -> Optional[ThreadModel]:
        pass

    @abstractmethod
    def get_by_collab_id(
        self, session: Session, collab_id: str
    ) -> Optional[ThreadModel]:
        pass

    @abstractmethod
    def get_by_collab_ids(
        self, session: Session, collab_ids: List[str]
    ) -> List[ThreadModel]:
        pass

    @abstractmethod
    def get_thread_by_link_code(
        self, session: Session, link_code: str
    ) -> Optional[ThreadModel]:
        """Get thread by link code (for associative group)."""

    @abstractmethod
    def get_threads_by_workspace(
        self, session: Session, workspace_id=0, last_id=0, page_size=20
    ) -> List[ThreadModel]:
        pass

    @abstractmethod
    def incr_thread_msg_counter(self, session: Session, thread_id: int) -> int:
        """Increases thread message counter."""

    @abstractmethod
    def add_thread_commenter(
        self, session: Session, user_id: str, thread_id: int
    ):
        """Adds an user_id to the list of commented users in a thread.

        This is used for sub-thread only to track who has commented to a
        thread.
        """

    @abstractmethod
    def update_thread_commenters(
        self, session: Session, thread_id: int, commenters: List[str]
    ):
        """Update list of commenters for thread."""

    @abstractmethod
    def update_cache(self, thread_d: Dict):
        """Update cache"""

    @abstractmethod
    def get_by_associate(
        self, session: Session, associate_link: str
    ) -> Optional[ThreadModel]:
        """Get by associate"""

    @abstractmethod
    def get_sub_thread_for_message(
        self, session: Session, thread_id: int, message_id: int
    ) -> Optional[ThreadModel]:
        pass

