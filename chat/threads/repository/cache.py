from logging import Logger
from typing import List, Optional

from redis import Redis

from chat import constant
from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat.dist_lock import Dist<PERSON>ockManager
from chat.participants.model import ThreadMemberWithFolder
from chat.utils.common import now
from chat.utils.json import json_loads

from chat.threads.model import ThreadModel


class ThreadCache(object):
    """
    Updates thread scores in folders and tracks active members.
    Each thread will have a score in a folder. Thread with higher score will be
    positioned at higher position.

    A pinned thread has much higher score, so it will stay on top:
        score = timestamp * 1000
    """

    # FIXME: will we have overflow problem ? timestamp + 1000 years
    def __init__(
        self, log: Logger, engine: RedisConnection,
    ):
        self.log = log
        self.client = engine.client
        self.chat_thread_key = "chat:cache:threads:{thread_id}"
        self.lock_key = "chat:lock:threads:{thread_id}"

    def update(self, thread: ThreadModel):
        key = self.chat_thread_key.format(thread_id=thread.id)
        self.client.set(key, thread.json())

    def get(self, thread_id) -> Optional[ThreadModel]:
        key = self.chat_thread_key.format(thread_id=thread_id)
        thread = self.client.get(key)
        if not thread:
            return None

        return ThreadModel(**json_loads(thread))
