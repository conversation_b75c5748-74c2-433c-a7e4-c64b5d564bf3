import time
from logging import Logger
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from chat import constant
from chat.threads.repository.interface import ThreadRepository
from chat.utils import common
from chat.utils.json import json_dumps, json_loads
from chat.threads.model import Thread<PERSON>odel
from .cache import ThreadCache

# short buffer to store thread_id
# to avoid dupplicate if we call this function at the same time
_thread_buffer = []


def generate_thread_id():
    thread_id = int(time.time() * 1000)

    # re-generate if it exists in _thread_buffer
    while True:
        if thread_id not in _thread_buffer:
            break
        thread_id = int(time.time() * 1000)

    _thread_buffer.append(thread_id)
    if len(_thread_buffer) > 10:
        _thread_buffer.pop(0)

    return thread_id

    # now_as_miliseconds = str(int(time.time()))
    # return int("{}{:03d}".format(now_as_miliseconds, random.randint(0, 999)))


G_NAME_FORMAT = "Nhóm {thread_id}"


def _make_group_name(group_id, name):
    return name or G_NAME_FORMAT.format(thread_id=group_id)


class ThreadInsertController(object):
    t_name = "threads"

    def __init__(self):
        super().__init__()
        self._common_field = [
            "id",
            "type",
            "member_count",
        ]

        self._direct_field = self._common_field + [
            "pair_ids",
            "creator",
            "banner",
            "message_count",
            "centralize_last_message",
            "_updated",
            "associate_link",
        ]

    def _q_insert(self, session: Session, query, payload):
        session.execute(query, payload)

    def create_direct(
        self,
        session,
        pair_ids,
        creator,
        banner=None,
        message_count=0,
        init_last_message=None,
        associate_link: str = "",
    ):
        new_id = generate_thread_id()
        if init_last_message:
            init_last_message = json_dumps(init_last_message)

        if banner:
            banner = json_dumps(banner)

        payload = {
            "id": new_id,
            "pair_ids": pair_ids,
            "type": "direct",
            "creator": creator,
            "member_count": 2,
            "banner": banner,
            "message_count": message_count,
            "centralize_last_message": init_last_message,
            "associate_link": associate_link,
            "_updated": common.now(),
        }
        _d_fields = ", ".join(self._direct_field)
        _d_place_holders = ", ".join(
            [":" + field for field in self._direct_field]
        )
        direct_insert_q = (
            f"INSERT INTO {self.t_name}"
            f"({_d_fields}) "
            "VALUES "
            f"({_d_place_holders})"
        )

        self._q_insert(session, direct_insert_q, payload)
        return new_id

    def create_page(self, session: Session, pair_ids, creator):
        new_id = generate_thread_id()
        payload = {
            "id": new_id,
            "pair_ids": pair_ids,
            "type": "page",
            "creator": creator,
            "member_count": 2,
        }
        _page_field = self._common_field + [
            "pair_ids",
            "creator",
        ]
        _p_fields = ", ".join(_page_field)
        _p_place_holders = ", ".join([":" + field for field in _page_field])
        page_insert_q = (
            f"INSERT INTO {self.t_name}"
            f"({_p_fields}) "
            "VALUES "
            f"({_p_place_holders})"
        )

        self._q_insert(session, page_insert_q, payload)
        return new_id

    def create_group(
        self,
        session: Session,
        name: str,
        member_count: int,
        avatar: Optional[str] = None,
        link_code=None,
        associate_link="",
        group_level=constant.GROUP_LEVEL_PRIVATE,
        group_type="group",
        workspace_id="",
        collab_id: Optional[str] = None,
        parent_id: Optional[int] = None,
        parent_thread_type: Optional[str] = None,
        root_message_id: Optional[int] = None,
    ):
        new_id = generate_thread_id()
        name = _make_group_name(new_id, name)

        # message_count = 1 is action_note message
        payload = {
            "id": new_id,
            "name": name,
            "type": group_type,
            "member_count": member_count,
            "avatar": avatar,
            "message_count": 0,
            "link": link_code,
            "associate_link": associate_link,
            "_updated": common.now(),
            "group_level": group_level,
            "workspace_id": workspace_id,
            "collab_id": collab_id,
            "parent_id": parent_id,
            "root_message_id": root_message_id,
            "commenters": "[]" if parent_id else None,
            "parent_thread_type": parent_thread_type,
        }
        _group_field = self._common_field + [
            "name",
            "avatar",
            "link",
            "_updated",
            "group_level",
            "associate_link",
            "workspace_id",
            "collab_id",
            "parent_id",
            "root_message_id",
            "commenters",
            "parent_thread_type",
        ]
        _g_fields = ", ".join(_group_field)
        _g_place_holders = ", ".join([":" + field for field in _group_field])
        group_insert_q = (
            f"INSERT INTO {self.t_name}"
            f"({_g_fields}) "
            "VALUES "
            f"({_g_place_holders})"
        )

        self._q_insert(session, group_insert_q, payload)
        return new_id

    def set_collab_id(self, session: Session, thread_id: int, collab_id: str):
        param = {"thread_id": thread_id, "collab_id": collab_id}
        query = (
            f"UPDATE {self.t_name} "
            "SET collab_id = :collab_id "
            "WHERE id = :thread_id"
        )
        return self._q_insert(session, query, param)

    def clear_collab_id(self, session: Session, thread_id: int):
        param = {"thread_id": thread_id}
        query = (
            f"UPDATE {self.t_name} "
            "SET collab_id = null "
            "WHERE id = :thread_id"
        )
        return self._q_insert(session, query, param)


class ThreadUpdateController(object):
    t_name = "threads"

    def __init__(self):
        super().__init__()

    def host_group(self, thread_id: int) -> str:
        # return f" /* hostgroup={thread_id % 3 + 1} */ "
        return ""

    def _q_update(self, session: Session, query, params):
        session.execute(query, params)

    def decrease_pin_count(
        self, session: Session, thread_id: int, message_id: int
    ):
        params = {
            "thread_id": thread_id,
            "message_id": message_id,
            "lim": 1,  # protect underflow
        }
        update_thread_decrease_pin_count_q = (
            f"UPDATE {self.t_name} AS t "
            f"{self.host_group(thread_id)}"
            " JOIN pin_collections AS pc "
            "   ON t.id=pc.thread_id "
            "SET t.pinned_count = t.pinned_count - 1 "
            "WHERE pc.thread_id=:thread_id "
            "AND pc.message_id=:message_id "
            "AND t.pinned_count >= :lim "
        )

        self._q_update(session, update_thread_decrease_pin_count_q, params)

    def increase_pin_count(
        self, session: Session, thread_id: int, message_id: int
    ):
        params = {
            "thread_id": thread_id,
            "message_id": message_id,
        }
        update_thread_increase_pin_count_q = (
            f"UPDATE {self.t_name} AS t "
            f"{self.host_group(thread_id)}"
            "SET t.pinned_count = t.pinned_count + 1 "
            "WHERE NOT EXISTS ( "
            " SELECT * FROM pin_collections AS pc WHERE "
            " pc.thread_id=:thread_id "
            "   AND pc.message_id=:message_id"
            ") AND t.id=:thread_id "
        )

        self._q_update(session, update_thread_increase_pin_count_q, params)

    def unblock_thread(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        unblock_thread_q = (
            f"UPDATE {self.t_name} "
            "SET blocked_by=Null "
            "WHERE id=:thread_id"
        )

        self._q_update(session, unblock_thread_q, params)

    def block_thread(self, session: Session, user_id: str, thread_id: int):
        params = {"user_id": user_id, "thread_id": thread_id}
        block_thread_q = (
            f"UPDATE {self.t_name} "
            "SET blocked_by=:user_id "
            "WHERE id=:thread_id"
        )

        self._q_update(session, block_thread_q, params)

    def block_thread_by_partner(
        self, session: Session, user_id: str, pair_ids
    ):
        params = {"pair_ids": pair_ids, "user_id": user_id}
        block_thread_by_pair_id_q = (
            f"UPDATE {self.t_name} "
            "SET blocked_by=:user_id "
            "WHERE pair_ids=:pair_ids"
        )

        self._q_update(session, block_thread_by_pair_id_q, params)

    def unblock_thread_by_partner(self, session: Session, pair_ids):
        params = {"pair_ids": pair_ids}
        unblock_thread_by_pair_id_q = (
            f"UPDATE {self.t_name} "
            "SET blocked_by=Null "
            "WHERE pair_ids=:pair_ids"
        )

        self._q_update(session, unblock_thread_by_pair_id_q, params)

    def update_msg_count(
        self, session: Session, thread_id: int, msg_count: int
    ):
        params = {"thread_id": thread_id, "message_count": msg_count}
        update_msg_count_q = (
            f"UPDATE {self.t_name} "
            f"{self.host_group(thread_id)}"
            "SET message_count=:message_count "
            "WHERE id=:thread_id"
        )

        self._q_update(session, update_msg_count_q, params)

    def update_last_message(
        self, session: Session, thread_id: int, message_id: int, message
    ):
        params = {
            "last_message": message,
            "thread_id": thread_id,
            "message_id": message_id,
        }
        update_last_message_q = (
            f"UPDATE {self.t_name} "
            f"{self.host_group(thread_id)}"
            "SET centralize_last_message=:last_message "
            "WHERE id=:thread_id "
            "AND message_count=:message_id"
        )

        self._q_update(session, update_last_message_q, params)

    def update_sub_threads_info(
        self, session: Session, parent_thread_id: int, edited_fields: Dict
    ):
        params = {"thread_id": parent_thread_id}
        updated_fields = []
        for k in ("name", "avatar", "description"):
            if k in edited_fields:
                updated_fields.append(f"{k}=:{k}")
                params[k] = edited_fields[k]

        if not updated_fields:
            return

        update_stmt = ",".join(updated_fields)
        query = (
            f"UPDATE {self.t_name} "
            f"{self.host_group(parent_thread_id)}"
            f"SET {update_stmt} "
            "WHERE parent_id = :thread_id"
        )
        self._q_update(session, query, params)

    def update_last_message_when_edit(
        self, session: Session, thread_id: int, message_id: int, body: str
    ):
        query = (
            f"UPDATE {self.t_name} SET "
            f"{self.host_group(thread_id)}"
            "centralize_last_message=:last_message, "
            "_updated=:updated_at "
            "WHERE id=:thread_id AND message_count<=:message_id"
        )

        session.execute(
            query,
            {
                "last_message": body,
                "thread_id": thread_id,
                "message_id": message_id,
                "updated_at": int(time.time()) * 1000,
            },
        )

    def update_thread_media_count(
        self, session: Session, thread_id: int, media_infos, decrease=True
    ):
        update_media_count_thread_f = (
            f"UPDATE {self.t_name} " "SET {media_types} WHERE id=:thread_id"
        )

        operator = -1 if decrease else 1
        media_types = " , ".join(
            [
                "{k}_count = greatest(cast({k}_count as signed) + {v}, 0)".format(  # noqa
                    k=k, v=v * operator
                )
                for k, v in media_infos
            ]
        )

        query = update_media_count_thread_f.format(media_types=media_types)
        params = {"thread_id": thread_id}
        self._q_update(session, query, params)

    def clear_thread(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        clear_q = (
            f"UPDATE {self.t_name} "
            f"{self.host_group(thread_id)}"
            "SET "
            "url_count=0, "
            "video_count=0, "
            "file_count=0, "
            "image_count=0, "
            "centralize_last_message=null "
            "WHERE id=:thread_id "
        )

        self._q_update(session, clear_q, params)

    def remove_banner(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        remove_banner_q = (
            f"UPDATE {self.t_name} " "SET banner=null WHERE id=:thread_id"
        )

        self._q_update(session, remove_banner_q, params)

    def _inc_thread_msg_counter(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        incr_thread_counter_q = (
            f"UPDATE {self.t_name} "
            "SET message_count=message_count + 1 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, incr_thread_counter_q, params)

    def pin_message(self, session: Session, thread_id: int, message_id=0):
        params = {"thread_id": thread_id, "message_id": message_id}
        pin_message_q = (
            f"UPDATE {self.t_name} "
            "SET pinned_message_id=:message_id "
            "WHERE id=:thread_id"
        )

        self._q_update(session, pin_message_q, params)

    def update_thread_banned_count(
        self, session: Session, thread_id: int, delta: int
    ):
        params = {"thread_id": thread_id, "number": delta}
        query = (
            f"UPDATE {self.t_name} "
            "SET ban_count = ban_count + :number "
            "WHERE id=:thread_id"
        )

        self._q_update(session, query, params)

    def update_thread_member_count(
        self, session: Session, thread_id: int, delta: int
    ):
        params = {
            "thread_id": thread_id,
            "number": delta,
            "nanotimestamp": common.now(),
        }
        query = (
            f"UPDATE {self.t_name} "
            "SET member_count = member_count + :number, "
            "_updated=:nanotimestamp "
            "WHERE id=:thread_id"
        )

        self._q_update(session, query, params)

    def update_threads_member_count(
        self, session: Session, thread_ids: List[int], delta: int
    ):
        params = {
            "thread_ids": thread_ids,
            "number": delta,
            "nanotimestamp": common.now(),
        }
        query = (
            f"UPDATE {self.t_name} "
            "SET member_count = member_count + :number, "
            "_updated=:nanotimestamp "
            "WHERE id IN :thread_ids"
        )

        self._q_update(session, query, params)

    def edit_info(self, session: Session, thread_id: int, edit_infos):
        params = {"thread_id": thread_id}
        if edit_infos.get("name") is not None:
            edit_infos["name"] = _make_group_name(
                thread_id, edit_infos["name"]
            )

        params.update(edit_infos)
        edit_info_format = (
            f"UPDATE {self.t_name} " "SET {fields} " "WHERE id=:thread_id"
        )

        query = edit_info_format.format(
            fields=", ".join(
                ["{key}=:{key}".format(key=key) for key in edit_infos.keys()]
            )
        )
        self._q_update(session, query, params)
        return edit_infos

    def toggle_privacy(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        toggle_privacy_q = (
            f"UPDATE {self.t_name} "
            "SET group_level=group_level ^ 0b00000001 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, toggle_privacy_q, params)

    def toggle_mute_all(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        toggle_mute_all_q = (
            f"UPDATE {self.t_name} "
            "SET group_level=group_level ^ 0b00000010 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, toggle_mute_all_q, params)

    def toggle_mute_all_sub(self, session: Session, thread_id: int):
        params = {"thread_id": thread_id}
        toggle_mute_all_q = (
            f"UPDATE {self.t_name} "
            "SET group_level=group_level ^ 0b100000000 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, toggle_mute_all_q, params)

    def toggle_only_admin_can_add_member(
        self, session: Session, thread_id: int
    ):
        params = {"thread_id": thread_id}
        toggle_mute_all_q = (
            f"UPDATE {self.t_name} "
            "SET group_level=group_level ^ 0b00000100 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, toggle_mute_all_q, params)

    def toggle_only_admin_can_update_info(
        self, session: Session, thread_id: int
    ):
        params = {"thread_id": thread_id}
        toggle_mute_all_q = (
            f"UPDATE {self.t_name} "
            "SET group_level=group_level ^ 0b00001000 "
            "WHERE id=:thread_id"
        )

        self._q_update(session, toggle_mute_all_q, params)

    def update_thread_link(
        self, session: Session, thread_id: int, link, level
    ):
        params = {"link": link, "thread_id": thread_id, "group_level": level}
        update_link_and_level_q = (
            f"UPDATE {self.t_name} "
            "SET link=:link, group_level=:group_level "
            "WHERE id=:thread_id"
        )

        self._q_update(session, update_link_and_level_q, params)

    def update_thread_group_level(
        self, session: Session, thread_id: int, group_level: int
    ):
        params = {"thread_id": thread_id, "group_level": group_level}
        query = (
            f"UPDATE {self.t_name} "
            "SET group_level=:group_level "
            "WHERE id=:thread_id"
        )

        self._q_update(session, query, params)

    def update_thread_and_member_count(
        self, session: Session, thread_id: int, information, number
    ):
        params = {
            "thread_id": thread_id,
            "information": json_dumps(information),
            "number": number,
            "nanotimestamp": common.now(),
        }
        update_thread_member_q = (
            f"UPDATE {self.t_name} "
            "SET "
            "information=:information, "
            "member_count = member_count + :number, "
            "_updated=:nanotimestamp "
            "WHERE id=:thread_id "
        )

        self._q_update(session, update_thread_member_q, params)

    def update_thread_workspace_id(
        self, session: Session, thread_id: int, workspace_id
    ):
        params = {
            "thread_id": thread_id,
            "workspace_id": workspace_id,
            "type": "group",
        }
        update_thread_workspace_id = (
            f"UPDATE {self.t_name} AS t "
            "SET t.workspace_id=:workspace_id "
            "WHERE t.id=:thread_id AND type=:type "
        )

        self._q_update(session, update_thread_workspace_id, params)

    def clear_associate_link(
        self, session: Session, thread_id: int, workspace_id
    ):
        params = {"thread_id": thread_id, "workspace_id": workspace_id}
        clear_associate_link_q = (
            f"UPDATE {self.t_name} AS t "
            "SET t.associate_link='' "
            "WHERE t.id =:thread_id AND t.workspace_id=:workspace_id"
        )
        self._q_update(session, clear_associate_link_q, params)

    def add_thread_commenter(
        self, session: Session, user_id: str, thread_id: int
    ):
        params = {
            "thread_id": thread_id,
            "user_id": user_id,
            "quote_user_id": f'"{user_id}"',
        }
        query = f"""UPDATE {self.t_name}
                    SET commenters  = JSON_ARRAY_INSERT(
                        commenters,
                        CONCAT('$[', JSON_LENGTH(commenters), ']'),
                        :user_id
                        )
                    WHERE id = :thread_id
                    AND NOT JSON_CONTAINS(commenters, :quote_user_id)
                """

        self._q_update(session, query, params)

    def update_thread_commenters(
        self, session: Session, thread_id: int, commenters: List[str]
    ):
        params = {"thread_id": thread_id, "commenters": commenters}
        query = (
            f"UPDATE {self.t_name} "
            "SET commenters = :commenters "
            "WHERE id = :thread_id "
        )
        self._q_update(session, query, params)


class ThreadItemController(object):
    t_name = "threads"

    def __init__(self):
        super().__init__()

    def _q_get_item(
        self, session: Session, query, params
    ) -> Optional[ThreadModel]:
        record = session.execute(query, params).fetchone()
        if record is None:
            return None
        return ThreadModel(**dict(record))

    # def get_thread_counter(self, session: Session, thread_id: int):
    #     params = {"thread_id": thread_id}
    #     get_counter_q = (
    #         "SELECT message_count "
    #         f"FROM {self.t_name} "
    #         "WHERE id=:thread_id"
    #     )

    #     record = self._q_get_item(session, get_counter_q, params)
    #     return record["message_count"]

    def get_by_id(
        self, session: Session, thread_id: int
    ) -> Optional[ThreadModel]:
        params = {"thread_id": thread_id}
        get_by_id_q = (
            f"SELECT * FROM {self.t_name} WHERE id=:thread_id LIMIT 1"
        )
        return self._q_get_item(session, get_by_id_q, params)

    def get_by_collab_id(
        self, session: Session, collab_id: str
    ) -> Optional[ThreadModel]:
        params = {"collab_id": collab_id}
        query = (
            f"SELECT * from {self.t_name} WHERE collab_id=:collab_id LIMIT 1"
        )
        return self._q_get_item(session, query, params)

    def get_thread_by_link_code(
        self, session: Session, link_code
    ) -> Optional[ThreadModel]:
        params = {"link": link_code}
        query = f"SELECT * FROM {self.t_name} WHERE link=:link LIMIT 1"
        return self._q_get_item(session, query, params)

    def get_by_associate(
        self, session: Session, associate_link: str
    ) -> Optional[ThreadModel]:
        params = {"associate_link": associate_link}
        query = f"SELECT * FROM {self.t_name} WHERE associate_link=:associate_link LIMIT 1"  # noqa
        return self._q_get_item(session, query, params)

    def get_sub_thread_for_message(
        self, session: Session, thread_id: int, message_id: int
    ) -> Optional[ThreadModel]:
        params = {"thread_id": thread_id, "message_id": message_id}
        query = f"""SELECT * FROM {self.t_name}
        WHERE parent_id=:thread_id AND root_message_id=:message_id"""

        return self._q_get_item(session, query, params)


class ThreadListController(object):
    t_name = "threads"

    def __init__(self):
        super().__init__()

    def _q_get_items(
        self, session: Session, query, params
    ) -> List[ThreadModel]:
        records = session.execute(query, params).fetchall()
        items: List[ThreadModel] = []
        for record in records:
            if not record:
                continue
            items.append(ThreadModel(**dict(record)))

        return items

    def get_threads_by_workspace(
        self, session: Session, workspace_id=0, last_id=0, page_size=20
    ) -> List[ThreadModel]:
        params = {
            "workspace_id": workspace_id,
            "last_id": last_id,
            "page_size": page_size,
        }
        query = (
            f"SELECT * FROM {self.t_name} "
            "WHERE workspace_id=:workspace_id "
            "AND id>:last_id "
            "LIMIT :page_size"
        )
        return self._q_get_items(session, query, params)

    def get_by_collab_ids(
        self, session: Session, collab_ids: List[str]
    ) -> List[ThreadModel]:
        if len(collab_ids) == 0:
            return []

        params = {"collab_ids": collab_ids}
        query = f"SELECT * from {self.t_name} WHERE collab_id in :collab_ids"
        return self._q_get_items(session, query, params)


class ThreadRepositoryMySQL(
    ThreadInsertController,
    ThreadItemController,
    ThreadListController,
    ThreadUpdateController,
    ThreadRepository,
):
    t_name = "threads"


    def __init__(self, log: Logger, cache: ThreadCache):
        super().__init__()
        self.log = log
        self.cache = cache

    def incr_thread_msg_counter(self, session: Session, thread_id: int) -> int:
        self._inc_thread_msg_counter(session, thread_id)
        thread = self.get_by_id(session, thread_id)
        if thread:
            m_count = thread.message_count
        else:
            m_count = 0
        session.commit()
        return m_count

    # def is_thread_page(self, thread):
    #     return thread["type"] == constant.PAGE_THREAD_TYPE

    def update_cache(self, thread_d: Dict):
        try:
            thread_o = ThreadModel(**thread_d)
            self.cache.update(thread_o)
        except Exception as e:
            self.log.error(e)
