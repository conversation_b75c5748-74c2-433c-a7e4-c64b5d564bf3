from typing import Dict, List, Optional, Set, Union

from pydantic import Field, conint, root_validator, validator

import chat.utils.thumb as thumb
from chat import constant
from chat.model import BaseModel
from chat import dto
from chat.threads.exception import ThreadNameTooLong
from chat.threads.model import ThreadSettings
from chat.utils import common
from chat.utils.validator_helper import (
    is_bot_id,
    is_department_id,
    is_gapo_image,
    is_group_id,
    is_system,
    is_timestamp_nano,
    is_user_id,
)

MAX_LENGTH_DESCRIPTION = 256
MAX_LENGTH_ALIAS = 256
MAX_LENGTH_NAME = 100
MAX_LENGTH_AVATAR = 500
MAX_PASSCODE = 4
MAX_SOURCE_THREADS = 10
MAX_LENGTH_TEXT = 5000

PAGE_SIZE = 100

# ALLOWED_MOVE_TO_FOLDER = (constant.FOLDER_SECRET, constant.FOLDER_DEFAULT)


def validate_avatar(v: str) -> str:
    stripped = v.strip()
    if not is_gapo_image(stripped):
        raise ValueError(f"{stripped} invalid url")
    return thumb.avatar(v)


def validate_pass_code(v: str) -> str:
    if v == "":
        return None  # type: ignore
    if not v.isdigit():
        raise ValueError(f"Type invalid {v}")
    if len(v) != MAX_PASSCODE:
        raise ValueError(f"Length invalid {v}")

    return v


def limit_description(v: str) -> str:
    stripped = v.strip()
    if not MAX_LENGTH_DESCRIPTION >= len(stripped):
        raise ValueError("Description too long")
    return stripped


def limit_name(v: str) -> str:
    stripped = v.strip()
    if not MAX_LENGTH_NAME >= len(stripped):
        raise ThreadNameTooLong()

    return stripped


def limit_alias(v: str) -> str:
    stripped = v.strip()
    if not MAX_LENGTH_ALIAS >= len(stripped):
        raise ValueError("Alias too long")
    return stripped


def limit_text(v: str) -> str:
    if v is None:
        return v
    stripped = v.strip()
    if not 0 < common.count_unicode(stripped) <= MAX_LENGTH_TEXT:
        raise ValueError("Text too long")
    return stripped


def check_thread_id(v: int) -> int:
    if is_timestamp_nano(v):
        return v
    raise ValueError(f"Invalid thread_id {v}")


class SchemaCreate(BaseModel):
    creator: str
    role: str
    type: str
    contact_name: str = ""
    source: Union[str, None] = None
    participant_ids: Set[str]
    name: str = ""
    avatar: str = ""
    pass_code: Union[None, str] = None
    has_system: bool = False
    workspace_id: str
    collab_id: Union[None, str] = None

    source_thread_ids: List[int] = []

    member_file_id: Union[None, str] = None

    _normalize_pass_code = validator("pass_code", allow_reuse=True)(validate_pass_code)
    _normalize_name = validator("name", allow_reuse=True)(limit_name)
    _normalize_avatar = validator("avatar", allow_reuse=True)(validate_avatar)

    @validator("type", pre=True)
    def type_check(cls, v):
        if v not in constant.THREAD_TYPES:
            raise ValueError(f"Type invalid {v}")
        return v

    @validator("workspace_id")
    def validate_workspace_id(cls, v, values):
        thread_type = values.get("type")
        if thread_type == constant.GROUP_THREAD_TYPE and not v:
            raise ValueError("Missing x-gapo-workspace-id header value")

        return v

    @validator("source")
    def source_check(cls, v, values, **kwargs):
        role = values["role"]
        thread_type = values["type"]
        if v == "contact":
            if role != "service":
                raise ValueError(f"Not allow source from {role}")
            if thread_type != constant.DIRECT_THREAD_TYPE:
                raise ValueError(f"Contact service doenst support {thread_type}")
        return v

    @validator("participant_ids")
    def remove_creator(cls, v, values, **kwargs):
        creator = values["creator"]
        v.discard(creator)
        if len(v) > 200:
            raise ValueError(f"Invite participants{v}")
        return list(v)

    @validator("source_thread_ids")
    def validate_source_thread_ids(cls, v, values):
        if len(v) >= MAX_SOURCE_THREADS:
            raise ValueError("Maximum 10 source_thread_ids are allowed")

        # unique value
        unique_vals = set([])
        for thread_id in v:
            check_thread_id(thread_id)
            unique_vals.add(thread_id)

        return list(unique_vals)

    # @validator("workspace_id")
    # def check_workspace_id(cls, v):
    #     if len(v) == 0:
    #         raise ValueError(f"Invalid workspace_id length = 0")
    #     return v

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        thread_type = values["type"]
        if thread_type == constant.DIRECT_THREAD_TYPE:
            _validate_thread_direct(values)
        elif thread_type in (
            constant.GROUP_THREAD_TYPE,
            constant.SUPER_GROUP_THREAD_TYPE,
        ):
            _validate_thread_group(values)
        else:
            raise ValueError(f"Doesnt support new type {thread_type}")
        return values


class SchemaOrgcGroupCreate(BaseModel):
    type: str
    org_c_ids: Set[str]
    org_type: str
    name: str = ""
    avatar: str = ""

    org_cs: Dict = {}  # will be filled by validator

    _normalize_name = validator("name", allow_reuse=True)(limit_name)
    _normalize_avatar = validator("avatar", allow_reuse=True)(validate_avatar)

    @validator("type", pre=True)
    def type_check(cls, v):
        if v != constant.GROUP_THREAD_TYPE:
            raise ValueError(f"Type invalid {v}")
        return v

    @validator("org_type", pre=True)
    def org_type_check(cls, v):
        if v not in ("title", "department"):
            raise ValueError(f"Org Type invalid {v}")
        return v

    @validator("org_c_ids")
    def remove_creator(cls, v, values, **kwargs):
        len_v = len(v)
        if not len_v:
            raise ValueError(f"Need orgc_ids{v}")
        elif len_v > 200:
            raise ValueError(f"Too much orgc_ids{len(v)}")
        return list(v)

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        org_c_ids = values["org_c_ids"]

        org_cs: Dict = {}

        for org_c_id in org_c_ids:
            w_id, d_id = org_c_id.split(":")
            d_ids = org_cs.get(w_id, [])
            d_ids.append(d_id)
            org_cs[w_id] = d_ids
        values["org_cs"] = org_cs
        return values


class SchemaOrgcObject(dto.OrgcObject):
    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        if not values["title_ids"] and not values["department_ids"]:
            raise ValueError("Create from orgc need title or department")
        return values


class SchemaGroupCreate(BaseModel):
    creator_id: str
    workspace_id: str

    avatar: str = ""
    name: str = ""

    participant_ids: Set[str] = set()
    member_file_id: str = ""
    source_thread_ids: Set[int] = set()
    orgcs: List[SchemaOrgcObject] = []

    _normalize_name = validator("name", allow_reuse=True)(limit_name)
    _normalize_avatar = validator("avatar", allow_reuse=True)(validate_avatar)

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        has_ids = 0
        has_ids += len(values["participant_ids"])
        has_ids += len(values["member_file_id"])
        has_ids += len(values["source_thread_ids"])
        has_ids += len(values["orgcs"])

        if not has_ids:
            raise ValueError("Create group require at least from a source")
        return values


class SchemaAutoJoinOrLeave(BaseModel):
    auto_type: str
    participant_ids: List[str]
    collab_id: Optional[str] = None
    thread_id: Optional[int] = None
    data_source: str

    @validator("auto_type")
    def check_auto_type(cls, v):
        if v not in ("join", "leave"):
            raise ValueError("auto type invalid")
        return v

    @validator("data_source")
    def check_data_source(cls, v):
        if v not in ("department", "group", "openapi"):
            raise ValueError("Data source invalid")
        return v

    def is_join(self):
        return self.auto_type == "join"

    def get_data_source(self):
        if self.is_join():
            return f"join_chat_by_{self.data_source}"
        return f"leave_group_by_{self.data_source}"


class SchemaCreateAssociate(BaseModel):
    creator: str
    associate_link: str
    name: str = ""
    avatar: str = ""
    text: str = ""

    _normalize_name = validator("name", allow_reuse=True)(limit_name)
    _normalize_avatar = validator("avatar", allow_reuse=True)(validate_avatar)
    _normalize_text = validator("text", allow_reuse=True)(limit_text)

    @validator("associate_link")
    def check_link(cls, v):
        links = v.split(":")
        if len(links) != 2:
            raise ValueError(f"Invalid associate_link {v}")

        link_type, link_id = links
        if link_type not in ("group", "department", "approval"):
            raise ValueError(f"Invalid associate_link {v}")

        if link_type == "group":
            if not is_group_id(link_id):
                raise ValueError(f"Invalid associate_link {v}")
        else:
            if not is_department_id(link_id):
                raise ValueError(f"Invalid associate_link {v}")

        return v


def _validate_thread_direct(values):
    if len(values["participant_ids"]) != 1:
        raise ValueError("Invite too much when create direct")
    partner_id = values["participant_ids"][0]
    if is_user_id(partner_id) or is_bot_id(partner_id):
        values["has_system"] = False
    elif is_system(partner_id):
        values["has_system"] = True
    else:
        raise ValueError("Invite people only support user")


def _validate_thread_group(values):
    num_source_threads = 0
    try:
        source_thread_ids = values.get("source_thread_ids")
        num_source_threads = len(source_thread_ids)
    except Exception:
        pass

    member_file_id = values.get("member_file_id")
    if (
        len(values["participant_ids"]) < 1
        and num_source_threads == 0
        and not member_file_id
    ):
        raise ValueError("Need members in when creating new group")
    # for user_id in values["participant_ids"]:
    #     if not is_user_id(user_id):
    #         raise ValueError("Invite people only support user")


class SchemaGetItemParams(BaseModel):
    thread_id: int
    pass_code: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)

    _normalize_pass_code = validator("pass_code", allow_reuse=True)(validate_pass_code)


class SchemaPin(BaseModel):
    thread_id: int
    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)


class SchemaGetInfoParams(BaseModel):
    workspace_id: Union[None, str] = None
    name: Union[None, str] = None
    last_id: int = 0
    page_size: conint(ge=1, le=PAGE_SIZE) = PAGE_SIZE  # type: ignore
    thread_ids: str = Field(0, alias="ids")  # type: ignore
    collab_ids: List[str] = Field(default=[], max_items=100)
    role: str

    @validator("thread_ids")
    def check_thread_ids(cls, v):
        thread_ids = v.split(",")
        length_ids = len(thread_ids)
        if length_ids > PAGE_SIZE or length_ids < 1:
            raise ValueError(f"Invalid length ids {length_ids}")

        return [check_thread_id(int(thread_id)) for thread_id in thread_ids]

    @validator("collab_ids", pre=True)
    def check_collab_ids(cls, v):
        if isinstance(v, list):
            return v

        if not isinstance(v, str):
            raise ValueError("Invalid 'collab_ids' value")

        collab_ids = v.split(",")
        collab_ids = [v.strip() for v in collab_ids if v.strip()]
        return collab_ids

    @validator("role")
    def check_role(cls, v):
        if v != constant.SERVICE_ROLE:
            raise ValueError(f"Invalid role {v}")
        return v


class SchemaGetParams(BaseModel):
    page_size: conint(ge=1, le=PAGE_SIZE) = PAGE_SIZE  # type: ignore
    last_id: conint(ge=0) = None  # type: ignore
    pass_code: Union[None, str] = None
    include_folders: Union[str, List[str]] = ["default", "stranger"]
    exclude_folders: Union[str, List[str]] = []

    _normalize_pass_code = validator("pass_code", allow_reuse=True)(validate_pass_code)

    @validator("include_folders")
    def check_include_folders(cls, v):
        if not isinstance(v, list):
            return [v]
        return v

    @validator("exclude_folders")
    def check_exclude_folders(cls, v):
        if not isinstance(v, list):
            return [v]
        return v


class SchemaMarkReadFlagToThread(BaseModel):
    thread_id: int
    unread: bool


class SchemaEdit(BaseModel):
    thread_id: int
    name: Optional[str] = None
    avatar: Optional[str] = None
    description: Optional[str] = None
    enable_notification: Optional[int] = None
    alias: Optional[str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)
    _normalize_name = validator("name", allow_reuse=True)(limit_name)
    _normalize_alias = validator("alias", allow_reuse=True)(limit_alias)
    _normalize_description = validator("description", allow_reuse=True)(
        limit_description
    )

    @validator("avatar")
    def validate_type_avatar(cls, v):
        stripped = v.strip()

        # hỗ trợ remove
        if stripped == "":
            return stripped
        if not is_gapo_image(stripped):
            raise ValueError(f"{stripped} invalid url")
        return thumb.avatar(v)

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        for k in list(values):
            if values[k] is None:
                values.pop(k)
        if not values:
            raise ValueError("Must change something")
        return values


class SchemaMoveFolder(BaseModel):
    thread_id: int
    to_folder: str
    pass_code: Union[None, str] = None

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)
    _normalize_pass_code = validator("pass_code", allow_reuse=True)(validate_pass_code)

    # @validator("to_folder")
    # def allowed_folder(cls, v):
    #     if v not in ALLOWED_MOVE_TO_FOLDER:
    #         raise ValueError(f"Doesnt support another folder {v}")

    #     return v


class SchemaLeave(BaseModel):
    thread_id: int
    clear_msg: bool = False

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)


class SchemaLeaveAllGroup(BaseModel):
    user_id: str
    workspace_id: str


class SchemaDeactivateUser(BaseModel):
    user_id: str
    workspace_id: str


class SchemaReactivateUser(BaseModel):
    user_id: str
    workspace_id: str


class SchemaPrivacy(BaseModel):
    thread_id: int
    action: str

    @validator("action")
    def validate_action(cls, v):
        if v not in ("public", "private"):
            raise ValueError("Action not allowed")
        return v

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)


class SchemaSetAutoDeleteMessage(BaseModel):
    thread_id: int
    day_num: int

    @validator("day_num")
    def validate_day_num(cls, v):
        if v not in (0, 1, 3, 7, 30):
            raise ValueError("day_num invalid")
        return v

    _normalize_thread_id = validator("thread_id", allow_reuse=True)(check_thread_id)


class SchemaPartner(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str
    read_count: int

    # @validator("id")
    # def validate_id(cls, v):
    #     if common.get_account_type(v) == constant.BOT_TYPE:
    #         variant_part = int(v) >> 0 & 0x3FFFF
    #         v = constant.BOT_SPECIAL_VARPART.get(str(variant_part), v)
    #     return v


class SchemaUser(BaseModel):
    id: str
    name: str
    avatar: Union[str, None] = None
    status_verify: int
    type: str


class SchemaLastMessage(BaseModel):
    id: int
    body: Union[None, str] = ""
    raw_body: Union[None, dict] = dict()
    sender: SchemaUser
    created_at: int
    user_id: str


class SchemaBanner(BaseModel):
    type: int
    data: dict
    show_ids: list


class SchemaOutput(BaseModel):
    name: Optional[str] = None
    alias: Optional[str] = None
    enable_notify: int = 0
    role: str
    avatar: Union[str, None] = None
    id: int
    parent_id: Union[int, None] = None
    parent_thread_type: Union[str, None] = None
    root_message_id: Union[int, None] = None
    group_level: int
    message_count: int
    member_count: int
    # video_count: int
    # image_count: int
    # url_count: int
    departments: Optional[str] = None
    ban_count: int
    folder: str
    pinned_message_id: int
    read_count: int
    delete_to: int = 0
    type: str
    blocked_by: Optional[str] = None
    link: Optional[str] = None
    description: Optional[str] = None
    partner: Union[SchemaPartner, None] = None
    partner_id: Union[None, str]
    last_message: Union[SchemaLastMessage, None] = None
    banner: Union[SchemaBanner, None] = None
    pinned_at: int = Field(..., alias="pin_pos")
    pinned_count: int = 0
    associate_link: Union[None, str] = ""
    tags: Union[None, str]
    can_call: Union[None, bool] = None
    collab_id: Union[None, str] = None

    mark_unread: bool = False

    referenced_message: Union[None, Dict] = None

    @validator("avatar")
    def type_check(cls, v):
        thumb_avatar = thumb.avatar(v)
        return thumb_avatar if thumb_avatar else v

    @validator("tags")
    def parse_tags(cls, v):
        if v:
            return [int(value) for value in v.split(",")[:-1]]
        return []

    @root_validator(skip_on_failure=True)
    def validate_type_other_field(cls, values):
        read_count = values["read_count"]
        total_count = values["message_count"]
        delete_to = values["delete_to"]

        readed_count = read_count if read_count > delete_to else delete_to
        unread_count = total_count - readed_count
        values["unread_count"] = unread_count if unread_count >= 0 else 0
        values.pop("delete_to")
        settings = ThreadSettings.decode(values["group_level"])

        if values["role"] not in ("owner", "admin"):
            values["ban_count"] = 0
            values["can_send_message"] = (
                0 if settings.disable_member_send_message else 1
            )
        else:
            values["can_send_message"] = 1

        values["settings"] = settings
        values["group_level"] = values["group_level"] & 0b1

        if values["type"] == constant.DIRECT_THREAD_TYPE:
            partner = values["partner"]
            values["partner_id"] = partner.id
            name = partner.name
            if name == "Gapo Member" or not name:
                banner = values["banner"]
                if banner and banner.type == 1:
                    partner.name = banner.data["contact_name"]
                    values["name"] = partner.name

        elif values["type"] == constant.SUPER_GROUP_THREAD_TYPE:
            values["type"] = constant.GROUP_THREAD_TYPE

        return values


class SchemaOutputPublic(BaseModel):
    name: Optional[str] = None
    avatar: Union[str, None] = None
    id: int
    message_count: int
    member_count: int
    type: str
    description: Optional[str] = None
    collab_id: Optional[str] = ""

    @validator("avatar")
    def type_check(cls, v):
        thumb_avatar = thumb.avatar(v)
        return thumb_avatar if thumb_avatar else v