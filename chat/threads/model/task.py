from typing import Union

from chat.model import BaseModel


class TaskLeaveGroup(BaseModel):
    thread_id: int
    user_id: str
    send_action_note: bool = False
    clear_msg: bool = False
    promote_new_owner_if_needed: bool = True
    deactivate_only: bool = False


class TaskDeactivateMemberInGroup(BaseModel):
    workspace_id: str
    user_id: str


class TaskReactivateMemberInGroup(BaseModel):
    workspace_id: str
    user_id: str


class ThreadCreatedEvent(BaseModel):
    thread_id: int
    creator_id: str
    creator_type: str
    type: str
    associate_link: str = ""
    partner_id: Union[str, None] = None
    partner_type: Union[str, None] = None

    # for sub-thread
    parent_id: Union[int, None] = None
    root_message_id: Union[int, None] = None
