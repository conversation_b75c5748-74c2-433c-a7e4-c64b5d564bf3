from __future__ import annotations
from dataclasses import dataclass
from typing import Any, Union, List, Dict

from pydantic import validator, root_validator
from typing_extensions import NotRequired, TypedDict

from chat.model import BaseModel
from chat.messages.model import LastMessageModel
from chat.utils import thumb
from chat.utils.json import json_loads
from chat.constant import MAX_MEMBER
from chat import constant

GROUP_PUBLICITY_BIT = 0b1
MESSAGE_MUTE_BIT = 0b10
ONLY_ADMIN_CAN_ADD_MEMBER_BIT = 0b100
ONLY_ADMIN_CAN_UPDATE_INFO_BIT = 0b1000

DELETE_MSG_AFTER_1_DAY = 0b1_0000
DELETE_MSG_AFTER_3_DAY = 0b10_0000
DELETE_MSG_AFTER_7_DAY = 0b100_0000
DELETE_MSG_AFTER_30_DAY = 0b1000_0000

MESSAGE_MUTE_BUT_NOT_SUB = 0b1_0000_0000

"""
Coi settings mỗi rule là 1 bit trong chuỗi 8 bit ( 1 byte )

    1. is_public là bit thứ nhất
    2. mute_all là bit thứ hai
    3. only_admin_add_member là bit thứ ba // sắp tới chuyển sang collab
    4. only admin update info là bit thứ tư

    sô bit còn lại dùng đẻ làm auto delete

    5. delete trong 1 ngày
    6. delete trong 3 ngày
    7. delete trong 1 tuần
    8. delete trong 1 tháng

    9. mute_all_sub

"""


class ThreadInfo(BaseModel):
    id: int
    collab_id: Union[None, str] = None
    name: Union[None, str]
    pair_ids: Union[None, str]
    type: str
    avatar: Union[None, str]

    @validator("avatar")
    def thumb_avatar(cls, v):
        if v:
            return thumb.avatar(v)


class ChatThread(TypedDict):
    id: int

    parent_id: NotRequired[int]
    """Id of parent thread. """

    parent_thread_type: NotRequired[str]
    """Type of parent thread (direct, group,...). """

    root_message_id: NotRequired[int]
    """ID of parent message. """

    commenters: NotRequired[str]
    """List of user ids who has commented in a sub-thread."""

    avatar: NotRequired[str]
    name: str
    blocked_by: NotRequired[str]
    type: str

    group_level: int
    """group level, a encoded number that store settings about thread."""

    message_count: int
    """number of messages in thread chat."""

    creator: str
    """Id of creator (for group chat only)"""

    centralize_last_message: NotRequired[Any]

    member_count: int
    """Total number of members in chat."""

    link: str
    """join link code. """

    associate_link: NotRequired[str]
    """link to group/department. Format: group:{group_id}
        or department:{department_id}."""

    description: NotRequired[str]

    pair_ids: NotRequired[str]

    video_count: int
    url_count: int
    image_count: int
    file_count: int
    ban_count: int
    pinned_count: int
    pinned_message_id: str
    information: NotRequired[Any]
    banner: NotRequired[Any]
    workspace_id: str

    collab_id: Union[None, str]
    """Id of collab that group chat is belong to. Only for group chat. """

    _updated: int
    _created: int
    _deleted: int


@dataclass
class ThreadSettings:
    is_public: int
    disable_member_send_message: int
    disable_member_send_sub_message: int

    only_admin_can_add_member: int
    only_admin_can_update_info: int

    delete_msg_after_days: int = 0

    def set_delete_msg_day(self, days: int):
        if days not in (0, 1, 3, 7, 30):
            raise Exception("wrong days")
        self.delete_msg_after_days = days

    def toggle_privacy(self):
        self.is_public = self.is_public ^ 0b1

    def toggle_send_msg(self):
        self.disable_member_send_message = (
            self.disable_member_send_message ^ 0b1
        )

        if self.disable_member_send_message is False:
            self.disable_member_send_sub_message = 0
        else:
            self.disable_member_send_sub_message = 1

    def toggle_send_sub_message(self):
        if not self.disable_member_send_message:
            raise Exception("cant toggle member send sub message")
        self.disable_member_send_sub_message = (
            self.disable_member_send_sub_message ^ 0b1
        )

    def toggle_add_mem(self):
        self.only_admin_can_add_member = self.only_admin_can_add_member ^ 0b1

    def toggle_update_info(self):
        self.only_admin_can_update_info = self.only_admin_can_update_info ^ 0b1

    def dict(self):
        return {
            "is_public": int(self.is_public),
            "disable_member_send_message": int(
                self.disable_member_send_message
            ),
            "disable_member_send_sub_message": int(
                self.disable_member_send_sub_message
            ),
            "only_admin_can_add_member": int(self.only_admin_can_add_member),
            "only_admin_can_update_info": int(self.only_admin_can_update_info),
            "delete_msg_after_days": int(self.delete_msg_after_days),
        }

    def enable_send_message(self):
        # when enable, both enable
        self.disable_member_send_message = 1
        self.disable_member_send_sub_message = 1

    def encode(self):
        bit_mask = 0
        days = self.delete_msg_after_days
        if days == 1:
            bit_mask = DELETE_MSG_AFTER_1_DAY
        elif days == 3:
            bit_mask = DELETE_MSG_AFTER_3_DAY
        elif days == 7:
            bit_mask = DELETE_MSG_AFTER_7_DAY
        elif days == 30:
            bit_mask = DELETE_MSG_AFTER_30_DAY

        return (
            (self.is_public & 0b1)
            | (self.disable_member_send_message << 1)
            | (self.only_admin_can_add_member << 2)
            | (self.only_admin_can_update_info << 3)
            | bit_mask
            | (self.disable_member_send_sub_message << 8)
        )

    @classmethod
    def decode(cls, group_level: int):
        bits = group_level & 0b1111_0000
        days = 0

        if bits == DELETE_MSG_AFTER_1_DAY:
            days = 1
        elif bits == DELETE_MSG_AFTER_3_DAY:
            days = 3
        elif bits == DELETE_MSG_AFTER_7_DAY:
            days = 7
        elif bits == DELETE_MSG_AFTER_30_DAY:
            days = 30
        return ThreadSettings(
            is_public=int((group_level & GROUP_PUBLICITY_BIT) > 0),
            disable_member_send_message=int(
                (group_level & MESSAGE_MUTE_BIT) > 1
            ),
            disable_member_send_sub_message=int(
                (group_level & MESSAGE_MUTE_BUT_NOT_SUB) > 8
            ),
            only_admin_can_add_member=int(
                (group_level & ONLY_ADMIN_CAN_ADD_MEMBER_BIT) > 2
            ),
            only_admin_can_update_info=int(
                (group_level & ONLY_ADMIN_CAN_UPDATE_INFO_BIT) > 3
            ),
            delete_msg_after_days=days,
        )


class BannerModel(BaseModel):
    type: int
    data: Dict
    show_ids: List


class ThreadInformation(BaseModel):
    clear_msg: dict = dict()


class ThreadModel(BaseModel):
    updated: int
    id: int
    message_count: int
    pair_ids: Union[None, str]
    link: Union[None, str]
    member_count: int
    type: str = "direct"
    information: Union[None, ThreadInformation] = ThreadInformation()
    group_level: int

    creator: Union[None, str] = ""

    centralize_last_message: Union[None, LastMessageModel] = None
    ban_count: int
    pinned_message_id: int

    blocked_by: Union[None, str]
    banner: Union[None, BannerModel]

    avatar: Union[None, str] = ""
    name: Union[None, str] = None
    status_verify: Union[None, int] = 0

    associate_link: Union[None, str]
    workspace_id: Union[None, str]
    departments: Union[None, str] = None

    pinned_count: int
    description: Union[None, str]

    collab_id: Union[None, str]
    commenters: Union[None, List[str]] = []

    parent_id: Union[int, None] = None
    parent_thread_type: Union[str, None] = None

    parent: Union[ThreadModel, None] = None

    root_message_id: Union[int, None] = None

    referenced_message: Union[None, dict] = None
    referenced_message_preview: Union[None, str] = ""  # for notify

    settings: ThreadSettings

    image_count: int = 0
    video_count: int = 0
    file_count: int = 0

    @validator("avatar")
    def thumb_avatar(cls, v):
        if v:
            return thumb.avatar(v)

    @validator("banner", pre=True)
    def parse_banner(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    def is_subthread(self) -> bool:
        return self.type == constant.SUB_THREAD_TYPE

    def is_direct(self) -> bool:
        return self.type == constant.DIRECT_THREAD_TYPE

    def is_parent_direct(self) -> bool:
        return self.parent_thread_type == constant.DIRECT_THREAD_TYPE

    def can_add(self):
        return self.member_count < MAX_MEMBER

    def is_group(self) -> bool:
        return self.type == constant.GROUP_THREAD_TYPE

    def is_associate_department(self) -> bool:
        if not self.associate_link:
            return False

        if not self.associate_link.startswith("department:"):
            return False

        return True

    def is_associate_group(self) -> bool:
        if not self.associate_link:
            return False

        if not self.associate_link.startswith("group:"):
            return False

        return True

    def update_department(self, d_info):
        self.departments = " > ".join(d_info["fullpath_name"])

    def update_link(self, domain):
        if not self.settings:
            return

        if self.settings.is_public:
            self.link = domain + "/joinchat/" + self.link
        else:
            self.link = ""

    def get_associate_id(self) -> str:
        if not self.associate_link:
            raise Exception("assocate_link is empty")
        return self.associate_link.split(":")[1]

    @validator("workspace_id")
    def fix_workspace_id(cls, v):
        if v is None:
            return ""
        return v

    @validator("commenters", pre=True)
    def parse_commenters(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    @validator("referenced_message", pre=True)
    def parse_ref_msg(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    @root_validator(pre=True)
    def fill_default_payload(cls, values):
        values["settings"] = ThreadSettings.decode(values["group_level"])
        values["updated"] = values.get("updated", values.get("_updated", 0))
        return values

    @validator("centralize_last_message", pre=True)
    def parse_last_message(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    @validator("information", pre=True)
    def parse_information(cls, v):
        if isinstance(v, str):
            v = json_loads(v)
        return v

    def update_information(self, user_id):
        if self.information is None:
            self.information = ThreadInformation()

        self.information.clear_msg[user_id] = self.message_count

    def __eq__(self, other):
        if not isinstance(other, ThreadModel):
            raise NotImplementedError

        return (
            self.id == other.id
            and self.message_count == other.message_count
            and self.pair_ids == other.pair_ids
            and self.member_count == other.member_count
            and self.type == other.type
            and self.group_level == other.group_level
            and self.collab_id == other.collab_id
            and self.updated == other.updated
            and self.updated > 0
        )
