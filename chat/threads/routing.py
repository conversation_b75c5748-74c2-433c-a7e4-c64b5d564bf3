from falcon import API

from chat.threads.container import Thread<PERSON>ontainer


def routing(app: API, module_container: ThreadContainer):
    resources = module_container.resources()

    app.add_route("/threads/{thread_id:int}", resources.thread_item())
    app.add_route(
        "/threads/{thread_id:int}/set-unread-flag", resources.unread_thread()
    )

    app.add_route("/threads/joinchat/{link}", resources.thread_join_by_link())

    app.add_route("/internal/threads/auto", resources.internal_auto())

    app.add_route("/threads/leavechat", resources.thread_leave())

    app.add_route(
        "/threads/leave_all_group_chat", resources.leave_all_groups()
    )

    app.add_route("/threads/{thread_id:int}/pin", resources.thread_pin())

    app.add_route("/threads", resources.threads())
    app.add_route("/threads/group", resources.thread_group())

    app.add_route("/threads/privacy", resources.thread_privacy())

    app.add_route(
        "/threads/set_auto_delete_message",
        resources.thread_set_auto_delete_message(),
    )

    app.add_route(
        "/threads/{thread_id:int}/toggle-privacy",
        resources.thread_toggle_privacy(),
    )

    app.add_route(
        "/threads/{thread_id:int}/toggle-add-member",
        resources.toggle_only_admin_add_member(),
    )
    app.add_route(
        "/threads/{thread_id:int}/toggle-update-info",
        resources.toggle_only_admin_update_info(),
    )

    app.add_route(
        "/threads/{thread_id:int}/toggle-mute-all", resources.toggle_mute_all()
    )

    app.add_route(
        "/threads/{thread_id:int}/toggle-mute-all-subthread",
        resources.toggle_mute_all_subthread(),
    )

    app.add_route(
        "/threads/{thread_id:int}/toggle-notify",
        resources.thread_toggle_notify(),
    )

    app.add_route(
        "/threads/{thread_id:int}/clear_history", resources.clear_history()
    )

    app.add_route(
        "/threads/{thread_id:int}/clear_conversation",
        resources.clear_conversation(),
    )

    app.add_route("/threads/{thread_id:int}/folder", resources.folder())

    # INTERNAL

    # app.add_route("/internal/threads", resources.internal_info())

    app.add_route(
        "/internal/threads/associate", resources.internal_associate()
    )

    app.add_route(
        "/internal/threads/associate/{thread_id:int}",
        resources.internal_associate_item(),
    )

    app.add_route("/threads/orgc", resources.orgc())

    app.add_route(
        "/threads/{thread_id:int}/disband", resources.disband_group()
    )

    app.add_route(
        "/threads/{thread_id:int}/messages/{message_id:int}/sub-threads",
        resources.sub_thread(),
    )

    app.add_route(
        "/threads/{thread_id:int}/sub-threads/{sub_thread_id:int}/toggle-notify",  # noqa
        resources.sub_thread_toggle_notify(),
    )

    app.add_route("/internal/users/deactivate", resources.deactivate_user())
    app.add_route("/internal/users/reactivate", resources.reactivate_user())
