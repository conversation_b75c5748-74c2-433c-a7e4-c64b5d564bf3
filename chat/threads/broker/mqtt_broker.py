from logging import Logger

from chat.config import Settings
from chat.publishers.mqtt import MqttPublisher


class ThreadMQTTBroker(object):
    def __init__(self, config: Settings, log: Logger, engine: MqttPublisher):
        self.config = config.mqtt
        self.log = log
        self.engine = engine

    def publish_thread_deleted_event(
        self,
        member_ids,
        thread_id,
        thread_type,
        message_to,
        clear_msg=False,
    ):
        msg = {
            "thread_id": thread_id,
            "message_to": message_to,
            "clear_msg": clear_msg,
        }
        event = {"event_type": "thread_deleted", "body": msg}
        self.engine.publish_status(member_ids, event)

    def _make_pin_event(
        self, thread_id: int, user_id: str, pinned_at: int, folder: str
    ):
        msg = {
            "thread_id": thread_id,
            "user_id": user_id,
            "pinned_at": pinned_at,
            "folder": folder,
        }
        event = {"event_type": "pin", "body": msg}
        return event

    def publish_thread_pinned_event(
        self, thread_id: int, user_id: str, pinned_at: int, folder: str
    ):
        event = self._make_pin_event(thread_id, user_id, pinned_at, folder)
        self.engine.publish_status((user_id,), event)

    def publish_set_unread_flag_event(
        self, user_id: str, thread_id: int, folder: str, unread_status: bool
    ):
        """Publishes event when a user mark as thread as read/unread."""

        event = self._make_set_unread_flag_event(
            thread_id, user_id, folder, unread_status
        )
        self.engine.publish_status((user_id,), event)

    def publish_thread_setting_updated_event(
        self, member_ids, thread_id: int, settings
    ):
        msg = {
            "thread_id": thread_id,
            "settings": settings,
        }
        event = {"event_type": "update_settings", "body": msg}
        self.engine.publish_status(member_ids, event)

    def publish_role_changed_event(self, member_ids, body):
        event = {"event_type": "authorize", "body": body}
        self.engine.publish_status(member_ids, event)

    def _make_set_unread_flag_event(
        self, thread_id: int, user_id: str, folder: str, unread_status: bool
    ):
        msg = {
            "thread_id": thread_id,
            "user_id": user_id,
            "mark_unread": unread_status,
            "folder": folder,
        }
        event = {"event_type": "mark_unread_thread", "body": msg}
        return event
