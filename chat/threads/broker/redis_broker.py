from logging import Logger
from typing import List, Optional

from redis import Redis

from chat import constant
from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat.dist_lock import DistLockManager
from chat.participants.model import ParticipantModel
from chat.utils.common import now


class ThreadRedisBroker(object):
    """
    Updates thread scores in folders and tracks active members.
    Each thread will have a score in a folder. Thread with higher score will be
    positioned at higher position.

    A pinned thread has much higher score, so it will stay on top:
        score = timestamp * 1000
    """

    # FIXME: will we have overflow problem ? timestamp + 1000 years

    def __init__(
        self,
        config: Settings,
        log: Logger,
        engine: RedisConnection,
        lock_manager: DistLockManager,
    ):
        self.config = config.redis_pcc
        self.log = log
        self.engine = engine
        self.tsf: str = self.config.thread_score_format
        """Thread score key format. """

        self.tfsf: str = self.tsf + "_{folder}"
        """Thread folder score key format. """

        self.msf = self.config.message_score_format
        """Message score key format."""

        self.__pin_count_key_fmt = (
            self.tsf + "_" + constant.FOLDER_PIN + "_{folder}"
        )
        """Redis key format to track number of pinned threads in a folder. """

        self.__pin_count_default = self.tsf + "_" + constant.FOLDER_PIN
        """Redis key to store number of pinned threads in default folder."""

        self.dnfrf = self.config.direct_n_f_rate_format
        self.super_group_set_key = self.config.super_group_list_key
        self.SECONDS_PER_DAY = 86400
        self.GMT = 7
        self.member_key = self.config.thread_member_format
        self._limit_pin = self.config.limit_pin_thread
        self.bad_comment_key = self.config.bad_comment_key
        self.member_key = self.config.thread_member_format

        self.lock_manager = lock_manager

    def get_ordered_thread_list(
        self, user_id: str, timestamp=None, limit: int = 20, folder=None
    ) -> List[int]:
        if limit is None:
            # both limit and num parameter must be set in zrevrangebyscore
            limit = 20

        folder_score_key = self._folder_score_key(user_id, folder)
        if timestamp:
            last_timestamp = timestamp
            timestamp = self.engine.client.zscore(folder_score_key, timestamp)
        if timestamp is None or timestamp == 0:
            last_timestamp = None
            timestamp = "+inf"
        result = [
            int(r)
            for r in self.engine.client.zrevrangebyscore(
                folder_score_key, timestamp, "-inf", start=0, num=limit
            )
        ]
        try:
            if last_timestamp:
                result.remove(last_timestamp)
        except ValueError:
            pass
        return result

    def get_di_nf_rlimit(self, user_id):
        return self.engine.client.get(self.dnfrf.format(user_id=user_id))

    def incr_di_nf_rlimit(self, user_id):
        return self.engine.client.incr(self.dnfrf.format(user_id=user_id))

    def set_di_nf_rlimit(self, user_id):
        # now = datetime.datetime.utcnow() + datetime.timedelta(self.GMT)
        # expire = self.SECONDS_PER_DAY - int(
        #     now.timestamp() -
        #     datetime.datetime(now.year, now.month, now.day).timestamp())
        expire = 60 * 60
        self.engine.client.expire(self.dnfrf.format(user_id=user_id), expire)

    def remove_thread_scores_for_users(
        self, thread_id: int, parts: List[ParticipantModel]
    ):
        pipeline = self.engine.client.pipeline()
        for part in parts:
            user_id = part.user_id
            folder = part.folder
            self._remove_thread(pipeline, user_id, thread_id, folder)

        pipeline.execute()

    def get_thread_score(self, user_id: str, thread_id: int, folder: str):
        key = self._folder_score_key(user_id, folder)
        return self.engine.client.zscore(key, thread_id)

    def set_thread_score(
        self,
        user_id: str,
        thread_id: int,
        folder: str,
        score: Optional[int] = None,
        set_default_folder=True,
    ):
        """Sets thread score in a folder."""

        if score is None:
            score = now()

        pipeline = self.engine.client.pipeline()
        self._set_thread_score_in_folder(
            pipeline, user_id, thread_id, score, folder
        )

        if set_default_folder and folder != constant.FOLDER_DEFAULT:
            self._set_thread_score_in_folder(
                pipeline, user_id, thread_id, score, constant.FOLDER_DEFAULT
            )

        pipeline.execute()

    def remove_thread_score(
        self,
        user_id: str,
        thread_id: int,
        folder: str,
        remove_from_default=True,
    ):
        pipeline = self.engine.client.pipeline()
        self._remove_thread_in_folder(pipeline, user_id, thread_id, folder)

        if remove_from_default and folder != constant.FOLDER_DEFAULT:
            self._remove_thread_in_folder(
                pipeline, user_id, thread_id, constant.FOLDER_DEFAULT
            )

        pipeline.execute()

    def add_members(self, thread_id: int, user_ids: List[str]):
        pipeline = self.engine.client.pipeline()

        key = self.member_key.format(thread_id=thread_id)
        for u in user_ids:
            pipeline.zadd(key, {u: now()})

        pipeline.execute()

    def remove_members(self, thread_id: int, user_ids: List[str]):
        """Removes members from a given thread."""
        if not isinstance(user_ids, list):
            user_ids = [user_ids]

        key = self.member_key.format(thread_id=thread_id)
        self.engine.client.zrem(key, *user_ids)

    def add_super_group(self, thread_id):
        self.engine.client.sadd(self.super_group_set_key, thread_id)

    def can_pin(self, user_id: str, folder):
        """Checks if an user can pin more thread to a folder."""
        lock_key = self._lock_pin_count_key(user_id, folder)
        with self.lock_manager.get_lock(lock_key):
            pin_count = self.engine.client.zcount(
                self._pin_count_key(user_id, folder),
                "-inf",
                "+inf",
            )
            return pin_count < self._limit_pin

        return False

    def get_pin(self, user_id: str, folder=constant.FOLDER_DEFAULT):
        lock_key = self._lock_pin_count_key(user_id, folder)
        with self.lock_manager.get_lock(lock_key):
            key = self._pin_count_key(user_id, folder)
            result = [
                int(r)
                for r in self.engine.client.zrevrangebyscore(
                    key, "+inf", "-inf"
                )
            ]
            return result

    def pin_thread(
        self,
        user_id: str,
        thread_id: int,
        pinned_at: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        """Pins a thread in a folder."""

        pin_score = pinned_at * 1000

        pipeline = self.engine.client.pipeline()

        self._set_thread_score_in_folder(
            pipeline, user_id, thread_id, pin_score, folder
        )
        self._add_thread_to_pinned_threads_set(
            pipeline, user_id, thread_id, pin_score, folder
        )

        pipeline.execute()

    def unpin_thread(
        self,
        user_id: str,
        thread_id: int,
        old_score: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        """Unpins a thread in a folder."""

        pipe = self.engine.client.pipeline()

        pipe.zadd(
            self._folder_score_key(user_id, folder),
            {str(thread_id): old_score},
        )

        pipe.zrem(
            self._pin_count_key(user_id, folder),
            thread_id,
        )
        pipe.execute()

    def remove_pin_count(
        self,
        user_id: str,
        thread_ids: List[int],
        folder=constant.FOLDER_DEFAULT,
    ):
        pipe = self.engine.client.pipeline()
        for thread_id in thread_ids:
            pipe.zrem(
                self._pin_count_key(user_id, folder),
                thread_id,
            )
        pipe.execute()

    def set_thread_scores_for_users(
        self,
        thread_id,
        parts: List[ParticipantModel],
        score: Optional[int] = None,
        set_default_folder=True,
    ):
        """Updates thread scores for list of thread members.
        If score is None, thread score will be current timestamp.

        """
        if score is None:
            score = now()

        pipe = self.engine.client.pipeline()
        for part in parts:
            folder = part.folder
            user_id = part.user_id

            self._set_thread_score_in_folder(
                pipe, user_id, thread_id, score, folder
            )
            if set_default_folder and folder != constant.FOLDER_DEFAULT:
                self._set_thread_score_in_folder(
                    pipe, user_id, thread_id, score, constant.FOLDER_DEFAULT
                )

        pipe.execute()

    def _folder_score_key(
        self, user_id: str, folder: str = constant.FOLDER_DEFAULT
    ):
        """Returns the redis key used to rank thread in a folder.
        Default is is default folder."""
        if folder is None or folder == constant.FOLDER_DEFAULT:
            return self.tsf.format(user_id=user_id)

        return self.tfsf.format(user_id=user_id, folder=folder)

    def active_member_count(self, thread_id: int):
        """Returns number of active members in a thread in last 10 minutes."""
        less_than_10_min = now() - 60 * 1000 * 10
        key = self.member_key.format(thread_id=thread_id)
        return self.engine.client.zcount(key, less_than_10_min, "+inf")

    def remove_folder(self, user_id: str, folder: str):
        self.engine.client.delete(self._folder_score_key(user_id, folder))
        self.engine.client.delete(self._pin_count_key(user_id, folder))

    def _pin_count_key(
        self, user_id: str, folder: str = constant.FOLDER_DEFAULT
    ):
        if folder == constant.FOLDER_DEFAULT:
            return self.__pin_count_default.format(user_id=user_id)
        else:
            return self.__pin_count_key_fmt.format(
                folder=folder, user_id=user_id
            )

    def _lock_pin_count_key(self, user_id: str, folder: str):
        return f"pin_{user_id}_{folder}"

    def _add_thread_to_folder(
        self,
        redis: Redis,
        user_id: str,
        thread_id: int,
        score: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        self._set_thread_score_in_folder(
            redis, user_id, thread_id, score, folder
        )
        if folder != constant.FOLDER_DEFAULT:
            self._set_thread_score_in_folder(
                redis, user_id, thread_id, score, constant.FOLDER_DEFAULT
            )

    def _remove_thread(
        self, redis: Redis, user_id: str, thread_id: int, thread_folder: str
    ):
        self._remove_thread_in_folder(redis, user_id, thread_id, thread_folder)
        self._remove_thread_in_folder(
            redis, user_id, thread_id, constant.FOLDER_DEFAULT
        )

    def _remove_thread_in_folder(
        self, redis: Redis, user_id: str, thread_id: int, folder: str
    ):
        self._remove_thread_score_in_folder(redis, user_id, thread_id, folder)
        self._remove_thread_from_pinned_threads_set(
            redis, user_id, thread_id, folder
        )

    def _set_thread_score_in_folder(
        self,
        redis: Redis,
        user_id: str,
        thread_id: int,
        score: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        redis.zadd(
            self._folder_score_key(user_id, folder),
            {str(thread_id): score},  # type: ignore
        )

    def _remove_thread_score_in_folder(
        self,
        redis: Redis,
        user_id: str,
        thread_id: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        redis.zrem(
            self._folder_score_key(user_id, folder),
            thread_id,
        )

    def _remove_thread_from_pinned_threads_set(
        self,
        redis: Redis,
        user_id: str,
        thread_id: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        redis.zrem(
            self._pin_count_key(user_id, folder),
            thread_id,
        )

    def _add_thread_to_pinned_threads_set(
        self,
        redis: Redis,
        user_id: str,
        thread_id: int,
        score: int,
        folder=constant.FOLDER_DEFAULT,
    ):
        redis.zadd(
            self._pin_count_key(user_id, folder),
            {str(thread_id): score},
        )
