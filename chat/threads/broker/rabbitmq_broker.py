import time
from logging import Logger
from typing import Dict
from uuid import uuid4

import pika

from chat import constant
from chat.broker import BaseRabbitmqBroker
from chat.config import Settings
from chat.publishers.rabbitmq import AmqpProducer
from chat.utils.json import json_dumps


class ThreadRabbitBroker(BaseRabbitmqBroker):
    def __init__(self, config: Settings, log: Logger, engine: AmqpProducer):
        super().__init__(config, log, engine)
        self.thread_queue_cfg = config.rabbitmq.thread_event_queue

    def publish_thread_event(
        self,
        event_type: str,
        body: Dict,
        priority: int = constant.AMQP_PRIORITY_MEDIUM,
    ):
        """Publishes events related to thread."""
        new_uuid = str(uuid4())
        event_at = int(time.time() * 1000)
        message = {
            "event_type": event_type,
            "body": body,
            "uuid": new_uuid,
            "version": "3",
            "event_at": event_at,
        }
        if self.engine.publish(
            self.thread_queue_cfg.exchange_name,
            self.thread_queue_cfg.routing_key,
            json_dumps(message),
            pika.BasicProperties(
                content_type="application/json",
                delivery_mode=1,
                priority=priority,
            ),
            mandatory=True,
        ):
            self.log.info(f"[AMQP] Publisher thread event {new_uuid}")
            return new_uuid

    def leave_group(self, body):
        return self.publish_message("leave_group", body)
