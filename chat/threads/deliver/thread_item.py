import datetime

import falcon

from chat.app import MyApp
from chat.app.resource import UserAuthResource
from chat.exception import (
    InternalServerError,
    InvalidParameters,
    PermissionDenied,
    ThreadNotFound,
    UserSecretFeatureDisabled,
)
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import (
    SchemaEdit,
    SchemaMoveFolder,
    SchemaOutput,
)
from chat.threads.usecase import ThreadUsecases


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadItemResource(UserAuthResource):
    def __init__(
        self,
        app: MyApp,
        usecases: ThreadUsecases,
    ):
        self.config = app.conf
        self.log = app.log
        self.usecases = usecases

    @falcon.before(load_request_body)
    def on_patch(self, req: falcon.Request, resp: falcon.Response, thread_id):
        raw = req.context.body
        raw["thread_id"] = thread_id
        body, errors = make_input(SchemaEdit, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        thread = self.usecases.edit_thread.edit_group(
            body.thread_id, user_id, body.dict()
        )

        if not thread:
            raise ThreadNotFound()

        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadItemFolderResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        raw = req.context.body
        raw["thread_id"] = thread_id
        body, errors = make_input(SchemaMoveFolder, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        if body.pass_code:
            raise UserSecretFeatureDisabled()

        user_id = self.user_id
        self.usecase.move_to_folder(
            body.thread_id, user_id, body.to_folder, body.pass_code
        )
        resp.status = falcon.HTTP_200


class ThreadItemClearHistoryResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.leave_thread

    def on_delete(self, req: falcon.Request, resp: falcon.Response, thread_id):
        try:
            datetime.datetime.fromtimestamp(thread_id / 1000)
        except Exception:
            resp.media = {"message": "thread_id not in timestamp miliseconds"}
            resp.status = falcon.HTTP_400
            return

        user_id = self.user_id
        self.usecase.clear_history(user_id, thread_id)
        resp.status = falcon.HTTP_200


class ThreadItemPinResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pin_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        try:
            datetime.datetime.fromtimestamp(thread_id / 1000)
        except Exception:
            resp.media = {"message": "thread_id not in timestamp miliseconds"}
            resp.status = falcon.HTTP_400
            return

        user_id = self.user_id
        self.usecase.pin(thread_id, user_id)
        resp.status = falcon.HTTP_200


class ThreadItemUnpinResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.pin_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        try:
            datetime.datetime.fromtimestamp(thread_id / 1000)
        except Exception:
            resp.media = {"message": "thread_id not in timestamp miliseconds"}
            resp.status = falcon.HTTP_400
            return

        user_id = self.user_id
        self.usecase.unpin(thread_id, user_id)
        resp.status = falcon.HTTP_200


class ThreadItemClearConversationResource(UserAuthResource):
    def __init__(self, app: MyApp, usecases: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecases.leave_thread

    def on_delete(self, req: falcon.Request, resp: falcon.Response, thread_id):
        try:
            datetime.datetime.fromtimestamp(thread_id / 1000)
        except Exception:
            resp.media = {"message": "thread_id not in timestamp miliseconds"}
            resp.status = falcon.HTTP_400
            return

        # FIXME: why this?
        raise PermissionDenied()  # no more can delete
