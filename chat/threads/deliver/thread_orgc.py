import falcon

from chat.app.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.constant import CALL_SOURCE_API, GROUP_LEVEL_PRIVATE
from chat.exception import (
    CantCreateThread,
    InternalServerError,
    InvalidParameters,
)
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import SchemaOrgcGroupCreate, SchemaOutput
from chat.threads.usecase import ThreadUsecases


def make_output_thread(thread):
    after_normalize, errors = make_output(SchemaOutput, thread)
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadOrgcGroupResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.create_thread

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body

        body, errors = make_input(SchemaOrgcGroupCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        thread = self.usecase.create_group_orgc(
            body.org_cs,
            body.org_type,
            self.user_id,
            body.name,
            body.avatar,
            group_level=GROUP_LEVEL_PRIVATE,
            group_type="group",
            workspace_id=self.workspace_id,
            call_source=CALL_SOURCE_API,
        )
        if not thread:
            raise CantCreateThread()
        else:
            result = make_output_thread(thread)
            resp.media = {"data": result}
            resp.status = falcon.HTTP_200
