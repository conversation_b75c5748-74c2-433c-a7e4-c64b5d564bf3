import falcon
from falcon import Request, Response

from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import InternalServiceResource
from chat.exception import InvalidParameters
from chat.hooks import load_request_body
from chat.model import make_input
from chat.threads.model.api import SchemaDeactivateUser, SchemaReactivateUser
from chat.threads.usecase import ThreadUsecases


class DeactivateUserResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.member_activation

    @falcon.before(load_request_body)
    def on_post(self, req: Request, resp: Response):
        raw = req.context.body
        body, errors = make_input(SchemaDeactivateUser, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        self.usecase.deactivate(body.user_id, body.workspace_id)

        resp.media = {"data": {"succeed": True}}
        resp.status = falcon.HTTP_200


class ReactivateUserResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.member_activation

    @falcon.before(load_request_body)
    def on_post(self, req: Request, resp: Response):
        raw = req.context.body
        body, errors = make_input(SchemaReactivateUser, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        self.usecase.reactivate(body.user_id, body.workspace_id)

        resp.media = {"data": {"succeed": True}}
        resp.status = falcon.HTTP_200
