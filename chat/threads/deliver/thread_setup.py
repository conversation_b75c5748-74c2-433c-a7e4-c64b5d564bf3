import falcon

from chat.app import <PERSON>A<PERSON>, UserAuthResource
from chat.exception import InternalServerError, InvalidParameters
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import (
    SchemaOutput,
    SchemaPrivacy,
    SchemaSetAutoDeleteMessage,
)
from chat.threads.usecase import ThreadUsecases


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadPrivacyResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread
        self.PAGE_SIZE = self.config.page_size
        self.NOT_ALLOW_ZERO_PART = ["group", "direct"]

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaPrivacy, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        if body.action == "public":
            thread = self.usecase.make_public(body.thread_id, user_id)
        else:
            thread = self.usecase.make_private(body.thread_id, user_id)

        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadTogglePrivacyResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        user_id = self.user_id
        thread = self.usecase.toggle_privacy(thread_id, user_id)
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadToggleOnlyAdminCanAddMemberSetting(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        user_id = self.user_id
        thread = self.usecase.toggle_only_admin_can_add_member_setting(
            thread_id, user_id
        )
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadToggleOnlyAdminCanUpdateInfo(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        user_id = self.user_id
        thread = self.usecase.toggle_only_admin_can_update_info_setting(
            thread_id, user_id
        )
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadToggleMuteAllResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        user_id = self.user_id
        thread = self.usecase.toggle_mute_all(thread_id, user_id)
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadToggleMuteAllSubthreadResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    def on_post(self, req: falcon.Request, resp: falcon.Response, thread_id):
        user_id = self.user_id
        thread = self.usecase.toggle_mute_all_subthread(thread_id, user_id)
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadToggleNotifyResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread
        self.PAGE_SIZE = self.config.page_size
        self.NOT_ALLOW_ZERO_PART = ["group", "direct"]

    def on_post(
        self,
        req: falcon.Request,
        resp: falcon.Response,
        thread_id: int,
    ):
        user_id = self.user_id
        thread = self.usecase.toggle_notify(thread_id, user_id)
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class ThreadSetAutoDeleteMessage(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id

        raw = req.context.body
        body, errors = make_input(SchemaSetAutoDeleteMessage, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        thread = self.usecase.set_auto_delete_message(
            body.thread_id, user_id, body.day_num
        )
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200


class SubThreadToggleNotifyResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.setup_thread
        self.PAGE_SIZE = self.config.page_size
        self.NOT_ALLOW_ZERO_PART = ["group", "direct"]

    def on_post(
        self,
        req: falcon.Request,
        resp: falcon.Response,
        thread_id,
        sub_thread_id,
    ):
        user_id = self.user_id
        thread = self.usecase.toggle_subthread_notify(
            thread_id, sub_thread_id, user_id
        )
        result = make_output_thread(thread)
        resp.media = {"data": result}
        resp.status = falcon.HTTP_200
