import falcon
from falcon import Request, Response

from chat.app.app import <PERSON><PERSON><PERSON>
from chat.app.resource import UserAuthResource
from chat.threads.usecase import ThreadUsecases


class DisbandGroupResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.disband_group

    def on_post(self, req: Request, resp: Response, thread_id):
        user_id = self.user_id

        self.usecase.disband_group(user_id, thread_id)

        resp.media = {"data": {"succeed": True}}
        resp.status = falcon.HTTP_200
