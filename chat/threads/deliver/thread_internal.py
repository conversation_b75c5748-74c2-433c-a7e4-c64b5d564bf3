import falcon

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource
from chat.constant import GROUP_LEVEL_PRIVATE
from chat.exception import (
    CantCreateThread,
    InternalServerError,
    InvalidParameters,
)
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import (
    SchemaAutoJoinOrLeave,
    SchemaCreateAssociate,
    SchemaOutput,
)
from chat.threads.usecase import ThreadUsecases


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadInternalAssociateResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        workspace_id = self.workspace_id
        body, errors = make_input(SchemaCreateAssociate, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)

        p_thread = self.usecase.create_thread.create_associate_group(
            body.creator,
            body.name,
            body.associate_link,
            body.avatar,
            group_level=GROUP_LEVEL_PRIVATE,
            group_type="group",
            workspace_id=workspace_id,
            tmp_text=body.text,
        )
        if not p_thread:
            raise CantCreateThread()
        else:
            result = make_output_thread(p_thread)
            resp.media = {"data": result}
            resp.status = falcon.HTTP_200
            return


class ThreadInternalAssociateItemResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase

    def on_patch(self, req: falcon.Request, resp: falcon.Response, thread_id):
        body = req.media
        workspace_id = body["workspace_id"]
        clear_link = body.get("clear_link", False)

        self.usecase.edit_thread.update_associate_group(
            thread_id, workspace_id, clear_link
        )
        resp.media = {"data": "Oke"}
        resp.status = falcon.HTTP_200


class ThreadInternalAutoResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaAutoJoinOrLeave, raw)
        if errors:
            self.log.error(errors)
            raise InvalidParameters(error_details=errors)
        if body.thread_id:
            collab_id = ""
            thread_id = int(body.thread_id)
        elif body.collab_id:
            collab_id = str(body.collab_id)
            thread_id = 0
        else:
            raise ValueError("Either thread_id or collab_id must be set")

        if body.is_join():
            self.log.info("Auto body %s", body.dict())
            self.usecase.join_thread.join_by_service(
                body.participant_ids, thread_id, collab_id, body.get_data_source()
            )
        else:
            self.usecase.leave_thread.leave_by_service(
                body.participant_ids, thread_id, collab_id, body.get_data_source()
            )
        resp.media = {"data": "Oke"}
        resp.status = falcon.HTTP_200
        return