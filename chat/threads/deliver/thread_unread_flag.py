import falcon

from chat.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.exception import InvalidParameters
from chat.hooks import load_request_body
from chat.model import make_input
from chat.threads.model.api import SchemaMarkReadFlagToThread
from chat.threads.usecase import ThreadUsecases


class SetThreadUnreadFlagResource(UserAuthResource):
    def __init__(
        self,
        app: MyApp,
        usecases: ThreadUsecases,
    ):
        self.config = app.conf
        self.log = app.log
        self.usecases = usecases

    @falcon.before(load_request_body)
    def on_post(
        self, req: falcon.Request, resp: falcon.Response, thread_id: int
    ):
        raw = req.context.body
        raw["thread_id"] = thread_id
        body, errors = make_input(SchemaMarkReadFlagToThread, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        user_id = self.user_id
        self.usecases.unread_thread.set_unread_flag(
            user_id, body.thread_id, body.unread
        )

        resp.media = {"data": {"messsage": "ok"}}
        resp.status = falcon.HTTP_200
