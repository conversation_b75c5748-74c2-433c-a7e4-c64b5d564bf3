from typing import List

import falcon

from chat import constant
from chat.app import <PERSON>A<PERSON>, UserAuthResource
from chat.exception import (
    CantCreateThread,
    InternalServerError,
    InvalidParameters,
)
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import (
    SchemaGroupCreate,
    SchemaCreate,
    SchemaOutput,
)
from chat.threads.usecase import ThreadUsecases
from chat import dto


def make_output_threads(threads):
    return [make_output_thread(t) for t in threads if t is not None]


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase
        self.PAGE_SIZE = self.config.page_size
        self.NOT_ALLOW_ZERO_PART = ["group", "direct"]

    def remove_myself(self, parts):
        try:
            parts.remove(self.user_id)
        except ValueError:
            pass

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        workspace_id = self.workspace_id

        raw = req.context.body
        raw["creator"] = user_id
        raw["role"] = self.role
        raw["workspace_id"] = workspace_id if workspace_id else ""

        body, errors = make_input(SchemaCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        part_ids: List = body.participant_ids  # type: ignore
        action_type = body.type

        if action_type == "group":
            name = body.name
            avatar = body.avatar
            group_level_default = 0
            # if action_type == "super_group":
            #     if self.role != constant.SERVICE_ROLE:
            #         resp.status = falcon.HTTP_403
            #         resp.media = {
            #             "message": self.response_messages.thread_cant_create  # noqa
            #         }
            #         return
            #     group_level_default = 1
            thread = self.usecase.create_thread.create_group(
                part_ids,
                user_id,
                name,
                avatar,
                group_level=group_level_default,
                group_type=action_type,
                workspace_id=workspace_id,
                collab_id=body.collab_id,
                source_thread_ids=body.source_thread_ids,
                member_file_id=body.member_file_id,
                call_source=constant.CALL_SOURCE_API,
            )
            if not thread:
                raise CantCreateThread()
            else:
                result = make_output_thread(thread)
                resp.media = {"data": result}
                resp.status = falcon.HTTP_200
                return
        elif action_type == "direct":
            partner_id = part_ids[0]  # type: ignore
            bypass_limit = self.role == constant.SERVICE_ROLE
            pass_code = body.pass_code

            if body.has_system:
                thread = self.usecase.create_thread.create_system_direct(
                    user_id, partner_id, actor_id=user_id
                )
            else:
                thread = self.usecase.create_thread.create_direct(
                    partner_id,
                    user_id,
                    source=body.source,
                    contact_name=body.contact_name,
                    bypass_limit=bypass_limit,
                    pass_code=pass_code,
                    workspace_id=body.workspace_id,
                )
            if not thread:
                raise CantCreateThread()
            else:
                result = make_output_thread(thread)
                resp.media = {"data": result}
                resp.status = falcon.HTTP_200


class ThreadGroupResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase
        self.PAGE_SIZE = self.config.page_size
        self.NOT_ALLOW_ZERO_PART = ["group", "direct"]

    def remove_myself(self, parts):
        try:
            parts.remove(self.user_id)
        except ValueError:
            pass

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        workspace_id = self.workspace_id

        raw = req.context.body
        raw["creator_id"] = user_id
        raw["workspace_id"] = workspace_id if workspace_id else ""

        body, errors = make_input(SchemaGroupCreate, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        group_info = dto.GroupInfo(
            creator_id=body.creator_id,
            workspace_id=body.workspace_id,
            avatar=body.avatar,
            name=body.name,
        )

        member_source = dto.MemberSource(
            participant_ids=list(body.participant_ids),
            member_file_id=body.member_file_id,
            source_thread_ids=list(body.source_thread_ids),  # type: ignore
            orgcs=[dto.OrgcObject(**orgc.dict()) for orgc in body.orgcs],
        )

        thread = self.usecase.create_thread.create_group_only(
            group_info,
            member_source,
        )
        if not thread:
            raise CantCreateThread()
        else:
            result = make_output_thread(thread)
            resp.media = {"data": result}
            resp.status = falcon.HTTP_200
            return
