from falcon import Request, Response

from chat.app import MyApp, UserAuthResource
from chat.exception import InternalServerError
from chat.model import make_output
from chat.threads.model.api import SchemaOutput
from chat.threads.usecase import ThreadUsecases


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class SubThreadResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.create_thread

    def on_post(
        self, req: Request, resp: Response, thread_id: int, message_id: int
    ):
        thread = self.usecase.create_sub_thread(
            thread_id, message_id, self.user_id, partner_ids=[]
        )
        resp.media = {"data": make_output_thread(thread)}
