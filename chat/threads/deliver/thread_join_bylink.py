import falcon

from chat.app.app import MyApp
from chat.app.resource import User<PERSON>uthResource
from chat.exception import InternalServerError, ThreadNotFound
from chat.model import make_output
from chat.threads.model.api import SchemaOutput
from chat.threads.usecase import ThreadUsecases


def make_output_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


def make_output_public_thread(p_thread):
    p_thread_d = p_thread.participant.dict()
    p_thread_d.update(p_thread.thread.dict())
    after_normalize, errors = make_output(
        SchemaOutput,
        p_thread_d,
    )
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadJoinByLinkResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.join_thread

    def on_get(self, req: falcon.Request, resp: falcon.Response, link: str):
        user_id = self.user_id
        if not user_id:
            thread = self.usecase.get_public_thread_by_link(link)
            if not thread:
                raise ThreadNotFound()

            result = make_output_public_thread(thread)
            resp.media = {"data": result}
            resp.status = falcon.HTTP_200
            return
        else:
            thread = self.usecase.join_by_link(user_id, link)
            if not thread:
                raise ThreadNotFound()

            result = make_output_thread(thread)
            resp.media = {"data": result}
            resp.status = falcon.HTTP_200
            return
