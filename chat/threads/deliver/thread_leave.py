import falcon

from chat.app.app import MyApp
from chat.app.resource import InternalServiceResource, UserAuthResource
from chat.exception import InternalServerError, InvalidParameters
from chat.hooks import load_request_body
from chat.model import make_input, make_output
from chat.threads.model.api import (
    SchemaLeave,
    SchemaLeaveAllGroup,
    SchemaOutputPublic,
)
from chat.threads.usecase import ThreadUsecases


def make_output_public_thread(thread):
    after_normalize, errors = make_output(SchemaOutputPublic, thread)
    if errors:
        raise InternalServerError(error_details=errors)
    return after_normalize.dict()


class ThreadLeaveResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.leave_thread

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        user_id = self.user_id
        raw = req.context.body
        body, errors = make_input(SchemaLeave, raw)
        if errors:
            self.log.warning(errors)
            raise InvalidParameters(error_details=errors)

        # disable clear_msg when leave
        self.usecase.leave_group(
            body.thread_id, user_id, True, clear_msg=False
        )
        resp.status = falcon.HTTP_200


class ThreadLeaveAllGroupResource(InternalServiceResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.leave_thread

    @falcon.before(load_request_body)
    def on_post(self, req: falcon.Request, resp: falcon.Response):
        raw = req.context.body
        body, errors = make_input(SchemaLeaveAllGroup, raw)
        if errors:
            raise InvalidParameters(error_details=errors)

        self.usecase.leave_all_groups(body.user_id, body.workspace_id)
        resp.status = falcon.HTTP_200
