import falcon
from falcon import Request, Response

from chat.app import <PERSON><PERSON><PERSON>, UserAuthResource
from chat.model import make_input
from chat.threads.model.api import SchemaPin
from chat.threads.usecase import ThreadUsecases


class ThreadPinResource(UserAuthResource):
    def __init__(self, app: MyApp, usecase: ThreadUsecases):
        self.config = app.conf
        self.log = app.log
        self.usecase = usecase.pin_thread

    def on_post(self, req: Request, resp: Response, thread_id):
        folder = req.get_param("folder", "")
        params = {"thread_id": thread_id}
        parse_params, errors = make_input(SchemaPin, params)
        if errors:
            self.log.warning(errors)
            resp.status = falcon.HTTP_400
            resp.media = {"message": errors}
            return
        user_id = self.user_id
        self.usecase.pin(parse_params.thread_id, user_id, folder)
        resp.status = falcon.HTTP_200
        resp.media = {"data": {"message": "ok"}}

    def on_delete(self, req: falcon.Request, resp: falcon.Response, thread_id):
        folder = req.get_param("folder", "")
        user_id = self.user_id
        params = {"thread_id": thread_id}
        parse_params, errors = make_input(SchemaPin, params)
        if errors:
            self.log.warning(errors)
            resp.status = falcon.HTTP_400
            resp.media = {"message": errors}
            return
        self.usecase.unpin(parse_params.thread_id, user_id, folder)
        resp.status = falcon.HTTP_200
        resp.media = {"data": {"message": "ok"}}
