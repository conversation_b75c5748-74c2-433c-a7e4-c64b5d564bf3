from dependency_injector import containers, providers

from chat.app import MyApp
from chat.threads.deliver import (
    DeactivateUserResource,
    DisbandGroupResource,
    ReactivateUserResource,
    SetThreadUnreadFlagResource,
    SubThreadResource,
    ThreadInternalAssociateItemResource,
    ThreadInternalAssociateResource,
    ThreadInternalAutoResource,
    ThreadItemClearConversationResource,
    ThreadItemClearHistoryResource,
    ThreadItemFolderResource,
    ThreadItemResource,
    ThreadJoinByLinkResource,
    ThreadLeaveAllGroupResource,
    ThreadLeaveResource,
    ThreadOrgcGroupResource,
    ThreadPinResource,
    ThreadPrivacyResource,
    ThreadResource,
    ThreadGroupResource,
    ThreadToggleMuteAllResource,
    ThreadToggleMuteAllSubthreadResource,
    ThreadToggleNotifyResource,
    ThreadTogglePrivacyResource,
    SubThreadToggleNotifyResource,
    ThreadSetAutoDeleteMessage,
)
from chat.threads.deliver.thread_setup import (
    ThreadToggleOnlyAdminCanAddMemberSetting,
    ThreadToggleOnlyAdminCanUpdateInfo,
)
from chat.threads.usecase import (
    CreateThreadUseCase,
    DisbandGroupUsecase,
    EditThreadUseCase,
    GetThreadUseCase,
    JoinThreadUseCase,
    LeaveThreadUseCase,
    MemberActivationUsecase,
    PinThreadUseCase,
    SetUnreadFlagToThreadUsecase,
    SetupThreadUsecase,
    ThreadUsecases,
)


class ThreadResourcesContainer(containers.DeclarativeContainer):
    """ThreadResourcesContainer."""

    app = providers.Dependency(MyApp)
    usecases = providers.Dependency(ThreadUsecases)

    threads = providers.Singleton(ThreadResource, app, usecases)
    thread_group = providers.Singleton(ThreadGroupResource, app, usecases)
    thread_item = providers.Singleton(ThreadItemResource, app, usecases)
    toggle_mute_all = providers.Singleton(
        ThreadToggleMuteAllResource, app, usecases
    )

    toggle_mute_all_subthread = providers.Singleton(
        ThreadToggleMuteAllSubthreadResource, app, usecases
    )
    toggle_only_admin_add_member = providers.Singleton(
        ThreadToggleOnlyAdminCanAddMemberSetting, app, usecases
    )
    toggle_only_admin_update_info = providers.Singleton(
        ThreadToggleOnlyAdminCanUpdateInfo, app, usecases
    )
    thread_join_by_link = providers.Singleton(
        ThreadJoinByLinkResource, app, usecases
    )
    thread_leave = providers.Singleton(ThreadLeaveResource, app, usecases)
    leave_all_groups = providers.Singleton(
        ThreadLeaveAllGroupResource, app, usecases
    )
    thread_pin = providers.Singleton(ThreadPinResource, app, usecases)
    thread_privacy = providers.Singleton(ThreadPrivacyResource, app, usecases)
    thread_toggle_privacy = providers.Singleton(
        ThreadTogglePrivacyResource, app, usecases
    )
    thread_toggle_notify = providers.Singleton(
        ThreadToggleNotifyResource, app, usecases
    )
    sub_thread_toggle_notify = providers.Singleton(
        SubThreadToggleNotifyResource, app, usecases
    )

    thread_set_auto_delete_message = providers.Singleton(
        ThreadSetAutoDeleteMessage, app, usecases
    )
    clear_history = providers.Singleton(
        ThreadItemClearHistoryResource, app, usecases
    )
    clear_conversation = providers.Singleton(
        ThreadItemClearConversationResource, app, usecases
    )
    folder = providers.Singleton(ThreadItemFolderResource, app, usecases)
    internal_associate = providers.Singleton(
        ThreadInternalAssociateResource, app, usecases
    )
    internal_associate_item = providers.Singleton(
        ThreadInternalAssociateItemResource, app, usecases
    )
    internal_auto = providers.Singleton(
        ThreadInternalAutoResource, app, usecases
    )
    orgc = providers.Singleton(ThreadOrgcGroupResource, app, usecases)

    disband_group = providers.Singleton(DisbandGroupResource, app, usecases)
    unread_thread = providers.Singleton(
        SetThreadUnreadFlagResource, app, usecases
    )

    sub_thread = providers.Singleton(SubThreadResource, app, usecases)

    deactivate_user = providers.Singleton(
        DeactivateUserResource, app, usecases
    )
    reactivate_user = providers.Singleton(
        ReactivateUserResource, app, usecases
    )


class ThreadContainer(containers.DeclarativeContainer):
    """ThreadContainer."""

    app = providers.Dependency(MyApp)

    create_thread = providers.Singleton(CreateThreadUseCase, app)
    edit_thread = providers.Singleton(EditThreadUseCase, app)
    get_thread = providers.Singleton(GetThreadUseCase, app)
    pin_thread = providers.Singleton(PinThreadUseCase, app)
    leave_thread = providers.Singleton(LeaveThreadUseCase, app)
    setup_thread = providers.Singleton(SetupThreadUsecase, app)
    join_thread = providers.Singleton(JoinThreadUseCase, app)
    disband_group = providers.Singleton(DisbandGroupUsecase, app)
    unread_thread = providers.Singleton(SetUnreadFlagToThreadUsecase, app)

    member_activation = providers.Singleton(MemberActivationUsecase, app)

    # usecases = providers.Dependency(ThreadUsecases)
    usecases = providers.Singleton(
        ThreadUsecases,
        create_thread=create_thread,
        edit_thread=edit_thread,
        get_thread=get_thread,
        pin_thread=pin_thread,
        leave_thread=leave_thread,
        setup_thread=setup_thread,
        join_thread=join_thread,
        disband_group=disband_group,
        unread_thread=unread_thread,
        member_activation=member_activation,
    )

    resources = providers.Container(
        ThreadResourcesContainer, app=app, usecases=usecases
    )
