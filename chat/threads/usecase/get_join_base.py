from chat import constant
from chat.exception import PermissionDenied
from .get_thread_usecase import GetThreadUseCase


class GetJoinThreadBase(GetThreadUseCase):
    def _join_or_rejoin(
        self,
        session,
        thread_id: int,
        user_id: str,
        msg_count=0,
        folder=constant.FOLDER_DEFAULT,
    ):
        rejoin = False
        new_mem = False
        exist_member = self.pt_repo.get_exist_record(
            session, thread_id, user_id
        )
        if exist_member:
            if exist_member.is_banned():
                raise PermissionDenied()

            if exist_member.is_removed:
                rejoin = True
            self.redis_read.update_read(
                thread_id, user_id, exist_member.read_count
            )
        else:
            new_mem = True

        if rejoin:
            self.pt_repo.rejoin(session, thread_id, user_id, msg_count)
        elif new_mem:
            self.pt_repo.create_member(
                session,
                thread_id,
                user_id,
                read_count=msg_count,
                folder=folder,
            )
        return rejoin, new_mem
