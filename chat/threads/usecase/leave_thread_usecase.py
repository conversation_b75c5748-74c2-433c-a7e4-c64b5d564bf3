import time
from typing import Any, Dict, List

from sqlalchemy.orm import Session

from chat import acl, action_type, constant
from chat.connections.gapo_client.membership import CollabGroupRole
from chat.connections.gapo_client.membership import (
    PermissionDenied as MembershipPermissionError,
)
from chat.exception import PermissionDenied, UserNotInThread, ThreadNotFound
from chat.participant_threads.model import ParticipantThreadModel
from chat.participants.model import ParticipantModel
from chat.threads import exception as ThreadError
from chat.threads.model import ThreadModel
from chat.threads.model.task import TaskLeaveGroup
from chat.utils import common

from .get_thread_usecase import GetThreadUseCase


class LeaveThreadUseCase(GetThreadUseCase):
    def leave_all_groups(self, user_id: str, workspace_id):
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_all_group_ids(session, user_id, workspace_id)
            self.log.info(
                f"""leave all group with user_id: {user_id}
                workspace_id: {workspace_id}
                thread_ids: {parts}"""
            )
        for part in parts:
            self.rb_broker.publish_background_task(
                constant.TASK_LEAVE_GROUP,
                TaskLeaveGroup(
                    user_id=user_id,
                    send_action_note=True,
                    thread_id=part.thread_id,
                ).dict(),
            )

    def leave_by_service(
        self, user_ids: List[str], thread_id: int, collab_id: str, leave_type: str
    ):
        if collab_id:
            with self.session_m.get_session() as session:
                thread = self.thread_repo.get_by_collab_id(session, collab_id)
                if not thread:
                    raise ThreadNotFound()

                thread_id = thread.id
        for user_id in user_ids:
            self.leave_group(
                thread_id,
                user_id,
                leave_type=leave_type,
            )

    def leave_group(
        self,
        thread_id: int,
        user_id: str,
        send_action_note=True,  # NOTE: deprecated
        deactivate_only=False,
        clear_msg=False,
        promote_new_owner_if_needed=True,
        leave_type: str = action_type.LEAVE_GROUP,
    ):
        """Remove user from a group. There is no action note will be sent.

        Params:
            thread_id: thread to remove from
            user_id: user to remove
            send_action_note: whether we send a action note to
            other members about this event
            clear_msg: clear all messages created by user.
            Other members in the group
            will see old messages as "Message deleted"
            deactivate_only: only deactivate user from threads
            promote_new_owner_if_needed: Promote new admin
            if the current is the owner of group
        """

        with self.session_m.get_session() as session:
            promote_event = None
            switch_owner_to_id: str = "0"
            new_owner: Dict = dict()

            p_thread = self.get_thread_for_user(
                session,
                thread_id,
                user_id,
            )
            if not p_thread.thread.is_group():
                raise UserNotInThread()

            folder = p_thread.participant.folder
            self._reset_folder(session, folder, user_id, thread_id)

            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_LEAVE_GROUP,
            )
            if not has_permission:
                raise ThreadError.LeaveGroupError(permission_name)

            # need to promote new user if
            # we wan't to deactivate an user
            if deactivate_only:
                promote_new_owner_if_needed = True

            if p_thread.participant.is_owner() and promote_new_owner_if_needed:
                # try to promote other user as owner

                random_members = self.pt_repo.get_members_by_roles(
                    session,
                    thread_id,
                    [constant.ADMIN_ROLE, constant.MEMBER_ROLE],
                    limit=2,
                )

                if random_members:
                    for _member in random_members:
                        switch_owner_to_id = _member.user_id
                        if _member.is_admin():
                            switch_owner_to_id = _member.user_id

                    promote_event = self._promote_as_group_owner(
                        session, switch_owner_to_id, thread_id
                    )
                    promoted_thread = self.get_thread_for_user(
                        session,
                        thread_id,
                        switch_owner_to_id,
                    )
                    new_owner.update(promoted_thread.participant.dict())

            if deactivate_only:
                self.pt_repo.set_deactivate_status(
                    session, user_id, thread_id, is_deactivated=True
                )
            else:
                self.pt_repo.leave_thread(session, thread_id, user_id)
            self.pt_repo.remove_cache(thread_id, user_id)

            if clear_msg:
                p_thread.thread.update_information(user_id)

            self._unpin_thread_in_all_folders(session, user_id, p_thread)

            self.thread_broker.remove_thread_scores_for_users(
                thread_id, [p_thread.participant]
            )
            self.thread_broker.remove_members(thread_id, [user_id])

            # leave from all sub-threads that user is in
            self._remove_member_from_sub_threads(
                session, user_id, thread_id, deactivate_only=deactivate_only
            )
            session.commit()

            if new_owner:
                self.pt_repo.update_cache(new_owner)

            if switch_owner_to_id and switch_owner_to_id != "0":
                self._membership_set_role(
                    actor_id=user_id,
                    receive_id=switch_owner_to_id,
                    thread=p_thread.thread,
                    role=CollabGroupRole.Owner,
                )
            self._membership_remove_member(
                creator_id=user_id,
                remove_user_id=user_id,
                thread_id=thread_id,
            )

            msg_id = self.publish_action_note(
                session,
                p_thread.thread,
                user_id,
                leave_type,
                option={"clear_msg": clear_msg},
                skip_all_action_notes=True,
            )
            lg_body = {"user_id": user_id, "thread_id": thread_id}
            self.rb_broker.leave_group(lg_body)

        member_ids = self._get_active_members(thread_id, p_thread.thread.member_count)
        member_ids += [user_id]
        member_ids = set(member_ids)
        notify_thread_message = {
            "thread_id": thread_id,
            "thread_type": "group",
            "message_to": msg_id,
            "member_ids": [user_id],
            "clear_msg": clear_msg,
        }

        self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)
        if promote_event:
            self.mqtt_broker.publish_role_changed_event(member_ids, promote_event)

    def clear_history(self, user_id: str, thread_id: int):
        notify_thread_message = None
        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)

            if p_thread:
                if p_thread.thread.is_subthread():
                    raise PermissionDenied()

                if p_thread.participant.partner_id in constant.GAPO_BOT_SYSTEM_IDS:
                    raise PermissionDenied()

                count = p_thread.thread.message_count
                if count:
                    self.pt_repo.clear_user(session, thread_id, user_id)
                    self.thread_broker.remove_thread_scores_for_users(
                        thread_id, [p_thread.participant]
                    )

                    notify_thread_message = {
                        "thread_id": thread_id,
                        "thread_type": p_thread.thread.type,
                        "clear_msg": False,
                        "message_to": count,
                        "member_ids": [user_id],
                    }

                self._reset_folder(
                    session, p_thread.participant.folder, user_id, thread_id
                )

                session.commit()
            # else:
            #     raise ThreadError.UserNotInThread()

            session.commit()

        if notify_thread_message:
            self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)
            self.rb_broker.publish_status(
                event_type=constant.EVENT_THREAD_DELETED,
                body=notify_thread_message,
            )

    def clear_conversation_thread(self, user_id: str, thread_id: int):
        notify_thread_message = None
        with self.session_m.get_session() as session:
            # thread = self.thread_repo.get_by_id(
            #     session, thread_id, with_lock=True
            # )
            # self.canDeleteThread(thread)
            p_thread = self._get_participant_thread_lock_for_delete(
                session, thread_id, user_id
            )
            count = p_thread.thread.message_count
            if count:
                members = self.pt_repo.get_members_in_list_threads(session, [thread_id])
                # self.part_repo.clearAll(session, thread_id)
                self.pt_repo.clear_thread_users(session, thread_id, count)
                self.thread_repo.clear_thread(session, thread_id)

                self.thread_broker.remove_thread_scores_for_users(
                    thread_id,
                    members,
                )

                notify_thread_message = {
                    "thread_id": thread_id,
                    "thread_type": "direct",
                    "clear_msg": False,
                    "message_to": count,
                    "member_ids": [m.user_id for m in members],
                }
            session.commit()

        if notify_thread_message:
            self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)
            self.rb_broker.publish_status(
                event_type=constant.EVENT_THREAD_DELETED,
                body=notify_thread_message,
            )

    def _promote_as_group_owner(self, session: Session, receiver_id, thread_id: int):
        self.pt_repo.update_role(session, receiver_id, thread_id, constant.OWNER_ROLE)
        return {"user_id": receiver_id, "role": constant.OWNER_ROLE}

    def _reset_folder(self, session: Session, crr_folder, user_id: str, thread_id: int):
        if common.is_folder_created_by_user(crr_folder):
            self.pt_repo.update_folder(
                session, user_id, thread_id, constant.FOLDER_DEFAULT
            )

    def _membership_remove_member(
        self, creator_id, remove_user_id: str, thread_id: int
    ):
        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_id(session, thread_id)
            if not thread:
                return

        try:
            if thread.collab_id:
                workspace_id = thread.workspace_id

                self.gapo_client.membership.remove_member(
                    caller_id=int(creator_id),
                    collab_id=thread.collab_id,
                    user_id=int(remove_user_id),
                    workspace_id=workspace_id or "",
                )
        except Exception as e:
            if isinstance(e, MembershipPermissionError):
                # maybe some issue here
                # however, it won't affect the output result, so
                # we just log warning here
                self.log.warning(
                    f"[Membership] Remove Member by self leave error, "
                    f"workspace_id: {workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {creator_id}, "
                    f"user_id: {remove_user_id}, "
                    f"detail: {str(e)}",
                )
            else:
                self.log.critical(
                    f"[Membership] Remove Member by self leave error, "
                    f"workspace_id: {workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {creator_id}, "
                    f"user_id: {remove_user_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )

    def _membership_set_role(
        self,
        actor_id: str,
        receive_id: str,
        thread: ThreadModel,
        role: CollabGroupRole,
    ):
        if not thread.collab_id:
            # not a collab group. i.e. Direct chat
            return

        try:
            self.gapo_client.membership.set_member_role(
                caller_id=int(actor_id),
                collab_id=thread.collab_id,
                user_id=int(receive_id),
                roles=[role],
                workspace_id=thread.workspace_id or "",
            )
        except Exception as e:
            # maybe due to slow syncing between chat-service and
            # membership, we may got permission error
            # we will reduce these errors by retry again
            # after some small duration
            if not isinstance(e, MembershipPermissionError):
                self.log.critical(
                    f"[Membership] Auto Set Role error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {actor_id}, "
                    f"user_id: {receive_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )
                return

            # retry
            try:
                time.sleep(0.2)
                self.gapo_client.membership.set_member_role(
                    caller_id=int(actor_id),
                    collab_id=thread.collab_id,
                    user_id=int(receive_id),
                    roles=[role],
                    workspace_id=thread.workspace_id or "",
                )
            except Exception as e:
                self.log.critical(
                    f"[Membership] Auto Set Role error, "
                    f"workspace_id: {thread.workspace_id}, "
                    f"collab_id: {thread.collab_id}, "
                    f"caller_id: {actor_id}, "
                    f"user_id: {receive_id}, "
                    f"detail: {str(e)}",
                    exc_info=True,
                )

    def _unpin_thread_in_all_folders(
        self, session: Session, user_id: str, p_thread: ParticipantThreadModel
    ):
        thread_id = p_thread.participant.thread_id
        is_pinned_in_folder = p_thread.participant.pin_pos > 0
        is_pinned_in_default_folder = p_thread.participant.pin_default_pos > 0
        old_score = self._get_thread_last_update_time(p_thread.thread)

        if is_pinned_in_folder:
            # unpin in its folder
            self._unpin_thread(
                session,
                user_id,
                thread_id,
                p_thread.participant.folder,
                old_score,
            )

        if is_pinned_in_default_folder:
            self._unpin_thread(
                session, user_id, thread_id, constant.FOLDER_DEFAULT, old_score
            )

    def _remove_member_from_sub_threads(
        self,
        session: Session,
        user_id: str,
        parent_thread_id: int,
        deactivate_only=False,
    ):
        parts = self.pt_repo.get_all_sub_threads_for_user(
            session, user_id, parent_thread_id
        )
        for part in parts:
            self._remove_member_from_sub_thread(
                session, part, deactivate_only=deactivate_only
            )
            self.pt_repo.remove_cache(part.thread_id, user_id)

    def _remove_member_from_sub_thread(
        self,
        session: Session,
        part: ParticipantModel,
        deactivate_only=False,
    ):
        if deactivate_only:
            self.pt_repo.set_deactivate_status(
                session, part.user_id, part.thread_id, is_deactivated=True
            )
        else:
            # remove thread from database
            self.pt_repo.leave_thread(session, part.thread_id, part.user_id)

        # remove thread score and data in redis
        self._remove_thread_scores(part.thread_id, [part], constant.FOLDER_SUB_THREAD)

        self.thread_broker.remove_members(part.thread_id, [part.user_id])

        # update member count
        # self.thread_repo.update_thread_member_count(session, thread_id, -1)

        # FIXME: update list of commenters ?

        # send mqtt message to delete thread in client
        notify_thread_message: Dict[str, Any] = {
            "thread_id": part.thread_id,
            "message_to": 1,  # FIXME: what is this for ?
            "member_ids": [part],
            "thread_type": constant.SUB_THREAD_TYPE,
        }
        self.mqtt_broker.publish_thread_deleted_event(**notify_thread_message)

    def _remove_thread_scores(
        self,
        thread_id: int,
        parts: List[ParticipantModel],
        folder: str = constant.FOLDER_DEFAULT,
    ):
        self.thread_broker.remove_thread_scores_for_users(thread_id, parts)

    def disable_thread_reactivate(self, user_id: str):
        with self.session_m.get_session() as session:
            self.log.info(f"Disabling thread reactivation for user_id: {user_id}")
            self.pt_repo.disable_reactivate(session, user_id)