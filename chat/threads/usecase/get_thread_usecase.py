from typing import (
    TYPE_CHECKING,
    Dict,
    Iterable,
    List,
    Optional,
    Tuple,
    Union,
)

from sqlalchemy.orm import Session

from chat.constant import (
    FOLDER_DEFAULT,
    FOLDER_NOT_FRIEND,
    GROUP_THREAD_TYPE,
    SUB_THREAD_TYPE,
    SUPER_GROUP_THREAD_TYPE,
)
from chat.exception import DataNotFound, UserNotInThread
from chat.messages.model.api import SchemaOutput as MessageOutput
from chat.model import make_output
from chat.participant_threads.model import ParticipantThreadModel
from chat.participants.model import (
    ParticipantModel,
)
from chat.threads import exception as ThreadError
from chat.threads.model import ThreadModel
from chat.users.model import PublicProfile
from chat.utils import validator_helper as vh
from chat.utils.common import make_pair_ids

from .base import ThreadUseCase

if TYPE_CHECKING:
    from chat.messages.usecase import MessageUseCases

FOLDER_GET_ALL = set([FOLDER_DEFAULT, FOLDER_NOT_FRIEND])
JOIN_ABLE = (G<PERSON>UP_THREAD_TYPE, SUPER_GROUP_THREAD_TYPE, SUB_THREAD_TYPE)


class GetThreadUseCase(ThreadUseCase):
    def inject_message_usecase(self, usecase: "MessageUseCases"):
        self.message_uscs = usecase

    def get_by_id(
        self, user_id: str, thread_id: int, pass_code=None
    ) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            return self._get_full_thread_for_user(
                session, user_id, thread_id, pass_code=pass_code
            )

    def get_thread_id_by_collab(
        self, collab_id: str, user_id: str, bypass=False
    ) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_collab_id(session, collab_id)
            if not thread:
                raise DataNotFound()
            if bypass:
                part = ParticipantModel.new_sub_participant(user_id, thread.id)
                return ParticipantThreadModel(thread=thread, participant=part)
        return self.get_by_id(user_id, thread.id)

    def get_direct_thread(
        self,
        sender_id: str,
        receiver_id: str,
        session: Session,
        pass_code: Union[str, None] = None,
        bypass: bool = True,
    ) -> Optional[ParticipantThreadModel]:
        pair_ids = make_pair_ids(sender_id, receiver_id)
        part = self.pt_repo.get_thread_by_pair_ids(session, sender_id, pair_ids)
        if part:
            thread = self.thread_repo.get_by_id(session, part.thread_id)
            if thread:
                p_thread = ParticipantThreadModel(
                    thread=thread, participant=part
                )
                self.update_user_threads(session, sender_id, [p_thread])
                return p_thread
        return None

    def get_public_thread_by_link(self, link) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            thread = self._get_thread_by_link_or_raise_error(session, link)
            session.commit()
            part = ParticipantModel.new_sub_participant(
                user_id="", thread_id=thread.id
            )

        return ParticipantThreadModel(thread=thread, participant=part)

    def _get_thread_by_link_or_raise_error(
        self, session: Session, link
    ) -> ThreadModel:
        """Gets thread by link, raise error if not found or thread is not open for
        other users to join."""
        thread = self.thread_repo.get_thread_by_link_code(session, link)
        if not thread:
            raise ThreadError.ThreadNotFoundByLink()

        if thread.is_direct():
            raise ThreadError.ThreadNotFoundByLink()
        return thread

    def _get_group_chat_for_user(
        self, session: Session, thread_id: int, member_id
    ) -> ParticipantThreadModel:
        """
        - Gets group info for an user.
        - Raises error if user isn't in that thread.
        """
        thread = self.thread_repo.get_by_id(session, thread_id)
        if not thread:
            raise UserNotInThread()

        part = self.pt_repo.get_group_for_member(session, thread_id, member_id)
        if not part:
            raise UserNotInThread()

        return ParticipantThreadModel(thread=thread, participant=part)

    def get_all_by_ids(
        self, user_id: str, thread_ids: List[int]
    ) -> List[ParticipantModel]:
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_threads_by_ids(
                session=session,
                user_id=user_id,
                thread_ids=thread_ids,
            )
            if not parts:
                return []
            return parts

    def update_user_threads(
        self,
        session: Session,
        user_id: str,
        p_threads: List[ParticipantThreadModel],
    ):
        if not isinstance(p_threads, (list, set)):
            p_threads = [
                p_threads,
            ]

        infos, part_threads = self._get_user_threads_info(
            session, user_id, p_threads
        )
        for p_thread in p_threads:
            if p_thread.thread.is_direct():
                self._update_direct_thread(p_thread, infos, part_threads)
            else:
                self._update_group_thread(p_thread, infos)
        self.update_sub_threads(session, user_id, p_threads)

    def update_sub_threads(
        self,
        session: Session,
        user_id: str,
        p_threads: List[ParticipantThreadModel],
    ):
        for p_thread in p_threads:
            if not p_thread.thread.is_subthread():
                continue
            self.add_referenced_message_to_sub_thread(
                session, user_id, p_thread
            )

    def add_referenced_message_to_sub_thread(
        self, session: Session, user_id: str, p_thread: ParticipantThreadModel
    ):
        thread_id = p_thread.thread.parent_id
        message_id = p_thread.thread.root_message_id
        if not thread_id or not message_id:
            return

        msg = self.message_uscs.get_message.get_by_id(
            user_id, thread_id, message_id
        )
        if not msg:
            return

        output_message, errors = make_output(MessageOutput, msg.dict())
        if not errors:
            p_thread.thread.referenced_message = output_message.dict()

    def _update_group_thread(
        self, p_thread: ParticipantThreadModel, infos: Dict[str, PublicProfile]
    ):
        if p_thread.participant.last_message:
            sender_id = p_thread.participant.last_message.user_id
            sender_info = infos[sender_id]
            p_thread.participant.last_message.update_sender(sender_info)

        if p_thread.thread.is_associate_department():
            a_id = p_thread.thread.get_associate_id()
            w_id = p_thread.thread.workspace_id
            d_info = self.gapo_client.orgc.get_department_info(w_id, a_id)
            p_thread.thread.update_department(d_info)
        p_thread.thread.update_link(self.chat_domain)

    def _update_direct_thread(
        self,
        p_thread: ParticipantThreadModel,
        infos: Dict[str, PublicProfile],
        part_threads: Dict[int, int],
    ):
        partner_id = p_thread.participant.partner_id
        if partner_id:
            partner_info = infos[partner_id]
            p_thread.participant.update_partner(partner_info)
            if p_thread.participant.partner:
                p_thread.participant.partner.read_count = part_threads[
                    p_thread.thread.id
                ]
                p_thread.thread.name = p_thread.participant.partner.name
                p_thread.thread.avatar = p_thread.participant.partner.avatar

        if p_thread.participant.last_message:
            sender_id = p_thread.participant.last_message.user_id
            sender_info = infos[sender_id]
            p_thread.participant.last_message.update_sender(sender_info)

    def _get_user_threads_info(
        self,
        session: Session,
        user_id: str,
        p_threads: List[ParticipantThreadModel],
    ):
        partner_ids = set()
        user_thread_ids: List[Tuple[str, int]] = list()
        for p_thread in p_threads:
            if p_thread.thread.is_direct():
                partner_id = p_thread.participant.partner_id
                if partner_id:
                    partner_ids.add(partner_id)
                    user_thread_ids.append((partner_id, p_thread.thread.id))
                if p_thread.participant.last_message:
                    sender_id = p_thread.participant.last_message.user_id
                    partner_ids.add(sender_id)
            else:
                if p_thread.participant.last_message:
                    sender_id = p_thread.participant.last_message.user_id
                    partner_ids.add(sender_id)

        if user_thread_ids:
            part_threads = self.pt_repo.get_thread_members_in_list(
                session, user_thread_ids
            )
            part_threads = {
                p_thread.thread.id: p.read_count for p in part_threads
            }
        else:
            part_threads = {}

        if partner_ids:
            part_infos = self._get_profiles(partner_ids)
        else:
            part_infos = {}

        infos: Dict[str, PublicProfile] = {}
        infos.update(part_infos)

        return infos, part_threads

    def _get_active_members(self, thread_id: int, member_count: int):
        if member_count > self._max_member_status:
            return []
        return self.redis_read.get_most_recent_thread_viewers(
            thread_id, self._max_member_status
        )

    def get_thread_for_user(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        """
        - Gets thread for an user.
        - Raises an exception if it's not that thread.
        """

        thread = self.thread_repo.get_by_id(session, thread_id)
        if not thread:
            raise UserNotInThread()

        part = self.pt_repo.get_thread_for_user(session, thread_id, user_id)
        if not part:
            if not thread.is_subthread():
                raise UserNotInThread()

            if not thread.parent_id:
                raise UserNotInThread()

            parent_thread = self.thread_repo.get_by_id(
                session, thread.parent_id
            )
            if not parent_thread:
                raise UserNotInThread()

            parent_part = self.pt_repo.get_thread_for_user(
                session, parent_thread.id, user_id
            )
            if not parent_part:
                raise UserNotInThread()

            part = ParticipantModel.new_sub_participant(user_id, thread_id)
            thread.parent = parent_thread

        return ParticipantThreadModel(thread=thread, participant=part)

    def _get_participant_thread_lock_for_delete(
        self, session: Session, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        thread = self.thread_repo.get_by_id(session, thread_id)
        if not thread:
            raise UserNotInThread()

        if not thread.is_direct():
            raise UserNotInThread()

        part = self.pt_repo.get_thread_for_user(session, thread_id, user_id)
        if not part:
            raise UserNotInThread()

        return ParticipantThreadModel(thread=thread, participant=part)

    def _get_full_thread_for_user(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        pass_code: Optional[str] = None,
        bypass_pass_code=False,
    ) -> ParticipantThreadModel:
        """
        - Gets full thread info for user.
        - Raises error if user not in thread.
        """
        p_thread = self.get_thread_for_user(session, thread_id, user_id)
        is_sub_thread = p_thread.thread.is_subthread()
        is_p_direct_thread = p_thread.thread.is_parent_direct()

        if is_sub_thread and is_p_direct_thread and p_thread.thread.parent_id:
            partner = self.pt_repo.get_partner(
                session, p_thread.thread.parent_id, user_id
            )
            if partner:
                partner_id = partner.user_id
                partner_info = self._get_user(partner_id)
                if partner:
                    p_thread.thread.name = partner_info.get("name")
                    p_thread.thread.avatar = partner_info.get("avatar")

        self.update_user_threads(session, user_id, [p_thread])
        self.after_update(
            session, p_thread.participant.user_id, p_thread.thread.dict()
        )
        return p_thread

    def _get_profiles(self, id_list: Iterable[str]):
        """Gets public profiles of list objects"""
        bot_ids = set()
        user_ids = set()
        for u in id_list:
            if vh.is_bot_id(u):
                bot_ids.add(u)
            else:
                user_ids.add(u)

        users = self.i_storage.get_users(list(user_ids))
        bots = self.i_storage.get_bots(list(bot_ids))
        infos: Dict[str, PublicProfile] = {}
        infos.update(users)
        infos.update(bots)

        return infos

    def after_update(self, session: Session, user_id: str, thread: Dict):
        self.thread_repo.update_cache(thread)
        members = self.pt_repo.get_exist_records(
            session, thread["id"], [user_id]
        )
        for m in members:
            self.pt_repo.update_cache(dict(m))

    def get_all_thread_by_user(
        self, user_id: str
    ) -> List[ParticipantThreadModel]:

        pthreads: List[ParticipantThreadModel] = []

        self.log.info(f"start: {user_id}")
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_all_thread_by_user(session, user_id)
            self.log.info(f"{user_id} found participant: {len(parts)}")

            for part in parts:
                thread = self.thread_repo.get_by_id(session, part.thread_id)
                if not thread:
                    continue
                pthread = ParticipantThreadModel(thread=thread, participant=part)
                pthreads.append(pthread)

        return pthreads