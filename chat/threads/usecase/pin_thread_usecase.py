from chat.constant import (
    FOLDER_DEFAULT,
    FOLDER_NOT_FRIEND,
    FOLDER_SECRET,
    FOLDER_SUB_THREAD,
    FOLDER_UNREAD,
    SUB_THREAD_TYPE,
)
from chat.exception import DataNotFound, PermissionDenied
from chat.threads import exception as ThreadError
from chat.threads.model import ChatThread
from chat.utils.common import now

from .get_thread_usecase import GetThreadUseCase


class PinThreadUseCase(GetThreadUseCase):
    def _verify_pin(self, session, user_id: str, folder: str):
        thread_ids = self.thread_broker.get_pin(user_id, folder)
        pinned_thread_object_ids = self.pt_repo.get_pinned_thread_ids(
            session, user_id, thread_ids, folder
        )

        pinned_thread_ids = [p.thread_id for p in pinned_thread_object_ids]

        remain_thread_ids = set(thread_ids) - set(pinned_thread_ids)
        if remain_thread_ids:
            self.thread_broker.remove_pin_count(
                user_id, list(remain_thread_ids), folder
            )

    def pin(self, thread_id: int, user_id: str, folder: str = FOLDER_DEFAULT):
        """Pins a thread to a folder."""

        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            thread_folder = p_thread.participant.folder

            # make it compatible with old clients
            # we support pin in both folders (thread's folder and default folder)
            pin_both_folders = False
            if not folder:
                if thread_folder == FOLDER_SUB_THREAD:
                    folder = FOLDER_SUB_THREAD
                else:
                    pin_both_folders = True
                    folder = FOLDER_DEFAULT

            if folder in (
                FOLDER_SECRET,
                FOLDER_NOT_FRIEND,
                FOLDER_UNREAD,
                # FOLDER_SUB_THREAD,
            ):
                raise ThreadError.PinInProtectedFolderError()

            # if we delete all messages in this thread
            # thread is considered deleted, and invisible
            # so we can't pin this thread
            #
            # new thread without message is invisible too
            if p_thread.participant.delete_to == p_thread.thread.message_count:
                raise DataNotFound()

            if folder != thread_folder and folder != FOLDER_DEFAULT:
                raise PermissionDenied()

            # only check if we don't pin both folder
            if not pin_both_folders and self._is_thread_pinned_in_folder(
                p_thread.participant, folder
            ):
                # try to sync thread score if we find that the value in database is different from value stored in database
                # this is because some syncing problem between database & redis
                # TODO: remove it after everything is ok
                thread_score = self.thread_broker.get_thread_score(
                    user_id, thread_id, folder
                )
                db_thread_score = (
                    self._get_pinned_time(p_thread.participant, folder) * 1000
                )
                if thread_score != db_thread_score:
                    self.thread_broker.pin_thread(
                        user_id, thread_id, db_thread_score / 1000, folder
                    )
                    self.log.info(
                        f"""[Pin thread] Not synced thread score,
                                  user_id: {user_id},
                                  thread_id: {thread_id},
                                  folder: {folder},
                                  db_score: {db_thread_score},
                                  redis_score: {thread_score}"""
                    )

                return

            if not self.thread_broker.can_pin(user_id, folder):
                self._verify_pin(session, user_id, folder)
                raise ThreadError.MaximumPin()

            # luc dau pin_pos de luu vi tri, gio doi thanh timestamp de client
            # sort
            pin_pos_at = now()

            self.pt_repo.pin(session, user_id, thread_id, pin_pos_at, folder)
            self.thread_broker.pin_thread(
                user_id, thread_id, pin_pos_at, folder
            )
            if pin_both_folders:
                # pin default folder
                self.pt_repo.pin(
                    session, user_id, thread_id, pin_pos_at, thread_folder
                )
                self.thread_broker.pin_thread(
                    user_id, thread_id, pin_pos_at, thread_folder
                )
            session.commit()
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            self.after_update(session, user_id, p_thread.thread.dict())
            self.mqtt_broker.publish_thread_pinned_event(
                thread_id, user_id, pin_pos_at, folder
            )
            if pin_both_folders:
                self.mqtt_broker.publish_thread_pinned_event(
                    thread_id, user_id, pin_pos_at, FOLDER_DEFAULT
                )

    def unpin(
        self, thread_id: int, user_id: str, folder: str = FOLDER_DEFAULT
    ):
        """Unpins a thread from a folder."""

        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            thread_folder = p_thread.participant.folder

            unpin_both_folders = False
            if not folder:
                unpin_both_folders = True
                folder = FOLDER_DEFAULT

            if not folder:
                folder = FOLDER_DEFAULT

            if folder != thread_folder and folder != FOLDER_DEFAULT:
                raise PermissionDenied()

            if (
                not unpin_both_folders
                and not self._is_thread_pinned_in_folder(
                    p_thread.participant, folder
                )
            ):
                # try to sync thread score if we find that the value in database is different from value stored in database
                # this is because some syncing problem between database & redis
                # TODO: remove it after everything is ok
                thread_score = self.thread_broker.get_thread_score(
                    user_id, thread_id, folder
                )
                db_thread_score = self._get_thread_last_update_time(
                    p_thread.thread
                )
                if db_thread_score != thread_score:
                    # update thread score
                    self.thread_broker.set_thread_score(
                        user_id, thread_id, folder, score=db_thread_score
                    )
                    self.log.info(
                        f"""[Unpin thread] Not synced thread score,
                                  user_id: {user_id},
                                  thread_id: {thread_id},
                                  folder: {folder},
                                  db_score: {db_thread_score},
                                  redis_score: {thread_score}"""
                    )

                return

            old_score = self._get_thread_last_update_time(p_thread.thread)

            self._unpin_thread(session, user_id, thread_id, folder, old_score)

            if unpin_both_folders:
                self._unpin_thread(
                    session, user_id, thread_id, thread_folder, old_score
                )

            session.commit()
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            self.after_update(
                session, p_thread.participant.user_id, p_thread.thread.dict()
            )

            self.mqtt_broker.publish_thread_pinned_event(
                thread_id, user_id, 0, folder
            )
            if unpin_both_folders:
                self.mqtt_broker.publish_thread_pinned_event(
                    thread_id, user_id, 0, thread_folder
                )
