import concurrent.futures
from typing import (
    TYPE_CHECKING,
    Any,
    Dict,
    Iterable,
    List,
    Optional,
    Set,
    Union,
)

from sqlalchemy import exc
from sqlalchemy.orm import Session

from chat import action_type, constant, dto
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.constant import (
    BOT_TYPE,
    DIRECT_THREAD_TYPE,
    FOLDER_DEFAULT,
    GROUP_LEVEL_PRIVATE,
    SOURCE_ACCEPT_FRIEND,
    SOURCE_CONTACT,
    SOURCE_NORMAL,
    SYSTEM_TYPE,
    TASK_CREATE_MEMBER,
)
from chat.exception import (
    DataNotFound,
    OnlyAdminCanCreate,
    PermissionDenied,
    QueueIsFull,
    RestrictedMembersInChatCreation,
    WorkspaceDisable,
)
from chat.messages.model import MessageModel
from chat.participant_threads.model import ParticipantThreadModel
from chat.participants.model import ParticipantModel
from chat.threads import exception as ThreadError
from chat.threads.model.task import ThreadCreated<PERSON>vent
from chat.utils import validator_helper as vh
from chat.utils.common import generate_random_string, make_pair_ids

# from chat.utils.group_level_helper import is_public
from .get_thread_usecase import GetThreadUseCase

if TYPE_CHECKING:
    # import message usecase this way
    # to avoid circular import issue
    from chat.messages.usecase import MessageUseCases


class CreateThreadUseCase(GetThreadUseCase):
    def inject_message_usecases(self, usecase):
        """Inject message usecase.

        We need to inject here to avoid circular import issue
        """
        self.message_uscs: MessageUseCases = usecase

    def create_system_direct(
        self, user_id: str, system_id: str, actor_id: str
    ) -> ParticipantThreadModel:
        """Create direct để tạo threads cần có:
        part_id: string
        user_id: string
        detail: boolean # có cái chỉ cần phải thêm detail
        """
        # NOTE: check part_id co phai int khong
        session = self.session_m.Session()
        try:
            p_thread = self.get_direct_thread(user_id, system_id, session)
            if p_thread:
                session.close()
                return p_thread

            pair_ids = make_pair_ids(user_id, system_id)
            t_id = self.thread_repo.create_direct(
                session, pair_ids, creator=user_id
            )

            self.create_participant_thread_owner(
                session, t_id, user_id, system_id
            )

            # notify thread created event
            self.rb_broker.publish_status(
                constant.THREAD_EVENT_THREAD_CREATED,
                ThreadCreatedEvent(
                    thread_id=t_id,
                    creator_id=user_id,
                    partner_id=system_id,
                    type=DIRECT_THREAD_TYPE,
                    creator_type=vh.get_id_type(user_id),
                    partner_type=SYSTEM_TYPE,
                ).dict(),
            )
            session.commit()
        except exc.IntegrityError:
            self.log.warning("SQL Integrity error")
            session.rollback()
            raise ThreadError.ConflictData()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

        if actor_id == user_id:
            thread = self.get_direct_thread(user_id, system_id, session)
        else:
            thread = self.get_direct_thread(system_id, user_id, session)

        session.close()
        assert thread
        return thread

    def _check_private_workspace(
        self, user_id: str, part_id: str, workspace_id: str
    ):
        private_wss = self.config.get_private_workspaces()
        if not workspace_id or not private_wss:
            return True

        is_bot_receiver = vh.is_bot_id(part_id)
        if is_bot_receiver:
            bot_infos = self.gapo_client.bot.get_by_ids([part_id])
            bot_info = bot_infos.get(part_id, None)
            if not bot_info:
                raise PermissionDenied()

            if bot_info.get("creator_type") == "service":
                return True

            receiver_workspace_ids = set([bot_info.get("workspace_id", "0")])
            self.log.info(f"Receiver workspace ids: {bot_info} {receiver_workspace_ids}")
        else:
            receiver_workspace_ids = self.gapo_client.workspace.get_workspaces(
                part_id
            )

        sender_workspace_ids = self.gapo_client.workspace.get_workspaces(
            user_id
        )

        if sender_workspace_ids.intersection(receiver_workspace_ids):
            return True

        for private_ws in private_wss:
            left_hit = sender_workspace_ids.intersection(private_ws)
            right_hit = receiver_workspace_ids.intersection(private_ws)

            if left_hit and not right_hit:
                self.log.error(
                    f"Private workspaces not hit {user_id}{left_hit} {part_id}{right_hit}"  # noqa
                )
                raise PermissionDenied()

            if right_hit and not left_hit:
                self.log.error(
                    f"Private workspaces not hit {user_id}{left_hit} {part_id}{right_hit}"  # noqa
                )
                raise PermissionDenied()

            if not left_hit and not right_hit:
                continue
            return True

        return True

    def create_direct(
        self,
        part_id: str,
        user_id: str,
        source: Optional[str] = None,
        contact_name=None,
        bypass_limit=False,
        pass_code=None,
        bypass_code=False,
        contact_code=0,
        associate_link: str = "",
        workspace_id: str = "",
    ) -> ParticipantThreadModel:
        """Creates a direct thread."""

        self._check_private_workspace(user_id, part_id, workspace_id)

        session = self.session_m.Session()
        try:
            p_thread = self.get_direct_thread(
                user_id,
                part_id,
                session,
                pass_code=pass_code,
                bypass=bypass_code,
            )
            if p_thread:
                session.close()
                # NOTE: "cheating here"
                # if thread is deleted one side
                # and our partner is a bot
                # we will notify new thread created event
                # because from users' point of view,
                # they are creating thread with this bot again.
                if (
                    p_thread.participant.delete_to
                    == p_thread.thread.message_count
                    and vh.get_id_type(part_id) == BOT_TYPE
                ):
                    self.rb_broker.publish_thread_event(
                        constant.THREAD_EVENT_THREAD_CREATED,
                        ThreadCreatedEvent(
                            thread_id=p_thread.thread.id,
                            creator_id=user_id,
                            partner_id=part_id,
                            associate_link=associate_link,
                            type="direct",
                            creator_type=vh.get_id_type(user_id),
                            partner_type=vh.get_id_type(part_id),
                        ).dict(),
                    )
                return p_thread

            self.is_blocked(session, user_id, part_id)

            init_last_msg = None
            init_msg_count = 0
            sender_show = False
            receiver_show = False
            banner = None

            # if bypass_limit:
            # is_friend = (
            #     self.is_friend(user_id, part_id)
            #     if not vh.is_bot_id(part_id)
            #     else True
            # )

            folder_name = FOLDER_DEFAULT

            if source == SOURCE_CONTACT:
                init_last_msg = self._init_initial_last_message_from_contact(
                    SOURCE_CONTACT
                )
                init_msg_count = 1
                sender_show = True
                receiver_show = True
            elif source == SOURCE_NORMAL:
                init_last_msg = self._init_initial_last_message_from_contact(
                    SOURCE_NORMAL
                )
                init_msg_count = 1
                sender_show = True
                receiver_show = True
            elif source == SOURCE_ACCEPT_FRIEND:
                init_last_msg = self._init_initial_last_message_from_contact(
                    SOURCE_NORMAL
                )
                init_msg_count = 1
                if contact_code == self.gapo_client.contact.CODE_BOTH_A_B:
                    sender_show = True
                    receiver_show = True
                elif contact_code == self.gapo_client.contact.CODE_A_HAS_B:
                    sender_show = True
                elif contact_code == self.gapo_client.contact.CODE_B_HAS_A:
                    receiver_show = True

            pair_ids = make_pair_ids(user_id, part_id)
            t_id = self.thread_repo.create_direct(
                session,
                pair_ids,
                creator=user_id,
                banner=banner,
                message_count=init_msg_count,
                init_last_message=init_last_msg,
                associate_link=associate_link,
            )
            self._create_owner(
                session,
                t_id,
                user_id,
                part_id,
                init_last_msg=init_last_msg,
                show=sender_show,
            )

            self._create_owner(
                session,
                t_id,
                part_id,
                user_id,
                init_last_msg=init_last_msg,
                folder=folder_name,
                show=receiver_show,
            )
            session.commit()

            # notify thread created event
            self.rb_broker.publish_thread_event(
                constant.THREAD_EVENT_THREAD_CREATED,
                ThreadCreatedEvent(
                    thread_id=t_id,
                    creator_id=user_id,
                    partner_id=part_id,
                    type=DIRECT_THREAD_TYPE,
                    creator_type=vh.get_id_type(user_id),
                    partner_type=vh.get_id_type(part_id),
                ).dict(),
            )
        except exc.IntegrityError:
            self.log.warning("SQL Integrity error")
            session.rollback()
            raise ThreadError.ConflictData()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

        p_thread = self.get_direct_thread(user_id, part_id, session)
        session.close()
        if not p_thread:
            raise DataNotFound()
        return p_thread

    def create_sub_thread(
        self,
        thread_id: int,
        message_id: int,
        user_id: str,
        partner_ids: List[str],
    ):
        if self.rb_broker.is_queue_full():
            raise QueueIsFull()

        with self.session_m.get_session() as session:
            # check permission
            p_thread = self.get_thread_for_user(session, thread_id, user_id)

            # try to get existing sub-thread
            sub_p_thread = self._get_existing_sub_thread(
                session, user_id, thread_id, message_id
            )
            if sub_p_thread:
                return sub_p_thread

            # cannot create thread from message in sub-thread
            if p_thread.thread.is_subthread():
                raise PermissionDenied()

            # get root message
            message = self.message_repo.get_by_id(thread_id, message_id)
            if not message:
                raise PermissionDenied()

            # message_sender_id = message.user_id
            if not self._can_create_sub_thread_from_message(message):
                raise PermissionDenied()

            # add sender to sub-thread member list
            partner_ids = self._get_related_partners_to_add_to_sub_thread(
                session, user_id, message, p_thread, partner_ids
            )

            sub_thread_id = self.thread_repo.create_group(
                session,
                self._generate_thread_name_for_sub_thread(p_thread),
                1,
                p_thread.thread.avatar,
                group_level=constant.GROUP_LEVEL_PRIVATE,
                group_type=constant.SUB_THREAD_TYPE,
                workspace_id=p_thread.thread.workspace_id or "",
                collab_id=None,  # no collab_id
                parent_id=thread_id,
                root_message_id=message_id,
                parent_thread_type=p_thread.thread.type,
            )

            message.attach_sub_thread(sub_thread_id)
            self.message_repo.attach_sub_thread_to_message(message)

            # add owner to database
            self.pt_repo.create_members(
                session,
                sub_thread_id,
                (),
                owner_id=user_id,
                folder=constant.FOLDER_SUB_THREAD,
            )
            session.commit()

            owner = self.pt_repo.get_exist_record(session, thread_id, user_id)
            if owner:
                self.pt_repo.update_cache(dict(owner))

            # add other members using background job
            self._create_members(
                user_id,
                sub_thread_id,
                partner_ids,
                folder=constant.FOLDER_SUB_THREAD,
                set_thread_score=False,  # make thread invisible until a new message is sent # noqa
            )
            session.commit()  # for test

            # publish action note to everyone
            p_thread = self._get_full_thread_for_user(
                session, user_id, sub_thread_id
            )
            self.message_uscs.create_message._build_preview_msg(
                message, p_thread
            )
            self.publish_action_note(
                session,
                p_thread.thread,
                user_id,
                action_type.GROUP_CREATE,
                receiver=partner_ids + [user_id],
                skip_all_action_notes=True,
            )

            # notify thread created event
            self.rb_broker.publish_thread_event(
                constant.THREAD_EVENT_THREAD_CREATED,
                ThreadCreatedEvent(
                    thread_id=sub_thread_id,
                    root_message_id=message_id,
                    parent_id=thread_id,
                    creator_id=user_id,
                    type=constant.SUB_THREAD_TYPE,
                    creator_type=vh.get_id_type(user_id),
                ).dict(),
            )

            # TODO: should we create a copy of message in new sub-thread?
            # create message for sub-thread
            # self.message_uscs.create_message.create(
            #     user_id, sub_thread_id, message.body.dict(), api_version="3"
            # )

            # update metrics (track if sub-thread feature is used or not )
            self.metrics.sub_threads.inc()

            return p_thread

    def _get_related_partners_to_add_to_sub_thread(
        self,
        session: Session,
        user_id: str,
        message: MessageModel,
        p_thread: ParticipantThreadModel,
        partner_ids: List[str],
    ):
        """Gets all related members to add to sub-thread.

        They includes:
        - the owner of root message
        - all people that are mentioned in root message (except @all)
        - new members.
        """
        # add sender to sub-thread member list
        partner_ids.append(message.user_id)

        # add mentioned users
        mentioned_ids = self._get_mentions_in_message(
            session, user_id, message, p_thread
        )
        partner_ids += mentioned_ids

        # exclude user_id from partner_ids
        partner_ids = list(set([id for id in partner_ids if id != user_id]))

        # only keep partner_ids that are in parent threads
        if partner_ids:
            partner_ids = [
                u.user_id
                for u in self.pt_repo.get_members_by_ids(
                    session, p_thread.thread.id, partner_ids
                )
            ]

        return partner_ids

    def _get_mentions_in_message(
        self,
        session: Session,
        user_id: str,
        message: MessageModel,
        p_thread: ParticipantThreadModel,
    ):
        """Return list of user_ids that are mentioned in a message."""

        tags: List[str] = self.message_uscs.create_message._get_tag(
            session, user_id, p_thread.thread.id, p_thread, message.body
        ).get("force_ids")

        # don't add everyone into sub-thread
        if "all" in tags:
            return []

        return tags

    def _get_existing_sub_thread(
        self, session: Session, user_id: str, thread_id: int, message_id: int
    ) -> Optional[ParticipantThreadModel]:
        existing_sub_thread = self.thread_repo.get_sub_thread_for_message(
            session, thread_id, message_id
        )
        if existing_sub_thread:
            sub_thread_id = existing_sub_thread.id
            p_thread = self._get_full_thread_for_user(
                session, user_id, sub_thread_id
            )
            return p_thread
        return None

    def _generate_thread_name_for_sub_thread(
        self, p_thread: ParticipantThreadModel
    ):
        if p_thread.thread.is_direct():
            return " "
        else:
            return p_thread.thread.name

    def _can_create_sub_thread_from_message(self, message: MessageModel):
        if not message:
            return False

        if message.is_deleted_2_side():
            return False

        # cannot create sub-thread from action_note
        if message.body.type == "action_note":
            return False

        return True

    def create_group_only(
        self, group_info: dto.GroupInfo, member_source: dto.MemberSource
    ):
        if self.rb_broker.is_queue_full():
            raise QueueIsFull("Quả tải rồi")

        members: Set[str] = set()

        if member_source.member_file_id:
            to_add = self.gapo_client.workspace.get_users_from_imported_file(
                group_info.workspace_id, member_source.member_file_id
            )
            for member_id in to_add:
                members.add(str(member_id))

        if member_source.participant_ids:
            # friends = self._fetch_member_from_relation(
            #     group_info.creator_id, member_source.participant_ids
            # )
            # if friends:
            #     members.update(friends)
            members.update(member_source.participant_ids)

        if member_source.orgcs:
            for orgc in member_source.orgcs:
                if orgc.title_ids:
                    o_mem_ids = self._fetch_member_from_orgc(
                        group_info.creator_id,
                        orgc.workspace_id,
                        "title",
                        orgc.title_ids,
                    )
                    members.update(o_mem_ids)
                if orgc.department_ids:
                    o_mem_ids = self._fetch_member_from_orgc(
                        group_info.creator_id,
                        orgc.workspace_id,
                        "department",
                        orgc.department_ids,
                    )
                    members.update(o_mem_ids)

        members.discard(group_info.creator_id)
        thread = self._create_group(
            group_info.creator_id,
            members,
            group_info.name,
            group_info.avatar,
            group_info.group_level,
            group_info.group_type,
            group_info.workspace_id,
            source_thread_ids=member_source.source_thread_ids,
        )
        return thread

    def create_group_orgc(
        self,
        org_cs: Dict[Any, List],
        org_type: str,
        user_id: str,
        name: str,
        avatar: Optional[str] = None,
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
        workspace_id="",
        collab_id: Optional[str] = None,
        call_source: Optional[str] = None,
    ):
        """Creates group or a organization."""

        if self.rb_broker.is_queue_full():
            raise QueueIsFull("Quả tải rồi")
        members: Set[str] = set()
        for w_id, o_ids in org_cs.items():
            # if org_type == "department":
            #     o_mems = self.gapo_client.orgc.get_members_in_departments(
            #         user_id, w_id, o_ids
            #     )
            # else:
            #     o_mems = self.gapo_client.orgc.get_members_by_roles(
            #         user_id, w_id, o_ids
            #     )
            o_mem_ids = self._fetch_member_from_orgc(
                user_id, w_id, org_type, o_ids
            )
            members.update(o_mem_ids)

        members.discard(user_id)

        thread = self._create_group(
            user_id,
            members,
            name,
            avatar,
            group_level,
            group_type,
            workspace_id,
            collab_id,
            call_source=call_source,
        )
        return thread

    def create_group(
        self,
        part_ids: List[str],
        user_id,
        name: str,
        avatar: Optional[str] = None,
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
        workspace_id="",
        collab_id: Optional[str] = None,
        source_thread_ids: List[int] = [],
        member_file_id: Optional[str] = None,
        call_source: Optional[str] = None,
    ):
        if self.rb_broker.is_queue_full():
            raise QueueIsFull("Quả tải rồi")

        # make sure that we don't pass user_id in this list
        part_ids = [part_id for part_id in part_ids if part_id != user_id]

        # check friend ship if the part_ids is not empty
        if len(part_ids) > 0:
            # friends = self.gapo_client.relation.get_friends_from_list(
            #     user_id, part_ids
            # )
            # friends = self._fetch_member_from_relation(user_id, part_ids)
            # if not friends:
            #     raise PermissionDenied()
            members = part_ids
        else:
            members = []

        thread = self._create_group(
            user_id,
            set(members),
            name,
            avatar,
            group_level,
            group_type,
            workspace_id,
            collab_id,
            source_thread_ids=source_thread_ids,
            member_file_id=member_file_id,
            call_source=call_source,
        )
        return thread

    def create_associate_group(
        self,
        user_id: str,
        name: str,
        associate_link: str,
        avatar: Optional[str] = None,
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
        workspace_id="",
        collab_id: Optional[str] = None,
        tmp_text: str = "",
        should_create_owner: bool = True,
    ) -> ParticipantThreadModel:
        if self.rb_broker.is_queue_full():
            raise QueueIsFull("Quả tải rồi")
        session = self.session_m.Session()
        try:
            # reuse đoạn code trên
            uids: Set = set()
            if not collab_id:
                collab_id = self._create_collab(
                    creator_id=int(user_id),
                    name=name,
                    workspace_id=workspace_id,
                    group_level=group_level,
                )

            link_code = generate_random_string()
            thread_id = self.thread_repo.create_group(
                session,
                name,
                1,
                avatar,
                link_code,
                associate_link=associate_link,
                group_level=int(group_level),
                group_type=group_type,
                workspace_id=workspace_id,
                collab_id=collab_id,
            )
            session.commit()

            if should_create_owner:
                self.pt_repo.create_members(
                    session, thread_id, list(uids), owner_id=user_id
                )
                uids.add(user_id)
                p_thread = self._get_full_thread_for_user(
                    session, user_id, thread_id
                )
                self._set_thread_scores_for_users(
                    uids, thread_id, constant.FOLDER_DEFAULT
                )
                self.thread_broker.add_members(thread_id, list(uids))

                session.commit()
                owner = self.pt_repo.get_exist_record(
                    session, thread_id, user_id
                )
                if owner:
                    self.pt_repo.update_cache(dict(owner))

                self.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.GROUP_CREATE,
                    receiver=list(uids),
                    tmp_text=tmp_text,
                )

                self.rb_broker.publish_thread_event(
                    constant.THREAD_EVENT_THREAD_CREATED,
                    ThreadCreatedEvent(
                        thread_id=thread_id,
                        creator_id=user_id,
                        type="group",
                        creator_type=vh.get_id_type(user_id),
                    ).dict(),
                )
            else:
                thread = self.thread_repo.get_by_id(session, thread_id)
                if not thread:
                    raise Exception("Thread not found")

                part = ParticipantModel.new_participant(
                    user_id=user_id, thread_id=thread_id
                )
                p_thread = ParticipantThreadModel(
                    thread=thread, participant=part
                )
        except exc.IntegrityError:
            self.log.warning("SQL Integrity error")
            session.rollback()
            raise ThreadError.ConflictData()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
        return p_thread

    # def is_friend(self, user_id: str, part_id):
    #     getters = (
    #         self.gapo_client.relation.is_friend,
    #         self.gapo_client.workspace.check_if_same_company,
    #     )
    #     results = self.make_check_friend_future_object(
    #         getters, user_id, part_id
    #     )
    #     return True in results

    def is_blocked(self, session: Session, user_id: str, part_id):
        is_blocked = self.block_repo.check_block(session, user_id, part_id)
        if is_blocked is not None:
            raise PermissionDenied()

    def make_check_friend_future_object(
        self, getters, from_user_id: str, to_user_id
    ):
        results = []
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = []
            for getter in getters:
                futures.append(
                    executor.submit(getter, from_user_id, to_user_id)
                )
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        return results

    def _create_owner(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id: str,
        folder: str = FOLDER_DEFAULT,
        init_read_count=0,
        init_last_msg=None,
        show=False,
    ):
        self.pt_repo.create_owner(
            session,
            thread_id,
            user_id,
            partner_id,
            read_count=init_read_count,
            folder=folder,
            last_message=init_last_msg,
        )
        if show:
            self.thread_broker.set_thread_score(
                user_id, thread_id, folder, None, set_default_folder=False
            )

    def create_participant_thread_owner(
        self,
        session: Session,
        thread_id: int,
        user_id: str,
        partner_id: str,
        folder=FOLDER_DEFAULT,
    ):
        self.pt_repo.create_owner(session, thread_id, user_id, partner_id)
        self.pt_repo.create_owner(
            session, thread_id, partner_id, user_id, folder=folder
        )

    def _create_members(
        self,
        user_id: str,
        thread_id: int,
        members: Iterable[str],
        folder: str = constant.FOLDER_DEFAULT,
        set_thread_score=True,
    ):
        # cho member vào thêm subthread
        # TODO: need check với thêm member cho clean code
        invite_ids = list(members)
        num_parts = len(invite_ids) // constant.MAX_INVITE_PER_TASK + 1
        for i in range(num_parts):
            start = i * constant.MAX_INVITE_PER_TASK
            end = start + constant.MAX_INVITE_PER_TASK

            p_invite_ids = invite_ids[start:end]
            if not p_invite_ids:
                continue
            is_heavy = self.redis_counter.set_backpressure(
                thread_id, len(p_invite_ids)
            )

            self.rb_broker.publish_background_task(
                TASK_CREATE_MEMBER,
                {
                    "user_id": user_id,
                    "thread_id": thread_id,
                    "members": p_invite_ids,
                    "folder": folder,
                    "set_thread_score": set_thread_score,
                },
                is_heavy=is_heavy,
            )

    def _add_members(
        self,
        user_id: str,
        thread_id: int,
        members: Iterable[str],
        folder: str = constant.FOLDER_DEFAULT,
        set_thread_score=True,
    ):
        invite_ids = [str(m) for m in members]
        num_parts = len(invite_ids) // constant.MAX_INVITE_PER_TASK + 1
        for i in range(num_parts):
            start = i * constant.MAX_INVITE_PER_TASK
            end = start + constant.MAX_INVITE_PER_TASK

            p_invite_ids = invite_ids[start:end]
            if not p_invite_ids:
                continue
            is_heavy = self.redis_counter.set_backpressure(
                thread_id, len(p_invite_ids)
            )

            self.rb_broker.publish_background_task(
                constant.TASK_ADD_MEMBER_COLLAB,
                {
                    "user_id": user_id,
                    "thread_id": thread_id,
                    "invite_ids": p_invite_ids,
                    "type": "invite_mem",
                    "skip_action_notes": True,
                },
                is_heavy=is_heavy,
            )

    def create_members_bg(
        self,
        user_id: str,
        thread_id: int,
        user_ids: List[str],
        folder=constant.FOLDER_DEFAULT,
        set_thread_score=True,
    ):
        session = self.session_m.Session()
        try:
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id
            )
            if not p_thread:
                return

            for mem_id in user_ids:
                if vh.is_bot_id(mem_id):
                    self.pt_repo.create_group_bot(
                        session, thread_id, mem_id, folder=folder
                    )
                else:
                    self.pt_repo.create_member(
                        session, thread_id, mem_id, folder=folder
                    )
                # self.thread_repo.update_thread_member_count(
                #     session, thread_id, 1
                # )

                try:
                    if p_thread.thread.collab_id:
                        self.gapo_client.membership.add_member(
                            caller_id=int(user_id),
                            collab_id=p_thread.thread.collab_id,
                            user_id=int(mem_id),
                            workspace_id=p_thread.thread.workspace_id or "",
                            roles=[CollabGroupRole.Member],
                        )
                except Exception as e:
                    self.log.critical(
                        f"[Membership] Add Member Background error, "
                        f"workspace_id: {p_thread.thread.workspace_id}, "
                        f"collab_id: {p_thread.thread.collab_id}, "
                        f"caller_id: {user_id}, "
                        f"user_id: {mem_id}, "
                        f"detail: {str(e)}",
                        exc_info=True,
                    )

            session.commit()

            if set_thread_score:
                # set redis thread score so thread will be visible in user
                # thread list
                self._set_thread_scores_for_users(user_ids, thread_id, folder)

            self.thread_broker.add_members(thread_id, user_ids)

            session.commit()
        except exc.IntegrityError:
            session.rollback()
            raise ThreadError.ConflictData()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def _create_collab(self, creator_id, name, workspace_id, group_level):
        try:
            if not workspace_id or workspace_id == "":
                return None

            # is_public_group = is_public(group_level)

            collab_id = self.gapo_client.membership.create_collab_group(
                caller_id=int(creator_id),
                # temporary fix. Collab service requires not empty name for
                # group while chat-service doesn't have
                # this constraint
                name=name or "Collab",
                workspace_id=workspace_id,
            )

            return collab_id
        except Exception as e:
            self.log.critical(
                f"[Membership] Create Collab error, "
                f"workspace_id: {workspace_id}, "
                f"detail: {str(e)}",
                exc_info=True,
            )
            raise e
            # dont return collab_id none

    def _create_group(
        self,
        user_id: str,
        members: Union[List[str], Set[str]],
        name: str,
        avatar: Optional[str],
        group_level: int,
        group_type: str,
        workspace_id: str,
        collab_id: Optional[str] = None,
        source_thread_ids: List[int] = [],
        member_file_id: Optional[str] = None,
        call_source: Optional[str] = None,
    ) -> ParticipantThreadModel:
        if workspace_id:
            if not self._fetch_ws_enable(workspace_id):
                raise WorkspaceDisable()

        session: Session = self.session_m.Session()
        try:
            members = self._get_members_to_add_to_group(
                session,
                user_id,
                workspace_id,
                members,
                source_thread_ids=source_thread_ids,
                member_file_id=member_file_id,
            )
            if len(members) > constant.MAX_CREATE_THREAD_FOR_USER:
                if not self.gapo_client.workspace.is_admin(
                    workspace_id, user_id
                ):
                    raise OnlyAdminCanCreate()

            self._special_thread_creation_checks_for_gapo_partners(
                call_source, workspace_id, members
            )

            if len(members) == 0:
                raise ThreadError.NoMoreData()

            # bots = [mem for mem in members if vh.is_bot_id(mem)]
            # if len(bots) > 1:
            #     raise ThreadError.MoreThanOneBot()

            if not collab_id:
                collab_id = self._create_collab(
                    creator_id=int(user_id),
                    name=name,
                    workspace_id=workspace_id,
                    group_level=group_level,
                )

            link_code = generate_random_string()
            thread_id = self.thread_repo.create_group(
                session,
                name,
                1,
                avatar,
                link_code,
                group_level=int(group_level),
                group_type=group_type,
                workspace_id=workspace_id,
                collab_id=collab_id,
            )

            self.pt_repo.create_members(
                session, thread_id, (), owner_id=user_id
            )

            self._set_thread_scores_for_users(
                [user_id], thread_id, constant.FOLDER_DEFAULT
            )
            self.thread_broker.add_members(thread_id, [user_id])
            session.commit()

            owner = self.pt_repo.get_exist_record(session, thread_id, user_id)
            if owner:
                self.pt_repo.update_cache(dict(owner))

            # background
            self._add_members(user_id, thread_id, members)
            session.commit()  # just for test

            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id
            )
            if not p_thread:
                return p_thread  # type: ignore

            self.publish_action_note(
                session,
                p_thread.thread,
                user_id,
                action_type.GROUP_CREATE,
                receiver=list(members) + [user_id],
            )

            # notify thread created event
            self.rb_broker.publish_thread_event(
                constant.THREAD_EVENT_THREAD_CREATED,
                ThreadCreatedEvent(
                    thread_id=thread_id,
                    creator_id=user_id,
                    type="group",
                    creator_type=vh.get_id_type(user_id),
                ).dict(),
            )
        except exc.IntegrityError:
            self.log.warning("SQL Integrity error")
            session.rollback()
            raise ThreadError.ConflictData()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
        return p_thread

    def _special_thread_creation_checks_for_gapo_partners(
        self, call_source: Optional[str], workspace_id: str, members
    ):
        """Special checks for Gapo's partners."""
        if not self.config.miscs.droppi_workspace_id:
            return

        if (
            str(workspace_id) != self.conf.miscs.droppi_workspace_id
            or call_source != constant.CALL_SOURCE_API
        ):
            return

        if len(members) > self.conf.miscs.droppi_limit_members_creating_chat:
            raise RestrictedMembersInChatCreation()

    def _get_members_to_add_to_group(
        self,
        session: Session,
        user_id: str,
        workspace_id: str,
        invite_ids: Iterable[str],
        source_thread_ids: List[int],
        member_file_id: Optional[str] = None,
    ) -> List[str]:
        unique_ids: Set[str] = set([])
        for invite_id in invite_ids:
            unique_ids.add(str(invite_id))

        if len(source_thread_ids) > 0:
            for source_thread_id in source_thread_ids:
                self.get_thread_for_user(
                    session, thread_id=source_thread_id, user_id=user_id
                )
            members = self.pt_repo.get_members_in_list_threads(
                session, source_thread_ids
            )
            for member in members:
                unique_ids.add(str(member.user_id))

        if member_file_id:
            # TODO: what if workspace_id is empty string?
            # this will raise errors
            to_add = self.gapo_client.workspace.get_users_from_imported_file(
                workspace_id, member_file_id
            )
            for member_id in to_add:
                unique_ids.add(str(member_id))

        unique_ids.discard(user_id)

        return list(unique_ids)

    def _set_thread_scores_for_users(
        self,
        user_ids: Iterable[str],
        thread_id: int,
        folder: str = constant.FOLDER_DEFAULT,
    ):
        members: List[ParticipantModel] = [
            ParticipantModel.new_participant(
                user_id=user_id, thread_id=thread_id
            )
            for user_id in user_ids
        ]
        visible_to_default_folder = folder not in (
            constant.FOLDER_SUB_THREAD,
            constant.FOLDER_SECRET,
        )
        self.thread_broker.set_thread_scores_for_users(
            thread_id, members, set_default_folder=visible_to_default_folder
        )

    def _fetch_member_from_orgc(
        self,
        caller_id: str,
        workspace_id: str,
        org_type: str,
        org_ids: List[str],
    ):
        if org_type == "department":
            func_call = self.gapo_client.orgc.get_members_in_departments
        else:
            func_call = self.gapo_client.orgc.get_members_by_roles

        o_mems = func_call(caller_id, workspace_id, org_ids)
        r_o_mem_ids = set()

        for o_mem in o_mems:
            r_o_mem_ids.update(o_mem["user_ids"])
        return r_o_mem_ids

    def _fetch_ws_enable(self, workspace_id: str) -> bool:
        return self.gapo_client.feature.is_ws_enable(workspace_id)
