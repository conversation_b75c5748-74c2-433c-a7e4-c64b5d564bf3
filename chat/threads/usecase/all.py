from dataclasses import dataclass

from .create_thread_usecase import CreateThreadUseCase
from .disband_group_usecase import DisbandGroupUsecase
from .edit_thread_usecase import EditThreadUseCase
from .get_thread_usecase import GetThreadUseCase
from .join_thread_usecase import Jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .leave_thread_usecase import LeaveT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .member_activation_usecase import MemberActivationUsecase
from .pin_thread_usecase import PinThreadUseCase
from .set_unread_flag_usecase import SetUnreadFlagToThreadUsecase
from .setup_thread_usecase import SetupThreadUsecase


@dataclass
class ThreadUsecases(object):
    create_thread: CreateThreadUseCase
    edit_thread: EditThreadUseCase
    get_thread: GetThreadUseCase
    pin_thread: PinThreadUseCase
    leave_thread: LeaveThreadUseCase
    setup_thread: SetupThreadUsecase
    join_thread: JoinThreadUseCase
    disband_group: DisbandGroupUsecase
    unread_thread: SetUnreadFlagToThreadUsecase

    member_activation: MemberActivationUsecase
