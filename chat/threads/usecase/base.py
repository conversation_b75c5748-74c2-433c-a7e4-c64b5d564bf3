from typing import TYPE_CHECKING, List, Union

from sqlalchemy.orm import Session

from chat.app import MyApp
from chat.app.usecase import AppUsecase
from chat.constant import (
    FOLDER_DEFAULT,
    GROUP_THREAD_TYPE,
    SOURCE_CONTACT,
    SOURCE_NORMAL,
    SUPER_GROUP_THREAD_TYPE,
    TASK_CREATE_MESSAGE,
    SYSTEM_OBJECT,
)
from chat.exception import UserNotFound
from chat.threads import exception as ThreadError
from chat.threads.model import ThreadModel
from chat.participants.model import ParticipantModel
from chat.participant_threads.model import ParticipantThreadModel
from chat.utils.common import now
from chat.utils.message_helpers import action_data_change, action_note_builder
from chat.utils.validator_helper import is_bot_id, is_system

if TYPE_CHECKING:
    pass

JOIN_ABLE = (GROUP_THREAD_TYPE, SUPER_GROUP_THREAD_TYPE)


class ThreadUseCase(AppUsecase):
    def __init__(
        self,
        app: MyApp,
    ):
        super(ThreadUseCase, self).__init__(app)
        self.i_storage = self.repos.info
        self.mqtt_broker = self.brokers.thread_mqtt
        self.thread_broker = self.brokers.thread_rd
        self.redis_counter = self.repos.counter
        self.redis_rate_limit = self.repos.rate_limit
        self.redis_read = self.repos.thread_read
        self.gapo_client = self.repos.gapo
        self.pt_repo = self.repos.pt
        self.info_repo = self.repos.info
        self.thread_repo = self.repos.thread
        self.user_repo = self.repos.user
        self.block_repo = self.repos.block
        self.message_repo = self.repos.message
        self.rb_broker = self.brokers.thread_rb
        self.config = self.conf
        self.log = self.log
        self.DNFRL = self.config.direct_n_f_rate_limit
        self.chat_domain = self.config.chat_domain
        self._max_member_status = self.conf.max_member_sync_status
        self.contact_last_message = "Bạn mới từ Danh bạ"
        self.pf_repo = self.repos.folder

        self.session_m = self.conns.mysql

        self.lock_manager = app.lock_manager

    def _init_initial_last_message_from_contact(self, source):
        if source == SOURCE_CONTACT:
            return {
                "id": 1,
                "body": "Bạn mới từ Danh bạ",
                "user_id": "system",
                "created_at": now(),
            }
        elif source == SOURCE_NORMAL:
            return {
                "id": 1,
                "body": "Bạn vừa kết bạn mới, hãy gửi lời chào",
                "user_id": "system",
                "created_at": now(),
            }

    def _unpin_thread(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        folder: str,
        old_score: int,
    ):
        self.pt_repo.unpin(session, user_id, thread_id, folder)
        self.thread_broker.unpin_thread(user_id, thread_id, old_score, folder)

    def _get_thread_score(self, p_thread: ParticipantThreadModel):
        thread_score = None

        last_m = (
            p_thread.participant.last_message
            or p_thread.thread.centralize_last_message
        )
        if last_m:
            thread_score = last_m.created_at

        # FIXME: Maybe a bug here?
        if thread_score is None:
            thread_score = now()

        return thread_score

    def _can_join_group(self, thread):
        if thread["type"] not in JOIN_ABLE:
            raise ThreadError.ThreadNotFoundByLink()

    def _audit_thread(
        self,
        user_id: str,
        thread_ids: List[int],
        redis_thread_ids: List[int],
        folder_name,
    ):
        # TODO: bị vấn đề là tin nhắn đang được score thread khác mà lại được
        # chuyển sang folder mới
        leak_ids = set(redis_thread_ids) - set(thread_ids)
        if leak_ids:
            for thread_id in leak_ids:
                self.thread_broker.remove_thread_score(
                    user_id, thread_id, folder_name, remove_from_default=False
                )

    def incr_message_counter(self, session: Session, thread: ThreadModel):
        thread_id = thread.id
        current_count = thread.message_count
        counter = self.redis_counter.inc_counter(thread_id)
        if counter <= current_count:
            counter = self.thread_repo.incr_thread_msg_counter(
                session, thread_id
            )
            self.redis_counter.set_counter(thread_id, counter)
        return counter

    def dcr_message_counter(self, session: Session, thread: ThreadModel):
        self.redis_counter.dcr_counter(thread.id)

    def publish_action_note(
        self,
        session: Session,
        thread: ThreadModel,
        user_id: str,
        note_type: str,
        option=None,
        receiver=None,
        skip_all_action_notes: bool = False,
        tmp_text: str = "",
        extra_data: Union[None, dict] = None,
    ):
        # thread = self.thread_repo.get_by_id(session, threadID)
        if skip_all_action_notes:
            skip_message = True
            message_id = thread.message_count or 0
        else:
            skip_message = False
        if is_bot_id(user_id):
            maker = self._get_bot(user_id)
        elif is_system(user_id):
            maker = SYSTEM_OBJECT
        else:
            maker = self._get_user(user_id)

        if not maker:
            raise UserNotFound()

        # update message count
        if not skip_message:
            message_id = self.incr_message_counter(session, thread)
            thread.message_count = message_id
        try:
            action_note = action_note_builder(
                note_type,
                message_id,
                maker,
                thread,
                option,
                receiver,
                tmp_text=tmp_text,
                extra_data=extra_data,
            )
        except Exception as e:
            if not skip_message:
                self.dcr_message_counter(session, thread)
            raise e

        self.rb_broker.publish_action_note(
            TASK_CREATE_MESSAGE, action_note, skip_message=skip_message
        )

        session.commit()
        return message_id

    def publish_action_on_data(
        self,
        session: Session,
        thread: ThreadModel,
        user_id: str,
        action_note_field,
        note_type,
        skip_message: bool = True,
    ):
        maker = self._get_user(user_id)
        if not maker:
            raise UserNotFound()

        if skip_message:
            message_count = thread.message_count or 0
        else:
            message_count = self.incr_message_counter(session, thread)

        action_note = action_data_change(
            note_type, message_count, maker, action_note_field, thread
        )
        self.log.info(f"publish action note: {action_note}")
        self.rb_broker.publish_action_note(
            TASK_CREATE_MESSAGE, action_note, skip_message=skip_message
        )
        session.commit()
        return message_count

    def _get_user(self, user_id: str):
        return self.i_storage.get_users([user_id]).get(str(user_id))

    def _get_bot(self, bot_id):
        bot_info = self.i_storage.get_bots([bot_id])[bot_id]
        return bot_info

    def _get_pinned_time(self, part: ParticipantModel, folder: str):
        if folder == FOLDER_DEFAULT:
            pin_pos = part.pin_default_pos
        else:
            pin_pos = part.pin_pos
        return pin_pos

    def _is_thread_pinned_in_folder(self, part: ParticipantModel, folder: str):
        pin_time = self._get_pinned_time(part, folder)

        return pin_time > 0

    def _is_thread_pinned(self, part: ParticipantModel):
        return part.pin_pos > 0 or part.pin_default_pos > 0

    def _get_thread_last_update_time(self, thread: ThreadModel):
        return thread.updated
