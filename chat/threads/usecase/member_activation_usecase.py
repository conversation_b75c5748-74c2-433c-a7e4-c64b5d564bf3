from sqlalchemy.orm import Session

from chat import constant
from chat.connections.gapo_client.membership import MemberAlreadyExist
from chat.participants.model import (
    ChatThreadWithMemberInfo,
    ThreadMemberWithFolder,
    ParticipantModel,
)
from chat.threads.model import task
from chat.threads.usecase.get_thread_usecase import GetThreadUseCase


class MemberActivationUsecase(GetThreadUseCase):
    def reactivate_bg(self, user_id: str, workspace_id: str):
        """Re-activate users in all of his/her old groups in a workspace."""
        with self.session_m.get_session() as session:

            parts = self.pt_repo.get_all_by_workspace(
                session, user_id, workspace_id
            )

            if not parts:
                return

            thread_ids = [part.thread_id for part in parts]

            self.log.info(
                f"Re-activating user_id {user_id} in workspace {workspace_id}"
            )

            self.pt_repo.reactivate_groups_for_user(session, user_id)
            self.thread_repo.update_threads_member_count(
                session, thread_ids, 1
            )
            session.commit()

            # sends action notes
            parts = self.pt_repo.get_threads_by_ids(
                session, user_id, thread_ids
            )
            for part in parts:
                thread_id = part.thread_id
                self._rejoin_collab(session, user_id, part, workspace_id)
                folder = part.folder  # "default" or "subthread"
                set_default_folder = folder not in (
                    constant.FOLDER_SUB_THREAD,
                    constant.FOLDER_SECRET,
                )
                self.thread_broker.set_thread_scores_for_users(
                    thread_id, [part], set_default_folder=set_default_folder
                )
                thread = self.thread_repo.get_by_id(session, thread_id)
                if thread:
                    self.publish_action_note(
                        session,
                        thread,
                        user_id,
                        "join_chat_by_link",
                        skip_all_action_notes=True,
                    )

    def deactivate_bg(self, user_id: str, workspace_id: str):
        """Deactivate user in all of his/her groups in a workspace."""
        with self.session_m.get_session() as session:
            parts = self.pt_repo.get_all_group_ids(
                session, user_id, workspace_id
            )

            if not parts:
                return

            self.log.info(
                f"Deactivating user_id {user_id} in workspace {workspace_id}"
            )

            for part in parts:
                # trigger leave group event
                self.rb_broker.publish_background_task(
                    constant.TASK_LEAVE_GROUP,
                    task.TaskLeaveGroup(
                        user_id=user_id,
                        thread_id=part.thread_id,
                        deactivate_only=True,  # deactivate user
                        send_action_note=False,  # don't send action note
                        clear_msg=False,
                        promote_new_owner_if_needed=True,
                    ).dict(),
                )

    def reactivate(self, user_id: str, workspace_id: str):
        self.rb_broker.publish_background_task(
            constant.TASK_REACTIVATE_MEMBER_IN_GROUP,
            task.TaskReactivateMemberInGroup(
                user_id=user_id,
                workspace_id=workspace_id,
            ).dict(),
        )

    def deactivate(self, user_id: str, workspace_id: str):
        self.rb_broker.publish_background_task(
            constant.TASK_DEACTIVATE_MEMBER_IN_GROUP,
            task.TaskDeactivateMemberInGroup(
                user_id=user_id,
                workspace_id=workspace_id,
            ).dict(),
        )

    def _rejoin_collab(
        self,
        session: Session,
        user_id: str,
        part: ParticipantModel,
        workspace_id: str,
    ):
        thread = self.thread_repo.get_by_id(session, part.thread_id)
        if not thread:
            return

        collab_id = thread.collab_id
        workspace_id = workspace_id or thread.workspace_id
        self.log.info(
            f"{user_id} rejoins collab {collab_id} in workspace {workspace_id}"
        )
        if not collab_id:
            return

        try:
            self.gapo_client.membership.join_collab(
                user_id=int(user_id),
                collab_id=collab_id,
                workspace_id=workspace_id or "",
            )
        except MemberAlreadyExist:
            pass
        except Exception as e:
            self.log.critical(
                f"[Membership] Join collab error, "
                f"workspace_id: {workspace_id}, "
                f"collab_id: {collab_id}, "
                f"user_id: {user_id}, "
                f"detail: {str(e)}",
                exc_info=True,
            )
