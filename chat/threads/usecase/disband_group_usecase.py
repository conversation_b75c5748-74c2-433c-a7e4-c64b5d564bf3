from chat import acl, constant
from chat.exception import PermissionDenied, UserNotInThread
from chat.threads.model.task import TaskLeaveGroup
from chat.threads.model import ThreadModel
from chat.threads.usecase.get_thread_usecase import GetThreadUseCase


class DisbandGroupUsecase(GetThreadUseCase):
    def _unlink_group(self, session, thread: ThreadModel):
        if thread.is_associate_department():
            department_id = thread.get_associate_id()
            if department_id != "":
                workspace_id = thread.workspace_id
                self.thread_repo.clear_associate_link(
                    session, thread.id, workspace_id
                )
                self.gapo_client.orgc.unlink_thread_from_department(
                    workspace_id,
                    department_id,
                )
        elif thread.is_associate_group():
            group_id = thread.get_associate_id()
            if group_id != "":
                workspace_id = thread.workspace_id
                self.thread_repo.clear_associate_link(
                    session, thread.id, workspace_id
                )
                self.gapo_client.group.unlink_thread_from_group(
                    group_id, thread.id
                )

        self.thread_repo.update_thread_link(
            session, thread.id, "", constant.GROUP_LEVEL_PRIVATE
        )

    def disband_group(
        self,
        user_id: str,
        thread_id: int,
    ):
        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            if not p_thread:
                raise UserNotInThread()

            if not p_thread.thread.is_group():
                raise UserNotInThread()

            member_role = p_thread.participant.role
            settings = p_thread.thread.settings

            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_DISBAND_GROUP,
            )

            if not has_permission:
                raise PermissionDenied()

            self._unlink_group(session, p_thread.thread)

            # commit o day do thang rabbit khi test mock lai
            session.commit()

            self.rb_broker.publish_background_task(
                constant.TASK_LEAVE_GROUP,
                TaskLeaveGroup(
                    user_id=user_id,
                    # don't notify other members about this event
                    send_action_note=False,
                    thread_id=thread_id,
                    clear_msg=False,
                    # don't try to promote other user as new owner
                    promote_new_owner_if_needed=False,
                ).dict(),
            )

            # publish an task
            self.rb_broker.publish_background_task(
                constant.TASK_DISBAND_GROUP,
                {
                    "group_id": thread_id,
                    "associate_link": p_thread.thread.associate_link,
                    "user_id": user_id,
                },
            )

    def disband_group_bg(self, user_id: str, thread_id: int):
        """Disbands group handler (background)."""

        with self.session_m.get_session() as session:
            members = self.pt_repo.get_members_in_list_threads(
                session, [thread_id]
            )
            for m in members:
                self.thread_broker.remove_thread_score(
                    m.user_id, thread_id, m.folder
                )
                self.rb_broker.publish_background_task(
                    constant.TASK_LEAVE_GROUP,
                    TaskLeaveGroup(
                        user_id=m.user_id,
                        # don't notify other members about this event
                        send_action_note=False,
                        thread_id=thread_id,
                        clear_msg=False,
                        # don't try to promote other user as new owner
                        promote_new_owner_if_needed=False,
                    ).dict(),
                )

            session.commit()
