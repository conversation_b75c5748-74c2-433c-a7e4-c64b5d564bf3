from .get_thread_usecase import GetThreadUseCase


class SetUnreadFlagToThreadUsecase(GetThreadUseCase):
    def set_unread_flag(
        self, user_id: str, thread_id: int, unread_status: bool
    ):
        """Sets a thread as read/unread.

        Note that this is just an indicator for user. It doesn't
        mean thread is read or not
        """
        with self.session_m.get_session() as session:
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id
            )

            self.pt_repo.set_unread_flag(
                session, user_id, thread_id, unread_status
            )
            session.commit()
            self.after_update(
                session, p_thread.participant.user_id, p_thread.thread.dict()
            )

            # publish mqtt event
            self.mqtt_broker.publish_set_unread_flag_event(
                user_id, thread_id, p_thread.participant.folder, unread_status
            )
