import time
from typing import List
from chat import constant
from chat.connections.gapo_client.membership import Member<PERSON><PERSON>readyExist
from chat.exception import ThreadNotFound
from chat.participants.model import ParticipantModel
from chat.participants.model.task import <PERSON><PERSON>oin<PERSON><PERSON>ab
from chat.participant_threads.model import ParticipantThreadModel

from chat import action_type

from chat.threads.model import Thread<PERSON>odel

from sqlalchemy.orm import Session

from .get_join_base import GetJoinThreadBase


class JoinThreadUseCase(GetJoinThreadBase):
    def join_by_service(
        self, user_ids: List[str], thread_id: int, collab_id: str, join_type: str
    ):
        with self.session_m.get_session() as session:
            for user_id in user_ids:
                if thread_id:
                    thread = self.thread_repo.get_by_id(session, thread_id)
                else:
                    thread = self.thread_repo.get_by_collab_id(session, collab_id)
                if thread is None:
                    raise ThreadNotFound()
                session.commit()
                if thread.collab_id:
                    self._add_member_to_collab(
                        user_id,
                        thread.collab_id,
                        thread.workspace_id or "",
                        join_type,
                    )
                else:
                    self._join_thread(session, thread, user_id, join_type)

    def join_by_link(self, user_id: str, link: str) -> ParticipantThreadModel:
        """Joins a thread by link."""

        join_type = action_type.JOIN_BY_LINK
        lock_key = self._join_thread_by_link_lock_key(user_id, link)
        # TODO: what is lock?
        with self.lock_manager.get_lock(lock_key=lock_key):
            with self.session_m.get_session() as session:
                thread = self._get_thread_by_link_or_raise_error(session, link)
                session.commit()
                if thread.collab_id:
                    self._add_member_to_collab(
                        user_id,
                        thread.collab_id,
                        thread.workspace_id or "",
                        join_type,
                    )
                else:
                    self._join_thread(session, thread, user_id, join_type)

            time.sleep(0.3)
            p_thread = self.get_thread_for_user(session, thread.id, user_id)

        return p_thread

    def join_collab(self, user_id: str, collab_id: str, data_source: str):
        """Joins  acollab.

        NOTE: there is no checking here. This should be used for
        internal service only."""
        self.rb_broker.publish_background_task(
            constant.TASK_JOIN_COLLAB,
            TaskJoinCollab(
                user_id=user_id, collab_id=collab_id, data_source=data_source
            ).dict(),
        )

    def join_collab_bg(
        self, user_id: str, collab_id: str, join_type=action_type.JOIN_BY_LINK
    ):
        """join collab handle (background)."""

        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_collab_id(session, collab_id)
            if thread is None:
                raise ThreadNotFound()
            session.commit()
            self._join_thread(session, thread, user_id, join_type=join_type)

    def _add_member_to_collab(
        self,
        user_id: str,
        collab_id: str,
        workspace_id: str,
        invite_type: str,
    ):
        try:
            self.gapo_client.membership.join_collab(
                user_id=int(user_id),
                collab_id=collab_id,
                workspace_id=workspace_id,
                data_source=invite_type,
            )
        except MemberAlreadyExist:
            pass
        except Exception as e:
            self.log.critical(
                f"[Membership] Join collab error, "
                f"workspace_id: {workspace_id}, "
                f"collab_id: {collab_id}, "
                f"user_id: {user_id}, "
                f"detail: {str(e)}",
                exc_info=True,
            )

    def _join_thread_by_link_lock_key(self, user_id: str, link: str):
        return f"joinbylink_{user_id}_{link}"

    def _join_thread(
        self,
        session: Session,
        thread: ThreadModel,
        user_id: str,
        join_type: str = action_type.JOIN_BY_LINK,
    ):
        rejoin, new_mem = self._join_or_rejoin(
            session, thread.id, user_id, msg_count=thread.message_count
        )

        if rejoin or new_mem:
            self.thread_repo.update_thread_member_count(session, thread.id, 1)
            session.commit()
            self.thread_broker.add_members(thread.id, [user_id])
            member: ParticipantModel = ParticipantModel.new_participant(
                user_id=user_id, thread_id=thread.id
            )
            self.thread_broker.set_thread_scores_for_users(thread.id, [member])
            member_ds = self.pt_repo.get_exist_records(session, thread.id, [user_id])
            if member_ds:
                self.pt_repo.update_cache(dict(member_ds[0]))

            if join_type != action_type.JOIN_BY_LINK:
                skip = False
            else:
                skip = True

            self.publish_action_note(
                session,
                thread,
                user_id,
                join_type,
                skip_all_action_notes=skip,
            )

        session.commit()