from time import sleep
from typing import Dict

from sqlalchemy.orm import Session

from chat import acl, action_type, constant
from chat.exception import PermissionDenied
from chat.participant_threads.model import ParticipantThreadModel

from .get_thread_usecase import GetThreadUseCase


class EditThreadUseCase(GetThreadUseCase):
    def edit_group(
        self, thread_id: int, user_id: str, edited_fields: Dict
    ) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )

            edited = self._edit_thread(
                session, p_thread, thread_id, edited_fields
            )
            updated_name = edited_fields.get("name")
            updated_avatar = edited_fields.get("avatar")
            updated_description = edited_fields.get("")
            if p_thread.thread.collab_id and any(
                [updated_name, updated_avatar, updated_description]
            ):
                try:
                    self.gapo_client.membership.update_collab_info(
                        collab_id=p_thread.thread.collab_id,
                        caller_id=int(user_id),
                        workspace_id=p_thread.thread.workspace_id or "",
                        name=updated_name,
                        avatar=updated_avatar,
                        description=updated_description,
                    )
                except Exception as e:
                    # maybe due to slow syncing between chat-service and
                    # membership, we may got permission error
                    # we will reduce these errors by retry again after some
                    # small duration
                    if not isinstance(e, PermissionDenied):
                        self.log.critical(
                            f"[Membership] Edit Group error, "
                            f"workspace_id: {p_thread.thread.workspace_id}, "
                            f"collab_id: {p_thread.thread.collab_id}, "
                            f"caller_id: {user_id}, "
                            f"detail: {str(e)}",
                            exc_info=True,
                        )
                        raise e

                    # retry
                    try:
                        sleep(0.3)  # sleep small time until data is synced
                        self.gapo_client.membership.update_collab_info(
                            collab_id=p_thread.thread.collab_id,
                            caller_id=int(user_id),
                            workspace_id=p_thread.thread.workspace_id or "",
                            name=updated_name,
                        )
                    except Exception as e:
                        self.log.critical(
                            f"[Membership] Edit Group error, "
                            f"workspace_id: {p_thread.thread.workspace_id}, "
                            f"collab_id: {p_thread.thread.collab_id}, "
                            f"caller_id: {user_id}, "
                            f"detail: {str(e)}",
                            exc_info=True,
                        )
                        raise e

            edited.update(
                self._edit_personal_info(
                    session, thread_id, user_id, edited_fields
                )
            )

            self._update_sub_threads_info(
                session, user_id, thread_id, edited_fields
            )

            session.commit()
            data_change = self._data_change(edited)
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )

            if data_change:
                self.publish_action_on_data(
                    session,
                    p_thread.thread,
                    user_id,
                    data_change,
                    action_type.UPDATE_GROUP,
                    skip_message=False,
                )

        return p_thread

    def update_associate_group(
        self, thread_id: int, workspace_id, clear_link=False
    ):
        with self.session_m.get_session() as session:
            if clear_link:
                self.thread_repo.clear_associate_link(
                    session, thread_id, workspace_id
                )
            else:
                self.thread_repo.update_thread_workspace_id(
                    session, thread_id, workspace_id
                )
            session.commit()

    def _check_group_edit_permission(self, p_thread: ParticipantThreadModel):
        member_role = p_thread.participant.role
        settings = p_thread.thread.settings
        has_permission, permission_name = acl.check_permission(
            settings, member_role, constant.PERMISSION_EDIT_GROUP
        )
        if not has_permission:
            raise PermissionDenied()

    def _update_sub_threads_info(
        self,
        session: Session,
        user_id: str,
        thread_id: int,
        edited_fields: Dict,
    ):
        updated_name = edited_fields.get("name")
        updated_avatar = edited_fields.get("avatar")
        updated_description = edited_fields.get("")
        if updated_name or updated_avatar or updated_description:
            self.thread_repo.update_sub_threads_info(
                session, thread_id, edited_fields
            )

    def _data_change(self, edited_field):
        action_note_key = {"name", "avatar"}
        action_note_field = {}
        for k in action_note_key:
            if edited_field.get(k):
                action_note_field[k] = edited_field[k]
        return action_note_field

    def _edit_thread(
        self,
        session: Session,
        p_thread: ParticipantThreadModel,
        thread_id: int,
        edit_fields,
    ):
        edit_key = {"name", "avatar", "description"}
        edit_thread_field = {}
        edited = {}

        for k in edit_key:
            if edit_fields.get(k) is not None:
                edit_thread_field[k] = edit_fields[k]
        if edit_thread_field:
            self._check_group_edit_permission(p_thread)
            edited = self.thread_repo.edit_info(
                session, thread_id, edit_thread_field
            )
        return edited

    def _edit_info(
        self, session: Session, thread_id: int, user_id: str, edit_fields
    ):
        edited = self.pt_repo.edit_info(
            session, thread_id, user_id, edit_fields
        )
        return edited

    def _edit_personal_info(
        self, session: Session, thread_id: int, user_id: str, edited_fields
    ):
        edited_keys = {"alias"}
        edit_personal_field = {}
        edited = {}

        for k in edited_keys:
            if edited_fields.get(k):
                edit_personal_field[k] = edited_fields[k]
        if edit_personal_field:
            edited = self._edit_info(
                session, thread_id, user_id, edit_personal_field
            )
        return edited
