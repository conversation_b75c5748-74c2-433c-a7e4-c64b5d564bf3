from chat import acl, constant, action_type
from chat.constant import (
    FOLDER_DEFAULT,
    FOLDER_NOT_FRIEND,
    FOLDER_SECRET,
    FOLDER_SUB_THREAD,
)
from chat.exception import PermissionDenied
from chat.participant_threads.model import (
    ParticipantThreadModel,
)
from chat.utils.common import generate_random_string, is_folder_created_by_user
from chat.participants.model import ThreadMemberWithFolder

from .get_join_base import GetJoinThreadBase


class SetupThreadUsecase(GetJoinThreadBase):

    def set_auto_delete_message(
        self, thread_id: int, user_id: str, day_num: int
    ) -> ParticipantThreadModel:
        """Makes a thread auto_delete."""

        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_AUTO_DELETE_MESSAGE,
            )
            if not has_permission:
                raise PermissionDenied()

            settings.set_delete_msg_day(day_num)

            self.thread_repo.update_thread_group_level(
                session,
                thread_id,
                settings.encode(),
            )

            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            if day_num == 0:
                self.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.DISABLE_AUTO_DELETE_MESSAGE,
                    option={"delete_msg_after_days": day_num},
                )
            else:
                self.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.SET_AUTO_DELETE_MESSAGE,
                    option={"delete_msg_after_days": day_num},
                    extra_data={"day_num": day_num},
                )

            return p_thread

    def make_public(
        self, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        """Makes a thread public."""

        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_TOGGLE_LINK,
            )
            if not has_permission:
                raise PermissionDenied()
            if not settings.is_public:
                if p_thread.thread.link:
                    link_code = p_thread.thread.link
                else:
                    link_code = generate_random_string()

                settings.is_public = True
                settings.only_admin_can_add_member = False

                self.thread_repo.update_thread_link(
                    session,
                    thread_id,
                    link_code,
                    settings.encode(),
                )

            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )

            return p_thread

    def make_private(self, thread_id: int, user_id: str):

        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_TOGGLE_LINK,
            )
            if not has_permission:
                raise PermissionDenied()
            if settings.is_public:
                settings.is_public = False
                self.thread_repo.update_thread_link(
                    session,
                    thread_id,
                    p_thread.thread.link,
                    settings.encode(),
                )

            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )

            return p_thread

    def toggle_privacy(self, thread_id: int, user_id: str):
        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_TOGGLE_LINK,
            )
            if not has_permission:
                raise PermissionDenied()

            settings.toggle_privacy()

            self.thread_repo.update_thread_group_level(
                session,
                thread_id,
                settings.encode(),
            )
            session.commit()
            thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )

            return thread

    def toggle_mute_all(
        self, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            thread = self.thread_repo.get_by_id(session, thread_id)
            if not thread:
                raise PermissionDenied()

            if thread.is_subthread():
                if not thread.parent_id:
                    raise PermissionDenied()
                p_r_thread = self._get_group_chat_for_user(
                    session, thread.parent_id, user_id
                )
            else:
                p_r_thread = self._get_group_chat_for_user(
                    session, thread_id, user_id
                )
            member_role = p_r_thread.participant.role
            settings = thread.settings

            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_TOGGLE_MUTE_ALL_NOTIS,
            )
            if not has_permission:
                raise PermissionDenied()
            settings.toggle_send_msg()

            self.thread_repo.update_thread_group_level(
                session,
                thread_id,
                settings.encode(),
            )
            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )

            if settings.disable_member_send_message:
                self.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.DISABLE_USER_SEND_MESSAGE,
                )
            else:
                self.publish_action_note(
                    session,
                    p_thread.thread,
                    user_id,
                    action_type.ENABLE_USER_SEND_MESSAGE,
                )
            assert p_thread is not None

        member_ids = self._get_active_members(
            thread_id, p_thread.thread.member_count
        )
        self.mqtt_broker.publish_thread_setting_updated_event(
            member_ids, thread_id, settings.dict()
        )

        return p_thread

    def toggle_mute_all_subthread(self, thread_id: int, user_id: str):
        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, permission_name = acl.get_permission(
                settings.is_public,
                member_role,
                constant.PERMISSION_TOGGLE_MUTE_ALL_NOTIS,
            )
            if not has_permission:
                raise PermissionDenied()
            if not settings.disable_member_send_message:
                raise PermissionDenied()

            settings.toggle_send_sub_message()

            self.thread_repo.update_thread_group_level(
                session,
                thread_id,
                settings.encode(),
            )
            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            assert p_thread is not None

        member_ids = self._get_active_members(
            thread_id, p_thread.thread.member_count
        )
        self.mqtt_broker.publish_thread_setting_updated_event(
            member_ids, thread_id, settings.dict()
        )

        return p_thread

    def toggle_notify(
        self, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        with self.session_m.get_session() as session:
            p_thread = self.get_thread_for_user(session, thread_id, user_id)

            self.pt_repo.toggle_notify(session, thread_id, user_id)
            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            return p_thread

    def toggle_subthread_notify(
        self, parent_thread_id: int, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        # check user in parent thread
        # check user in subthread
        #   if user already in subthread
        #       toggle notify
        #   else
        #       add user in subthread
        #       add scoring user in subthread folder
        with self.session_m.get_session() as session:
            self.get_thread_for_user(session, parent_thread_id, user_id)

            rejoin, new_mem = self._join_or_rejoin(
                session, thread_id, user_id, folder=constant.FOLDER_SUB_THREAD
            )
            need_score = rejoin or new_mem

            if not need_score:
                self.pt_repo.toggle_notify(session, thread_id, user_id)
            else:
                p_thread = self.get_thread_for_user(
                    session, thread_id, user_id
                )
                if p_thread.thread.parent_id != parent_thread_id:
                    raise PermissionDenied()

                self.thread_broker.set_thread_scores_for_users(
                    thread_id, [p_thread.participant], set_default_folder=False
                )

                # incase rejoin but already turn off before leave.
                if p_thread.participant.enable_notify == 0:
                    self.pt_repo.toggle_notify(session, thread_id, user_id)

            session.commit()
            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            return p_thread

    def toggle_only_admin_can_add_member_setting(
        self, thread_id: int, user_id: str
    ) -> ParticipantThreadModel:
        """Toggles only admin can add member setting.

        In a normal threads, non-admin members can add other members.
        If this setting is turned on, only admin can add new members.
        """

        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, _ = acl.check_permission(
                settings,
                member_role,
                constant.PERMISSION_TOGGLE_ONLY_ADMIN_CAN_ADD_MEMBER,
            )
            if not has_permission:
                raise PermissionDenied()

            settings.is_public = False
            settings.toggle_add_mem()

            if not settings.only_admin_can_add_member and settings.is_public:
                # reset join link and update group level
                link_code = ""  # disable join link
                self.thread_repo.update_thread_link(
                    session, thread_id, link_code, settings.encode()
                )
            else:
                self.thread_repo.update_thread_group_level(
                    session,
                    thread_id,
                    settings.encode(),
                )

            session.commit()

            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            assert p_thread is not None

        member_ids = self._get_active_members(
            thread_id, p_thread.thread.member_count
        )
        self.mqtt_broker.publish_thread_setting_updated_event(
            member_ids, thread_id, settings.dict()
        )

        return p_thread

    def toggle_only_admin_can_update_info_setting(
        self, thread_id: int, user_id: str
    ):
        """
        @deprecated
        Toggles only admin can update group information/cover.

        In a normal threads, non-admin members
        can update group information/cover.
        If this setting is turned on, only admin can do this.
        """

        with self.session_m.get_session() as session:
            p_thread = self._get_group_chat_for_user(
                session, thread_id, user_id
            )
            member_role = p_thread.participant.role
            settings = p_thread.thread.settings
            has_permission, _ = acl.check_permission(
                settings,
                member_role,
                constant.PERMISSION_TOGGLE_ONLY_ADMIN_CAN_UPDATE_INFO,
            )
            if not has_permission:
                raise PermissionDenied()

            # toggle setting
            settings.toggle_update_info()
            self.thread_repo.update_thread_group_level(
                session,
                thread_id,
                settings.encode(),
            )
            session.commit()

            p_thread = self._get_full_thread_for_user(
                session, user_id, thread_id, bypass_pass_code=True
            )
            assert p_thread is not None

        member_ids = self._get_active_members(
            thread_id, p_thread.thread.member_count
        )
        self.mqtt_broker.publish_thread_setting_updated_event(
            member_ids, thread_id, settings.dict()
        )

        return p_thread

    def move_to_folder(
        self, thread_id: int, user_id: str, to_folder: str, pass_code=None
    ):
        """Moves a thread to new folder."""

        with self.session_m.get_session() as session:
            if to_folder in [
                FOLDER_SECRET,
                FOLDER_DEFAULT,
                FOLDER_NOT_FRIEND,
            ]:
                folder_exists = True
            else:
                folder_exists = self.pf_repo.check_exists(
                    session, user_id, to_folder
                )

            if not folder_exists:
                raise ValueError(f"Not support folder {to_folder}")

            p_thread = self.get_thread_for_user(session, thread_id, user_id)

            from_folder = p_thread.participant.folder

            # cannot move threads from/to `subthread` folder
            # because it's special place to store all sub-threads.
            if (
                from_folder == FOLDER_SUB_THREAD
                or to_folder == FOLDER_SUB_THREAD
            ):
                raise PermissionDenied()
                return

            # threads from `secret` and `stranger` folder
            # can only move to `default` folder. This case also means
            # that thread is set back as normal thread.
            if from_folder in (
                # FOLDER_SECRET,
                FOLDER_NOT_FRIEND,
            ) and is_folder_created_by_user(to_folder):
                session.commit()
                return

            if from_folder == to_folder:
                self._fill_thread_score_if_missing(
                    user_id, thread_id, to_folder, p_thread
                )
                session.commit()
                return

            # update database
            self.pt_repo.move_folder(session, user_id, thread_id, to_folder)

            # update pin_pos/pin_default_pos in database
            # unpin in original folder if it isn't default folder
            if self._is_thread_pinned_in_folder(
                p_thread.participant, from_folder
            ):
                if from_folder != constant.FOLDER_DEFAULT:
                    self.pt_repo.unpin(
                        session, user_id, thread_id, from_folder
                    )

            # update thread scores in from/to folders
            # we will remove thread scores from 'from' folder
            # and update thread score in 'to' folder.
            # Some special logic
            # - if 'to' is secret folder,
            # we will also remove thread from 'default' folder.
            # - if from != (secret, stranger) and 'to' is default,
            # its means it's a delete folder action. We
            # - thread is unpinned
            # if it's pinned in 'from' folder and 'from' != 'default'
            # don't update thread score.

            # remove thread scores in 'from' folder

            remove_from_default_folder = False
            if to_folder == FOLDER_SECRET:
                remove_from_default_folder = True

            # remove thread score in original folder if
            # original folder isn't `default` folder
            # or we make thread private (to_folder='secret')
            if (
                from_folder != constant.FOLDER_DEFAULT
                or to_folder == FOLDER_SECRET
            ):
                self.thread_broker.remove_thread_score(
                    user_id,
                    thread_id,
                    from_folder,
                    remove_from_default=remove_from_default_folder,
                )

            # update thread score in 'to' folder

            need_update_thread_score = True
            if to_folder == FOLDER_DEFAULT and from_folder not in (
                FOLDER_SECRET,
                FOLDER_NOT_FRIEND,
            ):
                # action triggered by delete folder usecase, so we will ignore
                # this
                # TODO: use different event
                # to trigger delete folder in background
                need_update_thread_score = False

            if not need_update_thread_score:
                self._fill_thread_score_if_missing(
                    user_id, thread_id, to_folder, p_thread
                )
                session.commit()
                return

            # update thread score in current folder
            current_score = self._get_thread_score(p_thread)
            self.thread_broker.set_thread_score(
                user_id,
                thread_id,
                to_folder,
                current_score,
                set_default_folder=False,
            )
            session.commit()

    def _fill_thread_score_if_missing(
        self,
        user_id: str,
        thread_id: int,
        folder,
        p_thread: ParticipantThreadModel,
    ):
        """Fill thread score if it's missing.
        It's due to the inconsistent between database and redis."""
        # check if there is inconsistent between redis and db
        thread_score = self.thread_broker.get_thread_score(
            user_id, thread_id, folder
        )
        if thread_score is None:
            current_score = self._get_thread_score(p_thread)
            pin_score = (
                p_thread.participant.pin_default_pos
                if p_thread.participant.folder == "default"
                else p_thread.participant.pin_pos
            )
            if pin_score > 0:
                current_score = pin_score * 1000

            self.thread_broker.set_thread_score(
                user_id,
                thread_id,
                folder,
                current_score,
                set_default_folder=False,
            )
