from chat import codes, response_messages
from chat.exception import ChatError


class ThreadError(ChatError):
    pass


class ThreadNameTooLong(ThreadError):
    http_code = 400
    error = "name_too_long"
    i18n_message = response_messages.THREAD_NAME_TOO_LONG


class NoMoreData(ThreadError):
    http_code = 404
    error = "no_members"
    i18n_message = response_messages.NO_MORE_DATA


class MoreThanOneBot(ThreadError):
    http_code = 422
    error = "more_than_one_bot"
    i18n_message = response_messages.MORE_THAN_ONE_BOT


class PinInProtectedFolderError(ThreadError):
    http_code = 403
    error = "pin_in_protected_folder"
    i18n_message = response_messages.PIN_IN_PROTECTED_FOLDER


class PageNotFound(ThreadError):
    http_code = 404
    i18n_message = response_messages.PAGE_NOT_FOUND
    error = "page_not_found"


class ConflictData(ThreadError):
    http_code = 409
    i18n_message = response_messages.CONFLICT
    error = "conflict"


class ThreadBlockedByUserError(ThreadError):
    http_code = 403
    i18n_message = response_messages.PERMISSION_DENIED
    error = "thread_blocked"
    code = codes.BLOCKED_CODE


class ThreadNotFoundByLink(ThreadError):
    http_code = 404
    i18n_message = response_messages.LINK_NOT_FOUND
    error = "thread_not_found"
    code = codes.BLOCKED_CODE


class MissingPasscode(ThreadError):
    http_code = 400
    i18n_message = response_messages.MISSING_PASS_CODE
    error = "missing_passcode"
    code = codes.MISSING_PASS_CODE


class WrongPasscode(ThreadError):
    http_code = 403
    i18n_message = response_messages.WRONG_PASS_CODE
    error = "wrong_passcode"
    code = codes.WRONG_PASS_CODE


class MaximumPin(ThreadError):
    i18n_message = response_messages.LIMIT_PIN
    http_code = 403

    def __init__(self):
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "code": codes.MAX_PIN,
        }


class LeaveGroupError(ThreadError):
    i18n_message = response_messages.DENIED_LEAVE_GROUP
    http_code = 403

    def __init__(self, permission_name):
        self.permission_name = permission_name
        super().__init__()

    def to_dict(self):
        return {
            "message": self.message,
            "code": codes.LEAVE_GROUP,
            "permission": self.permission_name,
        }
