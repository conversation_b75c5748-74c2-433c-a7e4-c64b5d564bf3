from typing import Optional, Union
from pydantic import root_validator

from chat.model import BaseModel
from chat.utils import common

from chat.threads.model import ThreadModel
from chat.messages.model import MessageBody
from chat.profile.model import SchemaUser, SchemaPage


class NotifyObject(BaseModel):
    force_ids: list = []
    text: Optional[str] = None
    sound: Optional[str] = None
    receiver_ids: list = []
    ignore_ids: list = []


class MessageEvent(BaseModel):
    body: MessageBody
    thread: ThreadModel
    page: Union[None, SchemaPage]
    user: Union[None, SchemaUser]
    will_deleted_at: int = 0
    message_id: int

    sender_type: str
    sender_lang: str = "vi"

    client_id: Union[None, str] = None
    notify: Optional[NotifyObject] = None
    mentions: Optional[NotifyObject] = None
    enable_update_index: bool = True
    part_index: int = 0
    part_length: int = 5000
    part_source: str = "mysql"
    disable_notify: bool = False

    @root_validator(pre=True)
    def fill_default_payload(cls, values):
        return values

    def after_parse(self):
        if self.thread.settings:
            if isinstance(self.thread.settings, dict):
                day_num = self.thread.settings.get("delete_msg_after_days", 0)
            else:
                day_num = self.thread.settings.delete_msg_after_days
        else:
            day_num = 0
        metadata = self.body.metadata
        auto_delete_message_codes = (11, 10)
        if (
            metadata
            and metadata.get("action_note_type") in auto_delete_message_codes
        ):
            day_num = 0
        if day_num:
            self.will_deleted_at = common.from_now(day_num=day_num)
