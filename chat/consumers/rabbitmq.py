import select
import threading

from typing import List

import pika
from chat.connections.rabbitmq_c import RabbitMQConnectionV2
from chat.config import MessageQueueInfo

from logzero import logger

CONNECTIVITY_ERRORS = (
    pika.exceptions.AMQPConnectionError,
    pika.exceptions.ConnectionClosed,
    pika.exceptions.ChannelClosed,
    pika.exceptions.StreamLostError,
    pika.exceptions.ChannelWrongStateError,
    pika.exceptions.ConnectionClosedByBroker,
    select.error,  # XXX: https://github.com/pika/pika/issues/412
)


class AmqpConsumer(object):
    def __init__(
        self,
        name,
        client: RabbitMQConnectionV2,
        queue_name,
        x_routings,
        handler,
    ):
        self._name = name
        self._client = client
        self._stop = threading.Event()
        self._init_queue = False
        self._handler = handler

        self.queue_name = queue_name
        self.exchange_routings = x_routings.split(",")

    def close(self):
        logger.info(f"Closing rabbit publisher {self._name}")
        self._stop.set()
        logger.info(f"Closed rabbit publisher {self._name}")

    def _create_queue(self):
        if self._init_queue:
            return

        queues: List[MessageQueueInfo] = []
        for ex_routing in self.exchange_routings:
            ex_name, rk = ex_routing.split("|")

            queues.append(
                MessageQueueInfo(
                    queue_name=self.queue_name,
                    exchange_name=ex_name,
                    routing_key=rk,
                )
            )
        self._client.queues = queues
        self._client.handlers = {self.queue_name: self._handler}
        self._init_queue = True

    def communicate(self):
        while True:
            try:
                self._create_queue()
                self._client.run()
            except pika.exceptions.AMQPChannelError:
                self._client.reconnect()
                logger.info("Rabbit Restart channel")
            except CONNECTIVITY_ERRORS:
                self._client.reconnect()
                logger.info("Rabbit Restart connection")
            if self._stop.is_set():
                self._client.stop()
                break


class AmqpConsumerMock(object):
    pass
