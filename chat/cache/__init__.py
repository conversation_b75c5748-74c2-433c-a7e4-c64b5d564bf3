import queue
import time
from typing import Any, Dict, Iterable, Set, Tuple

from chat.bots.model import BotIn<PERSON>
from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat.users.model import UserInfo
from chat.utils.json import json_dumps, json_loads


class RedisCache(object):
    def __init__(self, config: Settings, conn: RedisConnection):
        self.cache_q = queue.Queue()  # type: ignore
        self._client = conn.client
        self.config = config
        self._setup()
        self.key_user_format = "cache_user_{user_id}_info"
        self.key_bot_format = "cache_bot_{bot_id}_info"

    def _setup(self):
        redis_cfg = self.config.redis_cache
        self.redis_key_refresh = redis_cfg.redis_refresh_key
        self.cache_hash_key = redis_cfg.cache_hash_key
        self.cache_timeout = redis_cfg.cache_timeout

    def _is_after_exiration_time(self, expire_at: int):
        return expire_at < int(time.time() * 1000)

    def _makeEtagAt(self):
        return int(time.time() * 1000) + self.cache_timeout

    def _make_user_key(self, user_ids: Iterable[Any]):
        if not isinstance(user_ids, (list, set)):
            return self.key_user_format.format(user_id=user_ids)
        return [self.key_user_format.format(user_id=u) for u in user_ids]

    def _make_bot_key(self, bot_ids: Iterable[Any]):
        if not isinstance(bot_ids, (list, set)):
            return self.key_bot_format.format(bot_id=bot_ids)
        return [self.key_bot_format.format(bot_id=b) for b in bot_ids]

    def _get_info_by_keys(self, keys):
        return self._client.hmget(self.cache_hash_key, keys)

    def update_users(self, values: Dict[str, Any]):
        if not values:
            return
        caches = {}
        for k, v in values.items():
            cache_o = {"value": v, "expire_at": self._makeEtagAt()}
            caches[self._make_user_key(k)] = json_dumps(cache_o)
        self._client.hset(self.cache_hash_key, mapping=caches)

    def update_bots(self, values: Dict[str, Any]):
        if not values:
            return
        caches = {}
        for k, v in values.items():
            cache_o = {"value": v, "expire_at": self._makeEtagAt()}
            caches[self._make_bot_key(k)] = json_dumps(cache_o)
        self._client.hset(self.cache_hash_key, mapping=caches)

    def _noti_refresh_cache(self, key):
        self._client.lpush(self.redis_key_refresh, key)

    def _get_infos(self, obj_ids: Iterable[Any], keys):
        cache = self._get_info_by_keys(keys)
        objects = dict()
        missed_ids = set()
        for u, c, k in zip(obj_ids, cache, keys):
            # invalid users without status field
            if c:
                cache_o = json_loads(c)
                expire_at = cache_o["expire_at"]
                self._noti_refresh_cache(k) if self._is_after_exiration_time(
                    expire_at
                ) else None

                info = cache_o["value"]

                # invalidate cache for old user data
                # status is None  and type = "user"
                if (
                    info.get("type") != "user"
                    or info.get("status") is not None
                ):
                    objects[u] = info
                else:
                    # no user status field
                    # stale value -> need to refetch later
                    # TODO: remove this
                    self._noti_refresh_cache(k)
                    missed_ids.add(u)
            else:
                missed_ids.add(u)
        return missed_ids, objects

    def get_users(
        self, user_ids: Iterable[Any]
    ) -> Tuple[Set, Dict[str, UserInfo]]:
        keys = self._make_user_key(user_ids)
        return self._get_infos(user_ids, keys)  # type: ignore

    def get_bots(
        self, bot_ids: Iterable[Any]
    ) -> Tuple[Set, Dict[str, BotInfo]]:
        keys = self._make_bot_key(bot_ids)
        return self._get_infos(bot_ids, keys)  # type: ignore
