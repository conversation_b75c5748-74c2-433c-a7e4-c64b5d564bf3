# import falcon
# from falcon import Request, Response

# from chat import constant
# from chat.app import MyApp
# from chat.app.resource import AdminResource
# from chat.exception import InvalidParameters
# from chat.hooks import load_request_body
# from chat.model import make_input

# from .model import SchemaFixThreadScore, TaskFixThreadScore


# class FixThreadScoreResource(AdminResource):
#     def __init__(self, app: MyApp):
#         self.log = app.log
#         self.config = app.conf
#         self.task_broker = app.brokers.thread_rb

#     @falcon.before(load_request_body)
#     def on_post(self, req: Request, resp: Response):
#         raw = req.context.body
#         body, errors = make_input(SchemaFixThreadScore, raw)
#         if errors:
#             raise InvalidParameters(error_details=errors)

#         task = TaskFixThreadScore(
#             user_ids=body.user_ids,
#             workspace_id=body.workspace_id,
#             file=body.file,
#         )

#         task_id = self.task_broker.publish_background_task(
#             constant.TASK_FIX_THREAD_SCORE, task.dict()
#         )

#         resp.media = {"data": {"job_id": task_id}}
