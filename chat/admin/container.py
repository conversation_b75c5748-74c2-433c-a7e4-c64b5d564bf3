# from dependency_injector import containers, providers

# from chat.app import MyApp

# from .deliver import FixThreadScoreResource


# class AdminResourcesContainer(containers.DeclarativeContainer):
#     app = providers.Dependency(MyApp)

#     fix_thread_score = providers.Singleton(FixThreadScoreResource, app)


# class AdminContainer(containers.DeclarativeContainer):
#     app = providers.Dependency(MyApp)

#     resources = providers.Singleton(AdminResourcesContainer, app=app)
