import gettext
from typing import Dict

AVAILABLE_LANGUAGES = ["en", "vi"]

translators: Dict[str, gettext.GNUTranslations] = {}
for locale in AVAILABLE_LANGUAGES:
    translator = gettext.translation(
        "base", localedir="locales", languages=[locale]
    )
    translator.install()
    translators[locale] = translator

DEFAULT_TRANSLATOR = translators["vi"]


def T(message: str, locale="vi", **kargs) -> str:
    """Translates a message."""
    translator = translators.get(locale, DEFAULT_TRANSLATOR)
    return translator.gettext(message.format(**kargs))


def _(message: str) -> str:
    """Identity function used as a mark to extract string to translate.
    You should use this function on any text that need to be translated."""

    return message
