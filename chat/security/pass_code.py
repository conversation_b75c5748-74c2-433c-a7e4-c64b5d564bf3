# pylint: skip-file

import hashlib
import uuid
from functools import lru_cache

from .format import PASS_CODE_FORMAT

USERS = 50_000_000  # 50 trieu user


def generate(clear_pc, salt=None):
    if salt is None:
        salt = uuid.uuid4().hex

    hashed_pc = hash_pc(clear_pc, salt)
    return PASS_CODE_FORMAT.format(salt=salt, hashed_pass_code=hashed_pc)


def hash_pc(clear_pc, salt):
    encode = (clear_pc + salt).encode("utf-8")
    return hashlib.sha512(encode).hexdigest()


@lru_cache
def validate(clear_pc, pc):
    salt, hashed_pc = pc.split("|")
    return hashed_pc == hash_pc(clear_pc, salt)
