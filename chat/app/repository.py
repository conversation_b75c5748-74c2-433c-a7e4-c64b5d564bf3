from chat.block_users.repository import BlockUserRepository
from chat.connections.gapo_client import GapoC<PERSON>
from chat.counter.redis import <PERSON><PERSON><PERSON>ounter
from chat.folders.repository import FolderRepository
from chat.health_check.repository import HealthCheckRepository
from chat.health_check.repository.revision import RevisionRepository
from chat.messages.repository import MessageCache, MessageRepository
from chat.participants.repository import (
    ParticipantCache,
    ParticipantThreadRepository,
)
from chat.quick_message.repository.mysql import QuickMessageRepositoryMySQL
from chat.repositories.media_collection import MediaCollectionRepositoryMySQL
from chat.repositories.pin_collection import PinCollectionRepositoryMySQL
from chat.repositories.rate_limit import RateLimitRedisRepository
from chat.repositories.read import RedisThreadReadRepo
from chat.service_storages.external_info import InfoStorage
from chat.threads.repository import ThreadCache, ThreadRepository
from chat.users.repository import UserRepository


class AppRepositories(object):
    """A holder class that hold all connections in chat."""

    def __init__(
        self,
        gapo: GapoClient,
        thread_repo: ThreadRepository,
        pin_collection_repo: PinCollectionRepositoryMySQL,
        folder_repo: FolderRepository,
        user_repo: UserRepository,
        pt_repo: ParticipantThreadRepository,
        block_repo: BlockUserRepository,
        message_repo: MessageRepository,
        media_collection_repo: MediaCollectionRepositoryMySQL,
        rate_limit_repo: RateLimitRedisRepository,
        thread_read_repo: RedisThreadReadRepo,
        info_repo: InfoStorage,
        counter_repo: RedisCounter,
        hc_repo: HealthCheckRepository,
        revision_repo: RevisionRepository,
        thread_cache: ThreadCache,
        part_cache: ParticipantCache,
        message_cache: MessageCache,
        quick_message_repo: QuickMessageRepositoryMySQL,
    ) -> None:
        # guard check
        assert isinstance(thread_repo, ThreadRepository)
        assert isinstance(pin_collection_repo, PinCollectionRepositoryMySQL)
        assert isinstance(folder_repo, FolderRepository)
        assert isinstance(user_repo, UserRepository)
        assert isinstance(pt_repo, ParticipantThreadRepository)
        assert isinstance(block_repo, BlockUserRepository)
        assert isinstance(message_repo, MessageRepository)
        assert isinstance(
            media_collection_repo, MediaCollectionRepositoryMySQL
        )
        assert isinstance(rate_limit_repo, RateLimitRedisRepository)
        assert isinstance(info_repo, InfoStorage)
        assert isinstance(counter_repo, RedisCounter)
        assert isinstance(hc_repo, HealthCheckRepository)
        assert isinstance(thread_cache, ThreadCache)
        assert isinstance(part_cache, ParticipantCache)
        assert isinstance(message_cache, MessageCache)
        assert isinstance(quick_message_repo, QuickMessageRepositoryMySQL)
        # external apis
        self.gapo = gapo
        """Gapo repository."""

        self.relation = gapo.relation
        """Relation repository."""

        self.user_info = gapo.user
        """User info repository."""

        self.bot = gapo.bot
        """Bot repository."""

        self.contact = gapo.contact
        """Contact repository."""

        self.react = gapo.react
        """Reaction repository."""

        self.workspace = gapo.workspace
        """Workspace management repository."""

        self.poll = gapo.poll
        """Poll management repository."""

        self.orgc = gapo.orgc
        """Organization repository."""

        self.thread = thread_repo
        """Thread repository."""

        self.folder = folder_repo
        """User folders management repository."""

        self.user = user_repo
        """User data related to chat. """

        self.pin_collection = pin_collection_repo
        """Pin collections management repository."""

        # participant thread repo
        self.pt = pt_repo
        """Participant thread relationship management repository."""

        self.block = block_repo
        """Block/unblock thread. """

        self.message = message_repo
        """Manage thread messages."""

        self.media_collection = media_collection_repo
        """Mange media files in chat thread. """

        self.rate_limit = rate_limit_repo
        """ Rate limited repository. """

        self.thread_read = thread_read_repo
        """Manage thread read state."""

        self.info = info_repo
        """General info storage."""

        self.counter = counter_repo
        """Track thread's related metrics."""

        # health check repos
        self.health_check = hc_repo
        """Health check repository."""

        self.revision = revision_repo
        """Revision repository."""

        self.thread_cache = thread_cache
        self.part_cache = part_cache
        self.message_cache = message_cache
        self.quick_message_repo = quick_message_repo