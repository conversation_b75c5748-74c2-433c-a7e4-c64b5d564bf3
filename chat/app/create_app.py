import atexit
import os
import sys
from glob import glob
from typing import Optional

import falcon
import sentry_sdk
from dotenv import load_dotenv
from falcon import media
from sentry_sdk.integrations.falcon import FalconIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from chat import load_cdn_mapping
from chat.config import Settings
from chat.container import App<PERSON>ontainer, HealthContainer
from chat.exception import add_error_handlers
from chat.folders.routing import routing as folder_routing
from chat.health_check.routing import routing as health_check_routing
from chat.messages.routing import routing as message_routing
from chat.metrics.middleware import PrometheusMiddleware
from chat.metrics.routing import routing as metric_routing
from chat.middlewares import AuthorizationMiddleware, RequestIDMiddleware
from chat.participants.routing import routing as participant_routing
from chat.quick_message.routing import routing as quick_message_routing
from chat.threads.routing import routing as thread_routing
from chat.tracking.routing import routing as tracking_routing

# from chat.users.routing import routing as user_routing
from chat.utils.json import json_dumps, json_loads


def delete_prometheus_db_files():
    folder = os.environ.get("PROMETHEUS_MULTIPROC_DIR", None)
    if folder is None:
        return

    try:
        files = glob(f"{folder}/*.db")
        for file in files:
            try:
                os.remove(file)
            except Exception:
                pass
    except Exception:
        pass


def handle_404(req: falcon.Request, resp: falcon.Response):
    resp.status = falcon.HTTP_404
    resp.media = {"error": "route_not_found", "message": "404 not found"}


def try_to_load_env_file():
    env_file = os.environ.get("ENV_FILE", ".env.prod")
    load_dotenv(env_file)


def try_to_report_error_to_sentry(e: Exception):
    try_to_load_env_file()
    sentry_dsn = os.environ.get("SENTRY_DSN")
    if sentry_dsn:
        print(
            """An ERROR has occurred. App fails to start.
                Try to report error to Sentry..."""
        )
        sentry_sdk.init(dsn=sentry_dsn)
        sentry_sdk.capture_exception(
            Exception(f"FAILED TO START APP! ERROR: {e}")
        )
        # capture error detail
        # add to extra seem doesn't work well.
        sentry_sdk.capture_exception(e)


def create_health_app(container: Optional[HealthContainer] = None):
    try:
        if container is None:
            container = HealthContainer()
            print("THIS IS ID", id(container))
            container.init_resources()

            container.wire(packages=[sys.modules["chat"]])

        # make sure everything is ok
        container.check_dependencies()
        # config: Settings = container.conf()
        api = falcon.App()
        health_check_routing(api, container.health())
        return api
    except Exception as e:
        try_to_report_error_to_sentry(e)
        raise e


def create_app(container: Optional[AppContainer] = None):
    """Create new Falcon app.
    Params:
        container: AppContainer instance.
            If None is passed, we will init new container.
    """

    try:
        if container is None:
            container = AppContainer()
            container.init_resources()

            container.wire(packages=[sys.modules["chat"]])

        # make sure everything is ok
        container.check_dependencies()

        config: Settings = container.conf()
        log = container.log()
        app = container.app()

        # close all connections when the program is closed
        atexit.register(app.conns.close_all)
        atexit.register(delete_prometheus_db_files)

        if config.sentry_dsn:
            sentry_sdk.init(
                dsn=config.sentry_dsn,
                integrations=[
                    FalconIntegration(transaction_style="path"),
                    # avoid logging duplication
                    # See: https://github.com/kiwicom/structlog-sentry#logging-as-json # noqa
                    LoggingIntegration(event_level=None, level=None),
                ],
            )

        load_cdn_mapping.load(config.cdn_mapping_filepath)

        middlewares = [
            PrometheusMiddleware(),
            RequestIDMiddleware(),
            AuthorizationMiddleware(config, log, app.repos.gapo),
        ]
        api = falcon.App(middleware=middlewares)

        # default 404 handle
        api.add_sink(handle_404)

        # add centralized exception handle
        # to capture every exceptions in app.
        add_error_handlers(
            api,
            metrics=app.app_metrics,
            include_server_error_trace=config.include_server_error_trace,
        )

        custom_media_serializers(api)

        # install routers here
        # block_user_routing(api, container.block_users())
        thread_routing(api, container.thread())
        participant_routing(api, container.participant())
        # user_routing(api, container.user())
        message_routing(api, container.message())
        health_check_routing(api, container.health())
        folder_routing(api, container.folder())
        metric_routing(api, container.metrics())
        tracking_routing(api, container.tracking())
        quick_message_routing(api, container.quick_message())
        return api
    except Exception as e:
        try_to_report_error_to_sentry(e)
        raise e


def custom_media_serializers(app: falcon.App):
    json_handler = media.JSONHandler(dumps=json_dumps, loads=json_loads)
    extra_handlers = {"application/json": json_handler}
    app.req_options.media_handlers.update(extra_handlers)
    app.resp_options.media_handlers.update(extra_handlers)


def init_app(container: Optional[AppContainer] = None):
    try:
        if container is None:
            container = AppContainer()
            print("THIS IS ID", id(container))
            container.init_resources()

            container.wire(packages=[sys.modules["chat"]])

        # make sure everything is ok
        container.check_dependencies()
        app = container

        # close all connections when the program is closed
        atexit.register(container.app().conns.close_all)
        atexit.register(delete_prometheus_db_files)
        return app
    except Exception as e:
        try_to_report_error_to_sentry(e)
        raise e
