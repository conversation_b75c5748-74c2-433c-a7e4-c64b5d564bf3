from logging import Logger
from typing import Sequence

AUTH_TYPE_GUEST = "guest"
AUTH_TYPE_USER = "user"
AUTH_TYPE_SERVICE = "service"
AUTH_TYPE_ADMIN = "admin"


class BaseResource(object):
    """Base resource class."""

    auth_types: Sequence[str] = (AUTH_TYPE_USER,)
    """Auth type set by this resource. By default it's (AUTH_TYPE_USER,) . """

    user_id: str = ""
    """User id (injected by auth middleware)"""

    lang: str = "vi"
    """ user lang  """

    workspace_id: str = ""
    """Workspace id (injected by auth middleware). """

    role: str = ""
    """Role of caller (user/service/guess) (injected by auth middleware). """

    token: str = ""
    """user token (injected by auth middleware). """

    request_id: str = ""
    """Request_id (injected by RequestIDMiddleware)."""

    log: Logger = None  # type: ignore
    """Logger for resource, injected by RequestIDMiddleware.
    """


class GuestResource(BaseResource):
    """Resource for everyone without permission check."""

    auth_types = (AUTH_TYPE_GUEST,)


class UserAuthResource(BaseResource):
    """Resource for authenticated users."""

    auth_types = (AUTH_TYPE_USER,)


class UserAndServiceResource(BaseResource):
    """TODO: remove all resources of this type.
    Mixing both internal/external API in same resource can
    cause troubles.
    """

    auth_types = (AUTH_TYPE_USER, AUTH_TYPE_SERVICE)


class InternalServiceResource(BaseResource):
    """Resource for internal services to call to."""

    auth_types = (AUTH_TYPE_SERVICE,)


class AdminResource(BaseResource):
    """Resource for admin (require key)."""

    auth_types: Sequence[str] = (AUTH_TYPE_ADMIN,)
