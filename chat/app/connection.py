from chat.connections.cassandra_c import CassandraConnection
from chat.connections.mqtt_c import MQTTConnection
from chat.connections.mysql_c import MySQLConnection
from chat.connections.rabbitmq_c import (
    RabbitMQConnection,
    RabbitMQConnectionV2,
)
from chat.connections.redis_c import RedisConnection
from chat.publishers.mqtt import MqttPublisher
from chat.publishers.rabbitmq import AmqpProducer


class AppConnections(object):
    """A class that hold all connections of chat-app."""

    def __init__(
        self,
        mqtt_c: MQTTConnection,
        mqtt_pub: MqttPublisher,
        mysql_c: MySQLConnection,
        cas_c: CassandraConnection,
        rb_c: RabbitMQConnection,
        rb_c_v2: RabbitMQConnectionV2,
        rb_pub: AmqpProducer,
        redis_persist_c: RedisConnection,
        redis_thread_c: RedisConnection,
        redis_msg_c: RedisConnection,
        redis_cache_c: RedisConnection,
    ):
        self.mqtt = mqtt_c
        self.mqtt_pub = mqtt_pub
        self.mysql = mysql_c
        self.cas = cas_c
        self.rb = rb_c
        self.rb_v2 = rb_c_v2
        self.rb_pub = rb_pub
        self.redis_persist_c = redis_persist_c
        self.redis_thread_c = redis_thread_c
        self.redis_msg_c = redis_msg_c
        self.redis_cache_c = redis_cache_c

        self._conns = [
            mqtt_c,
            cas_c,
            redis_persist_c,
            redis_thread_c,
            redis_msg_c,
            redis_cache_c,
            rb_c,
            rb_c_v2,
        ]
        print("Init conns ...")

    def close_all(self):
        for conn in self._conns:
            conn.close()  # type: ignore
