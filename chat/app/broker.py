from logging import Logger

from chat.block_users.broker.mqtt import Block<PERSON>ser<PERSON><PERSON><PERSON><PERSON>roker
from chat.config import Settings
from chat.connections.redis_c import RedisConnection
from chat.dist_lock import DistLockManager
from chat.messages.broker.mqtt_broker import Message<PERSON><PERSON>TT<PERSON>roker
from chat.messages.broker.rabbitmq_broker import MessageRabbitBroker
from chat.participants.broker.mqtt_broker import ParticipantMQTTBroker
from chat.participants.broker.rabbitmq_broker import ParticipantRabbitBroker
from chat.publishers.mqtt import MqttPublisher
from chat.publishers.rabbitmq import AmqpProducer
from chat.threads.broker.mqtt_broker import ThreadMQTTBroker
from chat.threads.broker.rabbitmq_broker import ThreadRabbitBroker
from chat.threads.broker.redis_broker import ThreadRedisBroker
from chat.users.broker.mqtt import UserMQTTBroker


class AppBrokers(object):
    """App brokers .
    Because we don't need to test these brokers,
    so we don't need to use DI here
    """

    def __init__(
        self,
        conf: Settings,
        log: Logger,
        redis_persist_c: RedisConnection,
        rb_producer: AmqpProducer,
        mqtt_pub: MqttPublisher,
        lock_manager: DistLockManager,
    ) -> None:

        # redis brokers
        self.thread_rd = ThreadRedisBroker(
            conf, log, redis_persist_c, lock_manager
        )
        """Thread broker implemented in redis."""

        # mqtt brokers
        self.pt_mqtt = ParticipantMQTTBroker(conf, log, mqtt_pub)
        """participant thread broker implemented in MQTT."""

        self.thread_mqtt = ThreadMQTTBroker(conf, log, mqtt_pub)
        """Thread broker implemented in MQTT."""

        self.user_mqtt = UserMQTTBroker(conf, log, mqtt_pub)
        """User broker implemented in MQTT."""

        self.message_mqtt = MessageMQTTBroker(conf, log, mqtt_pub)
        """Message broker plemented in MQTT."""

        self.block_user_mqtt = BlockUserMQTTBroker(conf, log, mqtt_pub)
        """Block users broker implemented in MQTT."""

        # rabitmq brokers
        self.thread_rb = ThreadRabbitBroker(conf, log, rb_producer)
        """Thread broker implemented in Rabbitmq."""

        self.pt_rb = ParticipantRabbitBroker(conf, log, rb_producer)
        """Participant thread broker implemented in Rabbitmq."""

        self.message_rb = MessageRabbitBroker(conf, log, rb_producer)
        """Message broker implemented in Rabbitmq."""
