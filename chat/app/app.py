from logging import Logger

from chat.app.broker import AppBrokers
from chat.config import Settings
from chat.dist_lock import Dist<PERSON>ockManager
from chat.metrics.model import AppMetrics

from .connection import AppConnections
from .repository import AppRepositories


class MyApp(object):
    """A class that hold all basic information."""

    def __init__(
        self,
        conf: Settings,
        log: Logger,
        app_conns: AppConnections,
        app_repos: AppRepositories,
        app_brokers: AppBrokers,
        app_metrics: AppMetrics,
        lock_manager: DistLockManager,
    ):
        self.log = log
        self.repos = app_repos
        self.conns = app_conns
        self.conf = conf
        self.brokers = app_brokers
        self.app_metrics = app_metrics
        self.lock_manager = lock_manager
