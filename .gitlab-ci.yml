image: docker:18.09.7

stages:
  - test
  - build
  #- check_migration
  - deploy
  - cleanup

variables:
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
  IMAGE_TAG_BUILDER: $CI_REGISTRY_IMAGE/builder:$CI_COMMIT_REF_NAME
  APP_NAME: gapo-chat-api
  # See https://stackoverflow.com/a/55310803
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  PIPENV_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pipenv"
  WORKON_HOME: "$CI_PROJECT_DIR/.cache/venv"

.build_template:
  services:
    - docker:18.09.7-dind
  stage: build
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
    - docker pull $IMAGE_TAG_BUILDER || echo "Building builder from scratch"
    - docker pull $IMAGE_TAG || echo "Building runtime from scratch"
    - >
      docker build
      --target=builder
      --cache-from $IMAGE_TAG_BUILDER
      -t $IMAGE_TAG_BUILDER
      -t $IMAGE_TAG_BUILDER-$CI_COMMIT_SHORT_SHA .
    - >
      docker build
      --cache-from $IMAGE_TAG
      --cache-from $IMAGE_TAG_BUILDER
      --build-arg GIT_COMMIT_TAG="$CI_COMMIT_SHA $(TZ=':Asia/Ho_Chi_Minh' date)"
      -t $IMAGE_TAG
      -t $IMAGE_TAG-$CI_COMMIT_SHORT_SHA .
    - docker push $IMAGE_TAG_BUILDER
    - docker push $IMAGE_TAG_BUILDER-$CI_COMMIT_SHORT_SHA
    - docker push $IMAGE_TAG
    - docker push $IMAGE_TAG-$CI_COMMIT_SHORT_SHA
  except:
    - schedules

cache:
  key: pip-cache
  paths:
    - .cache

test:
  stage: test
  image: binhnd1008/python-with-pipenv:3.8.2
  variables:
    WORKON_HOME: "$CI_PROJECT_DIR/venv"
    RABBITMQ_DEFAULT_USER: admin
    RABBITMQ_DEFAULT_PASS: admin
    RABBITMQ_DEFAULT_VHOST: chat
    MYSQL_ROOT_PASSWORD: root
    MYSQL_DATABASE: chat
    MYSQL_USER: admin
    MYSQL_PASSWORD: admin
    REDIS_PASSWORD: redis
    HEAP_NEWSIZE: 128M
    MAX_HEAP_SIZE: 128M
  coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  services:
    - cassandra:4
    - mysql:5.7
    - name: eclipse-mosquitto:2
      alias: mqtt
      command: ["mosquitto", "-c", "/mosquitto-no-auth.conf"]
    - rabbitmq:3-alpine
    - name: bitnami/redis:latest
      alias: redis
  before_script:
    - pip install pyright cqlsh pipenv==v2022.5.2 > /dev/null 2>&1
    - pipenv install --dev --system > /dev/null 2>&1
    - pipenv install --system > /dev/null 2>&1
    - export ENV=ci
  script:
    - if [[ -f dev-scripts/lint-and-test.sh ]] ;  then bash dev-scripts/lint-and-test.sh; fi

build:
  extends: .build_template
  only:
    - dev
    - /staging-v(\d+\.)?(\d+\.)?(\*|\d+)$/
    - /staging-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

build_prod:
  extends: .build_template
  when: manual
  allow_failure: false
  environment:
    name: production
  only:
    - /release-v(\d+\.)?(\d+\.)?(\*|\d+)$/
    - /release-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

build_uat:
  extends: .build_template
  allow_failure: false
  environment:
    name: uat
  only:
    - /uat-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

check_migration_work_staging:
  image: binhnd1008/python-with-pipenv:3.8.2
  stage: check_migration
  only:
    - /staging-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/
  before_script:
    - pip install pyright cqlsh pipenv==v2022.5.2 > /dev/null 2>&1
    - pipenv install --dev --system > /dev/null 2>&1
    - pipenv install --system > /dev/null 2>&1
    - export ENV=ci
    - export DEPLOY_BRANCH=staging
  script:
    - if [[ -f dev-scripts/check-migration.sh ]] ; then dev-scripts/check-migration.sh; fi

check_migration_work_prod:
  image: binhnd1008/python-with-pipenv:3.8.2
  stage: check_migration
  only:
    - /release-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/
  before_script:
    - pip install pyright cqlsh pipenv==v2022.5.2 > /dev/null 2>&1
    - pipenv install --dev --system > /dev/null 2>&1
    - pipenv install --system > /dev/null 2>&1
    - export ENV=ci
    - export DEPLOY_BRANCH=prod
  script:
    - if [[ -f dev-scripts/check-migration.sh ]] ; then dev-scripts/check-migration.sh; fi

.deploy_template:
  stage: deploy
  image: gapo2020/httpie
  script:
    - >
      http --auth "${WEBHOOKD_BASIC_AUTH}"
      "${WEBHOOKD_URL}?application=${APP_NAME}&branch=${DEPLOY_BRANCH}&commit=${CI_COMMIT_SHA}&version=$(echo $CI_COMMIT_REF_NAME | rev | cut -d'-' -f 1 | rev)"
      &> /dev/stdout | tee -a /tmp/status
    - grep -q "Patching done!" /tmp/status; [ $? -eq 0 ] && exit 0 || exit 1
  except:
    - schedules

deploy_staging:
  extends: .deploy_template
  environment:
    name: staging
  variables:
    DEPLOY_BRANCH: staging
    WEBHOOKD_BASIC_AUTH: $STAGING_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $STAGING_WEBHOOKD_URL
  only:
    - /staging-v(\d+\.)?(\d+\.)?(\*|\d+)$/

deploy_prod:
  extends: .deploy_template
  when: on_success
  environment:
    name: production
  variables:
    DEPLOY_BRANCH: prod
    WEBHOOKD_BASIC_AUTH: $PROD_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $PROD_WEBHOOKD_URL
  only:
    - /release-v(\d+\.)?(\d+\.)?(\*|\d+)$/

deploy_work_staging:
  extends: .deploy_template
  environment:
    name: staging
  variables:
    DEPLOY_BRANCH: staging
    WEBHOOKD_BASIC_AUTH: $STAGING_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $STAGING_WORK_WEBHOOKD_URL
  only:
    - /staging-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

deploy_work_prod:
  extends: .deploy_template
  when: on_success
  environment:
    name: production
  variables:
    DEPLOY_BRANCH: prod
    WEBHOOKD_BASIC_AUTH: $PROD_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $PROD_WORK_WEBHOOKD_URL
  only:
    - /release-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

deploy_work_uat:
  extends: .deploy_template
  when: on_success
  environment:
    name: uat
  variables:
    DEPLOY_BRANCH: prod_uat
    WEBHOOKD_BASIC_AUTH: $UAT_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $UAT_WORK_WEBHOOKD_URL
  only:
    - /uat-work-v(\d+\.)?(\d+\.)?(\*|\d+)$/

cleanup:
  stage: cleanup
  image: cosmintitei/bash-curl
  variables:
    WEBHOOKD_BASIC_AUTH: $STAGING_WEBHOOKD_BASIC_AUTH
    WEBHOOKD_URL: $STAGING_WEBHOOKD_CLEANUP_URL
  script:
    - export REGISTRY_IDS=($REGISTRY_ID)
    - >
      for i in "${REGISTRY_IDS[@]}"; do
      curl --compressed -sS
      -H 'Connection: keep-alive'
      -u "${WEBHOOKD_BASIC_AUTH}"
      "${WEBHOOKD_URL}?project=${CI_PROJECT_ID}&branch=${CI_COMMIT_REF_NAME}&registry=${i}"
      ; done
  only:
    - schedules
