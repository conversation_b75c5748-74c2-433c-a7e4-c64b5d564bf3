keyspace: "${CASSANDRA_KEYSPACE}"
profiles:
  prod:
    replication:
      class: SimpleStrategy
      replication_factor: 3
migrations_path: ./migrations
# Date-based migration names
new_migration_name: "v{date:YYYYMMDDHHmmss}_{desc}"

# Default migration names
new_migration_name: "v{next_version:03d}_{desc}"

# Custom initial migration content
new_migration_text: |
  /* Cassandra migration for keyspace {keyspace}.
     Version {next_version} - {date}

     {full_desc} */

# Custom initial migration content for cql scripts
new_cql_migration_text: |
  /* Cassandra migration for keyspace {keyspace}.
     Version {next_version} - {date}

     {full_desc} */
