from typing import Any
from uuid import uuid4

from sqlalchemy import (
    JSO<PERSON>,
    TIMESTAMP,
    Boolean,
    Column,
    Index,
    PrimaryKeyConstraint,
    String,
    text,
)
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, SMALLINT, TINYINT
from sqlalchemy.ext.declarative import declarative_base, declared_attr

STRING_ID = 24
DEFAULT_STRING_SIZE = 16
PAIR_ID = STRING_ID * 2 + 1
STRING_SIZE = 1024
BIGINT_SIZE = 8
COUNT_SIZE = 4


def generate_uuid_bin():
    _u = uuid4()
    return _u.bytes


class BaseAbstract:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()  # type: ignore

    __table_args__ = {"mysql_engine": "InnoDB"}
    __abstract__ = True


Base: Any = declarative_base(cls=BaseAbstract)


class ChatBase(Base):
    __abstract__ = True
    # id = Column(VARBINARY(16), primary_key=True, default=generate_uuid_bin)
    _created = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    _deleted = Column(TIMESTAMP)


class Threads(ChatBase):
    __table_args__ = (
        Index("idx__threads__parent_id", "parent_id", unique=False),
        Index("idx__threads__collab_id", "collab_id", unique=False),
        Index("idx__threads__associate_link", "associate_link", unique=False),
        Index(
            "idx__threads__parent_id_root_message_id",
            "parent_id",
            "root_message_id",
            unique=True,
        ),
        {"mysql_engine": "InnoDB"},
    )
    id = Column(BIGINT(BIGINT_SIZE, unsigned=True), primary_key=True)

    parent_id = Column(
        BIGINT(BIGINT_SIZE, unsigned=True),
        nullable=True,
    )
    parent_thread_type = Column(String(DEFAULT_STRING_SIZE))
    root_message_id = Column(BIGINT(COUNT_SIZE, unsigned=True), nullable=True)
    commenters = Column(JSON)

    avatar = Column(String(STRING_SIZE))
    name = Column(String(STRING_SIZE))
    blocked_by = Column(String(STRING_ID), default=None, nullable=True)
    pair_ids = Column(String(PAIR_ID), index=True, unique=True)
    type = Column(
        String(DEFAULT_STRING_SIZE),
        index=True,
        default="direct",
        server_default="direct",
    )
    # group_level = Column(
    #     TINYINT(unsigned=True), default=0, server_default="0", nullable=False
    # )

    message_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    creator = Column(String(STRING_ID))
    centralize_last_message = Column(JSON)
    member_count = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    link = Column(String(128), default="", index=True)
    associate_link = Column(String(256), default="")

    description = Column(String(256), default="")

    video_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    url_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    image_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    file_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    ban_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    pinned_count = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    pinned_message_id = Column(
        BIGINT(COUNT_SIZE, unsigned=True), default=0, server_default="0"
    )
    information = Column(JSON)
    _updated = Column(
        BIGINT(BIGINT_SIZE, unsigned=True),
        default=0,
        server_default="0",
        index=True,
    )
    banner = Column(JSON)
    workspace_id = Column(String(STRING_ID))
    collab_id = Column(String(STRING_ID))

    group_level = Column(
        BIGINT(COUNT_SIZE, unsigned=True),
        default=0,
        server_default="0",
        nullable=False,
    )


class ParticipantThreads(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "participant_threads"

    __table_args__ = (
        PrimaryKeyConstraint("user_id", "thread_id"),
        # Index("user_pin", "user_id", "pin_pos"),
        Index("_ix_user_thread", "thread_id", "user_id"),
        Index("idx__participant_threads__bot_type", "type", unique=False),
        Index(
            "idx__participant_threads__thread_id", "thread_id", unique=False
        ),
        Index(
            "idx__participant_threads__thread_id_removed",
            "thread_id",
            "is_removed",
            unique=False,
        ),
        {"mysql_engine": "InnoDB"},
    )
    user_id = Column(String(STRING_ID), nullable=False)
    partner_id = Column(String(STRING_ID), nullable=True)
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    _updated = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    is_removed = Column(TINYINT(), default=False, server_default="0")
    type = Column(
        String(DEFAULT_STRING_SIZE), default="user", server_default="user"
    )
    last_message = Column(JSON)

    pin_pos = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    """Time when the post was pinned in its folder"""

    pin_default_pos = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    """Time whan the post was pinned in default folder."""

    delete_to = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    role = Column(String(DEFAULT_STRING_SIZE), nullable=False)
    enable_notify = Column(TINYINT, default=True, server_default="1")
    alias = Column(String(STRING_SIZE))
    tags = Column(String(STRING_SIZE))

    read_count = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    video_count = Column(BIGINT(COUNT_SIZE), default=0, server_default="0")
    url_count = Column(BIGINT(COUNT_SIZE), default=0, server_default="0")
    image_count = Column(BIGINT(COUNT_SIZE), default=0, server_default="0")
    file_count = Column(BIGINT(COUNT_SIZE), default=0, server_default="0")

    banned_level = Column(
        SMALLINT(unsigned=True), default=0, server_default="0"
    )
    banned_by = Column(String(STRING_ID), nullable=True)
    banned_from = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=True)
    folder = Column(
        String(32),
        default="default",
        server_default="default",
    )

    mark_unread = Column(Boolean, default=False, server_default="0")

    is_deactivated = Column(Boolean, default=False, server_default="0")


class Users(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "users"

    __table_args__ = (PrimaryKeyConstraint("id"), {"mysql_engine": "InnoDB"})
    id = Column(String(STRING_ID), nullable=False)
    name = Column(String(STRING_SIZE))
    seen_at = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    channels = Column(String(STRING_SIZE), nullable=True)
    pass_code = Column(String(STRING_SIZE), nullable=True)
    enable_notify = Column(TINYINT, default=True, server_default="1")


class BlockUsers(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "block_users"

    __table_args__ = (
        PrimaryKeyConstraint("user_id", "blocked_id"),
        {"mysql_engine": "InnoDB"},
    )
    user_id = Column(String(STRING_ID), nullable=False)
    blocked_id = Column(String(STRING_ID), nullable=False)
    pair_ids = Column(String(PAIR_ID), index=True)
    is_removed = Column(TINYINT, default=False, server_default="0", index=True)
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True))
    type = Column(
        String(DEFAULT_STRING_SIZE), default="user", server_default="user"
    )


class ThreadLog(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "thread_logs"

    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "created_at"),
        {"mysql_engine": "InnoDB"},
    )
    created_at = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    deleted_at = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=True)
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    maker_id = Column(String(STRING_ID), nullable=False)
    detail = Column(JSON)


class MediaCollections(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "media_collections"

    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "type", "message_id"),
        Index("_ix_thread_message", "thread_id", "message_id", unique=False),
        {"mysql_engine": "InnoDB"},
    )
    created_at = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    user_id = Column(String(STRING_ID), nullable=False)
    message_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    detail = Column(JSON)
    length = Column(TINYINT(unsigned=True), server_default="1")
    type = Column(TINYINT(unsigned=True), server_default="0")


class PinCollections(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "pin_collections"

    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "message_id"),
        Index(
            "_ix_thread_message_pin", "thread_id", "message_id", unique=True
        ),
        {"mysql_engine": "InnoDB"},
    )
    created_at = Column(
        BIGINT(BIGINT_SIZE, unsigned=True),
        default=0,
        server_default="0",
        index=True,
    )
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    message_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)


class ParticipantFolders(Base):
    @declared_attr
    def __tablename__(self):
        return "participant_folders"

    __table_args__ = (
        PrimaryKeyConstraint("user_id", "position"),
        {"mysql_engine": "InnoDB"},
    )

    user_id = Column(INTEGER(10), index=True)
    name = Column(String(100), nullable=False)
    alias = Column(String(50), nullable=False)
    avatar = Column(String(200), default=None, nullable=True)
    position = Column(INTEGER(10))
    created_at = Column(
        TIMESTAMP,
        server_default=text("CURRENT_TIMESTAMP"),
    )
    deleted_at = Column(
        TIMESTAMP,
        nullable=True,
    )


class MediaCollectionDeleteLogs(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "media_collection_delete_logs"

    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "message_id", "user_id"),
        {"mysql_engine": "InnoDB"},
    )

    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    user_id = Column(String(STRING_ID), nullable=False)
    message_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)


class Policies(ChatBase):
    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "policy_id"),
        {"mysql_engine": "InnoDB"},
    )

    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    user_id = Column(String(STRING_ID), nullable=False)
    policy_id = Column(INTEGER(4, unsigned=True), nullable=False)
    body = Column(String(STRING_SIZE))
    is_removed = Column(TINYINT, default=False, server_default="0", index=True)


class FlirtSamples(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "flirt_samples"

    __table_args__ = {"mysql_engine": "InnoDB"}
    id = Column(SMALLINT(unsigned=True), primary_key=True, autoincrement=True)
    sample = Column(String(STRING_SIZE))


class ActivityLogs(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "activity_logs"

    __table_args__ = (
        PrimaryKeyConstraint("thread_id", "created_at"),
        Index("_ix_thread_user_message", "thread_id", "user_id", "created_at"),
        Index(
            "_ix_thread_user_type",
            "thread_id",
            "user_id",
            "type",
            "created_at",
        ),
        Index("_ix_thread_type", "thread_id", "type", "created_at"),
        {"mysql_engine": "InnoDB"},
    )
    created_at = Column(
        BIGINT(BIGINT_SIZE, unsigned=True), default=0, server_default="0"
    )
    thread_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    user_id = Column(String(STRING_ID), nullable=False)
    message_id = Column(BIGINT(BIGINT_SIZE, unsigned=True), nullable=False)
    detail = Column(JSON)
    type = Column(TINYINT(unsigned=True), server_default="0")


class AlembicVersions(Base):
    @declared_attr
    def __tablename__(cls):
        return "alembic_version"

    version_num = Column(String(32), primary_key=True, default=0)


class QuickMessage(ChatBase):
    @declared_attr
    def __tablename__(cls):
        return "quick_messages"

    __table_args__ = (
        PrimaryKeyConstraint("user_id", "command"),
        {"mysql_engine": "InnoDB"},
    )
    user_id = Column(String(STRING_ID), nullable=False)
    command = Column(String(STRING_ID), nullable=False)
    body = Column(JSON)