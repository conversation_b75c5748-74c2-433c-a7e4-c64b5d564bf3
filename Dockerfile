FROM python:3.8.13-slim as pip_layer
RUN --mount=type=cache,target=/var/cache/apt \
    apt-get update  \ 
    && apt-get install --no-upgrade -y git-core gettext-base default-libmysqlclient-dev gcc  \
    && rm -rf /var/lib/apt/lists/*
RUN --mount=type=cache,target=/root/.cache/pip pip3 install cython==0.29.30 opentelemetry-distro==0.44b0 opentelemetry-exporter-otlp opentelemetry-instrumentation-fastapi==0.44b0 \
    && pip3 install  -v --no-binary :all: falcon==3.1.1 \
    && pip install pipenv==2021.5.29

# RUN opentelemetry-bootstrap -a install
WORKDIR /app
COPY Pipfile Pipfile.lock /app/
RUN --mount=type=cache,target=/root/.cache/pipenv pipenv install  --system --deploy


FROM python:3.8.13-slim as runtime_layer
RUN --mount=type=cache,target=/var/cache/apt \
    apt-get update  \ 
    && apt-get install --no-upgrade -y git-core gettext-base default-libmysqlclient-dev gcc  \
    && rm -rf /var/lib/apt/lists/*

# COPY --from=pip_layer /var/cache/apt /var/cache/apt
COPY --from=pip_layer /usr/local/bin /usr/local/bin
COPY --from=pip_layer /usr/local/lib/python3.8 /usr/local/lib/python3.8

WORKDIR /app
ADD . /app
ENV PATH=/app/.venv/bin:$PATH
RUN chmod +x /app/run.sh

ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["/app/run.sh"]
