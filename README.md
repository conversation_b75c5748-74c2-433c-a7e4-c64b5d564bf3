# chat-api
Main repository for chat service.

## Development

### Prequisite

- [Docker](https://docs.docker.com/)
- [Pipenv](https://pipenv.pypa.io/en/latest/)
- [Python 3.8](https://www.how2shout.com/linux/install-python-3-9-or-3-8-on-ubuntu-22-04-lts-jammy-jellyfish/)


Prefer editor: [Vscode](https://code.visualstudio.com/).

### Setup

```bash
pipenv install
pipenv install --dev
# install pre-commit hook
pre-commit install
# setup config file like env.example. For staging env, you can use env.staging file directly.
# change ENV_FILE to your target env file.
ENV_FILE=env.staging pipenv run app 
```
### Testing

```bash
# setup local database for testing
cp docker-compose.test.yaml docker-compose.yaml
# setup local database ONCE (i.e. cassandra schema)
pipenv run setup-db 
pipenv run test
# or with coverage report
pipenv run test-cov
```

You don't need to run `pipenv run setup-db` everytime you want to test. This script
is required to setup Cassandra database schema, and you only need to do it once.

There are several integration tests with membership service. By default, they
are disabled because we need to setup valid `MEMBERSHIP_URL` environment variable.
To enable these test, you need to set `ENABLE_MEMBERSHIP_SERVICE_TESTS` flag. For example:

```bash
ENABLE_MEMBERSHIP_SERVICE_TESTS=true pytest
````

### i18n messages

```bash
pipenv run extract-i18n
# update translations in *.po files
pipenv run update-i18n

```

### Migration

Kiểm tra xem có cần revision: 

```bash
export DEPLOY_BRANCH=staging # or prod 
pipenv check-migration
Db schema is up to date, current revision: 60
```

Xem thêm tại [Docs](./docs/migrate.md)
### Docs

- [CONTRIBUTING.md](CONTRIBUTING.md)
- [Chat API docs (Redoc)](https://gapowork.redoc.ly/tag/Chat-Service_thread)
- [All docs](./docs/README.md)
- [CHANGELOG.md](./CHANGELOG.md)


### Notes

Code sử dụng  black formatter để fix các lỗi về format trong repo. Vì vậy, vui lòng chỉnh lại formatter python nếu cần thiết. Ví dụ hướng dẫn [setup black formatter trong vscode](https://dev.to/adamlombard/how-to-use-the-black-python-code-formatter-in-vscode-3lo0).
