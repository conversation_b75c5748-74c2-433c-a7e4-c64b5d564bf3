# Global options:

[mypy]
plugins = pydantic.mypy
python_version = 3.8

allow_untyped_globals=True

# Disallows calling functions without type annotations from functions with type annotations.
disallow_untyped_calls=False

# Disallows defining functions without type annotations or with incomplete type annotations.
disallow_untyped_defs=False

# Disallows defining functions with incomplete type annotations.
disallow_incomplete_defs=False


warn_unused_configs = False

# Suppresses error messages about imports that cannot be resolved.
# ignore_missing_imports = True

allow_redefinition = True

# Disallows usage of types that come from unfollowed imports (anything imported from an unfollowed import is automatically given a type of `Any`).
disallow_any_unimported = False 

# Shows a warning when encountering any code inferred to be unreachable or redundant after performing type analysis.
warn_unreachable=False

# Type-checks the interior of functions without type annotations.
# default -> False
check_untyped_defs = True

# Shows error codes in error messages. See [Error codes](https://mypy.readthedocs.io/en/stable/error_codes.html#error-codes) for more information.
show_error_codes = True

# Warns about unneeded `# type: ignore` comments.
warn_unused_ignores = False

# Warns about casting an expression to its inferred type.
warn_redundant_casts = False


# Shows errors for missing return statements on some execution paths.
warn_return_any = True 
# ignore_missing_imports = True

# follow_imports = silent
no_implicit_reexport = True 

no_implicit_optional=True

# Per-module options:

[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
warn_untyped_fields = True


[mypy-falcon,falcon.http_error,falcon.testing]
ignore_missing_imports = True

[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-cassandra.*]
ignore_missing_imports = True

[mypy-nanoid]
ignore_missing_imports = True

[mypy-pika]
ignore_missing_imports = True

[mypy-pika.*]
ignore_missing_imports = True

[mypy-bson]
ignore_missing_imports = True

[mypy-cerberus]
ignore_missing_imports = True

[mypy-paho.*]
ignore_missing_imports = True

[mypy-bs4]
ignore_missing_imports = True

[mypy-logzero]
ignore_missing_imports = True

[mypy-bitstring]
ignore_missing_imports = True

[mypy-user_agents]
ignore_missing_imports = True


[mypy-urlextract]
ignore_missing_imports = True

[mypy-redis_lock]
ignore_missing_imports = True

[mypy-tqdm]
ignore_missing_imports = True

[mypy-grpc]
ignore_missing_imports = True

[mypy-prometheus-client,_pytest,sentry_sdk.*,rich.*,pyparsing.*,pyparsing,importlib_metadata,furl,urlquote]
ignore_missing_imports = True

[mypy-confluent_kafka]
ignore_missing_imports = True
