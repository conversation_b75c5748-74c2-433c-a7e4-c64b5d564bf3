import sys
import os

from typing import Optional

from chat.backgrounds import setup as background_task_setup
from chat.config import Settings, load_config
from chat.consumers.rabbitmq import AmqpConsumer
from chat.container.app import AppContainer


def create_worker(
    config: Optional[Settings] = None, container: Optional[AppContainer] = None
):
    if not config:
        env_file = os.environ.get("ENV_FILE", ".env.prod")
        config = load_config(env_file)
    if container is None:

        container = AppContainer()
        container.init_resources()
        container.wire(packages=[sys.modules["chat"]])

        # make sure everything is ok
        container.check_dependencies()

    _, background_tasks = background_task_setup(
        container,
    )

    rb_cfg = config.rabbitmq
    app = container.app()
    rb_consumer = AmqpConsumer(
        "Api-background-consumer",
        app.conns.rb_v2,
        rb_cfg.background_route.queue_name,
        rb_cfg.background_route.exchange_routings,
        background_tasks,
    )

    app.conns._conns.append(rb_consumer)
    return rb_consumer


if __name__ == "__main__":
    worker = create_worker()
    worker.communicate()
