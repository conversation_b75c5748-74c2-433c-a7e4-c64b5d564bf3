[project]
name = "chat-api"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.8, <3.9"
readme = "README.md"
dependencies = [
    "alembic==1.13.2",
    "arrow==1.3.0",
    "async-timeout==4.0.3",
    "beautifulsoup4==4.12.3",
    "bitarray==2.9.2",
    "bitstring==4.2.3",
    "bs4==0.0.2",
    "bson==0.5.10",
    "cassandra-driver==3.29.1",
    "cassandra-migrate==0.3.3",
    "cerberus==1.3.5",
    "certifi==2024.7.4",
    "cffi==1.16.0",
    "charset-normalizer==3.3.2",
    "click==8.1.3",
    "confluent-kafka==2.5.0",
    "cryptography==43.0.0",
    "cython==0.29.30",
    "dependency-injector==4.41.0",
    "falcon==3.1.3",
    "filelock==3.15.4",
    "future==1.0.0",
    "geomet==0.2.1.post1",
    "gevent==24.2.1",
    "greenlet==3.0.3",
    "grpc-stubs==********",
    "grpcio==1.65.1",
    "grpcio-tools==1.65.1",
    "gunicorn==22.0.0",
    "hiredis==3.0.0",
    "idna==3.7",
    "importlib-metadata==8.0.0",
    "importlib-resources==6.4.0",
    "kafka-python==2.0.2",
    "logzero==1.7.0",
    "lz4==4.3.3",
    "mako==1.3.5",
    "markdown==3.6",
    "markdown-it-py==3.0.0",
    "markupsafe==2.1.5",
    "mdurl==0.1.2",
    "mysqlclient==1.4.6",
    "nanoid==2.0.0",
    "nose==1.3.7",
    "numpy==1.24.4",
    "orjson==3.10.6",
    "packaging==24.1",
    "paho-mqtt==1.6.1",
    "pandas==2.0.3",
    "phonenumbers==8.13.40",
    "pika==1.3.2",
    "platformdirs==4.2.2",
    "polib==1.2.0",
    "prometheus-client==0.20.0",
    "protobuf==5.27.2",
    "pycparser==2.22",
    "pydantic==1.10.15",
    "pygments==2.18.0",
    "pyjwt==2.8.0",
    "pymdown-extensions==10.8.1",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.1",
    "python-redis-lock==4.0.0",
    "pytz==2024.1",
    "pyyaml==3.13",
    "raven==6.10.0",
    "redis==5.0.7",
    "requests==2.32.3",
    "rich==13.7.1",
    "sentry-sdk==1.17.0",
    "setuptools==71.1.0",
    "six==1.16.0",
    "soupsieve==2.5",
    "sqlalchemy==1.4.52",
    "structlog==24.4.0",
    "structlog-sentry==2.1.0",
    "tabulate==0.9.0",
    "types-python-dateutil==2.9.0.20240316",
    "typing-extensions==4.12.2",
    "tzdata==2024.1",
    "ua-parser==0.18.0",
    "ujson==5.10.0",
    "uritools==4.0.3",
    "urlextract==1.9.0",
    "urllib3==2.2.2",
    "urlquote==2.1.0",
    "user-agents==2.2.0",
    "werkzeug==3.0.3",
    "wheel==0.43.0",
    "zipp==3.19.2",
    "zope-event==5.0",
    "zope-interface==6.4.post2",
]

[dependency-groups]
dev = [
    "mypy==1.10.0",
    "black==22.10.0",
    "pyright",
    "isort",
    "autoflake",
    "pre-commit",
    "pytest",
    "pytest-cov",
    "pytest-instafail",
    "pytest-timeout",
    "babel",
    "pylint",
    "types-Markdown==3.3.29",
    "types-redis==4.3.3",
    "types-requests==2.28.0",
    "types-ujson==5.4.0",
    "types-urllib3==1.26.16",
    "types-polib",
    "pytest-testmon",
    "tqdm",
    "types-protobuf",
    "mypy-protobuf",
    "types-pygments",
    "types-colorama",
    "types-commonmark",
    "types-mysqlclient",
    "types-beautifulsoup4",
    "radon",
]

[tool.uv]
package = false

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple"
