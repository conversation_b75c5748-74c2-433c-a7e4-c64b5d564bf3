from chat.utils.common import (
    is_valid_http_url,
    is_valid_image_url,
    normalize_and_valid_phone_number,
)


def test_validate_phone_number():
    valid_numbers = ["+84 354 123 456", "0354123456", "+84354123456"]
    for number in valid_numbers:
        assert normalize_and_valid_phone_number(number) == "+84354123456"

    invalid_numbers = ["354 123 23", "+83 032 123 123"]
    for number in invalid_numbers:
        assert not normalize_and_valid_phone_number(number)


def test_is_valid_image_url():
    valid_image_urls = [
        "https://link/a.jpg",
        "https://link/a.png",
        "https://link/a.jpeg",
    ]
    for image_url in valid_image_urls:
        assert is_valid_image_url(image_url)

    invalid_urls = ["ftp://link/a.jpg", "https://link/a.mp4"]
    for image_url in invalid_urls:
        assert not is_valid_image_url(image_url)


def test_is_valid_http_url():
    valid_urls = ["http://a/aaa?s=2&v=3", "https://b/a#sss"]
    for url in valid_urls:
        assert is_valid_http_url(url)

    invalid_urls = ["ftp://aaa/b"]
    for url in invalid_urls:
        assert not is_valid_http_url(url)
