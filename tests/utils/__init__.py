import os
from dataclasses import dataclass
from typing import Any, Optional

import pytest

from chat.config import Settings, load_config
from chat.model import make_input


@dataclass
class TestSchemaInput:
    name: str = ""
    data: Any = None
    error: Any = None


@dataclass
class TestAPIInput:
    name: str
    body: Any
    status_code: int
    error_code: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None

    def check_response(self, res):
        assert res.status_code == self.status_code
        if self.status_code != 200:
            body = res.json
            if self.error_code:
                assert self.error_code == body["error"]
            if self.error:
                assert self.error == body["error_details"][0]["msg"]

            if self.message is not None:
                assert self.message == body["message"]


def mocked_requests_post(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code

        def json(self):
            return self.json_data

        def text(self):
            return (args, kwargs)

    urlBlock = "http://TEST_BLOCK_URL"
    urlUnblock = "http://TEST_UNBLOCK_URL"
    token = "fakeToken"
    if urlBlock in kwargs["url"]:
        headerToken = kwargs["headers"]["Authorization"]
        body = kwargs["json"]
        if headerToken in (token,):
            if body == {"request_user_id": "521", "target_user_id": "522"}:
                return MockResponse(None, 200)

        return MockResponse(None, 403)
    elif urlUnblock in kwargs["url"]:
        headerToken = kwargs["headers"]["Authorization"]
        body = kwargs["json"]
        if headerToken in (token,):
            if body == {"request_user_id": "521", "target_user_id": "522"}:
                return MockResponse(None, 200)

    return MockResponse(None, 404)


def mocked_authen_get(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code

        def json(self):
            return self.json_data

        def text(self):
            return (args, kwargs)

    return MockResponse(
        {"data": [{"apiKey": "fakeToken", "scopes": None}]}, 200
    )


def mocked_requests_get(*args, **kwargs):
    urlContact = "http://TEST_CONTACT_URL/"
    urlRelationV2 = "http://TEST_RELATION_V2"
    relationApiKey = "test-key"
    urlIsFriend = "http://TEST_IS_FRIEND"
    urlIsFriends = "http://TEST_IS_FRIENDS"
    urlUserInfo = "http://TEST_USER_INFO"
    urlPageItemDefault = "http://TEST_PAGE_ITEM"
    urlPageCollection = "http://TEST_PAGE_COLLECTION"
    urlPageItemFound = "http://TEST_PAGE_ITEM/1774279461541427012"
    urlPageItemNotFound = "http://TEST_PAGE_ITEM/1774279461541427011"
    urlPagePermissionDefault = "http://TEST_PAGE_PERMISSION"
    urlPagePermissionAllow = "http://TEST_PAGE_PERMISSION/1774279461541427012"
    urlPagePermissionNotAllow = (
        "http://TEST_PAGE_PERMISSION/1774279461541427011"
    )
    pageChatPermission = "CHAT_AS_PAGE"
    pageFakeData = {
        "alias": "puq4mrbd",
        "avatar": "https://image-1.gapo.vn/images/2aac3e9f-29d6-4f59-9a74-76e601d35178.jpeg",  # noqa
        "avatar_thumb_pattern": "",
        "page_id": "1774279461541427012",
        "status_verify": 0,
        "title": "Nắng vàng, biển xanh...cát trắng, mây hồng",
    }

    user1234Fake = {
        "id": "1234",
        "display_name": "hello page",
        "status_verify": 1,
        "avatar": None,
    }

    user521Fake = {
        "id": "521",
        "display_name": "hello page",
        "status_verify": 1,
        "avatar": None,
    }

    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code

        def json(self):
            return self.json_data

        @property
        def text(self):
            return self.json_data

    if kwargs["url"] == urlContact + "internal/check_have_contact/521/522":
        return MockResponse(3, 200)
    elif kwargs["url"] == urlContact + "internal/check_have_contact/522/1234":
        return MockResponse(2, 200)
    elif kwargs["url"] == urlContact + "internal/check_have_contact/521/4567":
        return MockResponse(1, 200)
    elif kwargs["url"] == urlContact + "internal/check_have_contact/522/523":
        return MockResponse(0, 200)
    elif kwargs["url"] == urlIsFriend:
        return MockResponse({"isFriend": 1}, 200)
    elif kwargs["url"] == urlIsFriends:
        return MockResponse(
            {
                "isFriends": {
                    "1234": {"status": 1},
                    "4567": {"status": 1},
                    "5678": {"status": 0},
                }
            },
            200,
        )
    elif urlRelationV2 in kwargs["url"]:
        gapoKey = kwargs["headers"]["x-gapo-api-key"]
        if gapoKey in (relationApiKey,):
            return MockResponse(
                {
                    "data": {
                        "1234": 3,
                        "4567": 3,
                        "5678": 0,
                        "522": 3,
                        "523": 0,
                    }
                },
                200,
            )
    elif urlUserInfo in kwargs["url"]:
        return MockResponse(
            {"data": [user521Fake, user1234Fake]},
            200,
        )
    elif urlPageItemFound in kwargs["url"]:
        return MockResponse(
            {"data": pageFakeData},
            200,
        )
    elif urlPageItemNotFound in kwargs["url"]:
        return MockResponse(
            {"data": {}},
            404,
        )
    elif urlPageItemDefault in kwargs["url"]:
        return MockResponse(
            {"data": {}},
            200,
        )
    elif urlPageCollection in kwargs["url"]:
        return MockResponse(
            {"data": [pageFakeData]},
            200,
        )
    elif urlPagePermissionAllow in kwargs["url"]:
        return MockResponse(
            {"data": {"role_permissions": [pageChatPermission]}}, 200
        )
    elif urlPagePermissionNotAllow in kwargs["url"]:
        return MockResponse(None, 403)
    elif urlPagePermissionDefault in kwargs["url"]:
        return MockResponse(
            {"data": {"role_permissions": [pageChatPermission]}}, 200
        )

    return MockResponse(None, 404)


def mocked_cassandra(*args, **kwargs):
    class NewConnection:
        def __init__(*args, **kwargs):
            pass

    class NewCluster:
        def __init__(*args, **kwargs):
            pass

        def connect(self, *args, **kwargs):
            return NewConnection(*args, **kwargs)

    return NewCluster(*args, **kwargs)


def get_test_config():
    env = os.environ.get("ENV")
    if env == "ci":
        return load_config(".env.ci")
    conf = load_config(".env.test")

    _throw_if_using_non_test_config(conf)

    return conf


def _throw_if_using_non_test_config(setting: Settings):
    for valid_db_domain in ("mysql:3306", "127.0.0.1"):
        if valid_db_domain in setting.mysql.url:
            return

    raise Exception("Don't use non-test config for tests")


ENABLE_MEMBERSHIP_SERVICE_TESTS = (
    os.environ.get("ENABLE_MEMBERSHIP_SERVICE_TESTS", "false").lower()
    == "true"
)
"""Enable membership integration tests.

This flag is false by default because it's tricky to setup these tests.
You need to point to valid membership_url before set this environment variable
to false."""


skip_membership_tests = pytest.mark.skipif(
    not ENABLE_MEMBERSHIP_SERVICE_TESTS, reason="Skip membership tests"
)


def check_schema(cls, testcase: TestSchemaInput):
    _, errors = make_input(cls, testcase.data)
    if testcase.error:
        assert errors and errors[0]["msg"] == testcase.error  # type: ignore
    else:
        assert errors is None, errors
