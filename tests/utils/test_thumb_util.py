from chat.load_cdn_mapping import load
from chat.utils.common import encode_url
from chat.utils.thumb import video_thumb
from chat.utils.validator_helper import is_gapo_file
from chat.utils import cdn


def test_video_thumb():
    load("./cdn_mapping_example.json")
    thumb = video_thumb(
        "https://gapo-files.s3south.storage.com.vn/files/origin/0dbd4239-84e7-4f41-aab6-44c7b2020486/5F29568E-B1A8-4FB3-A4CB-26D69BE69060_1659493821.mp4"
    )
    assert (
        thumb
        == "https://files-3.gapo.vn/files/origin/0dbd4239-84e7-4f41-aab6-44c7b2020486/thumb.jpeg"
    )

    assert video_thumb("https://unknown_url/a/v.mp4") == None


def test_encode_url():
    url = "https://image-1.gapo.vn/images/491b4e54-7839-43ca-b311-6cde8dce94b9/ẢNH_CẮT-04.jpeg"
    output_url = "https://image-1.gapo.vn/images/491b4e54-7839-43ca-b311-6cde8dce94b9/%E1%BA%A2NH_C%E1%BA%AET-04.jpeg"
    assert encode_url(encode_url(url)) == output_url
    assert encode_url(url) == output_url


def test_valid_file():
    load("./cdn_mapping_example.json")
    urls = [
        "https://files-3.gapowork.vn/files/origin/acc10c1a-372e-4b2b-a9e2-0b926b1ec7da/bug_reload.mov"
    ]

    for url in urls:
        assert is_gapo_file(url)


def test_video_cdn():
    load("./cdn_mapping_example.json")
    urls = [
        "https://files-3.gapowork.vn/files/origin/acc10c1a-372e-4b2b-a9e2-0b926b1ec7da/bug_reload.mov"
    ]

    for url in urls:
        assert cdn.video(url)
