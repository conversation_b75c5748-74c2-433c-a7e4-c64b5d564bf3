import logging

import falcon
import pytest
from cassandra import DriverException
from falcon import testing
from pydantic import validator
from sqlalchemy.exc import SQLAlchemyError

from chat.exception import (
    CustomHTTPError,
    InternalServerError,
    InvalidParameters,
    add_error_handlers,
)
from chat.model import BaseModel


class Item(BaseModel):
    id: int

    @validator("id")
    def positive_id(cls, id: int):
        if id <= 0:
            raise ValueError("Must be a positive number")


def _create_app():
    app = falcon.App()
    LOG_FORMAT = "%(levelname)s %(asctime)s - %(message)s"
    logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
    add_error_handlers(app)

    class CustomResource(object):
        def __init__(self, handle):
            self._handle = handle

        def on_get(self, req, resp, *args):
            self._handle()

    def internal_error():
        raise InternalServerError("error", error_details=[{"msg": "some error"}])

    def general_error():
        raise Exception("my exception")

    def error_with_detail():
        raise InvalidParameters(error_details=[{"msg": "Thread not found"}])

    def pydantic_error():
        Item(id="test")  # type: ignore

    def pydantic_error2():
        Item(id=-3)

    def sql_exception():
        raise SQLAlchemyError()

    def cassandra_exception():
        raise DriverException()

    def custom_error_1():
        raise CustomHTTPError(falcon.HTTP_400, error={"message": "error1"})

    def custom_error_2():
        raise CustomHTTPError(400, error={"message": "error2"})

    class ParamResource:
        def on_get(self, req, res, param):
            res.media = {"param": param}

    app.add_route("/errors/params/{param:int}", ParamResource())
    app.add_route("/errors/internal", CustomResource(internal_error))
    app.add_route("/errors/with-detail", CustomResource(error_with_detail))
    app.add_route("/errors/pydantic", CustomResource(pydantic_error))
    app.add_route("/errors/pydantic2", CustomResource(pydantic_error2))
    app.add_route("/errors/sql", CustomResource(sql_exception))
    app.add_route("/errors/cql", CustomResource(cassandra_exception))
    app.add_route("/errors/custom-1", CustomResource(custom_error_1))
    app.add_route("/errors/custom-2", CustomResource(custom_error_2))
    app.add_route("/errors/general", CustomResource(general_error))

    return app


@pytest.fixture()
def client():
    app = _create_app()
    return testing.TestClient(app)


def test_handle_param_error(client: testing.TestClient):
    response = client.simulate_get("/errors/params/test")
    ## TODO: not sure why falcon returns not found error
    assert response.status_code == 404


def test_internal_error(client: testing.TestClient):
    response = client.simulate_get("/errors/internal")
    assert response.status_code == InternalServerError.http_code
    body = response.json
    assert body["error"] == InternalServerError.error
    assert len(body["error_details"]) == 0


def test_error_with_detail(client: testing.TestClient):
    response = client.simulate_get("/errors/with-detail")
    assert response.status_code == InvalidParameters.http_code
    body = response.json
    assert len(body["error_details"]) == 1


def test_sql_error(client: testing.TestClient):
    response = client.simulate_get("/errors/sql")
    assert response.status_code == InternalServerError.http_code


def test_cassandra_error(client: testing.TestClient):
    response = client.simulate_get("/errors/cql")
    assert response.status_code == InternalServerError.http_code


def test_pydantic_error(client: testing.TestClient):
    response = client.simulate_get("/errors/pydantic")
    body = response.json
    assert response.status_code == 400
    assert body["error"] == InvalidParameters.error


def test_pydantic_error_with_custom_validator(client: testing.TestClient):
    response = client.simulate_get("/errors/pydantic2")
    body = response.json
    assert response.status_code == 400
    assert body["error"] == InvalidParameters.error


def test_custom_http_error_1(client: testing.TestClient):
    response = client.simulate_get("/errors/custom-1")
    body = response.json
    assert response.status_code == 400
    assert body["message"] == "error1"


def test_custom_http_error2(client: testing.TestClient):
    response = client.simulate_get("/errors/custom-2")
    body = response.json
    assert response.status_code == 400
    assert body["message"] == "error2"


def test_general_exception(client: testing.TestClient):
    response = client.simulate_get("/errors/general")
    body = response.json
    assert response.status_code == 500
    assert body["error"] == InternalServerError.error
