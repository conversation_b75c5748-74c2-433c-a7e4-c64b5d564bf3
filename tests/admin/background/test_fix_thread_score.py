# from chat.backgrounds.fix_thread_score import fix_thread_score_for_workspace
# from chat.container import AppContainer


# def test_fix_thread_score(container: AppContainer):
#     # dummy call
#     logger = container.log()
#     fix_thread_score_for_workspace(logger, container, user_ids=["1"])
#     fix_thread_score_for_workspace(logger, container, workspace_id="1")
#     fix_thread_score_for_workspace(logger, container, file="/sf")
