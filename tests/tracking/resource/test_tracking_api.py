from falcon.testing import TestClient

from chat.container import AppContainer
from tests.conftest import TEST_U1, user_header


def test_api(container: AppContainer, client: TestClient):

    result = client.simulate_post(
        "/me/ack",
        headers=user_header(TEST_U1),
        json={"event": "example", "payload": {"hello": "workd"}, "level": "error"},
    )

    assert result.status_code == 200
