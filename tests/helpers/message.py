import json
from uuid import uuid4, U<PERSON><PERSON>

from cassandra.query import SimpleStatement

from chat.app.app import <PERSON><PERSON><PERSON>
from chat.messages.model import CURRENT_VERSION, MessageModel, MessageBody
from chat.utils.common import now


def create_message(app: MyApp, thread_id, msg_id, user_id, body, msg_count=1):
    conns = app.conns
    bucket = app.repos.message._get_bucket(msg_id)  # type: ignore
    message = MessageModel(
        thread_id=thread_id,
        id=msg_id,
        bucket=bucket,
        created_at=now(),
        user_id=user_id,
        version=CURRENT_VERSION,
        delete_level=0,
        uuid=str(uuid4()),
        body=body.json(),
    )
    session = conns.cas.session
    session.execute(
        SimpleStatement(
            """insert into messages (
            id,
            uuid,
            thread_id,
            bucket,
            body,
            user_id,
            created_at,
            delete_level,
            version) values
            (%s, %s, %s, %s, %s, %s, %s, %s, %s)"""
        ),
        (
            message.id,
            UUID(message.uuid),
            message.thread_id,
            message.bucket,
            message.body.json(),
            message.user_id,
            message.created_at,
            message.delete_level,
            message.version,
        ),
    )

    app.repos.message_cache.replace_last_message(message)
    app.repos.message_cache.add(message)

    # update message count
    with conns.mysql.session() as session:
        app.repos.thread.update_msg_count(session, thread_id, msg_count)
        app.repos.counter.inc_counter(thread_id)
        session.commit()
