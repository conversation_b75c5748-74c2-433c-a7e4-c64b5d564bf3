from time import sleep
from typing import List

from chat.container.app import App<PERSON>ontainer
from chat.threads.exception import ConflictData
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4
from chat.messages.model import MessageBody
from tests.helpers.message import create_message


def _try_until_success(fn):
    while True:
        try:
            return fn()
        except ConflictData:
            pass
        sleep(0.1)


def setup_threads_and_folder(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    mysql = container.app().conns.mysql
    pin_usc = usecases.pin_thread
    folder_uscs = container.folder().usecases()
    thread_broker = container.app().brokers.thread_rd

    # create private groups
    p_thread1 = _try_until_success(
        lambda: usecases.create_thread.create_group(
            [TEST_U2], TEST_U1, "Test group"
        )
    )

    p_thread2 = _try_until_success(
        lambda: usecases.create_thread.create_direct(
            TEST_U3,
            TEST_U1,
        )
    )

    p_thread3 = _try_until_success(
        lambda: usecases.create_thread.create_direct(
            TEST_U4,
            TEST_U1,
        )
    )

    thread1_id: int = p_thread1.thread.id
    thread2_id: int = p_thread2.thread.id
    thread3_id: int = p_thread3.thread.id

    folder = folder_uscs.create(
        TEST_U1, "test_folder", "", [thread1_id, thread2_id, thread3_id], []
    )
    folder_id = folder["alias"]

    # increase thread message counter because
    # zero-messages thread is considered as deleted
    with mysql.get_session() as session:
        repos.thread.incr_thread_msg_counter(session, thread1_id)
        repos.thread.incr_thread_msg_counter(session, thread2_id)
        repos.thread.incr_thread_msg_counter(session, thread3_id)
        session.commit()

    # set thread scores
    thread_broker.set_thread_score(TEST_U1, thread1_id, folder_id, 3)
    thread_broker.set_thread_score(TEST_U1, thread2_id, folder_id, 2)
    thread_broker.set_thread_score(TEST_U1, thread3_id, folder_id, 1)

    return [thread1_id, thread2_id, thread3_id], folder_id


def create_sub_thread_of_group(
    container: AppContainer,
    name: str,
    user_id: str,
    member_ids: List[str],
    sub_thread_member_ids: List[str] = [],
    workspace_id=None,
):
    usecases = container.thread().usecases()
    p_thread = usecases.create_thread.create_group(
        member_ids, user_id, name, workspace_id=workspace_id
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, user_id, body, msg_count=1
    )

    p_sub_thread = usecases.create_thread.create_sub_thread(
        thread_id, message_id, user_id, sub_thread_member_ids
    )
    sub_thread_id = p_sub_thread.thread.id

    return thread_id, sub_thread_id
