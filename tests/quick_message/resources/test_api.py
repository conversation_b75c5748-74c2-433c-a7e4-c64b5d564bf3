import random

from falcon.testing import TestClient

from chat.container import A<PERSON><PERSON>ontainer
from chat.quick_message.model.api import (
    SchemaCreateQuickMessage,
    SchemaEditQuickMessage,
)
from tests.conftest import (
    TEST_U1,
    user_header,
)


def test_create_quick_message_success(
    container: AppContainer, client: TestClient
):
    USER_ID = TEST_U1 + str(random.randint(1, 1000000))
    current_length = 0
    session_m = container.app().conns.mysql
    with session_m.get_session() as session:
        quick_messages = (
            container.app().repos.quick_message_repo.get_by_user_id(
                session, USER_ID
            )
        )

        current_length = len(quick_messages)
        assert current_length == 0

    headers = user_header(USER_ID)
    body = SchemaCreateQuickMessage(
        command="/hello",
        body={
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        },
    ).dict()

    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json=body,
    )

    assert result.status_code == 200
    assert result.json["data"]["command"] == "/hello"
    assert result.json["data"]["body"]["text"] == "Hi all"

    with session_m.get_session() as session:
        quick_messages = (
            container.app().repos.quick_message_repo.get_by_user_id(
                session, USER_ID
            )
        )
        assert len(quick_messages) == current_length + 1
        assert quick_messages[0].command == "/hello"
        assert quick_messages[0].body.text == "Hi all"


def test_create_quick_message_field_length(
    container: AppContainer, client: TestClient
):
    """Test field length validation for quick message creation."""
    headers = user_header(TEST_U1)

    # Test command too long (>20 chars)
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json={
            "command": "/" + "a" * 20,
            "body": {
                "metadata": {"mentions": []},
                "media": [],
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400

    # Test message too long (>2000 chars)
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json={
            "command": "/hello",
            "body": {
                "metadata": {"mentions": []},
                "media": [],
                "text": "a" * 20001,
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400


def test_update_quick_message(container: AppContainer, client: TestClient):
    """Test updating a quick message."""
    headers = user_header(TEST_U1)

    # First create a quick message
    create_body = SchemaCreateQuickMessage(
        command="/hello",
        body={
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        },
    ).dict()

    client.simulate_post(
        "/quick_messages",
        headers=headers,
        json=create_body,
    )

    # Then update it
    update_body = SchemaEditQuickMessage(
        command="/hello",
        body={
            "metadata": {"mentions": []},
            "media": [],
            "text": "Updated message",
            "type": "text",
            "is_markdown_text": False,
        },
    ).dict()

    result = client.simulate_patch(
        "/quick_messages",
        headers=headers,
        json=update_body,
    )

    assert result.status_code == 200
    assert result.json["data"]["command"] == "/hello"
    assert result.json["data"]["body"]["text"] == "Updated message"

    session_m = container.app().conns.mysql
    with session_m.get_session() as session:
        quick_messages = (
            container.app().repos.quick_message_repo.get_by_user_id(
                session, TEST_U1
            )
        )
        assert len(quick_messages) == 1
        assert quick_messages[0].command == "/hello"
        assert quick_messages[0].body.text == "Updated message"


def test_delete_quick_message(container: AppContainer, client: TestClient):
    """Test deleting a quick message."""
    headers = user_header(TEST_U1)

    # First create a quick message
    create_body = SchemaCreateQuickMessage(
        command="/hello",
        body={
            "metadata": {"mentions": []},
            "media": [],
            "text": "Updated message",
            "type": "text",
            "is_markdown_text": False,
        },
    ).dict()

    client.simulate_post(
        "/quick_messages",
        headers=headers,
        json=create_body,
    )

    # Then delete it
    result = client.simulate_delete(
        "/quick_messages", headers=headers, json={"command": "/hello"}
    )

    print(result.json)
    assert result.status_code == 200
    assert result.json["data"]["message"] == "ok"


def test_create_duplicate_quick_message(container: AppContainer, client: TestClient):
    """Test creating a quick message with duplicate command fails."""
    USER_ID = TEST_U1 + str(random.randint(1, 1000000))
    headers = user_header(USER_ID)
    
    # Create first quick message
    body = SchemaCreateQuickMessage(
        command="/hello",
        body={
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        },
    ).dict()

    # First creation should succeed
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json=body,
    )
    assert result.status_code == 200

    # Try to create second quick message with same command
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json=body,
    )
    
    # Should fail with 400 Bad Request
    assert result.status_code == 409
    # Verify only one quick message exists
    session_m = container.app().conns.mysql
    with session_m.get_session() as session:
        quick_messages = (
            container.app().repos.quick_message_repo.get_by_user_id(
                session, USER_ID
            )
        )
        assert len(quick_messages) == 1


def test_create_quick_message_invalid_command(container: AppContainer, client: TestClient):
    """Test creating quick message with invalid command format."""
    headers = user_header(TEST_U1)

    # Test command with space
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json={
            "command": "/hello world",
            "body": {
                "metadata": {"mentions": []},
                "media": [],
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400

    # Test command with comma
    result = client.simulate_post(
        "/quick_messages",
        headers=headers,
        json={
            "command": "/hello,world",
            "body": {
                "metadata": {"mentions": []},
                "media": [],
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400
