import pytest

from chat.utils.common import markdown_to_text

TEST_DATA = [
    ("**Bold text**", "Bold text"),
    ("_Italic text_", "Italic text"),
    ("## Heading text", "Heading text"),
]


@pytest.mark.parametrize("test_input,expected", TEST_DATA)
def test_markdown_to_text(test_input, expected):
    # test_input = "**This is bold text**"
    # expected = "This is bold text"
    assert markdown_to_text(test_input) == expected
