from typing import Dict

import pytest

from chat.messages.model import MessageBody
from chat.container import AppContainer
from chat.exception import UserNotInThread
from chat.messages.exception import DisallowToPinMessageTypeError
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.message import create_message


def test_pin_message(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    usecases.pin_message.pin(thread_id, msg_id, TEST_U1)

    with conns.mysql.session() as session:
        pin = repos.pin_collection.item(session, thread_id, msg_id)
        assert pin != None


def test_pin_message_without_permission(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(UserNotInThread) as ex_info:
        usecases.pin_message.pin(thread_id, msg_id, TEST_U3)


def test_unpin_message(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    usecases.pin_message.pin(thread_id, msg_id, TEST_U1)

    # anyone in this thread can unpin this message
    usecases.pin_message.unpin(thread_id, TEST_U2, msg_id)

    with conns.mysql.session() as session:
        pin = repos.pin_collection.item(session, thread_id, msg_id)
        assert pin is None


def test_unpin_message_without_permission(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    usecases.pin_message.pin(thread_id, msg_id, TEST_U1)

    with pytest.raises(UserNotInThread):
        usecases.pin_message.unpin(thread_id, TEST_U3, msg_id)


def test_pin_action_note(container: AppContainer):
    usecases = container.message().usecases()
    thread_uscs = container.thread().usecases()
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create an action note
    body = MessageBody(
        **{
            "media": [],
            "reply_to_msg": None,
            "reply_to_msg_object": None,
            "forward": None,
            "text": "Tuan Nguyen đã đổi tên nhóm thành Threadss",
            "tmp_text": None,
            "is_markdown_text": False,
            "metadata": {
                "action_note_receivers": [],
                "action_note_maker": "system",
                "action_note_type": 5,
                "action_note_data_change": {"name": "Threadss"},
            },
            "type": "action_note",
            "edited_at": 0,
            "client_id": None,
        }
    )
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(DisallowToPinMessageTypeError):
        usecases.pin_message.pin(thread_id, msg_id, TEST_U1)
