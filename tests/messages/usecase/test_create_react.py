import pytest

from chat.container import AppContainer
from chat.exception import UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.message import create_message
from tests.helpers.thread import create_sub_thread_of_group
from chat.messages.model import MessageBody


def test_create_react_direct_chat(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(UserNotInThread):
        usecases.react_message.react_message(thread_id, msg_id, TEST_U3, 1)

    usecases.react_message.react_message(thread_id, msg_id, TEST_U1, 1)

    msg = repos.message.get_by_id(thread_id, msg_id)
    assert msg is not None
    assert msg.react_binary != 0  # 16


def test_create_react_group(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(UserNotInThread):
        usecases.react_message.react_message(thread_id, msg_id, TEST_U3, 1)

    usecases.react_message.react_message(thread_id, msg_id, TEST_U1, 1)

    msg = repos.message.get_by_id(thread_id, msg_id)
    assert msg is not None
    assert msg.react_binary != 0  # -1


def test_create_react_to_sub_thread(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})
    msg_id = 1
    create_message(container.app(), sub_thread_id, msg_id, TEST_U1, body)

    # anyone in parent thread can react
    for user_id in (TEST_U1, TEST_U2):
        usecases.react_message.react_message(sub_thread_id, msg_id, user_id, 1)
