from time import sleep

import pytest
from chat.messages.model import MessageBody
from chat.container import App<PERSON>ontainer
from chat.exception import OutOfMsgEditPeriodError, PermissionDenied
from chat.messages.model.api import SchemaBodyEdit, SchemaMetadataEditText
from tests.conftest import TEST_U1, TEST_U2
from tests.helpers.message import create_message


def test_edit_message_success(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})
    new_body = SchemaBodyEdit(
        text="New message",
        metadata=SchemaMetadataEditText(layout={"haha": "hihi"}),
    )

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    usecases.edit_message.update(TEST_U1, thread_id, msg_id, new_body)

    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is not None
    assert msg.body.text == new_body.text
    assert msg.body.metadata == new_body.metadata
    assert msg.body.metadata["layout"] == {"haha": "hihi"}

    msg_cache = repos.message_cache.get(thread_id, msg_id)
    last_msg_cache = repos.message_cache.get_last_message(thread_id)

    assert msg == msg_cache
    assert msg == last_msg_cache


def test_edit_message_with_no_permission(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})
    new_body = SchemaBodyEdit(text="New message")

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(PermissionDenied):
        usecases.edit_message.update(TEST_U2, thread_id, msg_id, new_body)


def test_edit_message_after_allowed_edit_period(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})
    new_body = SchemaBodyEdit(text="New message")

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    sleep(2)

    with pytest.raises(OutOfMsgEditPeriodError):
        usecases.edit_message.update(TEST_U1, thread_id, msg_id, new_body)
