from typing import cast

import pytest

from chat import constant
from chat.container import AppContainer
from chat.exception import UserNotInThread
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.messages.model import MessageBody
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.message import create_message


def test_forward_message(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_src_thread = create_thread_usc.create_direct(TEST_U3, TEST_U1)
    p_dst_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    src_thread_id = p_src_thread.thread.id
    dst_thread_id = p_dst_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})
    # create message
    msg_id = 1
    create_message(container.app(), src_thread_id, msg_id, TEST_U1, body)

    # forward thread
    API_VERSION = "3.3"
    usecases.forward_message.forward(
        TEST_U1, src_thread_id, msg_id, dst_thread_id, API_VERSION
    )

    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    msg_body = msg["body"]["body"]
    # {"media": [], "reply_to_msg": null, "reply_to_msg_object": null, "forward": {"thread_id": 1653298018834, "message_id": 1, "user_id": "1", "user_name": "", "is_save_message": false}, "text": "{\\"type\\": \\"text\\", \\"text\\": \\"Hello\\"}", "is_markdown_text": false, "tmp_text": null, "metadata": null, "type": "text", "edited_at": 0, "workspace_id": null}
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE
    assert msg_body["forward"]["message_id"] == msg_id
    assert msg_body["forward"]["thread_id"] == src_thread_id


def test_forward_message_without_permission(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_src_thread = create_thread_usc.create_direct(TEST_U3, TEST_U1)
    p_dst_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    src_thread_id = p_src_thread.thread.id
    dst_thread_id = p_dst_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "Hello"})
    # create message
    msg_id = 1
    create_message(container.app(), src_thread_id, msg_id, TEST_U1, body)

    # forward thread
    API_VERSION = "3.3"

    # forward message to thread that user is not in
    with pytest.raises(UserNotInThread):
        usecases.forward_message.forward(
            TEST_U3, src_thread_id, msg_id, dst_thread_id, API_VERSION
        )

    # forward message from user that is not in the source thread
    with pytest.raises(UserNotInThread):
        usecases.forward_message.forward(
            TEST_U2, src_thread_id, msg_id, dst_thread_id, API_VERSION
        )


def test_forward_message_to_other_user(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_src_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    src_thread_id = p_src_thread.thread.id

    broker.messages = []  # remove thread created event

    body = MessageBody(**{"type": "text", "text": "Hello"})
    # create message
    msg_id = 1
    create_message(container.app(), src_thread_id, msg_id, TEST_U1, body)

    # forward thread
    API_VERSION = "3.3"
    with pytest.raises(UserNotInThread):
        usecases.forward_message.forward_to_partner(
            TEST_U3, src_thread_id, msg_id, TEST_U1, constant.USER_TYPE, API_VERSION
        )

    usecases.forward_message.forward_to_partner(
        TEST_U1, src_thread_id, msg_id, TEST_U3, constant.USER_TYPE, API_VERSION
    )
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    msg_body = msg["body"]["body"]["forward"]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE
    assert msg_body["user_id"] == TEST_U1
    assert msg_body["thread_id"] == src_thread_id
    assert msg_body["message_id"] == msg_id


def test_forward_to_thread_async(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_src_thread = create_thread_usc.create_direct(TEST_U3, TEST_U1)
    p_dst_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    src_thread_id = p_src_thread.thread.id
    dst_thread_id = p_dst_thread.thread.id

    # reset broker messages
    broker.messages = []

    body = MessageBody(**{"type": "text", "text": "Hello"})
    # create message
    msg_id = 1
    create_message(container.app(), src_thread_id, msg_id, TEST_U1, body)

    # forward thread
    API_VERSION = "3.3"
    # FIXME check permission
    # with pytest.raises(UserNotInThread):
    #     usecases.forward_message.forwards(TEST_U3, src_thread_id, [msg_id], dst_thread_id, API_VERSION)

    # with pytest.raises(UserNotInThread):
    #     usecases.forward_message.forwards(TEST_U2, src_thread_id, [msg_id], dst_thread_id, API_VERSION)

    usecases.forward_message.forward_many(
        TEST_U1, src_thread_id, [msg_id], dst_thread_id, API_VERSION
    )
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    msg_body = msg["body"]
    assert msg["event_type"] == constant.TASK_FORWARD_MULTIPLE_MESSAGES
    assert msg_body["user_id"] == TEST_U1
    assert msg_body["source_thread_id"] == src_thread_id
    assert msg_body["source_message_ids"] == [msg_id]
    assert msg_body["thread_id"] == dst_thread_id


def test_forward_to_partner_async(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_src_thread = create_thread_usc.create_direct(TEST_U3, TEST_U1)
    src_thread_id = p_src_thread.thread.id

    # reset broker events (thread created event)
    broker.messages = []

    body = MessageBody(**{"type": "text", "text": "Hello"})
    # create message
    msg_id = 1
    create_message(container.app(), src_thread_id, msg_id, TEST_U1, body)

    # forward thread
    API_VERSION = "3.3"
    # FIXME check permission
    # with pytest.raises(UserNotInThread):
    #     usecases.forward_message.forwards(TEST_U3, src_thread_id, [msg_id], dst_thread_id, API_VERSION)

    # with pytest.raises(UserNotInThread):
    #     usecases.forward_message.forwards(TEST_U2, src_thread_id, [msg_id], dst_thread_id, API_VERSION)

    usecases.forward_message.forward_many_to_partner(
        TEST_U1, src_thread_id, [msg_id], TEST_U2, constant.USER_TYPE, API_VERSION
    )
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    msg_body = msg["body"]
    assert msg["event_type"] == "forwards"
    assert msg_body["user_id"] == TEST_U1
    assert msg_body["source_thread_id"] == src_thread_id
    assert msg_body["source_message_ids"] == [msg_id]
