import json
from typing import cast

import pytest

from chat.connections.gapo_client.base import <PERSON><PERSON><PERSON><PERSON><PERSON>
from chat.connections.gapo_client.mock import Gap<PERSON><PERSON><PERSON><PERSON>ock
from chat.constant import TASK_CREATE_MESSAGE
from chat.container import AppContainer
from chat.exception import PermissionDenied
from chat.messages.model.api import SchemaBodyPollVote, SchemaVoteBody
from chat.messages.model import MessageBody
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2
from tests.helpers.message import create_message
from tests.helpers.thread import create_sub_thread_of_group


def test_create_poll(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # reset events
    broker.messages = []

    poll_body = SchemaBodyPollVote(
        votes=[SchemaVoteBody(title="test")],
        allow_add_choice=False,
        allow_multiple_choice=False,
        title="Test all",
    )

    # create message
    API_VERSION = "3.3"
    usecases.pollvote_message.create_poll_message(
        TEST_U1, thread_id, poll_body, API_VERSION
    )

    assert cast(GapoPollMock, repos.poll).create_poll_calls == 1

    # check pubsub message
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == TASK_CREATE_MESSAGE


def test_create_poll_in_sub_thread(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    thread_uscs = container.thread().usecases()
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    poll_body = SchemaBodyPollVote(
        votes=[SchemaVoteBody(title="test")],
        allow_add_choice=False,
        allow_multiple_choice=False,
        title="Test all",
    )

    # can create poll-vote in sub-thread of group
    # TODO: check with sub-thread of direct chat
    API_VERSION = "3.3"
    usecases.pollvote_message.create_poll_message(
        TEST_U1, sub_thread_id, poll_body, API_VERSION
    )

    # make sure sub-thread is updated
    msg = broker.messages[-1]
    assert msg["event_type"] == TASK_CREATE_MESSAGE
    assert msg["body"]["thread"]["referenced_message_preview"]
    assert msg["body"]["thread"]["message_count"] == 1

    # make sure creator is commenter list
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, sub_thread_id)
    assert p_thread.thread.commenters == [TEST_U1]


def test_create_poll_in_direct_thread(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    poll_body = SchemaBodyPollVote(
        votes=[SchemaVoteBody(title="test")],
        allow_add_choice=False,
        allow_multiple_choice=False,
        title="Test all",
    )

    # create message
    API_VERSION = "3.3"
    # with pytest.raises(PermissionDenied) as ex_info:
    #     usecases.pollvote_message.create_poll_message(
    #         TEST_U1, thread_id, poll_body, API_VERSION
    #     )


def test_chooose_vote_in_sub_thread(container: AppContainer):
    usecases = container.message().usecases()
    gapo_client = container.app().repos.gapo
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    API_VERSION = "3.3"
    poll_body = SchemaBodyPollVote(
        votes=[SchemaVoteBody(title="test")],
        allow_add_choice=False,
        allow_multiple_choice=False,
        title="Test all",
    )
    message_id = 1

    uuid, message_id = usecases.pollvote_message.create_poll_message(
        TEST_U1, thread_id, poll_body, API_VERSION
    )
    poll_id = "1"
    poll: SchemaPoll = gapo_client.poll.get_polls(TEST_U1, [poll_id])[poll_id]
    body = MessageBody(
        **{
            "media": [],
            "tmp_text": "Poll đã được tạo",
            "is_markdown_text": False,
            "metadata": {
                "poll_information": poll.dict(),
            },
            "type": "poll",
        }
    )
    create_message(
        container.app(), sub_thread_id, message_id, TEST_U1, body, message_id
    )

    vote_id = poll.votes[0].id

    # both direct and non-direct member can upvote
    for user_id in (TEST_U1, TEST_U2):
        usecases.pollvote_message.choose_vote_by_user(
            user_id, sub_thread_id, message_id, vote_id, "3"
        )
