from time import sleep
from typing import List, cast

import pytest

from chat import constant
from chat.container import AppContainer
from chat.exception import (
    MessageNotFound,
    OutOfMsgDeletionPeriodError,
    PermissionDenied,
)

from chat.messages.model import MessageBody
from chat.messages.exception import DeleteMultipleMessagesError
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2
from tests.helpers.message import create_message


def test_delete_message_item_tw(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    thread_uscs = container.thread().usecases()
    repos = container.app().repos
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    usecases.delete_message.delete(
        TEST_U1, thread_id, msg_id, level=constant.DELETE_MSG_TWO_SIDE
    )
    msg = repos.message.get_by_id(thread_id, msg_id)
    assert msg is not None
    assert msg.delete_level == constant.DELETE_MSG_TWO_SIDE
    assert msg.deleted is True
    assert msg.is_deleted_2_side() is True
    assert msg.is_deleted_1_side(TEST_U1) is False

    msg_cache = repos.message_cache.get(thread_id, msg_id)
    last_msg_cache = repos.message_cache.get_last_message(thread_id)

    assert msg == msg_cache
    assert msg == last_msg_cache

    # query thread
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    last_msg = (
        p_thread.thread.centralize_last_message
        or p_thread.participant_threads.last_message
    )
    assert last_msg.delete_level == 2

    # check amqp messages
    amqp_msg = [
        m
        for m in broker.messages
        if m["event_type"] == constant.EVENT_MESSAGE_DELETED
    ][-1]
    assert amqp_msg is not None


# def test_delete_message_item_oneside(container: AppContainer):
#     usecases = container.message().usecases()
#     repos = container.app().repos
#     conns = container.app().conns
#     create_thread_usc = container.thread().usecases().create_thread
#     thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
#     thread_id = thread["id"]

#     body = {"type": "text", "text": "hello"}

#     # create message
#     # because all work is done in worker here
#     # we try to create message directly in database,
#     # which now is in Cassandra
#     msg_id = 1
#     create_message(container.app(), thread_id, msg_id, TEST_U1, body)

#     usecases.delete_message.delete_item(
#         TEST_U1, thread_id, msg_id, level=constant.ONE_SIDE
#     )
#     msg = repos.message.get_by_id(thread_id, msg_id)
#     assert msg is not None
#     assert msg.delete_level == constant.ONE_SIDE


def test_delete_message_with_no_permission(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(PermissionDenied) as exc_info:
        usecases.delete_message.delete(
            TEST_U2, thread_id, msg_id, level=constant.DELETE_MSG_TWO_SIDE
        )

    assert str(exc_info.value) == "Not owner message"


def test_delete_message_after_deletion_period(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    sleep(2)

    with pytest.raises(OutOfMsgDeletionPeriodError):
        usecases.delete_message.delete(
            TEST_U1, thread_id, msg_id, level=constant.DELETE_MSG_TWO_SIDE
        )


def test_delete_message_from_save_bot_after_deletion_period(
    container: AppContainer,
):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_system_direct(
        TEST_U1, system_id=constant.SAVE_MESSAGE_BOT_ID, actor_id=TEST_U1
    )
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    sleep(2)

    usecases.delete_message.delete(
        TEST_U1, thread_id, msg_id, level=constant.DELETE_MSG_TWO_SIDE
    )


def test_delete_message_from_save_bot_after_deletion_period_one_side(
    container: AppContainer,
):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    repos = container.app().repos
    p_thread = create_thread_usc.create_system_direct(
        TEST_U1, system_id=constant.SAVE_MESSAGE_BOT_ID, actor_id=TEST_U1
    )
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    sleep(2)

    # can delete message one side
    usecases.delete_message.delete(
        TEST_U1, thread_id, msg_id, level=constant.DELETE_MSG_ONE_SIDE
    )
    msg = repos.message.get_by_id(thread_id, msg_id)

    msg_cache = repos.message_cache.get(thread_id, msg_id)
    last_msg_cache = repos.message_cache.get_last_message(thread_id)

    assert msg == msg_cache
    assert msg == last_msg_cache
    assert msg.is_deleted_1_side(TEST_U1)
    assert msg.is_deleted_2_side() is False


# def test_delete_message_oneside_from_peer(container: AppContainer):
#     usecases = container.message().usecases()
#     repos = container.app().repos
#     conns = container.app().conns
#     create_thread_usc = container.thread().usecases().create_thread
#     thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
#     thread_id = thread["id"]

#     body = {"type": "text", "text": "hello"}
#     api_version = "3.3"

#     # create message
#     msg_id = 1
#     create_message(container.app(), thread_id, msg_id, TEST_U1, body)

#     usecases.delete_message.delete_item(
#         TEST_U2, thread_id, msg_id, level=constant.ONE_SIDE
#     )

#     msg = repos.message.get_by_id(thread_id, msg_id)
#     assert msg is not None
#     assert msg.delete_level == constant.ONE_SIDE


def test_delete_pin_message(container: AppContainer):
    usecases = container.message().usecases()
    app = container.app()
    repos = app.repos
    conns = app.conns
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    # pin message
    usecases.pin_message.pin(thread_id, msg_id, TEST_U1)
    with conns.mysql.session() as session:
        # FIXME: why pinned_message_id == 0
        # thread = repos.thread.get_by_id(session, thread_id)

        pin = repos.pin_collection.get_latest_pin(session, thread_id)
        assert pin["message_id"] == msg_id  # type: ignore

    usecases.delete_message.delete(
        TEST_U1, thread_id, msg_id, level=constant.DELETE_MSG_TWO_SIDE
    )

    with conns.mysql.session() as session:

        pin = repos.pin_collection.get_latest_pin(session, thread_id)
        assert pin is None


def test_delete_multiple_messages_with_errors(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create the first message
    msg_id = 1
    create_message(container.app(), thread_id, 1, TEST_U1, body)

    # wait some time, so we can't delete message 1 anymore
    sleep(2)

    # create new message
    create_message(container.app(), thread_id, 2, TEST_U1, body, msg_count=2)

    # raise detail error if we delete single message
    with pytest.raises(OutOfMsgDeletionPeriodError):
        usecases.delete_message.delete_many(
            TEST_U1, thread_id, [1], level=constant.DELETE_MSG_TWO_SIDE
        )

    with pytest.raises(DeleteMultipleMessagesError) as exc_info:
        usecases.delete_message.delete_many(
            TEST_U1, thread_id, [1, 2, 3], level=constant.DELETE_MSG_TWO_SIDE
        )

    all_errs = exc_info.value
    assert len(all_errs.errors) == 2

    # we can't delete message 1 because it's out of deletion period
    # we can delete message 3 because it doesn't exist
    expected_result: List = [
        ("1", OutOfMsgDeletionPeriodError),
        ("3", MessageNotFound),
    ]
    for idx, sub_err in enumerate(all_errs.errors):
        (msg_id, err) = sub_err  # type: ignore
        expected = expected_result[idx]
        assert msg_id == expected[0]
        assert isinstance(err, expected[1])

    # test translate exception
    all_errs.translate("vi")
    err_json = all_errs.to_dict()
    assert len(err_json["error_details"]) == 2
