from typing import cast

from chat import constant
from chat.container import AppContainer
from chat.messages.model.task import TaskCreateMassDirectMessages
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_create_mass_direct_messages(container: AppContainer):
    app = container.app()
    usecase = container.message().usecases().create_message
    usecase.create_mass_direct_messages(
        sender_id=TEST_U1,
        user_ids=[TEST_U1, TEST_U2, TEST_U3],
        body={
            "type": "text",
            "text": "Sample text message",
            "metadata": {"mention": []},
        },
        api_version="3",
    )
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    messages = broker.messages

    create_message_events = [
        msg for msg in messages if msg["event_type"] == constant.TASK_CREATE_MESSAGE
    ]

    # cannot send message to yourself
    # so there only two messages
    assert len(create_message_events) == 2

    create_mass_direct_messages_event = [
        msg
        for msg in messages
        if msg["event_type"] == constant.TASK_CREATE_MASS_DIRECT_MESSAGES
    ]
    assert len(create_mass_direct_messages_event) == 1
    msg = create_mass_direct_messages_event[0]
    msg_body = msg["body"]
    msg["event_type"] == constant.TASK_CREATE_MASS_DIRECT_MESSAGES
    parsed = TaskCreateMassDirectMessages(**msg_body)
    assert parsed.sender_id == TEST_U1
    assert parsed.user_ids == [int(TEST_U1), int(TEST_U2), int(TEST_U3)]
