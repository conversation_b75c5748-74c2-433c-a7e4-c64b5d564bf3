import json
from typing import cast

import pytest

from chat import constant
from chat.container import AppContainer
from chat.exception import UserNotInThread, WorkspaceDisable
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.messages.usecase import CreateMessageParams
from chat.messages.model import MessageBody
from tests.conftest import (
    TEST_B1,
    TEST_COLLAB_ID,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    TEST_U4,
)
from tests.helpers.message import create_message
from tests.helpers.thread import create_sub_thread_of_group
from tests.conftest import TEST_WORKSPACE_DISABLE


def test_create_direct_message(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    usecases.create_message._create(TEST_U1, thread_id, body, "3")

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    # make sure thread name is set to the partner's name
    assert msg["body"]["thread"]["name"] == "u2"

    # check message_count
    assert msg["body"]["thread"]["message_count"] == 1
    assert msg["body"]["sender_lang"] == "vi"


# def test_create_message_workspace_disable(container: AppContainer):
#     usecases = container.message().usecases()
#     _ = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
#     create_thread_usc = container.thread().usecases().create_thread
#     thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
#     thread_id = thread["id"]

#     # create message
#     body = {"type": "text", "text": "Hello"}

#     with pytest.raises(WorkspaceDisable):
#         usecases.create_message._create(
#             TEST_U1, thread_id, body, "3", workspace_id=TEST_WORKSPACE_DISABLE
#         )


def test_create_reply_message_in_sub_thread(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container,
        "Test",
        TEST_U1,
        [TEST_U1, TEST_U2],
        sub_thread_member_ids=[TEST_U1],
    )

    # create  message of TEST_U1
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Root message"})
    create_message(
        container.app(), sub_thread_id, message_id, TEST_U1, body, msg_count=1
    )

    # create reply message of TEST_U2
    reply_body = MessageBody(
        **{
            "type": "text",
            "text": "Reply message",
            "metadata": {"mentions": []},
            "is_markdown_text": 0,
            "reply_to_msg": 1,
        }
    )
    usecases.create_message._create(TEST_U2, sub_thread_id, reply_body, "3")

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE
    assert msg["body"]["message_id"] == 2

    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2


def test_create_message_in_sub_thread_of_a_direct_thread(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    get_thread_usc = container.thread().usecases().get_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Root message"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    usecases.create_message._create(TEST_U1, sub_thread_id, body, "3")

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    # make sure thread name is set to the partner's name
    assert msg["body"]["thread"]["name"] == "u2"
    assert msg["body"]["thread"]["id"] == sub_thread_id
    assert msg["body"]["thread"]["parent_id"] == thread_id

    # make sure referenced_message_preview is set
    # so worker can use this update notifcation thread's name (for sub-thread)
    assert (
        msg["body"]["thread"]["referenced_message_preview"] == "Root message"
    )


def test_create_message_with_mention_in_sub_thread(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    get_thread_usc = container.thread().usecases().get_thread
    p_thread = create_thread_usc.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = p_sub_thread.thread.id
    assert p_sub_thread.thread.commenters == []

    # create message
    body = MessageBody(
        **{
            "type": "text",
            "text": "Hello @3",
            "metadata": {
                "mentions": [{"target": TEST_U2, "length": "1", "offset": "1"}]
            },
        }
    )

    # any user in thread can create message
    for i in range(2):
        for user_id in (TEST_U1, TEST_U2, TEST_U3):
            usecases.create_message._create(user_id, sub_thread_id, body, "3")

    # user not in parent thread cannot create message
    with pytest.raises(UserNotInThread):
        usecases.create_message._create(TEST_U4, sub_thread_id, body, "3")

    # make sure commenter list is updated
    p_thread = get_thread_usc.get_by_id(TEST_U1, sub_thread_id)
    assert p_thread.thread.commenters == [TEST_U1, TEST_U2, TEST_U3]

    # make sure thread score is correct in redis
    thread_broker = container.app().brokers.thread_rd
    for user_id in (TEST_U1, TEST_U2):
        # not set in sub-thread folder
        score = thread_broker.get_thread_score(
            user_id, sub_thread_id, constant.FOLDER_SUB_THREAD
        )
        assert not score  # will be set by chat-persistent-worker

        # not set in default folder
        default_folder_score = thread_broker.get_thread_score(
            user_id, sub_thread_id, constant.FOLDER_DEFAULT
        )
        assert not default_folder_score


def test_auto_add_sender_when_user_comment_on_sub_thread(
    container: AppContainer,
):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    pt_uscs = container.participant().usecases()
    p_thread = create_thread_usc.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # create message
    body = MessageBody(
        **{
            "type": "text",
            "text": "Hello",
            "metadata": {"mentions": []},
        }
    )

    usecases.create_message._create(TEST_U2, sub_thread_id, body, "3")

    # user TEST_U2 will be added to sub_thread
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2
    member_ids = set([m.user_id for m in members])
    assert member_ids == set([TEST_U1, TEST_U2])


def test_auto_add_member_when_user_comment_on_sub_thread(
    container: AppContainer,
):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    pt_uscs = container.participant().usecases()
    p_thread = create_thread_usc.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # create message
    body = MessageBody(
        **{
            "type": "text",
            "text": "Hello @TEST_U2",
            "metadata": {
                "mentions": [{"target": TEST_U2, "length": "1", "offset": "1"}]
            },
        }
    )

    usecases.create_message._create(TEST_U1, sub_thread_id, body, "3")

    # user TEST_U2 will be added to sub_thread
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2
    member_ids = set([m.user_id for m in members])
    assert member_ids == set([TEST_U1, TEST_U2])


def test_adding_all_members_when_user_mention_all_in_sub_thread(
    container: AppContainer,
):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    pt_uscs = container.participant().usecases()
    p_thread = create_thread_usc.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    # create sub thread
    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # create message with all mention
    body = MessageBody(
        **{
            "type": "text",
            "text": "@all Hello",
            "metadata": {
                "mentions": [{"target": "all", "length": "3", "offset": "0"}]
            },
        }
    )

    usecases.create_message._create(TEST_U1, sub_thread_id, body, "3")

    # user TEST_U3 will be added to sub_thread
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    member_ids = set([m.user_id for m in members])
    # ingore mention @all, updated
    assert member_ids == set([TEST_U1])


def test_create_collab_message(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    params = CreateMessageParams(
        collab_id=TEST_COLLAB_ID,
        user_id=TEST_U1,
        body=body,
        api_version="3",
        lang="en",
    )
    usecases.create_message.create_v2(params)

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    assert msg["body"]["thread"]["id"] == thread_id
    assert msg["body"]["sender_lang"] == "en"


def test_create_collab_message_with_bypass_enabled(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    params = CreateMessageParams(
        collab_id=TEST_COLLAB_ID,
        user_id=TEST_B1,
        body=body,
        api_version="3",
        bypass=True,
    )
    usecases.create_message.create_v2(params)

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    # make sure thread name is set to the partner's name
    assert msg["body"]["thread"]["id"] == thread_id


def test_create_message_with_partner(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    params = CreateMessageParams(
        user_id=TEST_U1,
        partner_id=TEST_U2,
        body=body,
        api_version="3",
        bypass=True,
    )

    usecases.create_message.create_v2(params)
    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    # make sure thread name is set to the partner's name
    assert msg["body"]["thread"]["name"] == "u2"


def test_create_system_message(container: AppContainer):
    usecases = container.message().usecases()
    repos = container.app().repos
    conns = container.app().conns
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = MessageBody(**{"type": "text", "text": "Hello"})

    params = CreateMessageParams(
        thread_id=thread_id,
        body=body,
        api_version="3",
        role=constant.SERVICE_ROLE,
    )

    usecases.create_message.create_v2(params)

    msg = broker.messages[-1]
    assert msg["event_type"] == constant.TASK_CREATE_MESSAGE

    assert msg["body"]["thread"]["id"] == thread_id
