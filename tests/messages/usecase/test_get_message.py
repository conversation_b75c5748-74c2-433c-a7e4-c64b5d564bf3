from typing import Any

import pytest

from chat.messages.model import MessageBody
from chat.container import AppContainer
from chat.exception import UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.message import create_message


def test_get_message_success(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is not None


def test_get_message_without_permission(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    with pytest.raises(UserNotInThread):
        _ = usecases.get_message.get_by_id(TEST_U3, thread_id, msg_id)


def test_get_message_in_empty_conversation(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    msg_id = 1

    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is None


def test_passing_invalid_message_id(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    msg_id: Any = "invalid_id"

    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is None

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    msg_id = "invalid_id"
    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)  # type: ignore # noqa
    assert msg is None


def test_get_nonexist_message(container: AppContainer):
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    msg_id: Any = "invalid_id"

    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is None

    body = MessageBody(**{"type": "text", "text": "hello"})

    # create message
    msg_id = 1
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    msg_id = 2
    msg = usecases.get_message.get_by_id(TEST_U1, thread_id, msg_id)
    assert msg is None
