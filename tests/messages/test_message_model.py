import pytest

from chat.load_cdn_mapping import (
    PREFIX_FILE_URLS,
    PREFIX_IMAGE_URLS,
    PREFIX_STICKER_URLS,
    load,
)
from chat.messages.model import MessageModel
from chat.messages.model.api import (
    MAX_QUICK_REPLY_OPTIONS,
    SchemaBodyAnimatedSticker,
    SchemaBodyAsana,
    SchemaBodyCarouselMessage,
    SchemaBodyConference,
    SchemaBodyFile,
    SchemaBodyImage,
    SchemaBodyMeeting,
    SchemaBodyMeetingRecord,
    SchemaBodyMultiImage,
    SchemaBodyQuickReplies,
    SchemaBodySticker,
    SchemaBodyStory,
    SchemaBodyText,
    SchemaBodyVideo,
    SchemaBodyVoice,
    SchemaBodyCardMessage,
    SchemaBodyDynamicMessage,
    SchemaBodyCallService,
)
from chat.utils.common import now
from tests.utils import TestSchemaInput, check_schema

load("./cdn_mapping_example.json")
GAPO_FILE_DOMAIN = PREFIX_FILE_URLS[0]
GAPO_IMAGE_DOMAIN = PREFIX_IMAGE_URLS[0]
GAPO_STICKER_DOMAIN = PREFIX_STICKER_URLS[0]

VALID_UUID = "2d8c8308-b0ee-4609-8d2b-8d7d907cc2ae"


def test_build_media_links():
    thread_id = 1
    # bucket = 1
    # id = 1
    body = '{"type": "text", "text": "Hello https://link.com/link https://link2.com/link "}'  # noqa
    msg = MessageModel(
        thread_id=thread_id,
        user_id="1",
        bucket=1,
        id=1,
        body=body,  # type: ignore
        uuid="",
        react_binary=0,
        deleted_by=set(),
        type="text",
        version="3",
        delete_level=0,
        created_at=now(),
    )
    media_links = msg.build_media_link()
    assert media_links is not None
    assert sorted(media_links["body"]) == [
        "https://link.com/link",
        "https://link2.com/link",
    ]


@pytest.mark.parametrize(
    "testcase",
    [TestSchemaInput(name="valid", data={"type": "text", "text": "hello"})],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_text_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyText, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Valid",
            data={
                "type": "image",
                "media": [GAPO_IMAGE_DOMAIN + "/image.jpg"],
                "metadata": {
                    "mentions": [],
                    "image_information": [
                        {
                            "type": "png",
                            "size": 20000,
                            "quality": "hd",
                            "id": "61abe144-666d-482f-9d05-ebb08316aa35",
                        }
                    ],
                },
            },
        ),
        TestSchemaInput(
            name="Invalid medadata",
            data={
                "type": "image",
                "media": [GAPO_IMAGE_DOMAIN + "/image.jpg"],
                "metadata": None,
            },
            error="Invalid metadata",
        ),
        TestSchemaInput(
            name="invalid url",
            data={"type": "image", "media": ["INVALID_URL"]},
            error="INVALID_URL invalid url",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_image_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyImage, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Valid",
            data={
                "type": "image",
                "media": [GAPO_IMAGE_DOMAIN + "/image.jpg"],
                "metadata": {"mentions": []},
            },
        ),
        TestSchemaInput(
            name="Invalid medadata",
            data={
                "type": "image",
                "media": [GAPO_IMAGE_DOMAIN + "/image.jpg"],
                "metadata": None,
            },
            error="Invalid metadata",
        ),
        TestSchemaInput(
            name="Too many images",
            data={"type": "image", "media": ["url"] * 51},
            error="Invalid length media",
        ),
        TestSchemaInput(
            name="missing image",
            data={"type": "image", "media": []},
            error="Invalid length media",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_multi_image_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyMultiImage, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="missing file",
            data={"type": "file", "media": []},
            error="Invalid length media",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "file",
                "media": [GAPO_FILE_DOMAIN + "/test.pdf"],
                "metadata": {
                    "mentions": [],
                    "file_information": [
                        {
                            "type": "pdf",
                            "name": "a.pdf",
                            "size": 5000,
                            "source": "source",
                            "id": "39e1cf0a-0f20-47ec-8339-0bc9b633ff87",
                        }
                    ],
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_file_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyFile, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="missing file",
            data={"type": "video", "media": []},
            error="'media' must have length 1",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "video",
                "media": [GAPO_FILE_DOMAIN + "/test.mp4"],
                "metadata": {
                    "mentions": [],
                    "video_information": [
                        {
                            "name": "Test.mp4",
                            "type": "mp4",
                            "size": 130000,
                            "id": "2d8c8308-b0ee-4609-8d2b-8d7d907cc2ae",
                            "source": "source",
                            "duration": 20,
                        }
                    ],
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_video_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyVideo, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="missing file",
            data={"type": "video", "media": []},
            error="Invalid length media",
        ),
        TestSchemaInput(
            name="null metadata",
            data={
                "type": "sticker",
                "media": [GAPO_IMAGE_DOMAIN + "/test.mp4"],
                "metadata": None,
            },
            error="Invalid metadata",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "sticker",
                "media": [GAPO_IMAGE_DOMAIN + "/test.mp4"],
                "metadata": {"mentions": []},
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_sticker_message(testcase: TestSchemaInput):
    check_schema(SchemaBodySticker, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="type must be meeting",
            data={
                "type": "me",
                "text": "text",
                "metadata": {
                    "mentions": [],
                    "meeting_information": {
                        "meeting_name": "Test",
                        "meeting_url": "",
                        "start_time": "",
                        "meeting_type": 1,
                        "meeting_status": "inprogress",
                    },
                },
            },
            error="type must be meeting",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "meeting",
                "media": [],
                "metadata": {
                    "mentions": [],
                    "meeting_information": {
                        "meeting_name": "Test",
                        "meeting_url": "",
                        "start_time": "",
                        "meeting_type": 1,
                        "meeting_status": "inprogress",
                    },
                },
                "text": "text",
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_meeting_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyMeeting, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="type must be meeting_record",
            data={
                "type": "me",
                "text": "text",
                "metadata": {
                    "file_information": {"file_size": 100, "link": ""},
                },
            },
            error="type must be meeting_record",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "meeting_record",
                "media": [],
                "metadata": {
                    "file_information": {"file_size": 100, "link": ""},
                },
                "text": "text",
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_meeting_record_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyMeetingRecord, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Invalid url",
            data={
                "type": "animated_sticker",
                "media": ["test"],
                "metadata": {},
            },
            error="test is invalid animated sticker url",
        ),
        TestSchemaInput(
            name="null metadata",
            data={
                "type": "animated_sticker",
                "media": [GAPO_STICKER_DOMAIN + "/sticker.json"],
                "metadata": None,
            },
            error="Invalid metadata",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "animated_sticker",
                "media": [GAPO_STICKER_DOMAIN + "/sticker.json"],
                "metadata": {},
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_animated_sticker_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyAnimatedSticker, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Media required",
            data={"type": "story", "media": [], "text": "text"},
            error="Invalid length media",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "story",
                "text": "",
                "media": [GAPO_STICKER_DOMAIN + "/sticker.gif"],
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_story_message_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyStory, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Media required",
            data={
                "type": "voice",
                "media": [],
                "text": "text",
                "metadata": {
                    "voice_information": [
                        {
                            "size": 100,
                            "type": "mp3",
                            "id": VALID_UUID,
                            "name": "test.mp3",
                        }
                    ]
                },
            },
            error="Invalid length media",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "voice",
                "text": "",
                "media": [GAPO_FILE_DOMAIN + "/sticker.mp3"],
                "metadata": {
                    "voice_information": [
                        {
                            "size": 100,
                            "type": "mp3",
                            "id": VALID_UUID,
                            "name": "test.mp3",
                        }
                    ]
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_voice_message_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyVoice, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Invalid type",
            data={
                "type": "voice",
                "text": "text",
                "metadata": {
                    "acted_by": {
                        "user_id": "",
                        "user_name": "",
                        "type": "",
                        "status": "",
                    },
                    "asana_task_information": None,
                },
            },
            error="Type must be asana",
        ),
        TestSchemaInput(
            name="valid",
            data={
                "type": "asana",
                "text": "",
                "metadata": {
                    "acted_by": {
                        "user_id": "",
                        "user_name": "",
                        "type": "",
                        "status": "",
                    },
                    "asana_task_information": None,
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_asana_message_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyAsana, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="valid",
            data={
                "type": "conference",
                "metadata": {
                    "conference_information": {
                        "id": 1,
                        "uuid": "",
                        "type": 1,
                        "timezone": "",
                        "host_email": "",
                        "join_url": "",
                        "password": "",
                        "encrypted_password": "",
                        "created_at": "",
                        "start_time": "",
                        "topic": "topic",
                    },
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_conference_message_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyConference, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Too many options",
            data={
                "type": "quick_replies",
                "text": "Please select an option",
                "metadata": {
                    "options": [{"title": "Option1", "payload": "payload"}]
                    * (MAX_QUICK_REPLY_OPTIONS + 1)
                },
            },
            error="Only maxium 15 options are allowed",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_quick_replies_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyQuickReplies, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK",
            data={
                "type": "carousel",
                "metadata": {
                    "carousel_cards": [
                        {
                            "title": "Best book",
                            "image_url": "http://link.com/photo.jpg",
                            "buttons": [
                                {
                                    "title": "Visit",
                                    "type": "web_url",
                                    "payload": "http://link.com/test-product",
                                },
                                {
                                    "title": "Call seller",
                                    "type": "phone_number",
                                    "payload": "+84 354 232 111",
                                },
                                {
                                    "title": "See price",
                                    "type": "postback",
                                    "payload": "see price",
                                },
                            ],
                        },
                        {
                            "title": "2nd book",
                            "image_url": "http://link2.com/a.png",
                        },
                        {"title": "3nd book"},
                    ]
                },
            },
        ),
        TestSchemaInput(
            name="Invalid phone number",
            data={
                "type": "carousel",
                "metadata": {
                    "carousel_cards": [
                        {
                            "title": "Best book",
                            "image_url": "http://link.com/photo.jpg",
                            "buttons": [
                                {
                                    "title": "Call seller",
                                    "type": "phone_number",
                                    "payload": "+84 111 232 111",
                                }
                            ],
                        },
                    ]
                },
            },
            error="Invalid phone number",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_carousel_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyCarouselMessage, testcase)


# @pytest.mark.parametrize(
#     "testcase",
#     [
#         TestSchemaInput(
#             name="OK",
#             data={
#                 "type": "card",
#                 "metadata": {
#                     "card_information": {
#                         "type": "approval.yes_no",
#                         "title": "test",
#                         "sub_title": "hello world",
#                         "created_at": 123123,
#                         "widgets": {
#                             "buttons": [
#                                 {
#                                     "title": "Visit",
#                                     "type": "web_url",
#                                     "payload": "http://link.com/test-product",  # noqa
#                                 },
#                                 {
#                                     "title": "Call seller",
#                                     "type": "phone_number",
#                                     "payload": "+84 354 232 111",
#                                 },
#                                 {
#                                     "title": "See price",
#                                     "type": "postback",
#                                     "payload": "see price",
#                                 },
#                             ],
#                             "text_objects": [
#                                 {
#                                     "type": "title-something",
#                                     "name": "tên hàng được đặt",
#                                     "value": "tên hàng chưa đặt",
#                                 },
#                                 {
#                                     "type": "title-something-2",
#                                     "name": "số lượng",
#                                     "value": "2",
#                                 },
#                                 {
#                                     "type": "title-something-2",
#                                     "name": "Đơn giá",
#                                     "value": "40.000.000",
#                                 },
#                             ],
#                         },
#                     },
#                 },
#             },
#         ),
#         TestSchemaInput(
#             name="Invalid type card.",
#             data={
#                 "type": "card",
#                 "metadata": {
#                     "card_information": {
#                         "created_at": 123123,
#                         "type": "not approval",
#                         "title": "test",
#                         "sub_title": "hello world",
#                     },
#                 },
#             },
#             error="Invalid type card.",
#         ),
#     ],
#     ids=lambda vals: vals.name,  # type: ignore
# )
# def test_card_schema(testcase: TestSchemaInput):
#     check_schema(SchemaBodyCardMessage, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK",
            data={
                "type": "dynamic",
                "text": "123",
                "metadata": {
                    "layout": {
                        "direction": "vertical",
                        "border": {
                            "width": 1,
                            "corner_radius": 16,
                            "color": "#DADDE1",
                        },
                        "type": "container",
                        "children": [
                            {
                                "insets": "12,12,12,12",
                                "item_spacing": 12,
                                "type": "container",
                                "direction": "vertical",
                                "children": [
                                    {
                                        "text_object": {
                                            "font": {
                                                "name": "SF Pro Text",
                                                "size": 16,
                                                "style": "medium",
                                            },
                                            "text": "Tích hợp Chat GTP",
                                            "color": "#18202A",
                                        },
                                        "type": "text",
                                    },
                                    {
                                        "children": [
                                            {
                                                "direction": "horizontal",
                                                "type": "container",
                                                "children": [
                                                    {
                                                        "width": 80,
                                                        "type": "text",
                                                        "text": {
                                                            "text": "Danh sách:",
                                                            "font": {
                                                                "style": "regular",
                                                                "size": 14,
                                                                "name": "SF Pro Text",
                                                            },
                                                            "color": "#73787E",
                                                        },
                                                    },
                                                    {
                                                        "type": "text",
                                                        "text_object": {
                                                            "font": {
                                                                "style": "regular",
                                                                "size": 14,
                                                                "name": "SF Pro Text",
                                                            },
                                                            "text": "Tích hợp",
                                                            "color": "#18202A",
                                                        },
                                                    },
                                                ],
                                            },
                                            {
                                                "direction": "horizontal",
                                                "type": "container",
                                                "children": [
                                                    {
                                                        "text_object": {
                                                            "font": {
                                                                "style": "regular",
                                                                "size": 14,
                                                                "name": "SF Pro Text",
                                                            },
                                                            "text": "Trong mục:",
                                                            "color": "#73787E",
                                                        },
                                                        "width": 80,
                                                        "type": "text",
                                                    },
                                                    {
                                                        "text_object": {
                                                            "text": "Đang thực hiện",
                                                            "color": "#18202A",
                                                            "font": {
                                                                "size": 14,
                                                                "name": "SF Pro Text",
                                                                "style": "regular",
                                                            },
                                                        },
                                                        "type": "text",
                                                    },
                                                ],
                                            },
                                        ],
                                        "item_spacing": 8,
                                        "type": "container",
                                        "direction": "vertical",
                                    },
                                ],
                            },
                            {
                                "direction": "horizontal",
                                "insets": "12,12,12,12",
                                "item_spacing": 8,
                                "background": "#F5F7F9",
                                "type": "container",
                                "children": [
                                    {
                                        "type": "container",
                                        "direction": "vertical",
                                        "children": [
                                            {
                                                "direction": "vertical",
                                                "text": {
                                                    "text": "Tạo công việc mới",
                                                    "color": "#18202A",
                                                    "font": {
                                                        "style": "medium",
                                                        "name": "SF Pro Text",
                                                        "size": 14,
                                                    },
                                                },
                                                "type": "text",
                                            },
                                            {
                                                "text": {
                                                    "color": "#73787E",
                                                    "text": "Bởi Vũ Quang Nghĩa",
                                                    "font": {
                                                        "size": 12,
                                                        "style": "regular",
                                                        "name": "SF Pro Text",
                                                    },
                                                },
                                                "type": "text",
                                            },
                                        ],
                                    },
                                    {
                                        "border": {"corner_radius": 8},
                                        "width": 80,
                                        "text": {
                                            "font": {
                                                "name": "SF Pro Text",
                                                "size": 16,
                                                "style": "medium",
                                            },
                                            "color": "#FFF",
                                            "text": "Chi tiết",
                                        },
                                        "type": "button",
                                        "background": "#3AB67B",
                                    },
                                ],
                            },
                        ],
                    },
                },
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_dynamic_schema(testcase: TestSchemaInput):
    check_schema(SchemaBodyDynamicMessage, testcase)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="direct call",
            data={
                "type": "call",
                "metadata": {
                    "call_information": {
                        "id": "12312313",
                        "type": 1,
                        "status": 1,
                        "created_at": 1,
                        "stopped_at": 1,
                        "duration": 1,
                    },
                },
            },
        ),
        TestSchemaInput(
            name="group chat call",
            data={
                "type": "call_group_chat",
                "metadata": {
                    "call_information": {
                        "id": "12312313",
                        "type": 1,
                        "status": 1,
                        "created_at": 1,
                        "stopped_at": 1,
                        "duration": 1,
                    },
                },
            },
        ),
        # TestSchemaInput(
        #     name="null metadata",
        #     data={
        #         "type": "sticker",
        #         "media": [GAPO_IMAGE_DOMAIN + "/test.mp4"],
        #         "metadata": None,
        #     },
        #     error="Invalid metadata",
        # ),
        # TestSchemaInput(
        #     name="valid",
        #     data={
        #         "type": "sticker",
        #         "media": [GAPO_IMAGE_DOMAIN + "/test.mp4"],
        #         "metadata": {"mentions": []},
        #     },
        # ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_call_message(testcase: TestSchemaInput):
    check_schema(SchemaBodyCallService, testcase)
