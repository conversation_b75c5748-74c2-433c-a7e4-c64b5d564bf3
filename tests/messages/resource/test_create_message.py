from typing import cast

from falcon.testing import TestClient
from chat.messages.model import MessageBody
from chat.container import AppContainer
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.messages.model.api import (
    SchemaCreateAuthServiceMessage,
    SchemaBodyAuth,
    SchemaMetadataAuth,
    SchemaAuthInformation,
    SchemaCreateCallServiceMessage,
    SchemaBodyCallService,
    SchemaMetadataCallSerivce,
    SchemaCallServiceInformation,
    SchemaCreateMeetingMessage,
    SchemaBodyMeeting,
    SchemaMetadataMeeting,
    SchemaMeetingInformation,
    SchemaCreateZoomMessage,
    SchemaBodyConference,
    SchemaMetadataConference,
    SchemaConferenceInformation,
    SchemaCreateWalletMessage,
    SchemaBodyDonate,
    SchemaMetadataDonate,
    SchemaDonateInformation,
    SchemaPostForwards,
)
from tests.conftest import (
    TEST_COLLAB_ID,
    TEST_U1,
    TEST_U2,
    user_header,
    service_header,
    TEST_API_KEY,
)
from tests.helpers.message import create_message
from tests.helpers.thread import create_sub_thread_of_group


def test_create_auth_msg_success_internal(
    container: AppContainer, client: TestClient
):
    auth_header = service_header(TEST_API_KEY)
    auth_header["x-gapo-user-id"] = TEST_U1
    body = SchemaBodyAuth(
        type="login",
        metadata=SchemaMetadataAuth(
            auth_information=SchemaAuthInformation(
                request_ip="123123",
                device_id="12312321",
                user_agent="13123",
                created_at=213123,
            ),
        ),
    ).dict()
    body["type"] = "login"
    post_body = SchemaCreateAuthServiceMessage(
        body=body,
    ).dict()

    post_body["body"]["type"] = "login"

    body["type"] = "login"

    result = client.simulate_post(
        "/messages/auth",
        headers=auth_header,
        json=post_body,
    )

    assert result.status_code == 200


def test_create_call_msg_success_internal(
    container: AppContainer, client: TestClient
):
    auth_header = service_header(TEST_API_KEY)
    auth_header["x-gapo-user-id"] = TEST_U1
    body = SchemaBodyCallService(
        type="call",
        metadata=SchemaMetadataCallSerivce(
            call_information=SchemaCallServiceInformation(
                id="123123",
                type=1,
                status=2,
                created_at=213123,
                stopped_at=213123,
            ),
        ),
    ).dict()
    post_body = SchemaCreateCallServiceMessage(
        partner_id=TEST_U2,
        body=body,
    ).dict()

    result = client.simulate_post(
        "/messages/call",
        headers=auth_header,
        json=post_body,
    )

    assert result.status_code == 200


def test_create_meeting_msg_success_internal(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread
    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    auth_header = service_header(TEST_API_KEY)
    # auth_header["x-gapo-user-id"] = TEST_U1

    body = SchemaBodyMeeting(
        type="meeting",
        text="hihi",
        metadata=SchemaMetadataMeeting(
            meeting_information=SchemaMeetingInformation(
                meeting_name="name",
                meeting_url="name",
                start_time="100",
                end_time="200",
                meeting_type=1,
                meeting_status="end",
            ),
        ),
    ).dict()
    post_body = SchemaCreateMeetingMessage(
        thread_id=thread_id,
        user_id=TEST_U2,
        body=body,
    ).dict()

    result = client.simulate_post(
        "/messages/meeting",
        headers=auth_header,
        json=post_body,
    )

    assert result.status_code == 200


def test_create_zoom_msg_success_internal(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread
    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    auth_header = service_header(TEST_API_KEY)
    # auth_header["x-gapo-user-id"] = TEST_U1

    body = SchemaBodyConference(
        type="text",
        text="hihi",
        metadata=SchemaMetadataConference(
            conference_information=SchemaConferenceInformation(
                uuid="123",
                id=123,
                host_email="asf",
                topic="12313",
                type=1,
                join_url="12312",
                password="123123",
                encrypted_password="123213",
                created_at="123",
                start_time="123",
                timezone="111",
            ),
        ),
    )
    post_body = SchemaCreateZoomMessage(
        thread_id=thread_id,
        user_id=TEST_U2,
        body=body,
    ).dict()

    result = client.simulate_post(
        "/messages/zoom",
        headers=auth_header,
        json=post_body,
    )

    assert result.status_code == 200


def test_create_wallet_msg_success_internal(
    container: AppContainer, client: TestClient
):
    # thread_usc = container.thread().usecases().create_thread
    # p_thread = thread_usc.create_group(
    #     [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    # )
    # thread_id = p_thread.thread.id

    auth_header = service_header(TEST_API_KEY)
    auth_header["x-gapo-user-id"] = TEST_U1

    body = SchemaBodyDonate(
        type="donate",
        text="hihi",
        metadata=SchemaMetadataDonate(
            donate_information=SchemaDonateInformation(
                amount=100,
                message="hihi",
                currency="usd",
                transaction_id="123123",
            ),
        ),
    )
    post_body = SchemaCreateWalletMessage(
        partner_id=TEST_U2,
        body=body,
    ).dict()

    count = 0
    while count <= 10:
        count += 1
        result = client.simulate_post(
            "/messages/wallet",
            headers=auth_header,
            json=post_body,
        )

        assert result.status_code == 200


def test_create_save_msg_success_internal(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread
    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    save_bot_id = "5802302828844154881"
    auth_header = service_header(TEST_API_KEY)
    auth_header["x-gapo-user-id"] = save_bot_id

    post_body = SchemaPostForwards(
        source_thread_id=thread_id,
        source_message_ids=[1],
        partner_id=TEST_U1,
        partner_type="user",
    ).dict()

    post_body.pop("thread_id")

    result = client.simulate_post(
        "/messages/save_message",
        headers=auth_header,
        json=post_body,
    )

    assert result.status_code == 200


def test_create_text_msg_success(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        "/messages",
        headers=user_header(TEST_U1),
        json={
            "thread_id": thread_id,
            "body": {
                "metadata": {"mentions": []},
                "media": [],
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 200


def test_create_reply_message_in_sub_thread(
    container: AppContainer, client: TestClient
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container,
        "Test",
        TEST_U1,
        [TEST_U1, TEST_U2],
        sub_thread_member_ids=[TEST_U1],
    )

    # create  message of TEST_U1
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Root message"})
    create_message(
        container.app(), sub_thread_id, message_id, TEST_U1, body, msg_count=1
    )

    # create reply message of TEST_U2
    reply_body = MessageBody(
        **{
            "type": "text",
            "text": "Reply message",
            "metadata": {"mentions": []},
            "is_markdown_text": 0,
            "reply_to_msg": 1,
        }
    )
    result = client.post(
        "/messages",
        headers=user_header(TEST_U2),
        json={
            "thread_id": sub_thread_id,
            "body": reply_body.dict(),
        },
    )
    assert result.status_code == 200

    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2


def test_create_text_msg_with_partner_id(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    _ = thread_usc.create_direct(TEST_U2, TEST_U1)
    body = MessageBody(
        **{
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        }
    )

    result = client.simulate_post(
        "/messages",
        headers=user_header(TEST_U1),
        json={
            "partner_id": TEST_U2,
            "partner_type": "user",
            "body": body.dict(),
        },
    )
    assert result.status_code == 200


def test_create_message_to_collab_id(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id
    collab_id = p_thread.thread.collab_id
    body = MessageBody(
        **{
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        }
    )

    result = client.simulate_post(
        "/messages",
        headers=user_header(TEST_U1),
        json={
            "collab_id": collab_id,
            "body": body.dict(),
        },
    )
    assert result.status_code == 200
    out = result.json["data"]
    assert out["thread_id"] == thread_id


def test_create_message_to_collab_id_rate_limit(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id
    collab_id = p_thread.thread.collab_id
    body = MessageBody(
        **{
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "text",
            "is_markdown_text": False,
        }
    )

    count = 0

    while count <= 10:
        count += 1
        result = client.simulate_post(
            "/messages",
            headers=user_header(TEST_U1),
            json={
                "collab_id": collab_id,
                "body": body.dict(),
            },
        )

    assert result.status_code == 429


def test_create_msg_with_wrong_type(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id
    body = MessageBody(
        **{
            "metadata": {"mentions": []},
            "media": [],
            "text": "Hi all",
            "type": "invalid_type",
            "is_markdown_text": False,
        }
    )

    result = client.simulate_post(
        "/messages",
        headers=user_header(TEST_U1),
        json={
            "thread_id": thread_id,
            "body": body.dict(),
        },
    )
    assert result.status_code == 400
    body = result.json
    assert body["error"] == "invalid_parameters"
    assert body["error_details"][0]["msg"] == "Invalid body type invalid_type"
