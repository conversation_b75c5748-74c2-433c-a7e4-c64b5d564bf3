from falcon.testing import TestClient
from chat.messages.model import MessageBody
from chat.container import AppContainer
from tests.helpers.message import create_message
from tests.conftest import (
    TEST_API_KEY,
    TEST_B1,
    TEST_B2,
    TEST_COLLAB_ID,
    TEST_DEACTIVATED_USER,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    service_header,
)


def test_create_text_msg_with_invalid_partner_id(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    _ = thread_usc.create_direct(TEST_U2, TEST_U1)

    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "partner_id": TEST_B2,  # partner_id must be a valid user_id
            "partner_type": "user",
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 404


def test_create_text_msg_with_invalid_thread_id(
    container: AppContainer, client: TestClient
):
    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "thread_id": -111,  # partner_id must be a valid user_id
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400


def test_create_text_msg_with_invalid_user_id(
    container: AppContainer, client: TestClient
):
    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "partner_id": 1231241241241,
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )

    assert result.status_code != 200


def test_create_text_msg_to_deactivated_user(
    container: AppContainer, client: TestClient
):
    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "partner_id": TEST_DEACTIVATED_USER,
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )

    body = result.json
    assert body["error"] == "user_not_found"
    assert result.status_code == 404


def test_create_msg_to_collab(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    _ = thread_usc.create_group(
        [TEST_U2, TEST_U3, TEST_B1],
        TEST_U1,
        "Test group",
        collab_id=TEST_COLLAB_ID,
    )

    count = 0
    while count <= 10:
        count += 1
        result = client.simulate_post(
            "/messages/bot",
            headers=service_header(TEST_API_KEY),
            json={
                "collab_id": TEST_COLLAB_ID,
                "bot_id": TEST_B1,
                "body": {
                    "metadata": {"mentions": []},
                    "media": None,
                    "text": "Hi all",
                    "type": "text",
                    "is_markdown_text": False,
                },
            },
        )

        assert result.status_code == 200


def test_create_msg_to_subthread(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group(
        [TEST_U2, TEST_U3, TEST_B1],
        TEST_U1,
        "Test group",
        collab_id=TEST_COLLAB_ID,
    )
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Root message"})
    create_message(
        container.app(),
        p_thread.thread.id,
        message_id,
        TEST_U1,
        body,
        msg_count=1,
    )
    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "collab_id": TEST_COLLAB_ID,
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )

    assert result.status_code == 200

    result = client.simulate_post(
        "/messages/bot",
        headers=service_header(TEST_API_KEY),
        json={
            "collab_id": TEST_COLLAB_ID,
            "message_id": 1,
            "bot_id": TEST_B1,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )

    assert result.status_code == 200
