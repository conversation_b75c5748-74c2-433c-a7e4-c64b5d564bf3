from dataclasses import dataclass
from typing import Any, Optional

import pytest
from falcon.testing import TestClient

from chat.container import AppContainer
from chat.messages.model import MessageBody
from tests.helpers.message import create_message
from chat.connections.gapo_client.base import SchemaPoll
from chat.messages.model.api import (
    SchemaPollCreate,
    SchemaPollEdit,
    SchemaPollBody,
    SchemaBodyPollVote,
    SchemaVoteBody,
    SchemaUserVoteChoiceCreate,
    SchemaPollVoteCreate,
    SchemaPollVoteEdit,
    SchemaPollVoteDelete,
)
from tests.conftest import TEST_U1, TEST_U2, user_header


@dataclass
class TestSchemaInput:
    name: str = ""
    data: Any = None
    error: Optional[str] = None


@dataclass
class TestAPIInput:
    name: str
    body: Any
    status_code: int
    error_code: Optional[str] = None
    error: Optional[str] = None

    def check_response(self, res):
        assert res.status_code == self.status_code
        if self.status_code != 200:
            body = res.json
            if self.error_code:
                assert self.error_code == body["error"]
            if self.error:
                assert self.error == body["error_details"][0]["msg"]


@pytest.mark.parametrize(
    "testcase",
    [
        TestAPIInput(
            "OK - Success case",
            body=lambda thread_id: SchemaPollCreate(
                thread_id=thread_id,
                body=SchemaBodyPollVote(
                    votes=[SchemaVoteBody(title="test")],
                    allow_add_choice=False,
                    allow_multiple_choice=False,
                    title="Test all",
                ),
            ).dict(),
            status_code=200,
        ),
        TestAPIInput(
            "Fail - Invalid poll format",
            body=lambda thread_id: {},
            status_code=400,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_create_poll_api(
    container: AppContainer, client: TestClient, testcase: TestAPIInput
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = testcase.body(thread_id)
    result = client.simulate_post(
        "/messages/polls",
        headers=user_header(TEST_U1),
        json=body,
    )
    testcase.check_response(result)


def test_edit_poll_api(container: AppContainer, client: TestClient):
    gapo_client = container.app().repos.gapo
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = SchemaPollCreate(
        thread_id=thread_id,
        body=SchemaBodyPollVote(
            votes=[SchemaVoteBody(title="test")],
            allow_add_choice=False,
            allow_multiple_choice=False,
            title="Test all",
        ),
    ).dict()
    result = client.simulate_post(
        "/messages/polls",
        headers=user_header(TEST_U1),
        json=body,
    )

    assert result.status_code == 200

    resp = result.json
    # {
    #     "data": {
    #         "event_id": "737e9028-6c51-4486-9ebc-2bb72e947b44",
    #         "thread_id": 1725954049303,
    #         "message_id": 2,
    #     }
    # }
    message_id = resp["data"]["message_id"]
    poll_id = "1"
    poll: SchemaPoll = gapo_client.poll.get_polls(TEST_U1, [poll_id])[poll_id]
    body = MessageBody(
        **{
            "media": [],
            "tmp_text": "Poll đã được tạo",
            "is_markdown_text": False,
            "metadata": {
                "poll_information": poll.dict(),
            },
            "type": "poll",
        }
    )
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, message_id
    )

    body = SchemaPollEdit(
        thread_id=thread_id,
        message_id=message_id,
        body=SchemaPollBody(title="hihi 2"),
    ).dict()

    result = client.simulate_patch(
        "/messages/polls",
        headers=user_header(TEST_U1),
        json=body,
    )

    assert result.status_code == 200


def test_vote_poll_api(container: AppContainer, client: TestClient):
    gapo_client = container.app().repos.gapo
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = SchemaPollCreate(
        thread_id=thread_id,
        body=SchemaBodyPollVote(
            votes=[SchemaVoteBody(title="test")],
            allow_add_choice=False,
            allow_multiple_choice=False,
            title="Test all",
        ),
    ).dict()
    result = client.simulate_post(
        "/messages/polls",
        headers=user_header(TEST_U1),
        json=body,
    )

    assert result.status_code == 200

    resp = result.json
    # {
    #     "data": {
    #         "event_id": "737e9028-6c51-4486-9ebc-2bb72e947b44",
    #         "thread_id": 1725954049303,
    #         "message_id": 2,
    #     }
    # }
    message_id = resp["data"]["message_id"]
    poll_id = "1"
    poll: SchemaPoll = gapo_client.poll.get_polls(TEST_U1, [poll_id])[poll_id]
    body = MessageBody(
        **{
            "media": [],
            "tmp_text": "Poll đã được tạo",
            "is_markdown_text": False,
            "metadata": {
                "poll_information": poll.dict(),
            },
            "type": "poll",
        }
    )
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, message_id
    )

    body = SchemaPollVoteCreate(
        thread_id=thread_id,
        message_id=message_id,
        body=SchemaVoteBody(title="hihi"),
    ).dict()
    result = client.simulate_post(
        "/messages/votes",
        headers=user_header(TEST_U1),
        json=body,
    )
    assert result.status_code == 200

    body = SchemaPollVoteEdit(
        thread_id=thread_id,
        message_id=message_id,
        vote_id="0",
        body=SchemaVoteBody(title="hihi 2"),
    ).dict()
    result = client.simulate_patch(
        "/messages/votes",
        headers=user_header(TEST_U1),
        json=body,
    )
    assert result.status_code == 200

    body = SchemaPollVoteDelete(
        thread_id=thread_id,
        message_id=message_id,
        vote_id="0",
    ).dict()
    result = client.simulate_delete(
        "/messages/votes",
        headers=user_header(TEST_U1),
        params=body,
    )
    assert result.status_code == 200


def test_vote_choice_poll_api(container: AppContainer, client: TestClient):
    gapo_client = container.app().repos.gapo
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create message
    body = SchemaPollCreate(
        thread_id=thread_id,
        body=SchemaBodyPollVote(
            votes=[SchemaVoteBody(title="test")],
            allow_add_choice=False,
            allow_multiple_choice=False,
            title="Test all",
        ),
    ).dict()
    result = client.simulate_post(
        "/messages/polls",
        headers=user_header(TEST_U1),
        json=body,
    )

    assert result.status_code == 200

    resp = result.json
    # {
    #     "data": {
    #         "event_id": "737e9028-6c51-4486-9ebc-2bb72e947b44",
    #         "thread_id": 1725954049303,
    #         "message_id": 2,
    #     }
    # }
    message_id = resp["data"]["message_id"]
    poll_id = "1"
    poll: SchemaPoll = gapo_client.poll.get_polls(TEST_U1, [poll_id])[poll_id]
    body = MessageBody(
        **{
            "media": [],
            "tmp_text": "Poll đã được tạo",
            "is_markdown_text": False,
            "metadata": {
                "poll_information": poll.dict(),
            },
            "type": "poll",
        }
    )
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, message_id
    )

    body = SchemaPollVoteCreate(
        thread_id=thread_id,
        message_id=message_id,
        body=SchemaVoteBody(title="hihi"),
    ).dict()
    result = client.simulate_post(
        "/messages/votes",
        headers=user_header(TEST_U1),
        json=body,
    )
    assert result.status_code == 200

    vote_id = "0"

    body = SchemaUserVoteChoiceCreate(
        thread_id=thread_id,
        message_id=message_id,
        vote_id=vote_id,
    ).dict()
    result = client.simulate_post(
        "/messages/votes/choose",
        headers=user_header(TEST_U1),
        json=body,
    )
    assert result.status_code == 200

    body = SchemaUserVoteChoiceCreate(
        thread_id=thread_id,
        message_id=message_id,
        vote_id=vote_id,
    ).dict()
    result = client.simulate_post(
        "/messages/votes/unchoose",
        headers=user_header(TEST_U1),
        json=body,
    )
    assert result.status_code == 200
