from falcon.testing import TestClient

from chat.container import AppContainer
from tests.conftest import TEST_API_KEY, TEST_U1, TEST_U2, service_header


def test_validation_error(container: AppContainer, client: TestClient):
    result = client.simulate_post(
        "/internal/messages/mass_direct_messages",
        headers=service_header(TEST_API_KEY),
        json={
            "sender_id": TEST_U1,
            "user_ids": [TEST_U2] * 1001,
            "body": {
                "metadata": {"mentions": []},
                "media": None,
                "text": "Hi all",
                "type": "text",
                "is_markdown_text": False,
            },
        },
    )
    assert result.status_code == 400
