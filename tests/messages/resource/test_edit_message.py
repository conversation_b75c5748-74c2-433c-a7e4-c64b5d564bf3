from dataclasses import dataclass
from typing import Any, Optional

import pytest
from falcon.testing import TestClient
from chat.messages.model import Message<PERSON>ody
from chat.container import AppContainer
from chat.messages.model.api import MAX_LENGTH_TEXT
from tests.conftest import TEST_U1, TEST_U2, user_header
from tests.helpers.message import create_message


@dataclass
class TestCase:
    __test__ = False
    name: str
    body: Any
    status_code: int
    error: Optional[str] = None
    error_details: Optional[str] = None

    def check_response(self, res):
        assert res.status_code == self.status_code
        if self.status_code != 200:
            body = res.json
            if self.error:
                assert self.error == body["error"]
            if self.error_details:
                assert self.error_details == body["error_details"][0]["msg"]


@pytest.mark.parametrize(
    "testcase",
    [
        TestCase(
            "OK - Success case", body={"text": "New body"}, status_code=200
        ),
        TestCase(
            "Fail -  Passing long text",
            body={"text": "X" * (MAX_LENGTH_TEXT + 1)},
            error="invalid_parameters",
            error_details="Text too long",
            status_code=400,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_edit_msg(
    container: AppContainer, client: TestClient, testcase: TestCase
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create message
    msg_id = 1
    body = MessageBody(**{"type": "text", "text": "Old message"})
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    result = client.simulate_put(
        f"/messages/{msg_id}",
        headers=user_header(TEST_U1),
        json={
            "thread_id": thread_id,
            "body": testcase.body,
        },
    )
    testcase.check_response(result)
