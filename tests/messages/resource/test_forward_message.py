from dataclasses import dataclass
from typing import Any, Optional

import pytest
from falcon.testing import TestClient

from chat.messages.model import Message<PERSON>ody
from chat.container import AppContainer
from chat.messages.model.api import SchemaDeleteItems
from chat.model import make_input
from tests.conftest import TEST_U1, TEST_U2, user_header
from tests.helpers.message import create_message


@dataclass
class TestSchemaInput:
    name: str = ""
    data: Any = None
    error: Optional[str] = None


@dataclass
class TestAPIInput:
    name: str
    body: Any
    status_code: int
    error_code: Optional[str] = None
    error: Optional[str] = None

    def check_response(self, res):
        assert res.status_code == self.status_code
        if self.status_code != 200:
            body = res.json
            if self.error_code:
                assert self.error_code == body["error"]
            if self.error:
                assert self.error == body["error_details"][0]["msg"]


@pytest.mark.parametrize(
    "testcase",
    [
        TestAPIInput(
            "OK - Success case",
            body={"message_ids": [1], "level": 2, "thread_id": 1},
            status_code=200,
        ),
        TestAPIInput(
            "Fail -  Message id must be int",
            body={"message_ids": ["x"], "level": 1, "thread_id": 1},
            error_code="invalid_parameters",
            error="Message id must be int",
            status_code=400,
        ),
        TestAPIInput(
            "Fail -  Invalid message id",
            body={"message_ids": [-20], "level": 1, "thread_id": 1},
            error_code="invalid_parameters",
            error="Invalid message id",
            status_code=400,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_delete_messages_resource(
    container: AppContainer, client: TestClient, testcase: TestAPIInput
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create message
    msg_id = 1
    body = MessageBody(**{"type": "text", "text": "Old message"})
    create_message(container.app(), thread_id, msg_id, TEST_U1, body)

    body = testcase.body
    body["thread_id"] = thread_id
    result = client.simulate_post(
        f"/messages/deletes",
        headers=user_header(TEST_U1),
        json=body,
    )
    testcase.check_response(result)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK - Success case",
            data={"message_ids": [1], "level": 2, "thread_id": 1},
        ),
        TestSchemaInput(
            "Fail -  Message id must be int",
            data={"message_ids": ["x"], "level": 1, "thread_id": 1},
            error="Message id must be int",
        ),
        TestSchemaInput(
            "Fail -  Invalid message id",
            data={"message_ids": [-20], "level": 1, "thread_id": 1},
            error="Invalid message id",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_input_schema(testcase: TestSchemaInput):
    data, errors = make_input(SchemaDeleteItems, testcase.data)
    if testcase.error:
        assert errors is not None and errors[0]["msg"] == testcase.error
    else:
        assert errors is None
