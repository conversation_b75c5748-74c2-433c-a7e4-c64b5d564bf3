from chat.container import AppContainer
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_transaction_rollback(container: AppContainer):
    app = container.app()
    repos = app.repos

    thread_usecases = container.thread().usecases()
    p_thread = thread_usecases.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test"
    )
    thread_id = p_thread.thread.id

    mysql_conn = app.conns.mysql

    with mysql_conn.get_session() as session:
        member = repos.pt.get_member(session, thread_id, TEST_U2)
        assert member is not None

    try:
        with app.conns.mysql.get_session() as session:
            repos.pt.create_member(session, thread_id, TEST_U3)
            raise Exception("unknown")
    except:
        pass

    with app.conns.mysql.get_session() as session:
        member = repos.pt.get_member(session, thread_id, TEST_U3)
        assert member is None
