import pytest

from chat.connections.gapo_client import Gapo<PERSON>lient<PERSON>ock, SchemaUserProfile


@pytest.fixture()
def client():
    return GapoClientMock(
        user_data=[
            SchemaUserProfile(id="1", name="user1"),  # type: ignore
            SchemaUserProfile(id="2", name="user1"),  # type: ignore
        ]
    )


def test_get_info(client: GapoClientMock):
    users = client.user.get_by_ids(["1"])
    assert len(users) == 1
    assert users["1"]["id"] == "1"
