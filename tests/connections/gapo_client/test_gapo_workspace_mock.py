import pytest

from chat.connections.gapo_client import Gapo<PERSON>lient<PERSON>ock, SchemaUserProfile
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


@pytest.fixture()
def client():
    return GapoClientMock(
        user_data=[
            SchemaUserProfile(id="1", name="user1"),  # type: ignore
            SchemaUserProfile(id="2", name="user1"),  # type: ignore
        ],
        file_data={"test-file": ["2"]},
        company_data={"1": "A", "2": "A", "3": "B"},
    )


def test_get_users_in_imported_file(client: GapoClientMock):
    user_ids = client.workspace.get_users_from_imported_file(
        "dummy-workspace", "not-found"
    )
    assert len(user_ids) == 0

    user_ids = client.workspace.get_users_from_imported_file(
        "dummy-workspace", "test-file"
    )
    assert user_ids == ["2"]


def test_check_users_in_workspace(client: GapoClientMock):
    same_ws = client.workspace.check_if_same_company(TEST_U1, TEST_U2)
    assert same_ws is True

    not_same_ws = client.workspace.check_if_same_company(TEST_U1, TEST_U3)
    assert not_same_ws is False


def test_get_users_in_workspace(client: GapoClientMock):
    wss = client.workspace.get_workspaces(TEST_U1)
    assert wss == set(["A"])
