import pytest

from chat.connections.gapo_client.membership import (
    CollabGroupNotFound,
    CollabGroupRole,
    MemberNotExist,
    MembershipClient,
    PermissionDenied,
)
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


@skip_membership_tests
def test_remove_member_from_nonexist_collab_group_fail(client: MembershipClient):
    collab_id = "non_exist_group"
    workspace_id = "1"
    with pytest.raises(CollabGroupNotFound):
        client.remove_member(
            caller_id=4,
            collab_id=collab_id,
            user_id=100,
            workspace_id=workspace_id,
        )


@skip_membership_tests
def test_remove_non_exist_member(client: MembershipClient):
    workspace_id = "1"
    user_id = 100
    caller_id = 4
    name = "Test"

    collab_id = client.create_collab_group(
        name=name,
        caller_id=caller_id,
        workspace_id=workspace_id,
    )

    with pytest.raises(MemberNotExist):
        client.remove_member(
            caller_id=caller_id,
            collab_id=collab_id,
            user_id=user_id,
            workspace_id=workspace_id,
        )


@skip_membership_tests
@pytest.mark.parametrize(
    "user_id, role",
    [
        (100, CollabGroupRole.Owner),
        (101, CollabGroupRole.Admin),
        (102, CollabGroupRole.Member),
    ],
)
def test_owner_remove_member(user_id, role, client: MembershipClient):
    workspace_id = "1"
    creator_id = 4
    name = "Test"

    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    result = client.remove_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
    )

    assert result is None


@skip_membership_tests
@pytest.mark.parametrize(
    "user_id, role, is_success",
    [
        (100, CollabGroupRole.Owner, True),  # expected fail
        (101, CollabGroupRole.Admin, True),  # expected fail
        (102, CollabGroupRole.Member, True),
    ],
)
def test_admin_remove_member(user_id, role, is_success, client: MembershipClient):
    workspace_id = "1"
    creator_id = 4
    admin_user = 5
    name = "Test"

    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    # Add Chat Admin member
    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=admin_user,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Admin],
    )

    # Owner add member
    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    # Admin remove Member
    if not is_success:
        with pytest.raises(PermissionDenied):
            result = client.remove_member(
                caller_id=admin_user,
                collab_id=collab_id,
                user_id=user_id,
                workspace_id=workspace_id,
            )

        return

    result = client.remove_member(
        caller_id=admin_user,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
    )

    assert result is None


@skip_membership_tests
@pytest.mark.parametrize(
    "user_id, role",
    [
        (100, CollabGroupRole.Owner),
        (101, CollabGroupRole.Admin),
        (102, CollabGroupRole.Member),
    ],
)
def test_member_remove_member(user_id, role, client: MembershipClient):
    workspace_id = "1"
    creator_id = 4
    chat_member = 5
    name = "Test"

    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    # Add Member
    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=chat_member,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Member],
    )

    # Owner add member
    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    # Member remove Member
    with pytest.raises(PermissionDenied):
        client.remove_member(
            caller_id=chat_member,
            collab_id=collab_id,
            user_id=user_id,
            workspace_id=workspace_id,
        )
