import pytest

from chat.connections.gapo_client.membership import CollabSettings, MembershipClient
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


# @skip_membership_tests
# def test_add_member_to_pending_list(client: MembershipClient):
#     workspace_id = "1"
#     creator_id = 4
#     user_id = 100
#     name = "Test"
#     collab_id = client.create_collab_group(
#         name=name,
#         caller_id=creator_id,
#         workspace_id=workspace_id,
#         settings=CollabSettings(discovery=False, public=True, need_approve=True),
#     )
#     collab = client.get_collab_group(creator_id, collab_id, workspace_id)
#     assert collab.settings.need_approve

#     client.request_join(user_id, collab_id, workspace_id)

#     pending_members = client.list_all_pending_members(
#         creator_id, collab_id, workspace_id
#     )
#     assert len(pending_members) == 1
#     assert pending_members[0].user_id == user_id

#     # re-add member don't create new entry
#     client.request_join(user_id, collab_id, workspace_id)
#     pending_members = client.list_all_pending_members(
#         creator_id, collab_id, workspace_id
#     )
#     assert len(pending_members) == 1


# @skip_membership_tests
# def test_add_mem_to_pending_list_of_collab_without_approval(client: MembershipClient):
#     workspace_id = "1"
#     creator_id = 4
#     user_id = 100
#     name = "Test"
#     collab_id = client.create_collab_group(
#         name=name,
#         caller_id=creator_id,
#         workspace_id=workspace_id,
#         settings=CollabSettings(discovery=False, public=True, need_approve=False),
#     )
#     collab = client.get_collab_group(creator_id, collab_id, workspace_id)
#     assert collab.settings.need_approve == False

#     client.request_join(user_id, collab_id, workspace_id)

#     pending_members = client.list_all_pending_members(
#         creator_id, collab_id, workspace_id
#     )
#     assert len(pending_members) == 0  # member didn't add to collab
