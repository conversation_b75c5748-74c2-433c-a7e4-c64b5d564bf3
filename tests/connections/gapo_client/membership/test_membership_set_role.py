import pytest

from chat.connections.gapo_client.membership import (
    CollabGroupNotFound,
    CollabGroupRole,
    MemberNotExist,
    MembershipClient,
    PermissionDenied,
)
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


@skip_membership_tests
def test_set_role_from_nonexist_collab_group_fail(client: MembershipClient):
    collab_id = "non_exist_group"
    workspace_id = "1"
    with pytest.raises(CollabGroupNotFound):
        client.set_member_role(
            caller_id=4,
            collab_id=collab_id,
            user_id=100,
            roles=[CollabGroupRole.Admin],
            workspace_id=workspace_id,
        )


@skip_membership_tests
def test_set_role_to_yourself(client: MembershipClient):
    workspace_id = "1"
    user_id = 100
    caller_id = 4

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=caller_id,
        workspace_id=workspace_id,
    )

    with pytest.raises(PermissionDenied):
        client.set_member_role(
            caller_id=user_id,
            collab_id=collab_id,
            user_id=user_id,
            roles=[CollabGroupRole.Admin],
            workspace_id=workspace_id,
        )


@skip_membership_tests
def test_set_role_for_non_exist_member_fail(client: MembershipClient):
    workspace_id = "1"
    user_id = 100
    caller_id = 4

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=caller_id,
        workspace_id=workspace_id,
    )

    with pytest.raises(MemberNotExist):
        client.set_member_role(
            caller_id=caller_id,
            collab_id=collab_id,
            user_id=user_id,
            roles=[CollabGroupRole.Admin],
            workspace_id=workspace_id,
        )


@skip_membership_tests
@pytest.mark.parametrize(
    "from_role, to_role",
    [
        (CollabGroupRole.Owner, CollabGroupRole.Admin),
        (CollabGroupRole.Owner, CollabGroupRole.Member),
        (CollabGroupRole.Admin, CollabGroupRole.Owner),
        (CollabGroupRole.Admin, CollabGroupRole.Member),
        (CollabGroupRole.Member, CollabGroupRole.Owner),
        (CollabGroupRole.Member, CollabGroupRole.Admin),
    ],
)
def test_owner_set_role_for_member(from_role, to_role, client: MembershipClient):
    workspace_id = "1"
    owner_id = 4
    user_id = 100

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=owner_id,
        workspace_id=workspace_id,
    )

    # Add Member with from_role
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[from_role],
    )

    # Set to_role for member
    result = client.set_member_role(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=user_id,
        roles=[to_role],
        workspace_id=workspace_id,
    )

    assert result is None


@skip_membership_tests
@pytest.mark.parametrize(
    "from_role, to_role, is_success",
    [
        (CollabGroupRole.Owner, CollabGroupRole.Admin, False),
        (CollabGroupRole.Owner, CollabGroupRole.Member, False),
        (CollabGroupRole.Admin, CollabGroupRole.Owner, False),
        (CollabGroupRole.Admin, CollabGroupRole.Member, True),
        (CollabGroupRole.Member, CollabGroupRole.Owner, False),
        (CollabGroupRole.Member, CollabGroupRole.Admin, True),
    ],
)
def test_admin_set_role_for_member(
    from_role, to_role, is_success, client: MembershipClient
):
    workspace_id = "1"
    owner_id = 4
    admin_user = 5
    user_id = 100

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=owner_id,
        workspace_id=workspace_id,
    )

    # Owner add Member as Admin
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=admin_user,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Admin],
    )

    # Owner Add Member with from_role
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[from_role],
    )

    # Admin set role for Member
    if not is_success:
        with pytest.raises(PermissionDenied):
            client.set_member_role(
                caller_id=admin_user,
                collab_id=collab_id,
                user_id=user_id,
                roles=[to_role],
                workspace_id=workspace_id,
            )

        return

    result = client.set_member_role(
        caller_id=admin_user,
        collab_id=collab_id,
        user_id=user_id,
        roles=[to_role],
        workspace_id=workspace_id,
    )

    assert result is None


@skip_membership_tests
@pytest.mark.parametrize(
    "from_role, to_role",
    [
        (CollabGroupRole.Owner, CollabGroupRole.Admin),
        (CollabGroupRole.Owner, CollabGroupRole.Member),
        (CollabGroupRole.Admin, CollabGroupRole.Owner),
        (CollabGroupRole.Admin, CollabGroupRole.Member),
        (CollabGroupRole.Member, CollabGroupRole.Owner),
        (CollabGroupRole.Member, CollabGroupRole.Admin),
    ],
)
def test_member_set_role_for_member_fail(from_role, to_role, client: MembershipClient):
    workspace_id = "1"
    owner_id = 4
    member_id = 5
    user_id = 100

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=owner_id,
        workspace_id=workspace_id,
    )

    # Owner add Member as Member
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=member_id,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Member],
    )

    # Owner Add Member with from_role
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[from_role],
    )

    # Member set role for Member
    with pytest.raises(PermissionDenied):
        client.set_member_role(
            caller_id=member_id,
            collab_id=collab_id,
            user_id=user_id,
            roles=[to_role],
            workspace_id=workspace_id,
        )


@skip_membership_tests
def test_self_change_role(client: MembershipClient):
    workspace_id = "1"
    owner_id = 4
    another_owner_id = 5

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=owner_id,
        workspace_id=workspace_id,
    )

    # Owner add Member as Member
    client.add_member(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=another_owner_id,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Owner],
    )

    # self set role to admin
    result = client.set_member_role(
        caller_id=owner_id,
        collab_id=collab_id,
        user_id=owner_id,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Admin],
    )

    assert result is None
