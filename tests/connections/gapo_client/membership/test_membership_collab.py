import pytest

from chat.connections.gapo_client.membership import (
    CollabGroup,
    CollabGroupNotFound,
    MembershipClient,
)
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


@skip_membership_tests
def test_get_non_exist_collab_group_fail(client: MembershipClient):
    with pytest.raises(CollabGroupNotFound):
        _ = client.get_collab_group(
            caller_id=4,
            collab_id="non_exist_collab_group",
            workspace_id="1",
        )


@skip_membership_tests
def test_create_collab_group_success(client: MembershipClient):
    caller_id = 4
    expected_name = "Test"
    expected_workspace_id = "1"
    id = client.create_collab_group(
        caller_id=caller_id,
        name=expected_name,
        workspace_id=expected_workspace_id,
    )

    collab_group = client.get_collab_group(
        caller_id=caller_id,
        collab_id=id,
        workspace_id=expected_workspace_id,
    )

    assert isinstance(id, str)
    assert isinstance(collab_group, CollabGroup)
    assert collab_group.workspace_id == expected_workspace_id
    assert collab_group.collab_group_id == id
    assert collab_group.name == expected_name
    # assert collab_group.auto_accept == True  # TODO: Temporary ignore check auto_accept


@skip_membership_tests
@pytest.mark.parametrize(
    "auto_accept, expect_auto_accept", [(True, True), (False, False)]
)
def test_create_collab_group_with_auto_accept_success(
    auto_accept, expect_auto_accept, client: MembershipClient
):
    caller_id = 4
    expected_name = "Test"
    expected_workspace_id = "1"
    id = client.create_collab_group(
        caller_id=caller_id,
        name=expected_name,
        workspace_id=expected_workspace_id,
    )

    collab_group = client.get_collab_group(
        caller_id=caller_id,
        collab_id=id,
        workspace_id=expected_workspace_id,
    )

    assert isinstance(id, str)
    assert isinstance(collab_group, CollabGroup)
    assert collab_group.workspace_id == expected_workspace_id
    assert collab_group.collab_group_id == id
    assert collab_group.name == expected_name
    # assert collab_group.auto_accept == expect_auto_accept # TODO: Temporary ignore check auto_accept


@skip_membership_tests
def test_change_collab_group_name_success(client: MembershipClient):
    caller_id = 4
    expected_new_name = "New Name"
    expected_workspace_id = "1"
    id = client.create_collab_group(
        caller_id=caller_id,
        name="Test",
        workspace_id=expected_workspace_id,
    )

    client.update_collab_info(
        collab_id=id,
        caller_id=caller_id,
        workspace_id=expected_workspace_id,
        name=expected_new_name,
    )

    collab_group = client.get_collab_group(
        caller_id=caller_id,
        collab_id=id,
        workspace_id=expected_workspace_id,
    )

    assert isinstance(id, str)
    assert isinstance(collab_group, CollabGroup)
    assert collab_group.workspace_id == expected_workspace_id
    assert collab_group.collab_group_id == id
    assert collab_group.name == expected_new_name


@skip_membership_tests
@pytest.mark.parametrize(
    "fields",
    [
        {"name": "new name"},
        {"description": "new description"},
        {"avatar": "new avatar"},
        {"name": "new name", "description": "new description", "avatar": "new avatar"},
    ],
)
def test_update_collab_info(client: MembershipClient, fields):
    caller_id = 4
    expected_workspace_id = "1"
    id = client.create_collab_group(
        caller_id=caller_id,
        name="name",
        description="description",
        avatar="avatar",
        workspace_id=expected_workspace_id,
    )

    # update name only
    client.update_collab_info(
        collab_id=id, caller_id=caller_id, workspace_id=expected_workspace_id, **fields
    )

    collab_group = client.get_collab_group(
        caller_id=caller_id,
        collab_id=id,
        workspace_id=expected_workspace_id,
    )

    expected = {"name": "name", "description": "description", "avatar": "avatar"}
    for field, value in fields.items():
        expected[field] = value

    for field, value in expected.items():
        assert collab_group.__getattribute__(field) == value
