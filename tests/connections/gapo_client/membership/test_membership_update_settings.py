from time import sleep

import pytest

from chat.connections.gapo_client.membership import CollabSettings, MembershipClient
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


@skip_membership_tests
def test_update_settings(client: MembershipClient):
    workspace_id = "1"
    owner_id = 4
    another_owner_id = 5

    collab_id = client.create_collab_group(
        name="Test",
        caller_id=owner_id,
        workspace_id=workspace_id,
        settings=CollabSettings(discovery=False, public=False, need_approve=False),
    )

    collab = client.get_collab_group(owner_id, collab_id, workspace_id)
    assert not collab.settings.need_approve
    assert not collab.settings.discovery
    assert not collab.settings.public
    assert collab.name == "Test"  # Don't change name

    client.update_collab(
        owner_id,
        collab_id,
        workspace_id,
        name=None,
        settings=CollabSettings(discovery=True, public=True, need_approve=True),
    )
    sleep(0.5)

    collab = client.get_collab_group(owner_id, collab_id, workspace_id)
    collab = client.get_collab_group(owner_id, collab_id, workspace_id)
    assert collab.settings.need_approve
    assert collab.settings.discovery
    assert collab.settings.public
    assert collab.name == "Test"
