import grpc
import pytest

from chat.connections.gapo_client.membership import (
    CollabGroupNotFound,
    CollabGroupRole,
    CollabSettings,
    MemberAlreadyExist,
    MembershipClient,
)
from tests.utils import get_test_config, skip_membership_tests


@pytest.fixture()
def client():
    cfg = get_test_config()
    return MembershipClient(cfg)


@skip_membership_tests
def test_add_member_to_nonexist_collab_group_fail(client: MembershipClient):
    collab_id = "non_exist_group"
    workspace_id = "1"
    with pytest.raises(CollabGroupNotFound):
        client.add_member(
            caller_id=4,
            collab_id=collab_id,
            user_id=100,
            workspace_id=workspace_id,
            roles=[CollabGroupRole.Owner],
        )


# Always add user as Member in this stage
@pytest.mark.parametrize(
    "user_id, role",
    [
        (102, CollabGroupRole.Member),
    ],
)
@skip_membership_tests
def test_owner_add_member_to_exist_collab_group_success(
    user_id, role, client: MembershipClient
):
    workspace_id = "1"
    creator_id = 4
    name = "Test"
    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    member_id = client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    assert isinstance(member_id, int)

    members = client.get_member_by_ids(
        caller_id=creator_id,
        collab_id=collab_id,
        workspace_id=workspace_id,
        user_ids=[user_id],
    )

    member = members[0]
    # assert member.membership_id == member_id # not pass yet ?
    assert member.user_id == user_id
    assert member.roles[0] == role


# Always add user as Member in this stage
@skip_membership_tests
@pytest.mark.parametrize(
    "user_id, role, is_success",
    [
        (102, CollabGroupRole.Member, True),
    ],
)
def test_admin_add_member_to_exist_collab_group(
    user_id, role, is_success, client: MembershipClient
):
    workspace_id = "1"
    creator_id = 4
    admin_user = 5
    name = "Test"
    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    # Add Chat Admin member
    member_id = client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=admin_user,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Admin],
    )

    # Admin add member
    if not is_success:
        with pytest.raises(grpc.RpcError):  # Should raise PermissionDenied
            member_id = client.add_member(
                caller_id=admin_user,
                collab_id=collab_id,
                user_id=user_id,
                workspace_id=workspace_id,
                roles=[role],
            )

        return

    member_id = client.add_member(
        caller_id=admin_user,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    assert isinstance(member_id, int)

    members = client.get_member_by_ids(
        caller_id=creator_id,
        collab_id=collab_id,
        workspace_id=workspace_id,
        user_ids=[user_id],
    )

    member = members[0]
    # assert member.membership_id == member_id # not pass yet
    assert member.user_id == user_id
    assert member.roles[0] == role


# Always add user as Member in this stage
@skip_membership_tests
@pytest.mark.parametrize(
    "user_id, role, is_success",
    [
        (102, CollabGroupRole.Member, True),
    ],
)
def test_member_add_member_to_exist_collab_group_success(
    user_id, role, is_success, client: MembershipClient
):
    workspace_id = "1"
    creator_id = 4
    chat_member = 5
    name = "Test"
    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    # Add Chat Member member
    member_id = client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=chat_member,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Admin],
    )

    # Member add member
    if not is_success:
        with pytest.raises(grpc.RpcError):  # Should raise PermissionDenied
            member_id = client.add_member(
                caller_id=chat_member,
                collab_id=collab_id,
                user_id=user_id,
                workspace_id=workspace_id,
                roles=[role],
            )

        return

    member_id = client.add_member(
        caller_id=chat_member,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[role],
    )

    assert isinstance(member_id, int)

    members = client.get_member_by_ids(
        caller_id=creator_id,
        collab_id=collab_id,
        workspace_id=workspace_id,
        user_ids=[user_id],
    )

    member = members[0]
    # assert member.membership_id == member_id # not pass yet!
    assert member.user_id == user_id
    assert member.roles[0] == role


@skip_membership_tests
def test_add_exist_member_to_group_should_fail(client: MembershipClient):
    workspace_id = "1"
    creator_id = 4
    user_id = 100
    name = "Test"
    collab_id = client.create_collab_group(
        name=name,
        caller_id=creator_id,
        workspace_id=workspace_id,
    )

    client.add_member(
        caller_id=creator_id,
        collab_id=collab_id,
        user_id=user_id,
        workspace_id=workspace_id,
        roles=[CollabGroupRole.Member],
    )

    # Add again
    with pytest.raises(MemberAlreadyExist):
        client.add_member(
            caller_id=creator_id,
            collab_id=collab_id,
            user_id=user_id,
            workspace_id=workspace_id,
            roles=[CollabGroupRole.Member],
        )


# @skip_membership_tests
# def test_member_add_member_to_collab_requiring_approval(client: MembershipClient):
#     workspace_id = "1"
#     creator_id = 4
#     chat_member = 5
#     name = "Test"
#     collab_id = client.create_collab_group(
#         name=name,
#         caller_id=creator_id,
#         workspace_id=workspace_id,
#         settings=CollabSettings(public=False, discovery=False, need_approve=True),
#     )

#     # Add Chat Member member
#     member_id = client.add_member(
#         caller_id=creator_id,
#         collab_id=collab_id,
#         user_id=chat_member,
#         workspace_id=workspace_id,
#         roles=[CollabGroupRole.Admin],
#     )
#     members = client.list_members(creator_id, collab_id)
#     assert len(members) == 2

#     pending_members = client.list_all_pending_members(
#         creator_id, collab_id, workspace_id
#     )
#     assert len(pending_members) == 0
