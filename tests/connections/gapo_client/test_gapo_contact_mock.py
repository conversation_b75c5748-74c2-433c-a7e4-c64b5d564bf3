import pytest

from chat.connections.gapo_client import GapoClientMock
from chat.connections.gapo_client.mock import Contact


@pytest.fixture()
def client():
    return GapoClientMock(contact_data=[Contact(u1="1", u2="2", code=3)])


def test_has_contact(client: GapoClientMock):
    assert client.contact.get_relationship_status("1", "2") == 3
    assert client.contact.get_relationship_status("1", "3") == 0
