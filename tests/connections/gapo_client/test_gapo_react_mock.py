import pytest

from chat.connections.gapo_client import GapoClientMock


@pytest.fixture()
def client():
    return GapoClientMock()


def test_get_react(client: GapoClientMock):
    reacts = client.react.get_react("1", 2, [1], [1])
    assert len(reacts) == 0


def test_get_react_users(client: GapoClientMock):
    users = client.react.get_react_users("1", 1000, 1, 3, 3, "")
    assert len(users) == 0


def test_post_react(client: GapoClientMock):
    ok = client.react.post_react("1", 1, 1, 1, 1)
    assert ok
