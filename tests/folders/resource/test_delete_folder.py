from falcon.testing import TestClient

from chat.constant import FOLDER_DEFAULT
from chat.container import AppContainer
from chat.container.app import AppContainer
from chat.utils.common import now
from tests.conftest import TEST_U1, user_header
from tests.helpers.thread import setup_threads_and_folder


def is_score_of_pinned_thread(score: int):
    return score > now() * 100


def test_delete_folder(container: AppContainer, client: TestClient):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder_id = setup_threads_and_folder(container)

    # pin a test thread in default folder
    thread_uscs.pin_thread.pin(th2, TEST_U1, FOLDER_DEFAULT)

    resp = client.simulate_delete(
        f"/folders/{folder_id}",
        headers=user_header(TEST_U1),
    )
    assert resp.status_code == 200

    # check thread score
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert len(thread_ids) == 0

    # thread is unpinned when moved to new folder
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert len(thread_ids) == 3

    # make sure thread 1 is still pinned
    # this check is because we make a move_folder action
    # from the origin folder to default folder
    # so we don't want score of thread 2 is to be overwritten
    th2_score = broker.get_thread_score(TEST_U1, th2, FOLDER_DEFAULT)
    assert is_score_of_pinned_thread(th2_score)
