from time import sleep

from falcon.testing import TestClient

from chat.container import AppContainer
from chat.container.app import App<PERSON>ontainer
from chat.threads.exception import ConflictData
from chat.utils.common import now
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4, user_header


def is_score_of_pinned_thread(score: int):
    return score > now() * 100


def _try_until_success(fn):
    while True:
        try:
            return fn()
        except ConflictData:
            pass
        sleep(0.1)


def setup_threads_and_folder(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    mysql = container.app().conns.mysql
    pin_usc = usecases.pin_thread
    folder_uscs = container.folder().usecases()
    thread_broker = container.app().brokers.thread_rd

    # create private groups
    p_thread1 = _try_until_success(
        lambda: usecases.create_thread.create_group([TEST_U2], TEST_U1, "Test group")
    )

    p_thread2 = _try_until_success(
        lambda: usecases.create_thread.create_direct(
            TEST_U3,
            TEST_U1,
        )
    )

    p_thread3 = _try_until_success(
        lambda: usecases.create_thread.create_group([TEST_U4], TEST_U1, "Test group 2")
    )

    thread1_id: int = p_thread1.thread.id
    thread2_id: int = p_thread2.thread.id
    thread3_id: int = p_thread3.thread.id

    folder1 = folder_uscs.create(
        TEST_U1, "test_folder", "", [thread1_id, thread2_id], [], 1
    )
    folder1_id = folder1["alias"]

    folder2 = folder_uscs.create(TEST_U1, "test_folder 2", "", [thread3_id], [], 2)
    folder2_id = folder2["alias"]

    # increase thread message counter because
    # zero-messages thread is considered as deleted
    with mysql.get_session() as session:
        repos.thread.incr_thread_msg_counter(session, thread1_id)
        repos.thread.incr_thread_msg_counter(session, thread2_id)
        repos.thread.incr_thread_msg_counter(session, thread3_id)
        session.commit()

    # set thread scores
    thread_broker.set_thread_score(TEST_U1, thread1_id, folder1, 3)
    thread_broker.set_thread_score(TEST_U1, thread2_id, folder1, 2)
    thread_broker.set_thread_score(TEST_U1, thread3_id, folder2, 1)

    return [thread1_id, thread2_id, thread3_id], folder1_id, folder2_id


def test_move_thread_to_other_folder(container: AppContainer, client: TestClient):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)
    thread_uscs.pin_thread.pin(th2, TEST_U1, folder1)

    resp = client.simulate_post(
        "/folders/move",
        headers=user_header(TEST_U1),
        json={"thread_ids": [th2], "user_ids": [], "new_folder": folder2},
    )
    assert resp.status_code == 200

    # check thread score
    old_thread_score = broker.get_thread_score(TEST_U1, th2, folder1)
    assert old_thread_score is None

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(TEST_U1, th2, folder2)
    assert not is_score_of_pinned_thread(thread_score)
    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th2, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos == 0
