import pytest

from chat.constant import FOLDER_DEFAULT, FOLDER_SECRET, FOLDER_SUB_THREAD
from chat.container.app import AppContainer
from chat.exception import PermissionDenied
from chat.messages.model import MessageBody
from chat.utils.common import now
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4
from tests.helpers.message import create_message


def is_score_of_pinned_thread(score: int):
    return score > now() * 100


def setup_threads_and_folder(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    mysql = container.app().conns.mysql
    pin_usc = usecases.pin_thread
    folder_uscs = container.folder().usecases()
    thread_broker = container.app().brokers.thread_rd

    # create private groups
    p_thread1 = usecases.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group"
    )

    p_thread2 = usecases.create_thread.create_direct(
        TEST_U3,
        TEST_U1,
    )

    p_thread3 = usecases.create_thread.create_group(
        [TEST_U4], TEST_U1, "Test group 2"
    )

    thread1_id = p_thread1.thread.id
    thread2_id = p_thread2.thread.id
    thread3_id = p_thread3.thread.id

    folder1 = folder_uscs.create(
        TEST_U1, "test_folder", "", [thread2_id], [], 1
    )
    folder1_id = folder1["alias"]

    folder2 = folder_uscs.create(
        TEST_U1, "test_folder 2", "", [thread3_id], [], 2
    )
    folder2_id = folder2["alias"]

    # increase thread message counter because
    # zero-messages thread is considered as deleted
    with mysql.get_session() as session:
        repos.thread.incr_thread_msg_counter(session, thread1_id)
        repos.thread.incr_thread_msg_counter(session, thread2_id)
        repos.thread.incr_thread_msg_counter(session, thread3_id)
        session.commit()

    # set thread scores
    thread_broker.set_thread_score(TEST_U1, thread1_id, folder1, 3)
    thread_broker.set_thread_score(TEST_U1, thread2_id, folder1, 2)
    thread_broker.set_thread_score(TEST_U1, thread3_id, folder2, 1)

    return [thread1_id, thread2_id, thread3_id], folder1_id, folder2_id


def test_move_thread_to_other_folder(container: AppContainer):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)
    # th1 in default folder, th2 in folder2, th3 in folder2
    thread_uscs.pin_thread.pin(th2, TEST_U1, folder1)

    thread_uscs.setup_thread.move_to_folder(th2, TEST_U1, folder2)

    # check thread score
    old_thread_score = broker.get_thread_score(TEST_U1, th2, folder1)
    assert old_thread_score is None

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(TEST_U1, th2, folder2)
    assert not is_score_of_pinned_thread(thread_score)
    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th2, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos == 0


def test_move_thread_to_default_folder(container: AppContainer):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)
    # th1 in default folder, th2 in folder2, th3 in folder2
    thread_uscs.pin_thread.pin(th2, TEST_U1, folder1)

    thread_uscs.setup_thread.move_to_folder(th2, TEST_U1, FOLDER_DEFAULT)

    # check thread score
    old_thread_score = broker.get_thread_score(TEST_U1, th2, folder1)
    assert old_thread_score is None

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(TEST_U1, th2, FOLDER_DEFAULT)
    assert not is_score_of_pinned_thread(thread_score)
    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th2, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos == 0


def test_move_thread_from_default_folder(container: AppContainer):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)
    # th1 in default folder, th2 in folder2, th3 in folder2
    thread_uscs.pin_thread.pin(th1, TEST_U1, FOLDER_DEFAULT)

    thread_uscs.setup_thread.move_to_folder(th1, TEST_U1, folder1)

    # check thread score
    old_thread_score = broker.get_thread_score(TEST_U1, th1, FOLDER_DEFAULT)
    assert is_score_of_pinned_thread(old_thread_score)

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(TEST_U1, th1, folder1)
    assert not is_score_of_pinned_thread(thread_score)

    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th1, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos != 0


# def test_make_thread_in_default_folder_private(container: AppContainer):
#     app = container.app()
#     session_m = app.conns.mysql
#     thread_uscs = container.thread().usecases()
#     passcode_usc = container.user().usecases()
#     broker = app.brokers.thread_rd

#     # create and pin a thread
#     [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)

#     # create test pass code
#     pass_code = "1234"
#     passcode_usc.create(TEST_U1, pass_code)

#     # make thread private by move it to secret folder
#     with pytest.raises(MissingPasscode):
#         thread_uscs.setup_thread.move_to_folder(th1, TEST_U1, FOLDER_SECRET)
#     thread_uscs.setup_thread.move_to_folder(
#         th1, TEST_U1, FOLDER_SECRET, pass_code
#     )

#     # make sure thread is moved to secret folder
#     thread_score = broker.get_thread_score(TEST_U1, th1, "default")
#     assert thread_score is None

#     p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, th1, pass_code)
#     assert p_thread.participant.folder == FOLDER_SECRET


# def test_make_thread_in_non_default_folder_private(container: AppContainer):
#     app = container.app()
#     session_m = app.conns.mysql
#     thread_uscs = container.thread().usecases()
#     passcode_usc = container.user().usecases()
#     broker = app.brokers.thread_rd

#     # create and pin a thread
#     [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)

#     # create test pass code
#     pass_code = "1234"
#     passcode_usc.create(TEST_U1, pass_code)

#     # move thread 1 to folder1
#     thread_uscs.setup_thread.move_to_folder(th1, TEST_U1, folder1)

#     # make thread private by move it to secret folder
#     with pytest.raises(MissingPasscode):
#         thread_uscs.setup_thread.move_to_folder(th1, TEST_U1, FOLDER_SECRET)
#     thread_uscs.setup_thread.move_to_folder(
#         th1, TEST_U1, FOLDER_SECRET, pass_code
#     )

#     # make sure thread is moved to secret folder
#     thread_score = broker.get_thread_score(TEST_U1, th1, "default")
#     assert thread_score is None
#     thread_score = broker.get_thread_score(TEST_U1, th1, folder1)
#     assert thread_score is None

#     p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, th1, pass_code)
#     assert p_thread.participant.folder == FOLDER_SECRET


def test_make_thread_public(container: AppContainer):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    passcode_usc = container.user().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder1, folder2 = setup_threads_and_folder(container)

    # create test pass code
    pass_code = "1234"
    passcode_usc.create(TEST_U1, pass_code)

    # make thread private by move it to secret folder
    thread_uscs.setup_thread.move_to_folder(
        th1, TEST_U1, FOLDER_SECRET, pass_code
    )

    # make thread public again by moving it to default folder
    thread_uscs.setup_thread.move_to_folder(th1, TEST_U1, FOLDER_DEFAULT)

    # check thread score
    thread_score = broker.get_thread_score(TEST_U1, th1, FOLDER_DEFAULT)
    assert thread_score is not None

    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, th1)
    assert p_thread.participant.folder == FOLDER_DEFAULT


def test_cannot_move_sub_thread_to_other_folder(container: AppContainer):
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    sub_thread = thread_uscs.create_thread.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    sub_thread_id = sub_thread.thread.id

    with pytest.raises(PermissionDenied):
        thread_uscs.setup_thread.move_to_folder(
            sub_thread_id, TEST_U1, FOLDER_DEFAULT
        )
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, sub_thread_id)
    assert p_thread.participant.folder == FOLDER_SUB_THREAD
