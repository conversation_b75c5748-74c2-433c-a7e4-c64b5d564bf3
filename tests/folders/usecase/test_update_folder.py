from typing import cast

from chat.container import AppContainer
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2


def test_update_folder_normal(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.folder().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    repos = container.app().repos
    session_m = container.app().conns.mysql

    # create public group
    p_thread = create_thread_usc.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    thread_ids = [thread_id]
    folder = usecases.create(TEST_U1, "Test folder", "", thread_ids, [])
    folder_alias = folder["alias"]

    folder = usecases.update(TEST_U1, folder_alias, "Test2", "")
    assert folder["name"] == "Test2"
