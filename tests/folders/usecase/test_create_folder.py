from typing import cast

from chat import constant
from chat.container import AppContainer
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_create_folder_normal(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecases = container.folder().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    repos = container.app().repos

    # create public group
    p_thread = create_thread_usc.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    thread_ids = [thread_id]
    # FIXME: BUG: we should check user thread permission  before creating new folder
    folder = usecases.create(TEST_U3, "Test folder", "", thread_ids, [])
    assert folder["name"] == "Test folder"

    msg = broker.messages[-1]
    msg_body = msg["body"]
    assert msg["event_type"] == constant.TASK_MOVE_FOLDER
