from chat.constant import FOLDER_DEFAULT
from chat.container import App<PERSON>ontainer
from chat.utils.common import now
from tests.conftest import TEST_U1, TEST_U2
from chat.participants.model import ParticipantModel


def test_count_thread_member(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    num_active = broker.active_member_count(thread_id)
    assert num_active == 0

    broker.add_members(thread_id, [TEST_U1, TEST_U2])

    num_active = broker.active_member_count(thread_id)
    assert num_active == 2

    broker.remove_members(thread_id, [TEST_U2])
    num_active = broker.active_member_count(thread_id)
    assert num_active == 1


def test_set_thread_score_to_default(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    folder = "test"
    broker.set_thread_score(
        TEST_U1, thread_id, folder, set_default_folder=True
    )

    threads = broker.get_ordered_thread_list(TEST_U1, folder=FOLDER_DEFAULT)
    assert len(threads) == 1

    threads = broker.get_ordered_thread_list(TEST_U1, folder=folder)
    assert len(threads) == 1


def test_update_score(container: AppContainer):
    broker = container.brokers().thread_rd
    th1 = 1
    th2 = 2
    th3 = 3
    folder = "test"

    for thread_id in range(1, 4):
        new_part = ParticipantModel.new_participant(
            user_id=TEST_U1, thread_id=thread_id
        )
        new_part.folder = folder
        broker.set_thread_scores_for_users(thread_id, [new_part], thread_id)

    threads = broker.get_ordered_thread_list(TEST_U1, folder=FOLDER_DEFAULT)
    assert threads == [th3, th2, th1]

    broker.set_thread_scores_for_users(th1, [new_part], 10)
    threads = broker.get_ordered_thread_list(TEST_U1, folder=FOLDER_DEFAULT)
    assert threads == [th1, th3, th2]


def test_remove_thread_score(container: AppContainer):
    broker = container.brokers().thread_rd
    th1 = 1
    th2 = 2
    th3 = 3
    folder = "test"

    for thread_id in range(1, 4):
        new_part = ParticipantModel.new_participant(
            user_id=TEST_U1, thread_id=thread_id
        )
        new_part.folder = folder
        broker.set_thread_scores_for_users(thread_id, [new_part], thread_id)

    threads = broker.get_ordered_thread_list(TEST_U1, folder=FOLDER_DEFAULT)
    assert threads == [th3, th2, th1]

    broker.remove_thread_scores_for_users(th2, [new_part])

    threads = broker.get_ordered_thread_list(TEST_U1, folder=FOLDER_DEFAULT)
    assert threads == [th3, th1]

    threads = broker.get_ordered_thread_list(TEST_U1, folder=folder)
    assert threads == [th3, th1]


def test_get_thread_score(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    folder = "test"

    for thread_id in range(1, 4):
        new_part = ParticipantModel.new_participant(
            user_id=TEST_U1, thread_id=thread_id
        )
        new_part.folder = folder
        broker.set_thread_scores_for_users(thread_id, [new_part], thread_id)

    score = broker.get_thread_score(TEST_U1, thread_id, folder)
    assert score == 3


def test_set_thread_score(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    folder = "test"

    broker.set_thread_score(TEST_U1, thread_id, folder, 3)

    score = broker.get_thread_score(TEST_U1, thread_id, folder)
    assert score == 3

    score = broker.get_thread_score(TEST_U1, thread_id, FOLDER_DEFAULT)
    assert score == 3


def test_del_thread_score(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    folder = "test"

    broker.set_thread_score(TEST_U1, thread_id, folder, 3)

    broker.remove_thread_score(
        TEST_U1, thread_id, folder, remove_from_default=False
    )
    score = broker.get_thread_score(TEST_U1, thread_id, folder)
    assert score is None

    score = broker.get_thread_score(TEST_U1, thread_id, FOLDER_DEFAULT)
    assert score == 3


def test_remove_thread_for_users(container: AppContainer):
    broker = container.brokers().thread_rd
    thread_id = 1
    folder = "test"

    broker.set_thread_score(TEST_U1, thread_id, folder, 3)
    member = ParticipantModel.new_participant(
        user_id=TEST_U1, thread_id=thread_id
    )
    member.folder = folder

    broker.remove_thread_scores_for_users(thread_id, [member])
    score = broker.get_thread_score(TEST_U1, thread_id, folder)
    assert score is None

    score = broker.get_thread_score(TEST_U1, thread_id, FOLDER_DEFAULT)
    assert score is None


def test_pin_limit_per_folder(container: AppContainer):
    broker = container.brokers().thread_rd
    conf = container.app().conf
    max_pins_per_folder = conf.redis_pcc.limit_pin_thread
    folder = "test"

    for i in range(max_pins_per_folder):
        thread_id = i
        broker.pin_thread(TEST_U1, thread_id, now(), folder)

    can_pin_more = broker.can_pin(TEST_U1, folder)
    assert can_pin_more is False

    assert broker.can_pin(TEST_U1, FOLDER_DEFAULT)
    assert broker.can_pin(TEST_U1, "other_folder")

    # try to unpin
    broker.unpin_thread(TEST_U1, thread_id, 1, folder)
    assert broker.can_pin(TEST_U1, folder)


def test_pin_limit_in_default_folder(container: AppContainer):
    broker = container.brokers().thread_rd
    conf = container.app().conf
    max_pins_per_folder = conf.redis_pcc.limit_pin_thread

    for i in range(max_pins_per_folder):
        thread_id = i
        broker.pin_thread(TEST_U1, thread_id, now())

    can_pin_more = broker.can_pin(TEST_U1, FOLDER_DEFAULT)
    assert can_pin_more is False

    assert broker.can_pin(TEST_U1, "other_folder")

    # try to unpin
    broker.unpin_thread(TEST_U1, thread_id, 1)
    assert broker.can_pin(TEST_U1, FOLDER_DEFAULT)


def test_del_pinned_thread(container: AppContainer):
    broker = container.brokers().thread_rd
    conf = container.app().conf
    max_pins_per_folder = conf.redis_pcc.limit_pin_thread
    folder = "test"

    for i in range(max_pins_per_folder):
        thread_id = i
        broker.pin_thread(TEST_U1, thread_id, now(), folder)

    can_pin_more = broker.can_pin(TEST_U1, folder)
    assert can_pin_more is False

    member = ParticipantModel.new_participant(user_id=TEST_U1, thread_id=0)
    member.folder = folder

    broker.remove_thread_scores_for_users(
        0, [member]
    )

    assert broker.can_pin(TEST_U1, folder)


def test_del_pinned_thread_in_default_folder(container: AppContainer):
    broker = container.brokers().thread_rd
    conf = container.app().conf
    max_pins_per_folder = conf.redis_pcc.limit_pin_thread
    folder = "test"

    for i in range(max_pins_per_folder):
        thread_id = i
        broker.pin_thread(TEST_U1, thread_id, now(), FOLDER_DEFAULT)

    can_pin_more = broker.can_pin(TEST_U1, FOLDER_DEFAULT)
    assert can_pin_more is False

    member = ParticipantModel.new_participant(user_id=TEST_U1, thread_id=0)
    member.folder = folder

    broker.remove_thread_scores_for_users(0, [member])

    assert broker.can_pin(TEST_U1, FOLDER_DEFAULT)
