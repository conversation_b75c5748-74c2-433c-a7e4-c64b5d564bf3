from falcon.testing import TestClient

from chat.constant import FOLDER_DEFAULT
from chat.container import AppContainer
from tests.conftest import TEST_U1, user_header
from tests.helpers.thread import setup_threads_and_folder


def test_pin_thread_in_folder(container: AppContainer, client: TestClient):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    result = client.simulate_post(
        f"/threads/{th2}/pin?folder={folder_id}",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th1, th2, th3]


def test_pin_thread_in_without_passing_folder(
    container: AppContainer, client: TestClient
):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    result = client.simulate_post(
        f"/threads/{th2}/pin",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]


def test_pin_thread_in_without_passing_folder2(
    container: AppContainer, client: TestClient
):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    # already pin thread in FOLDER DEFAULT
    usecases.pin_thread.pin(th2, TEST_U1, FOLDER_DEFAULT)

    result = client.simulate_post(
        f"/threads/{th2}/pin",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]


def test_unpin_thread_in_folder(container: AppContainer, client: TestClient):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    usecases.pin_thread.pin(th2, TEST_U1, folder_id)

    result = client.simulate_delete(
        f"/threads/{th2}/pin?folder={folder_id}",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th1, th2, th3]


def test_unpin_thread_without_passing_folder(
    container: AppContainer, client: TestClient
):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    # only pin to folder folder_id
    usecases.pin_thread.pin(th2, TEST_U1, folder_id)

    result = client.simulate_delete(
        f"/threads/{th2}/pin",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]


def test_unpin_thread_without_passing_folder2(
    container: AppContainer, client: TestClient
):
    usecases = container.thread().usecases()
    pt_uscs = container.participant().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    # only pin to folder default
    usecases.pin_thread.pin(th2, TEST_U1, FOLDER_DEFAULT)

    result = client.simulate_delete(
        f"/threads/{th2}/pin",
        headers=user_header(TEST_U1),
    )
    assert result.status_code == 200

    # get list thread in folder_id folder
    # result = client.simulate_get(
    #     f"/threads?include_folders={folder_id}", headers=user_header(TEST_U1)
    # )
    # assert result.status_code == 200
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]

    # # get list in default folder
    # result = client.simulate_get(
    #     f"/threads?include_folders=default", headers=user_header(TEST_U1)
    # )
    # body = result.json["data"]
    # assert len(body) == 3
    # thread_ids = [th["id"] for th in body]
    # assert thread_ids == [th2, th1, th3]
