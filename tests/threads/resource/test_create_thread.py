from dataclasses import dataclass
from typing import Any, Optional

import pytest
from falcon.testing import TestClient

from chat.messages.model import <PERSON><PERSON>ody
from chat.container import AppContainer
from chat.model import make_input
from chat.threads.model.api import SchemaCreate  # type: ignore
from chat.threads.model.api import SchemaGroupCreate  # type: ignore
from tests.conftest import TEST_MEMBER_FILE_ID, TEST_U1, TEST_U2, user_header
from tests.helpers.message import create_message


@dataclass
class TestSchemaInput:
    name: str = ""
    data: Any = None
    error: Optional[str] = None


@dataclass
class TestAPIInput:
    name: str
    body: Any
    status_code: int
    error_code: Optional[str] = None
    error: Optional[str] = None

    def check_response(self, res):
        assert res.status_code == self.status_code
        if self.status_code != 200:
            body = res.json
            if self.error_code:
                assert self.error_code == body["error"]
            if self.error:
                assert self.error == body["error_details"][0]["msg"]


@pytest.mark.parametrize(
    "testcase",
    [
        TestAPIInput(
            "OK - Success case",
            body={"type": "direct", "participant_ids": [TEST_U2]},
            status_code=200,
        ),
        TestAPIInput(
            "OK - Create from imported excel file",
            body={
                "type": "group",
                "participant_ids": [],
                "member_file_id": TEST_MEMBER_FILE_ID,
            },
            status_code=200,
        ),
        TestAPIInput(
            "Fail -  Invalid thread type",
            body={"type": "unknown", "particiant_ids": [TEST_U2]},
            error_code="invalid_parameters",
            error="Type invalid unknown",
            status_code=400,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_create_thread_api(
    container: AppContainer, client: TestClient, testcase: TestAPIInput
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    body = testcase.body
    body["thread_id"] = thread_id
    result = client.simulate_post(
        "/threads",
        headers=user_header(TEST_U1, workspace_id="1"),
        json=body,
    )
    testcase.check_response(result)


def test_create_group_without_workspace_id(
    container: AppContainer, client: TestClient
):
    result = client.simulate_post(
        "/threads",
        headers={"x-gapo-user-id": TEST_U1, "x-gapo-role": "user"},
        json={
            "type": "group",
            "participant_ids": [TEST_U2],
            "name": "Test group",
        },
    )
    assert result.status_code == 400


def test_create_thread_api_with_members_from_other_thread(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        "/threads",
        headers=user_header(TEST_U1, workspace_id="1"),
        json={
            "source_thread_ids": [thread_id],
            "participant_ids": [],
            "type": "group",
            "name": "Test group 2",
        },
    )
    assert result.status_code == 200
    thread = result.json["data"]
    assert thread["name"] == "Test group 2"


def test_create_group_thread_api_with_members_from_other_thread(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        "/threads/group",
        headers=user_header(TEST_U1, workspace_id="1"),
        json={
            "source_thread_ids": [thread_id],
            "participant_ids": [],
            "type": "group",
            "name": "Test group 2",
        },
    )
    assert result.status_code == 200
    thread = result.json["data"]
    assert thread["name"] == "Test group 2"


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK - Group type",
            data={
                "type": "group",
                "creator": 1,
                "role": "user",
                "participant_ids": [2],
                "workspace_id": "1",
            },
        ),
        TestSchemaInput(
            name="OK - Direct type",
            data={
                "type": "direct",
                "creator": 1,
                "role": "user",
                "participant_ids": [2],
                "workspace_id": "",
            },
        ),
        TestSchemaInput(
            name="Fail - Invite too much when create direct",
            data={
                "type": "direct",
                "creator": 1,
                "role": "user",
                "participant_ids": [2, 4],
                "workspace_id": "1",
            },
            error="Invite too much when create direct",
        ),
        TestSchemaInput(
            "Fail - Invalid thread type",
            data={
                "type": "test",
                "creator": 1,
                "role": "user",
                "particpant_ids": [],
                "workspace_id": "",
            },
            error="Type invalid test",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_create_thread_api_input_schema(testcase: TestSchemaInput):
    _, errors = make_input(SchemaCreate, testcase.data)
    if testcase.error:
        assert errors is not None and errors[0]["msg"] == testcase.error
    else:
        assert errors == None


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK - Group type",
            data={
                "creator_id": 1,
                "participant_ids": [2],
                "workspace_id": "1",
            },
        ),
        TestSchemaInput(
            name="Fail workspace",
            data={
                "creator_id": 1,
                "particpant_ids": [],
                "workspace_id": "",
            },
            error="Create group require at least from a source",
        ),
        TestSchemaInput(
            name="Fail workspace",
            data={
                "creator_id": 1,
                "particpant_ids": ["213"],
                "workspace_id": "",
            },
            error="Create group require at least from a source",
        ),
        TestSchemaInput(
            name="OK",
            data={
                "creator_id": 1,
                "workspace_id": "1",
                "source_thread_ids": [123],
            },
        ),
        TestSchemaInput(
            name="OK",
            data={
                "creator_id": 1,
                "workspace_id": "1",
                "orgcs": [
                    {
                        "workspace_id": "123",
                        "title_ids": ["1234", "4567"],
                        "department_ids": ["1234", "4567"],
                    }
                ],
            },
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_create_group_thread_api_input_schema(testcase: TestSchemaInput):
    _, errors = make_input(SchemaGroupCreate, testcase.data)
    if testcase.error:
        assert errors is not None and errors[0]["msg"] == testcase.error
    else:
        assert errors == None


def test_create_sub_thread(container: AppContainer, client: TestClient):
    usecase = container.thread().create_thread()
    pt_uscs = container.participant().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.app().repos.pt
    message_uscs = container.message().usecases()

    p_thread = usecase.create_group([TEST_U1, TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    result = client.simulate_post(
        f"/threads/{thread_id}/messages/{message_id}/sub-threads",
        headers=user_header(TEST_U1, workspace_id="1"),
    )
    assert result.status_code == 200
    thread = result.json["data"]
    assert thread["referenced_message"]
    assert thread["type"] == "subthread"
    assert thread["name"] == "Test group"
