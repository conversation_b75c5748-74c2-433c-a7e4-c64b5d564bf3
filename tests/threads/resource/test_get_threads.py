from falcon.testing import TestClient

from chat.container import AppContainer
from tests.conftest import (  # type: ignore
    TEST_API_KEY,
    TEST_U1,
    TEST_U2,
    service_header,
    user_header,
)


# def test_get_threads_api(container: AppContainer, client: TestClient):
#     app = container.app()
#     thread_usc = container.thread().usecases().create_thread

#     p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test")
#     thread_id = p_thread.thread.id

#     result = client.simulate_get(
#         f"/threads",
#         headers=user_header(TEST_U1),
#     )
#     assert result.status_code == 200
#     body = result.json["data"]
#     assert len(body) == 1
#     assert body[0]["id"] == thread_id


# def test_get_thread_by_collab(container: AppContainer, client: TestClient):
#     app = container.app()
#     thread_usc = container.thread().usecases().create_thread

#     collab_id = "1234123"
#     thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test", collab_id=collab_id)
#     thread_id = thread["id"]

#     result = client.simulate_get(
#         f"/threads/by-collab/{collab_id}",
#         headers=user_header(TEST_U1),
#     )
#     assert result.status_code == 200
#     body = result.json["data"]
#     assert body["id"] == thread_id
#     assert body["collab_id"] == collab_id


def test_get_threads_internal(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test")
    thread_id = p_thread.thread.id

    headers = service_header(TEST_API_KEY)
    headers["x-gapo-user-id"] = TEST_U1
    result = client.simulate_get(
        f"/internal/threads?ids={thread_id}",
        headers=headers,
    )
    assert result.status_code == 404
    # body = result.json["data"]
    # assert len(body) == 1
    # thread_output = body[0]
    # assert thread_output["id"] == thread_id
    # assert thread_output["type"] == "group"
    # assert thread_output["name"] == "Test"


def test_get_threads_by_collabs_internal(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    collab_id = "123452"
    p_thread = thread_usc.create_group(
        [TEST_U2], TEST_U1, "Test", collab_id=collab_id
    )
    thread_id = p_thread.thread.id

    headers = service_header(TEST_API_KEY)
    result = client.simulate_get(
        f"/internal/threads?collab_ids={collab_id}",
        headers=headers,
    )
    assert result.status_code == 404
    # body = result.json["data"]
    # assert len(body) == 1
    # thread_output = body[0]
    # assert thread_output["id"] == thread_id
    # assert thread_output["type"] == "group"
    # assert thread_output["name"] == "Test"
