from falcon.testing import TestClient

from chat.container import AppContainer
from chat.threads.model.api import (  # type: ignore
    MAX_LENGTH_ALIAS,
    MAX_LENGTH_DESCRIPTION,
    MAX_LENGTH_NAME,
    SchemaEdit,
)
from tests.conftest import TEST_U1, TEST_U2, user_header


def test_set_unread_flag(container: AppContainer, client: TestClient):
    app = container.app()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test"
    )
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        f"/threads/{thread_id}/set-unread-flag",
        headers=user_header(TEST_U1),
        json={"unread": True},
    )
    assert result.status_code == 200

    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.participant.mark_unread is True
