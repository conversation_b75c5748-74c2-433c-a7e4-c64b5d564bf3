from falcon.testing import TestClient

from chat.container import AppContainer
from chat.threads.model import ThreadSettings
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, user_header


def test_toggle_strict_moderation(container: AppContainer, client: TestClient):
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    # increase message count, so it can mark as active thread
    resp = client.simulate_post(
        f"/threads/{thread_id}/toggle-add-member",
        headers=user_header(TEST_U1),
    )

    thread = resp.json["data"]
    assert thread["settings"]["only_admin_can_add_member"] == 1

    # verify in database
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.settings.only_admin_can_add_member
