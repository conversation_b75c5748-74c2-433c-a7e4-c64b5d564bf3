from falcon.testing import TestClient

from chat.container import AppContainer
from tests.conftest import TEST_U1, TEST_U2, user_header


def test_leave_thread_api(container: AppContainer, client: TestClient):
    app = container.app()
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test")
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        f"/threads/leavechat",
        headers=user_header(TEST_U1),
        json={"thread_id": thread_id},
    )
    assert result.status_code == 200
