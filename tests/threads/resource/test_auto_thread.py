from falcon.testing import TestClient

from chat.container import AppContainer
from chat.threads.model.api import SchemaAutoJoinOrLeave
from tests.conftest import (  # type: ignore
    TEST_API_KEY,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    service_header,
    user_header,
)


def test_join_threads_internal(container: AppContainer, client: TestClient):
    app = container.app()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id
    headers = service_header(TEST_API_KEY)
    headers["x-gapo-user-id"] = TEST_U1

    # result = client.simulate_get(
    #     f"/internal/participants?thread_ids={thread_id}",
    #     headers=service_header(TEST_API_KEY),
    # )
    # assert result.status_code == 200
    # data = result.json["data"]
    # assert len(data) == 2

    result = client.simulate_post(
        "/internal/threads/auto",
        headers=headers,
        json=SchemaAutoJoinOrLeave(
            auto_type="join",
            participant_ids=[TEST_U3],
            thread_id=thread_id,
            data_source="department",
        ).dict(),
    )
    assert result.status_code == 200

    # result = client.simulate_get(
    #     f"/internal/participants?thread_ids={thread_id}",
    #     headers=service_header(TEST_API_KEY),
    # )
    # assert result.status_code == 200
    # data = result.json["data"]
    # assert len(data) == 3


def test_leave_threads_internal(container: AppContainer, client: TestClient):
    app = container.app()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id
    headers = service_header(TEST_API_KEY)
    headers["x-gapo-user-id"] = TEST_U1

    # result = client.simulate_get(
    #     f"/internal/participants?thread_ids={thread_id}",
    #     headers=service_header(TEST_API_KEY),
    # )
    # assert result.status_code == 200
    # data = result.json["data"]
    # assert len(data) == 3

    result = client.simulate_post(
        "/internal/threads/auto",
        headers=headers,
        json=SchemaAutoJoinOrLeave(
            auto_type="leave",
            participant_ids=[TEST_U3],
            thread_id=thread_id,
            data_source="department",
        ).dict(),
    )
    assert result.status_code == 200

    # result = client.simulate_get(
    #     f"/internal/participants?thread_ids={thread_id}",
    #     headers=service_header(TEST_API_KEY),
    # )
    # assert result.status_code == 200
    # data = result.json["data"]
    # assert len(data) == 2
