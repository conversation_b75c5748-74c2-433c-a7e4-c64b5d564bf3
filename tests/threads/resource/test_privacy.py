from falcon.testing import TestClient

from chat.container import AppContainer
from chat.threads.model import ThreadSettings
from chat.threads.model.api import SchemaPrivacy
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4, user_header


def test_privacy(container: AppContainer, client: TestClient):
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    body = SchemaPrivacy(thread_id=thread_id, action="public")
    # increase message count, so it can mark as active thread
    resp = client.simulate_post(
        "/threads/privacy", headers=user_header(TEST_U1), json=body.dict()
    )

    thread = resp.json["data"]
    assert thread["settings"]["is_public"] == 1

    body = SchemaPrivacy(thread_id=thread_id, action="private")
    # increase message count, so it can mark as active thread
    resp = client.simulate_post(
        "/threads/privacy", headers=user_header(TEST_U1), json=body.dict()
    )

    thread = resp.json["data"]
    assert thread["settings"]["is_public"] == 0


def test_join_link(container: AppContainer, client: TestClient):
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    body = SchemaPrivacy(thread_id=thread_id, action="public")
    # increase message count, so it can mark as active thread
    resp = client.simulate_post(
        "/threads/privacy", headers=user_header(TEST_U1), json=body.dict()
    )

    thread = resp.json["data"]
    assert thread["settings"]["is_public"] == 1

    assert thread["link"] is not None
    link = thread["link"].split("/")[-1]

    resp = client.simulate_get(
        "/threads/joinchat/" + link,
        headers=user_header(None),
    )

    assert resp.status_code == 200

    resp = client.simulate_get(
        "/threads/joinchat/" + link,
        headers=user_header(TEST_U4),
    )

    assert resp.status_code == 200
