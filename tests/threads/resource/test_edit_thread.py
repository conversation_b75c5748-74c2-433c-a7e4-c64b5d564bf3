import pytest
from falcon.testing import TestClient

from chat.container import App<PERSON>ontaine<PERSON>
from chat.i18n import T
from chat.model import make_input
from chat.threads.exception import ThreadNameTooLong
from chat.threads.model.api import (  # type: ignore
    MAX_LENGTH_ALIAS,
    MAX_LENGTH_DESCRIPTION,
    MAX_LENGTH_NAME,
    SchemaEdit,
)
from tests.conftest import TEST_U1, TEST_U2, user_header
from tests.utils import TestAPIInput, TestSchemaInput, check_schema


@pytest.mark.parametrize(
    "testcase",
    [
        TestAPIInput(
            "OK - Success case",
            body={"name": "Test"},
            status_code=200,
        ),
        TestAPIInput(
            "Error - Name too long",
            body={"name": "x" * (MAX_LENGTH_NAME + 1)},
            error_code=ThreadNameTooLong.error,
            message=T(ThreadNameTooLong.i18n_message),
            status_code=400,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_edit_thread_api(
    container: AppContainer, client: TestClient, testcase: TestAPIInput
):
    app = container.app()
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Test")
    thread_id = p_thread.thread.id

    body = testcase.body
    body["thread_id"] = thread_id
    result = client.simulate_patch(
        f"/threads/{thread_id}",
        headers=user_header(TEST_U1),
        json=body,
    )
    testcase.check_response(result)


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK - Valid input",
            data={"name": "Test"},
        ),
        TestSchemaInput(
            name="Fail - Alias too long",
            data={"alias": "x" * (MAX_LENGTH_ALIAS + 1)},
            error="Alias too long",
        ),
        TestSchemaInput(
            "Fail - Description too long",
            data={"description": "x" * (MAX_LENGTH_DESCRIPTION + 1)},
            error="Description too long",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_edit_thread_api_input_schema(testcase: TestSchemaInput):
    data = testcase.data
    data["creator"] = 1
    data["thread_id"] = 1
    data["role"] = "user"
    _, errors = make_input(SchemaEdit, testcase.data)

    if testcase.error:
        assert errors is not None and errors[0]["msg"] == testcase.error
    else:
        assert errors == None


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="Fail - Name too long",
            data={"name": "x" * (MAX_LENGTH_NAME + 1)},
            error=ThreadNameTooLong,
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_threads_invalid_schema(testcase: TestSchemaInput):
    with pytest.raises(ThreadNameTooLong):
        check_schema(SchemaEdit, testcase)
