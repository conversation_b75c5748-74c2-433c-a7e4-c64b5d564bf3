from falcon.testing import Test<PERSON><PERSON>

from chat.container import AppContainer
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, user_header


def is_thread_deleted(thread):
    return thread["delete_to"] == thread["message_count"]


def test_disband_group(container: AppContainer, client: TestClient):
    # create test group
    thread_repo = container.repos().thread
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    session_m = container.app().conns.mysql

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    # add thread to TEST_U2's folder
    folder = folder_uscs.create(TEST_U2, "test_folder", "", [thread_id], [])

    # increase message count, so it can mark as active thread
    with session_m.get_session() as session:
        thread_repo.incr_thread_msg_counter(session, thread_id)
        session.commit()

    # increase message count, so it can mark as active thread
    resp = client.simulate_post(
        f"/threads/{thread_id}/disband",
        headers=user_header(TEST_U2),
    )

    assert resp.status_code == 403
    assert resp.json["error"] == "access_denied"

    # don't need to check 200 case
