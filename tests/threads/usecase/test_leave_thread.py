from typing import Optional

import pytest

from chat import constant
from chat.container import App<PERSON>ontainer
from chat.exception import UserNotInThread
from chat.participants.model import ChatThreadMember
from chat.threads.model import ThreadModel
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.thread import (
    create_sub_thread_of_group,
    setup_threads_and_folder,
)


def test_leave_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    leave_thread_usc = usecases.leave_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    # normal member can't edit group information
    with pytest.raises(UserNotInThread):
        leave_thread_usc.leave_group(thread_id, TEST_U3)

    leave_thread_usc.leave_group(thread_id, TEST_U2)

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    with leave_thread_usc.session_m.session() as session:
        thread_member = repos.pt.get_member(session, thread_id, TEST_U2)
        assert thread_member is None


def test_leave_thread_with_clear_msg(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    leave_thread_usc = usecases.leave_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    # normal member can't edit group information
    with pytest.raises(UserNotInThread):
        leave_thread_usc.leave_group(thread_id, TEST_U3)

    leave_thread_usc.leave_group(thread_id, TEST_U2, clear_msg=True)

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    with leave_thread_usc.session_m.session() as session:
        thread_member = repos.pt.get_member(session, thread_id, TEST_U2)
        assert thread_member is None
        thread = repos.thread.get_by_id(session, thread_id)
        assert thread is not None


def test_leave_pinned_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    session_m = container.app().conns.mysql
    leave_thread_usc = usecases.leave_thread

    [th1, _, __], folder_id = setup_threads_and_folder(container)

    # pin thread in folder_id and default folder
    usecases.pin_thread.pin(th1, TEST_U1, folder_id)
    usecases.pin_thread.pin(th1, TEST_U1, constant.FOLDER_DEFAULT)

    # normal member can't edit group information
    leave_thread_usc.leave_group(th1, TEST_U1)

    with session_m.get_session() as session:
        # check thread member info
        result = session.execute(
            "select * from participant_threads where thread_id=:thread_id and user_id=:user_id",
            {"thread_id": th1, "user_id": TEST_U1},
        ).fetchone()
        result: Optional[ChatThreadMember] = dict(result) if result else None  # type: ignore
        assert result is not None
        assert result["pin_default_pos"] == 0
        assert result["pin_pos"] == 0


def test_cannot_leave_sub_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    with pytest.raises(UserNotInThread):
        usecases.leave_thread.leave_group(sub_thread_id, TEST_U1)

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
