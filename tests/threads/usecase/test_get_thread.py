import pytest

from chat.container import AppContainer
from chat.exception import DataNotFound, UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_get_all(container: AppContainer):
    usecase = container.participant().usecases().get_participant
    threads = usecase.get_all_threads_in_folders(TEST_U1, ["test"])
    assert len(threads) == 0


def test_get_by_id(container: AppContainer):
    usecase = container.thread().usecases().get_thread
    create_usc = container.thread().usecases().create_thread

    with pytest.raises(UserNotInThread):
        thread = usecase.get_by_id(TEST_U1, 1)

    p_thread = create_usc.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Group test"
    )

    thread = usecase.get_by_id(TEST_U1, p_thread.thread.id)
    assert thread is not None

    with pytest.raises(UserNotInThread):
        usecase.get_by_id(TEST_U3, p_thread.thread.id)


def test_get_by_collab_id(container: AppContainer):
    usecase = container.thread().usecases().get_thread
    create_usc = container.thread().usecases().create_thread

    collab_id = "1234123"
    p_thread = create_usc.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Group test", collab_id=collab_id
    )
    thread_id = p_thread.thread.id

    with pytest.raises(DataNotFound):
        usecase.get_thread_id_by_collab("notfound", TEST_U1)

    with pytest.raises(UserNotInThread):
        usecase.get_thread_id_by_collab(collab_id, TEST_U3)

    p_thread = usecase.get_thread_id_by_collab(collab_id, TEST_U1)
    assert p_thread.thread.collab_id == collab_id
    assert p_thread.thread.id == thread_id


def test_get_public_info_by_collab_ids(container: AppContainer):
    usecase = container.thread().usecases().get_thread
    create_usc = container.thread().usecases().create_thread

    collab_id = "1234123"
    p_thread = create_usc.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Group test", collab_id=collab_id
    )
    thread_id = p_thread.thread.id
