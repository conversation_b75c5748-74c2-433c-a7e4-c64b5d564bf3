import pytest

from chat import constant
from chat.constant import (
    FOLDER_DEFAULT,
    FOLDER_NOT_FRIEND,
    FOLDER_SECRET,
    FOLDER_UNREAD,
)
from chat.messages.model import MessageBody
from chat.container import AppContainer
from chat.exception import PermissionDenied, UserNotInThread
from chat.threads.exception import PinInProtectedFolderError, MaximumPin
from chat.utils.common import now
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.message import create_message
from tests.helpers.thread import (
    create_sub_thread_of_group,
    setup_threads_and_folder,
)
from chat.threads.model import ThreadModel
from chat.participants.model import ParticipantModel


def is_score_of_pinned_thread(score: int):
    return score > now() * 100


def test_max_pin_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    conf = container.app().conf
    pin_usc = usecases.pin_thread

    limit_pin_thread = conf.redis_pcc.limit_pin_thread
    more_pin = limit_pin_thread + 1

    # create private group
    for i in range(more_pin):
        p_thread = usecases.create_thread.create_group(
            [TEST_U1, TEST_U2],
            TEST_U1,
            "Test group",
        )
        with pin_usc.session_m.session() as session:
            repos.thread.update_msg_count(session, p_thread.thread.id, 2)
            session.commit()

        if i == limit_pin_thread:
            with pytest.raises(MaximumPin):
                pin_usc.pin(p_thread.thread.id, TEST_U1)
        else:
            pin_usc.pin(p_thread.thread.id, TEST_U1)


def test_auto_fix_pin_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    pin_usc = usecases.pin_thread
    conf = container.app().conf

    limit_pin_thread = conf.redis_pcc.limit_pin_thread
    more_pin = limit_pin_thread + 2
    should_pin = limit_pin_thread // 2
    should_not_pin = limit_pin_thread - should_pin

    # create private group
    for i in range(more_pin):
        p_thread = usecases.create_thread.create_group(
            [TEST_U1, TEST_U3],
            TEST_U1,
            "Test group",
        )
        with pin_usc.session_m.session() as session:
            repos.thread.update_msg_count(session, p_thread.thread.id, 2)
            session.commit()
        if i < should_pin:
            pin_usc.pin(p_thread.thread.id, TEST_U3)
        elif i <= should_not_pin:
            pin_usc.thread_broker.pin_thread(
                TEST_U3, p_thread.thread.id, now()
            )

        if i == more_pin - 2:
            with pytest.raises(MaximumPin):
                pin_usc.pin(p_thread.thread.id, TEST_U3)
        elif i == more_pin - 1:
            pin_usc.pin(p_thread.thread.id, TEST_U3)


def test_pin_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    pin_usc = usecases.pin_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    with pin_usc.session_m.session() as session:
        repos.thread.update_msg_count(session, thread_id, 2)
        session.commit()

    # normal member can't edit group information
    with pytest.raises(UserNotInThread):
        pin_usc.pin(thread_id, TEST_U3)

    pin_usc.pin(thread_id, TEST_U1)

    with pin_usc.session_m.session() as session:
        thread_o = repos.thread.get_by_id(session, thread_id)
        thread_co = repos.thread_cache.get(thread_id)

        assert thread_o == thread_co

        part_o = repos.pt.get_exist_record(session, thread_id, TEST_U1)

        part_c = repos.pt.cache.get(thread_id, TEST_U1)

        assert part_c == part_o


def test_unpin_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    pin_usc = usecases.pin_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    with pin_usc.session_m.session() as session:
        repos.thread.update_msg_count(session, thread_id, 2)
        session.commit()

    # pin thread
    pin_usc.pin(thread_id, TEST_U1)

    # normal member can't edit group information
    with pytest.raises(UserNotInThread):
        pin_usc.unpin(thread_id, TEST_U3)


# TODO: Pin secret folder and stranger folder


def test_pin_thread_in_folder(container: AppContainer):
    usecases = container.participant().usecases()
    repos = container.app().repos
    mysql = container.app().conns.mysql
    pin_usc = container.thread().usecases().pin_thread
    folder_uscs = container.folder().usecases()

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    pin_usc.pin(th2, TEST_U1, folder_id)

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert set(thread_ids) == set([th2, th1, th3])


def test_pin_thread_in_wrong_folder(container: AppContainer):
    usecases = container.thread().usecases()
    pin_usc = usecases.pin_thread

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)

    with pytest.raises(PermissionDenied):
        pin_usc.pin(th2, TEST_U1, "unknown")


def test_pin_thread_in_protected_folders(container: AppContainer):
    usecases = container.thread().usecases()
    pin_usc = usecases.pin_thread

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)

    protected_folders = [FOLDER_UNREAD, FOLDER_NOT_FRIEND, FOLDER_SECRET]
    for folder in protected_folders:
        with pytest.raises(PinInProtectedFolderError):
            pin_usc.pin(th2, TEST_U1, folder)


def test_pin_thread_in_default_folder(container: AppContainer):

    usecases = container.participant().usecases()
    pin_usc = container.thread().usecases().pin_thread

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert thread_ids == []

    # pin in default folder
    pin_usc.pin(th2, TEST_U1, FOLDER_DEFAULT)

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert thread_ids == []

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    # pin in folder_id folder

    pin_usc.pin(th3, TEST_U1, folder_id)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert set(thread_ids) == set([th3, th1, th2])

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert thread_ids == []


def test_unpin_thread_that_is_unpinned(container: AppContainer):
    usecases = container.participant().usecases()
    pin_usc = container.thread().usecases().pin_thread

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    pin_usc.pin(th2, TEST_U1, folder_id)

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert set(thread_ids) == set([th2, th1, th3])

    pin_usc.unpin(th1, TEST_U1, folder_id)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert set(thread_ids) == set([th2, th1, th3])


def test_unpin_thread_in_folder(container: AppContainer):
    usecases = container.participant().usecases()
    pin_usc = container.thread().usecases().pin_thread
    repos = container.app().repos

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert thread_ids == [th1, th2, th3]

    pin_usc.pin(th2, TEST_U1, folder_id)

    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    # thread_scores {th2: +inf, th1: 3, th3: 1}
    assert set(thread_ids) == set([th2, th1, th3])

    pin_usc.unpin(th2, TEST_U1, folder_id)
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    # thread_scores: {th2: 0, th1: 3, th3: 1}
    assert set(thread_ids) == set([th1, th3, th2])

    # check in default folder
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert thread_ids == []

    with pin_usc.session_m.session() as session:
        thread_o = repos.thread.get_by_id(session, th2)
        thread_co = repos.thread_cache.get(th2)

        assert thread_o == thread_co

        part_o = repos.pt.get_exist_record(session, th2, TEST_U1)
        part_c = repos.pt.cache.get(th2, TEST_U1)

        assert part_c == part_o


def test_unpin_thread_in_wrong_folder(container: AppContainer):
    usecases = container.thread().usecases()
    pin_usc = usecases.pin_thread

    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    pin_usc.pin(th2, TEST_U1, folder_id)

    with pytest.raises(PermissionDenied):
        pin_usc.unpin(th2, TEST_U1, "unknown")


def test_thread_pin_status_when_user_delete_message(container: AppContainer):
    app = container.app()
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    # create and pin a thread
    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_uscs.pin_thread.pin(th2, TEST_U1, folder_id)

    # check thread score
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert thread_ids == []

    # check thread score
    thread_ids = usecases.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert set(thread_ids) == set([th2, th1, th3])

    thread_id = th2

    # create message
    msg_id = 2
    msg_id0 = 1
    body = MessageBody(**{"type": "text", "text": "hello"})
    create_message(container.app(), thread_id, msg_id0, TEST_U1, body)
    create_message(
        container.app(), thread_id, msg_id, TEST_U1, body, msg_count=2
    )

    # usecases.delete_message.delete_item(
    #     TEST_U1, thread_id, msg_id, level=constant.ONE_SIDE
    # )

    # make sure thread 2 is stilled pin folder
    broker = container.brokers().thread_rd

    thread_score = broker.get_thread_score(TEST_U1, thread_id, folder_id)
    assert is_score_of_pinned_thread(thread_score)

    thread_score = broker.get_thread_score(
        TEST_U1, thread_id, constant.FOLDER_DEFAULT
    )
    assert not is_score_of_pinned_thread(thread_score)


def test_move_pinned_thread_to_other_folder(container: AppContainer):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_uscs.pin_thread.pin(th2, TEST_U1, folder_id)

    thread_uscs.setup_thread.move_to_folder(th2, TEST_U1, FOLDER_DEFAULT)

    # check thread score
    old_thread_score = broker.get_thread_score(TEST_U1, th2, folder_id)
    assert old_thread_score is None

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(
        TEST_U1, th2, constant.FOLDER_DEFAULT
    )
    assert not is_score_of_pinned_thread(thread_score)

    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th2, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos == 0


def test_move_pinned_thread_in_default_folder_to_other_folder(
    container: AppContainer,
):
    app = container.app()
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    broker = app.brokers.thread_rd

    # create and pin a thread
    [th1, th2, th3], folder_id = setup_threads_and_folder(container)
    thread_uscs.pin_thread.pin(th2, TEST_U1, FOLDER_DEFAULT)

    thread_uscs.setup_thread.move_to_folder(th2, TEST_U1, folder_id)

    # thread is unpinned when moved to new folder
    thread_score = broker.get_thread_score(TEST_U1, th2, folder_id)
    assert not is_score_of_pinned_thread(thread_score)

    # thread is still pinned in default folder
    assert is_score_of_pinned_thread(
        broker.get_thread_score(TEST_U1, th2, FOLDER_DEFAULT)
    )

    with session_m.get_session() as session:
        part = app.repos.pt.get_member(session, th2, TEST_U1)
        assert part is not None
        assert part.pin_pos == 0
        assert part.pin_default_pos != 0


def test_pin_sub_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    pin_usc = usecases.pin_thread

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    with pin_usc.session_m.session() as session:
        repos.thread.update_msg_count(session, sub_thread_id, 2)
        session.commit()

    # cannot pin sub thread
    pin_usc.pin(sub_thread_id, TEST_U1)
