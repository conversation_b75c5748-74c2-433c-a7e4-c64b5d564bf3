from typing import cast

import pytest

from chat import constant
from chat.connections.gapo_client.mock import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from chat.container import App<PERSON>ontaine<PERSON>
from chat.exception import PermissionDenied, UserNotInThread
from chat.threads.exception import ThreadNotFoundByLink
from chat.utils.common import get_link_code_from_link
from tests.conftest import TEST_ROLE, TEST_U1, TEST_U2, TEST_U3, TEST_WORKSPACE
from tests.helpers.thread import create_sub_thread_of_group


def is_thread_deleted(thread):
    return thread["delete_to"] == thread["message_count"]


def test_disband_group(container: AppContainer):
    # create test group
    thread_repo = container.repos().thread
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    folder_uscs = container.folder().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.repos().pt
    repos = container.app().repos

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    # add thread to TEST_U2's folder
    folder = folder_uscs.create(TEST_U2, "test_folder", "", [thread_id], [])
    folder_id = folder["alias"]

    # increase message count, so it can mark as active thread
    with session_m.get_session() as session:
        thread_repo.incr_thread_msg_counter(session, thread_id)
        session.commit()

    with pytest.raises(PermissionDenied):
        thread_uscs.disband_group.disband_group(TEST_U2, thread_id)

    thread_uscs.disband_group.disband_group(TEST_U1, thread_id)

    for user_id in [TEST_U1, TEST_U2, TEST_U3]:
        threads = pt_uscs.get_participant.get_all_threads_in_folders(
            user_id, [constant.FOLDER_DEFAULT]
        )
        assert len(threads) == 0

        with session_m.get_session() as session:
            member = pt_repo.get_group_for_member(session, thread_id, user_id)
            assert member is None

        member = repos.part_cache.get(thread_id, user_id)
        assert member is None
        # make sure we also set delete_to = 0

    # check folder folder_id
    threads = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U2, [folder_id]
    )
    assert len(threads) == 0


def test_disband_associate_group(container: AppContainer):
    app = container.app()
    pt_repo = app.repos.pt
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    associate_link = "http://link"
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test associate group",
        associate_link,
        group_level=constant.GROUP_LEVEL_PUBLIC,
    )

    link_code = get_link_code_from_link(p_thread.thread.link)
    thread_id = p_thread.thread.id

    thread_uscs.join_thread.join_by_link(TEST_U2, link_code)
    thread_uscs.join_thread.join_by_link(TEST_U3, link_code)

    thread_uscs.disband_group.disband_group(TEST_U1, thread_id)

    for user_id in [TEST_U1, TEST_U2, TEST_U3]:
        threads = pt_uscs.get_participant.get_all_threads_in_folders(
            user_id, [constant.FOLDER_DEFAULT]
        )
        assert len(threads) == 0

        with session_m.get_session() as session:
            member = pt_repo.get_group_for_member(session, thread_id, user_id)
            assert member is None

    with pytest.raises(ThreadNotFoundByLink):
        thread_uscs.join_thread.join_by_link(TEST_U1, link_code)

    # recreate associate group
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test associate group",
        associate_link,
        group_level=constant.GROUP_LEVEL_PUBLIC,
    )

    link_code = get_link_code_from_link(p_thread.thread.link)
    thread_id = p_thread.thread.id
    thread_uscs.join_thread.join_by_link(TEST_U2, link_code)
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2


def test_disband_group_associated_with_department(container: AppContainer):
    app = container.app()
    pt_repo = app.repos.pt
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    gapo_client = cast(GapoClientMock, app.repos.gapo)

    # thread associated with department has format department:xxxx
    associate_link = "department:d275e30fba344ab8a91419af6db5bb0d"
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test department",
        associate_link,
    )
    thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 1

    thread_uscs.disband_group.disband_group(TEST_U1, thread_id)
    # make sure we unlink from department
    assert gapo_client.orgc.unlink_thread_in_department_calls > 0

    for user_id in [TEST_U1, TEST_U2, TEST_U3]:
        threads = pt_uscs.get_participant.get_all_threads_in_folders(
            user_id, [constant.FOLDER_DEFAULT]
        )
        assert len(threads) == 0

        with session_m.get_session() as session:
            member = pt_repo.get_group_for_member(session, thread_id, user_id)
            assert member is None

    # makesure associate_link is set to ""
    with session_m.get_session() as session:
        th = dict(
            session.execute(
                "select * from threads where id=:thread_id",
                {"thread_id": thread_id},
            ).fetchone()
        )
        assert th["associate_link"] == ""

    # recreate orgc group
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test department",
        associate_link,
    )
    new_thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members(
        [new_thread_id], ["owner", "member", "admin"]
    )
    assert len(members) == 1


def test_disband_group_associated_with_group(container: AppContainer):
    app = container.app()
    pt_repo = app.repos.pt
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    gapo_client = cast(GapoClientMock, app.repos.gapo)

    # thread associated with department has format department:xxxx
    associate_link = "group:d275e30fba344ab8a91419af6db5bb0d"
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test group",
        associate_link,
    )
    thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 1

    thread_uscs.disband_group.disband_group(TEST_U1, thread_id)
    # make sure we unlink from department
    assert gapo_client.group.unlink_thread_from_group_calls > 0

    for user_id in [TEST_U1, TEST_U2, TEST_U3]:
        threads = pt_uscs.get_participant.get_all_threads_in_folders(
            user_id, [constant.FOLDER_DEFAULT]
        )
        assert len(threads) == 0

        with session_m.get_session() as session:
            member = pt_repo.get_group_for_member(session, thread_id, user_id)
            assert member is None

    # makesure associate_link is set to ""
    with session_m.get_session() as session:
        th = dict(
            session.execute(
                "select * from threads where id=:thread_id",
                {"thread_id": thread_id},
            ).fetchone()
        )
        assert th["associate_link"] == ""

    # recreate orgc group
    p_thread = thread_uscs.create_thread.create_associate_group(
        TEST_U1,
        "Test group",
        associate_link,
    )
    new_thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members([new_thread_id])
    assert len(members) == 1


def test_disband_orgc_group_by_role(container: AppContainer):
    app = container.app()
    pt_repo = app.repos.pt
    session_m = app.conns.mysql
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    p_thread = thread_uscs.create_thread.create_group_orgc(
        {TEST_WORKSPACE: [TEST_ROLE]},
        constant.ORGC_GROUP_BY_ROLE,
        TEST_U1,
        "Test orgc",
        group_level=constant.GROUP_LEVEL_PRIVATE,
        group_type="group",
    )
    thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2

    thread_uscs.disband_group.disband_group(TEST_U1, thread_id)

    for user_id in [TEST_U1, TEST_U2, TEST_U3]:
        threads = pt_uscs.get_participant.get_all_threads_in_folders(
            user_id, [constant.FOLDER_DEFAULT]
        )
        assert len(threads) == 0

        with session_m.get_session() as session:
            member = pt_repo.get_group_for_member(session, thread_id, user_id)
            assert member is None

    # recreate orgc group
    p_thread = thread_uscs.create_thread.create_group_orgc(
        {TEST_WORKSPACE: [TEST_ROLE]},
        constant.ORGC_GROUP_BY_ROLE,
        TEST_U1,
        "Test orgc",
        group_level=constant.GROUP_LEVEL_PRIVATE,
        group_type="group",
    )
    new_thread_id = p_thread.thread.id
    members = pt_uscs.get_participant.get_all_members(
        [new_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2


def test_cannot_disband_sub_thread(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    pin_usc = usecases.pin_thread
    session_m = usecases.create_thread.session_m

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    with session_m.session() as session:
        repos.thread.update_msg_count(session, sub_thread_id, 1)
        session.commit()

    # TODO: raise PermissionDenied instead?
    with pytest.raises(UserNotInThread):
        usecases.disband_group.disband_group(TEST_U1, sub_thread_id)
