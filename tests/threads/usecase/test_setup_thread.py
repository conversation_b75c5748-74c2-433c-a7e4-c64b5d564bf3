import pytest
from typing import cast

from chat import constant
from chat.container import AppContainer
from chat.exception import PermissionDenied, UserNotInThread, SendMessageForbidden
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.participants import exception as PartError

from chat.messages.model import MessageBody

from chat.threads.model import ThreadSettings
from tests.helpers.thread import create_sub_thread_of_group

from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4


def test_make_public(container: AppContainer):
    usecases = container.thread().usecases()
    # repos = container.app().repos
    edit_usc = usecases.setup_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )

    thread_id = p_thread.thread.id

    # normal member can't edit group information
    with pytest.raises(PermissionDenied):
        edit_usc.make_public(thread_id, TEST_U2)

    with pytest.raises(UserNotInThread):
        edit_usc.make_public(thread_id, TEST_U3)

    p_updated = edit_usc.make_public(thread_id, TEST_U1)
    assert p_updated.thread.settings.is_public
    assert not p_updated.thread.settings.only_admin_can_add_member


def test_make_private(container: AppContainer):
    usecases = container.thread().usecases()
    # repos = container.app().repos
    edit_usc = usecases.setup_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group", group_level=1
    )
    assert p_thread.thread.group_level == 1
    thread_id = p_thread.thread.id

    # normal member can't edit group information
    with pytest.raises(PermissionDenied):
        edit_usc.make_private(thread_id, TEST_U2)

    with pytest.raises(UserNotInThread):
        edit_usc.make_private(thread_id, TEST_U3)

    p_updated = edit_usc.make_private(thread_id, TEST_U1)
    assert p_updated.thread.group_level == 0


def test_toggle_only_admin_can_add_member_setting_in_private_group(
    container: AppContainer,
):
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    participant_uscs.add_participant.add_members(TEST_U2, thread_id, [TEST_U3])

    with pytest.raises(PermissionDenied):
        thread_uscs.setup_thread.toggle_only_admin_can_add_member_setting(
            thread_id, TEST_U2
        )

    # change setting
    p_thread = (
        thread_uscs.setup_thread.toggle_only_admin_can_add_member_setting(
            thread_id, TEST_U1
        )
    )
    settings = p_thread.thread.settings
    assert settings.only_admin_can_add_member == 1

    # verify that normal user can't add user anymore
    with pytest.raises(PartError.AddMemberPermissionError):
        participant_uscs.add_participant.add_members(
            TEST_U2, thread_id, [TEST_U4]
        )

    # admin can add member as normal
    participant_uscs.add_participant.add_members(TEST_U1, thread_id, [TEST_U4])


def test_toggle_only_admin_can_add_member_setting_in_public_group(
    container: AppContainer,
):
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PUBLIC,
    )
    assert p_thread.thread.group_level == 1

    thread_id = p_thread.thread.id
    join_link = p_thread.thread.link

    participant_uscs.add_participant.add_members(TEST_U2, thread_id, [TEST_U3])

    with pytest.raises(PermissionDenied):
        thread_uscs.setup_thread.toggle_only_admin_can_add_member_setting(
            thread_id, TEST_U2
        )

    # change setting
    p_thread = (
        thread_uscs.setup_thread.toggle_only_admin_can_add_member_setting(
            thread_id, TEST_U1
        )
    )
    settings = p_thread.thread.settings
    assert not settings.is_public
    assert settings.only_admin_can_add_member

    # make sure join link is updated
    assert p_thread.thread.link != join_link

    # verify that normal user can't add user anymore
    with pytest.raises(PartError.AddMemberPermissionError):
        participant_uscs.add_participant.add_members(
            TEST_U2, thread_id, [TEST_U4]
        )

    # admin can add member as normal
    participant_uscs.add_participant.add_members(TEST_U1, thread_id, [TEST_U4])


def test_toggle_only_admin_can_update_info(container: AppContainer):
    thread_uscs = container.thread().usecases()
    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PUBLIC,
    )
    assert p_thread.thread.group_level == 1
    thread_id = p_thread.thread.id

    with pytest.raises(PermissionDenied):
        thread_uscs.setup_thread.toggle_only_admin_can_update_info_setting(
            thread_id, TEST_U2
        )

    # change setting
    p_thread = (
        thread_uscs.setup_thread.toggle_only_admin_can_update_info_setting(
            thread_id, TEST_U1
        )
    )
    settings = p_thread.thread.settings
    assert settings.only_admin_can_update_info

    # verify that normal user can't add user anymore
    with pytest.raises(PermissionDenied):
        thread_uscs.edit_thread.edit_group(
            thread_id, TEST_U2, {"name": "New name"}
        )

    # admin can update group
    thread_uscs.edit_thread.edit_group(
        thread_id, TEST_U1, {"name": "New name"}
    )


def test_toggle_notify_group(
    container: AppContainer,
):
    thread_uscs = container.thread().usecases()
    repos = container.app().repos

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )
    assert p_thread.thread.group_level == 0

    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.enable_notify == 1

    p_thread = thread_uscs.setup_thread.toggle_notify(thread_id, TEST_U1)
    assert p_thread.participant.enable_notify == 0

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.enable_notify == 0


def test_toggle_mute_all(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )
    assert p_thread.thread.group_level == 0

    thread_id = p_thread.thread.id

    _ = thread_uscs.setup_thread.toggle_mute_all(thread_id, TEST_U1)
    p_thread = thread_uscs.setup_thread.toggle_mute_all(thread_id, TEST_U1)

    m2 = broker.messages.pop()
    m1 = broker.messages.pop()

    assert m1["event_type"] == "create_message"
    assert m1["body"]["body"]["metadata"]["action_note_type"] == 12
    assert m1["body"]["thread"]["settings"]["disable_member_send_message"] == 1

    assert m2["event_type"] == "create_message"
    assert m2["body"]["body"]["metadata"]["action_note_type"] == 13
    assert m2["body"]["thread"]["settings"]["disable_member_send_message"] == 0


def test_toggle_mute_all_a_subthread(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()

    # create private group

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container,
        "Test",
        TEST_U1,
        [TEST_U1, TEST_U2],
        sub_thread_member_ids=[TEST_U2],
    )

    _ = thread_uscs.setup_thread.toggle_mute_all(sub_thread_id, TEST_U1)
    _ = thread_uscs.setup_thread.toggle_mute_all(sub_thread_id, TEST_U1)

    m2 = broker.messages.pop()
    m1 = broker.messages.pop()

    assert m1["event_type"] == "create_message"
    assert m1["body"]["body"]["metadata"]["action_note_type"] == 12
    assert m1["body"]["thread"]["settings"]["disable_member_send_message"] == 1

    assert m2["event_type"] == "create_message"
    assert m2["body"]["body"]["metadata"]["action_note_type"] == 13
    assert m2["body"]["thread"]["settings"]["disable_member_send_message"] == 0


def test_toggle_mute_all_subthread(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()
    usecases = container.message().usecases()

    # create private group
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container,
        "Test",
        TEST_U1,
        [TEST_U1, TEST_U2],
        sub_thread_member_ids=[TEST_U1],
    )

    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    _ = thread_uscs.setup_thread.toggle_mute_all(thread_id, TEST_U1)
    with pytest.raises(SendMessageForbidden):
        body = MessageBody(**{"type": "text", "text": "Hello"})
        usecases.create_message._create(TEST_U2, sub_thread_id, body, "3")

    _ = thread_uscs.setup_thread.toggle_mute_all_subthread(
        thread_id, TEST_U1
    )
    # body = MessageBody(**{"type": "text", "text": "Hello"})
    # usecases.create_message._create(TEST_U2, sub_thread_id, body, "3")


    _ = thread_uscs.setup_thread.toggle_mute_all_subthread(
        thread_id, TEST_U1
    )
    with pytest.raises(SendMessageForbidden):
        body = MessageBody(**{"type": "text", "text": "Hello"})
        usecases.create_message._create(TEST_U2, sub_thread_id, body, "3")

    _ = thread_uscs.setup_thread.toggle_mute_all(thread_id, TEST_U1)
    with pytest.raises(PermissionDenied):
        _ = thread_uscs.setup_thread.toggle_mute_all_subthread(
            thread_id, TEST_U1
        )

    m2 = broker.messages.pop()
    m1 = broker.messages.pop()

    assert m1["event_type"] == "create_message"
    assert m1["body"]["body"]["metadata"]["action_note_type"] == 12
    assert m1["body"]["thread"]["settings"]["disable_member_send_message"] == 1

    assert m2["event_type"] == "create_message"
    assert m2["body"]["body"]["metadata"]["action_note_type"] == 13
    assert m2["body"]["thread"]["settings"]["disable_member_send_message"] == 0


def test_set_auto_delete_message(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()
    msg_uscs = container.message().usecases()

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )
    assert p_thread.thread.group_level == 0

    thread_id = p_thread.thread.id

    for day_num in (1, 3, 7, 30):
        p_thread = thread_uscs.setup_thread.set_auto_delete_message(
            thread_id, TEST_U1, day_num
        )
        settings = p_thread.thread.settings
        assert settings.delete_msg_after_days == day_num
        while broker.messages:
            m = broker.messages.pop()
            if m["event_type"] in ("thread_created", "create_message"):
                if m["event_type"] == "create_message":
                    assert m["body"]["body"]["type"] == "action_note"
                    assert m["body"]["body"]["metadata"][
                        "action_note_type"
                    ] in (
                        0,
                        1,
                        10,
                    )
                    if m["body"]["body"]["metadata"]["action_note_type"] == 11:
                        assert m["body"]["will_deleted_at"] == 0

        body = MessageBody(**{"type": "text", "text": "Hello"})
        msg_uscs.create_message._create(
            TEST_U1, thread_id, body, api_version=3
        )
        while broker.messages:
            m = broker.messages.pop()
            assert m["event_type"] == "create_message"
            assert m["body"]["will_deleted_at"] > 0


def test_disable_auto_delete_message(
    container: AppContainer,
):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()
    msg_uscs = container.message().usecases()

    # create private group
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=constant.GROUP_LEVEL_PRIVATE,
    )
    assert p_thread.thread.group_level == 0

    thread_id = p_thread.thread.id

    for day_num in (0,):
        p_thread = thread_uscs.setup_thread.set_auto_delete_message(
            thread_id, TEST_U1, day_num
        )
        settings = p_thread.thread.settings
        assert settings.delete_msg_after_days == day_num
        while broker.messages:
            m = broker.messages.pop()
            if m["event_type"] in ("thread_created", "create_message"):
                if m["event_type"] == "create_message":
                    assert m["body"]["body"]["type"] == "action_note"
                    assert m["body"]["body"]["metadata"][
                        "action_note_type"
                    ] in (
                        11,
                        1,
                        0,
                    )
                    if m["body"]["body"]["metadata"]["action_note_type"] == 11:
                        assert m["body"]["will_deleted_at"] == 0
        body = MessageBody(**{"type": "text", "text": "Hello"})
        msg_uscs.create_message._create(
            TEST_U1, thread_id, body, api_version=3
        )
        while broker.messages:
            m = broker.messages.pop()
            assert m["event_type"] == "create_message"
            assert m["body"]["will_deleted_at"] == 0
