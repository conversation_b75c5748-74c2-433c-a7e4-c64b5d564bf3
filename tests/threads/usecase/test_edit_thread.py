import pytest

from chat.constant import GROUP_LEVEL_PRIVATE
from chat.container import AppContainer
from chat.exception import PermissionDenied, UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.thread import create_sub_thread_of_group

from chat.threads.model import ThreadSettings, ThreadModel


def test_edit_public_group(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    edit_usc = usecases.edit_thread

    # create public group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    new_data = {"name": "New group"}

    # normal member can't edit group information
    with pytest.raises(PermissionDenied):
        edit_usc.edit_group(thread_id, TEST_U2, new_data)

    with pytest.raises(UserNotInThread):
        edit_usc.edit_group(thread_id, TEST_U3, new_data)

    updated = edit_usc.edit_group(thread_id, TEST_U1, new_data)
    assert updated.thread.name == new_data["name"]

    with edit_usc.session_m.session() as session:
        thread = repos.thread.get_by_id(session, thread_id)
        thread_co = repos.thread_cache.get(thread_id)

        assert thread == thread_co


def test_edit_private_group(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    edit_usc = usecases.edit_thread

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=GROUP_LEVEL_PRIVATE,
    )
    thread_id = p_thread.thread.id

    new_data = {"name": "New group"}

    # member can edit private group
    updated = edit_usc.edit_group(thread_id, TEST_U2, new_data)
    assert updated.thread.name == new_data["name"]

    with edit_usc.session_m.session() as session:
        thread = repos.thread.get_by_id(session, thread_id)
        thread_co = repos.thread_cache.get(thread_id)

        assert thread == thread_co


def test_edit_group_with_only_admin_can_edit_setting(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    edit_usc = usecases.edit_thread

    # create private group
    settings = ThreadSettings.decode(0b0)
    settings.only_admin_can_update_info = True
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        group_level=settings.encode(),
    )
    thread_id = p_thread.thread.id

    new_data = {"name": "New group"}

    # member can't edit group info
    with pytest.raises(PermissionDenied):
        edit_usc.edit_group(thread_id, TEST_U2, new_data)

    # only admin can update
    edit_usc.edit_group(thread_id, TEST_U1, new_data)

    with edit_usc.session_m.session() as session:
        thread = repos.thread.get_by_id(session, thread_id)
        thread_co = repos.thread_cache.get(thread_id)

        assert thread == thread_co


def test_cannot_edit_sub_thread(container: AppContainer):
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    # member can edit private group
    new_data = {"name": "New group"}

    with pytest.raises(UserNotInThread):
        updated = thread_uscs.edit_thread.edit_group(
            sub_thread_id, TEST_U2, new_data
        )


def test_edit_thread_with_sub_threads(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    conns = container.app().conns
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    usecases.edit_thread.edit_group(
        thread_id, TEST_U1, {"name": "Updated name"}
    )

    for thread_id in (thread_id, sub_thread_id):
        p_thread = usecases.get_thread.get_by_id(TEST_U1, sub_thread_id)
        assert p_thread.thread.name == "Updated name"

    with usecases.edit_thread.session_m.session() as session:
        thread = repos.thread.get_by_id(session, thread_id)
        thread_co = repos.thread_cache.get(thread_id)

        assert thread == thread_co
