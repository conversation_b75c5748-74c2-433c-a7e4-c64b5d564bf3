import pytest

from chat.container import AppContainer
from chat.exception import UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def is_thread_deleted(thread):
    return thread["delete_to"] == thread["message_count"]


def test_set_read_flag(container: AppContainer):
    # create test group
    thread_repo = container.repos().thread
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.repos().pt
    repos = container.app().repos

    p_thread = thread_uscs.create_thread.create_group([TEST_U2], TEST_U1, "test-group")
    assert p_thread.participant.mark_unread is False
    thread_id = p_thread.thread.id

    # increase message count, so it can mark as active thread
    with session_m.get_session() as session:
        thread_repo.incr_thread_msg_counter(session, thread_id)
        session.commit()

    with pytest.raises(UserNotInThread):
        thread_uscs.unread_thread.set_unread_flag(TEST_U3, thread_id, False)

    # can set read flag
    thread_uscs.unread_thread.set_unread_flag(TEST_U1, thread_id, True)
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.participant.mark_unread is True
    part_c = repos.pt.cache.get(thread_id, TEST_U1)
    assert part_c.mark_unread is True

    # make sure that this only apply to user TERST_U2
    p_u2_thread = thread_uscs.get_thread.get_by_id(TEST_U2, thread_id)
    assert p_u2_thread.participant.mark_unread is False

    # unset flag
    thread_uscs.unread_thread.set_unread_flag(TEST_U1, thread_id, False)
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.participant.mark_unread is False

    part_c = repos.pt.cache.get(thread_id, TEST_U1)
    assert part_c.mark_unread is False
