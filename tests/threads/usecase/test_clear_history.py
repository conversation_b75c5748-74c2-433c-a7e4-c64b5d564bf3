import pytest

from chat.container import App<PERSON>ontainer
from chat.exception import Permission<PERSON><PERSON>ed, UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.thread import create_sub_thread_of_group


def test_clear_history(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    usecase = usecases.leave_thread

    # create direct thread
    p_thread = usecases.create_thread.create_direct(TEST_U2, TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # update message count
    with usecase.session_m.session() as session:
        # set last mesage for user 1
        repos.pt.update_last_message(
            session, TEST_U1, thread_id, '{"id": 1, "body": "test"}'
        )
        repos.thread.update_msg_count(session, thread_id, 2)

    # normal member can't edit group information
    with pytest.raises(UserNotInThread):
        usecase.clear_conversation_thread(TEST_U3, thread_id)

    usecase.clear_conversation_thread(TEST_U1, thread_id)

    with usecase.session_m.session() as session:
        pt = repos.pt.get_member(session, thread_id, TEST_U1)
        session.commit()
        assert pt is not None
        assert pt.last_message is None


def test_cannot_clear_sub_thread_history(container: AppContainer):
    usecases = container.thread().usecases()
    repos = container.app().repos
    usecase = usecases.leave_thread
    message_uscs = container.message().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    # update message count
    with usecase.session_m.session() as session:
        # set last mesage for user 1
        repos.pt.update_last_message(
            session, TEST_U1, sub_thread_id, '{"id": 1, "body": "test"}'
        )
        repos.thread.update_msg_count(session, sub_thread_id, 2)

    with pytest.raises(PermissionDenied):
        usecase.clear_history(TEST_U1, sub_thread_id)
