from typing import cast

import pytest

from chat import constant
from chat.constant import (
    FOLDER_DEFAULT,
    GROUP_LEVEL_PRIVATE,
    ORGC_GROUP_BY_DEPARTMENT,
    ORGC_GROUP_BY_ROLE,
)
from chat.container.app import AppContainer
from chat.exception import (
    OnlyAdminCanCreate,
    PermissionDenied,
    RestrictedMembersInChatCreation,
    WorkspaceDisable,
)
from chat.messages.model import MessageBody
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.threads.exception import ThreadNotFoundByLink
from chat.utils.common import get_link_code_from_link
from tests.conftest import (
    TEST_B1,
    TEST_DEPARTMENT,
    TEST_MEMBER_FILE_ID,
    TEST_ROLE,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    TEST_U4,
    TEST_USER_ADMIN,
    TEST_WORKSPACE,
    TEST_WORKSPACE_DISABLE,
)
from tests.helpers.message import create_message


def test_create_direct(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()

    p_thread = usecase.create_direct(TEST_U1, TEST_U2)
    assert p_thread.participant.partner_id == TEST_U1
    assert p_thread.thread.is_direct()

    # check sent event
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "direct"


def test_create_direct_in_private_workspace(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()
    old_config = usecase.config.private_workspace
    usecase.config.private_workspace = "A,B"

    assert set("A") in usecase.config.get_private_workspaces()
    assert set("B") in usecase.config.get_private_workspaces()

    if True:
        p_thread = usecase.create_direct(TEST_U1, TEST_U2, workspace_id="A")
        assert p_thread.participant.partner_id == TEST_U1
        assert p_thread.thread.is_direct()

        # check sent event
        assert len(broker.messages) > 0
        msg = broker.messages[-1]
        assert msg["event_type"] == "thread_created"
        assert msg["body"]["type"] == "direct"

    if True:
        with pytest.raises(PermissionDenied):
            p_thread = usecase.create_direct(
                TEST_U1, TEST_U3, workspace_id="A"
            )

    if True:
        with pytest.raises(PermissionDenied):
            p_thread = usecase.create_direct(
                TEST_U3, TEST_U1, workspace_id="B"
            )

    usecase.config.private_workspace = old_config

def test_create_direct_in_private_workspace_with_bot(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()
    old_config = usecase.config.private_workspace
    usecase.config.private_workspace = "A,B"

    assert set("A") in usecase.config.get_private_workspaces()
    assert set("B") in usecase.config.get_private_workspaces()

    if True:
        p_thread = usecase.create_direct(TEST_B1, TEST_U1, workspace_id="A")
        assert p_thread.participant.partner_id == TEST_B1
        assert p_thread.thread.is_direct()

        # check sent event
        assert len(broker.messages) > 0
        msg = broker.messages[-1]
        assert msg["event_type"] == "thread_created"
        assert msg["body"]["type"] == "direct"

    if True:
        with pytest.raises(PermissionDenied):
            p_thread = usecase.create_direct(
                TEST_U1, TEST_U3, workspace_id="A"
            )

    if True:
        with pytest.raises(PermissionDenied):
            p_thread = usecase.create_direct(
                TEST_U3, TEST_U1, workspace_id="B"
            )

    usecase.config.private_workspace = old_config


def test_re_create_direct_chat_to_bot(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()

    p_thread = usecase.create_direct(TEST_B1, TEST_U2)
    assert p_thread.participant.partner_id == TEST_B1
    assert p_thread.thread.is_direct()

    # check sent event
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "direct"

    # remove all messages and try again
    broker.messages = []

    # re-fire thread_created event again
    p_thread = usecase.create_direct(TEST_B1, TEST_U2)
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "direct"


def test_create_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()
    pt_repo = container.app().repos.pt

    p_thread = usecase.create_group([TEST_U1, TEST_U2], TEST_U1, "Test group")
    assert p_thread.thread.is_group()
    # TODO: should we set the creator field?
    assert p_thread.thread.creator is None
    assert p_thread.thread.name == "Test group"

    with usecase.session_m.session() as session:
        member = pt_repo.get_member(session, p_thread.thread.id, TEST_U1)
        assert member is not None
        assert member.is_owner()

    # check sent event
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "group"


def test_create_super_large_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()
    pt_repo = container.app().repos.pt
    members = list(range(1, 10005))

    with pytest.raises(OnlyAdminCanCreate):
        p_thread = usecase.create_group(members, TEST_U1, "Test group")
    p_thread = usecase.create_group(members, TEST_USER_ADMIN, "Test group")

    assert p_thread.thread.is_group()
    # TODO: should we set the creator field?
    assert p_thread.thread.creator is None
    assert p_thread.thread.name == "Test group"

    with usecase.session_m.session() as session:
        member = pt_repo.get_member(
            session, p_thread.thread.id, TEST_USER_ADMIN
        )
        assert member is not None
        assert member.is_owner()

    # check sent event
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "group"

    assert len(broker.heavy_messages) == 3


def test_create_large_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    usecase = container.thread().create_thread()
    pt_repo = container.app().repos.pt
    members = list(range(1, 1005))

    with pytest.raises(OnlyAdminCanCreate):
        p_thread = usecase.create_group(members, TEST_U1, "Test group")
    p_thread = usecase.create_group(members, TEST_USER_ADMIN, "Test group")

    assert p_thread.thread.is_group()
    # TODO: should we set the creator field?
    assert p_thread.thread.creator is None
    assert p_thread.thread.name == "Test group"

    with usecase.session_m.session() as session:
        member = pt_repo.get_member(
            session, p_thread.thread.id, TEST_USER_ADMIN
        )
        assert member is not None
        assert member.is_owner()

    # check sent event
    assert len(broker.messages) > 0
    msg = broker.messages[-1]
    assert msg["event_type"] == "thread_created"
    assert msg["body"]["type"] == "group"
    assert len(broker.heavy_messages) == 1


def test_create_group_workspace_disable(container: AppContainer):

    usecase = container.thread().create_thread()

    with pytest.raises(WorkspaceDisable):
        _ = usecase.create_group(
            [TEST_U1, TEST_U2],
            TEST_U1,
            "Test group",
            workspace_id=TEST_WORKSPACE_DISABLE,
        )


def test_create_sub_thread_from_message_with_mentions(container: AppContainer):
    create_thread_usc = container.thread().usecases().create_thread
    pt_uscs = container.participant().usecases()
    get_thread_usc = container.thread().usecases().get_thread
    p_thread = create_thread_usc.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    # create test message from TEST_U1 with mention to TEST_U2
    message_id = 1
    body = MessageBody(
        **{
            "type": "text",
            "text": "Hello @2",
            "metadata": {
                "mentions": [{"target": TEST_U2, "length": "1", "offset": "1"}]
            },
        }
    )
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    # TEST_U3 creates new sub-thread
    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U3, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # TEST_U1, TEST_U2, TEST_U3 are in sub-thread
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3

    # make sure member is in sub-thread folder
    for member in members:
        assert member.folder == constant.FOLDER_SUB_THREAD

    p_thread = get_thread_usc.get_by_id(TEST_U1, sub_thread_id)

    # make sure thread score isn't set in both sub-thread and default folder
    thread_broker = container.app().brokers.thread_rd
    for user_id in (TEST_U1, TEST_U2, TEST_U3):
        for folder in (constant.FOLDER_SUB_THREAD, constant.FOLDER_DEFAULT):
            # not set in sub-thread folder
            score = thread_broker.get_thread_score(
                user_id, sub_thread_id, folder
            )
            assert not score


def test_create_sub_thread_from_message_of_other(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    usecases = container.message().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    pt_uscs = container.participant().usecases()
    get_thread_usc = container.thread().usecases().get_thread
    p_thread = create_thread_usc.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Root message"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = create_thread_usc.create_sub_thread(
        thread_id, message_id, TEST_U2, []
    )
    sub_thread_id = p_sub_thread.thread.id

    # both TEST_U1 (owner of message) and TEST_U2 are members of sub-thread.
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2
    # make sure member is in sub-thread folder
    for member in members:
        assert member.folder == constant.FOLDER_SUB_THREAD

    p_thread = get_thread_usc.get_by_id(TEST_U1, sub_thread_id)

    # make sure thread score isn't set in both sub-thread and default folder
    thread_broker = container.app().brokers.thread_rd
    for user_id in (TEST_U1, TEST_U2):
        for folder in (constant.FOLDER_SUB_THREAD, constant.FOLDER_DEFAULT):
            # not set in sub-thread folder
            score = thread_broker.get_thread_score(
                user_id, sub_thread_id, folder
            )
            assert not score


def test_create_sub_thread(container: AppContainer):
    usecase = container.thread().create_thread()
    pt_uscs = container.participant().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.app().repos.pt
    message_uscs = container.message().usecases()

    p_thread = usecase.create_group([TEST_U1, TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = usecase.create_sub_thread(
        thread_id, message_id, TEST_U1, [TEST_U2, TEST_U4]
    )
    sub_thread_id = p_sub_thread.thread.id

    assert p_sub_thread.thread.is_subthread()
    assert p_sub_thread.thread.parent_id == thread_id
    assert p_sub_thread.thread.root_message_id == message_id
    assert p_sub_thread.participant.folder == constant.FOLDER_SUB_THREAD
    assert p_sub_thread.thread.referenced_message
    assert p_sub_thread.thread.name == p_thread.thread.name

    # re-create sub-thread resulting in same thread
    p_sub_thread2 = usecase.create_sub_thread(
        thread_id, message_id, TEST_U1, [TEST_U2, TEST_U4]
    )
    assert p_sub_thread2.thread.id == sub_thread_id

    # check thread members
    members = pt_uscs.get_participant.get_all_members(
        [sub_thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2
    # make sure member is in sub-thread folder
    for member in members:
        assert member.folder == constant.FOLDER_SUB_THREAD

    # make sure thread score isn't set in default folder
    thread_broker = container.app().brokers.thread_rd
    for user_id in (TEST_U1, TEST_U2):
        # not set in sub-thread folder
        score = thread_broker.get_thread_score(
            user_id, sub_thread_id, constant.FOLDER_SUB_THREAD
        )
        assert not score

        # not set in default folder
        default_folder_score = thread_broker.get_thread_score(
            user_id, sub_thread_id, constant.FOLDER_DEFAULT
        )
        assert not default_folder_score

    # cannot create sub-thread of sub-thread
    with pytest.raises(PermissionDenied):
        usecase.create_sub_thread(sub_thread_id, message_id, TEST_U1, [])

    # make sure sub_thread_id is stored in message
    message = message_uscs.get_message.get_by_id(
        TEST_U1, thread_id, message_id
    )
    assert message is not None
    assert message.sub_thread_id == sub_thread_id

    # check list sub-threads
    with session_m.get_session() as session:
        sub_threads = pt_repo.get_all_sub_threads_for_user(
            session, TEST_U1, thread_id
        )
        assert len(sub_threads) == 1


def test_recreating_sub_thread(container: AppContainer):
    usecase = container.thread().create_thread()
    pt_uscs = container.participant().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.app().repos.pt
    message_uscs = container.message().usecases()

    p_thread = usecase.create_group([TEST_U1, TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    for user_id in (TEST_U1, TEST_U2):
        for i in range(2):
            p_sub_thread = usecase.create_sub_thread(
                thread_id, message_id, user_id, []
            )


def test_create_sub_thread_for_direct_thread(container: AppContainer):
    usecase = container.thread().create_thread()
    pt_uscs = container.participant().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.app().repos.pt
    message_uscs = container.message().usecases()

    p_thread = usecase.create_direct(TEST_U2, TEST_U1)
    thread_id = p_thread.thread.id

    # create test message
    message_id = 1
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(
        container.app(), thread_id, message_id, TEST_U1, body, msg_count=1
    )

    p_sub_thread = usecase.create_sub_thread(
        thread_id, message_id, TEST_U1, []
    )
    assert p_sub_thread.thread.is_subthread()
    assert p_sub_thread.thread.parent_id == thread_id
    assert p_sub_thread.thread.root_message_id == message_id
    assert p_sub_thread.participant.folder == constant.FOLDER_SUB_THREAD
    assert p_sub_thread.thread.name == "u2"


def test_create_group_with_members_from_other_threads(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)

    members_usc = container.participant().usecases().get_participant
    usecase = container.thread().create_thread()

    p_thread1 = usecase.create_group([TEST_U1, TEST_U2], TEST_U1, "Test group")
    th1_id = p_thread1.thread.id
    p_thread2 = usecase.create_group(
        [TEST_U1, TEST_U3], TEST_U1, "Test group", source_thread_ids=[th1_id]
    )

    members = members_usc.get_all_members(
        [p_thread2.thread.id], ["owner", "admin", "member"]
    )
    assert len(members) == 3
    assert len(broker.heavy_messages) == 0


def test_create_associate_group(container: AppContainer):
    app = container.app()
    usecase = container.thread().create_thread()
    thread_uscs = container.thread().usecases()
    pt_uscs = container.participant().usecases()
    associate_link = "http://link"
    p_thread = usecase.create_associate_group(
        TEST_U1,
        "Test associate group",
        associate_link,
        group_level=constant.GROUP_LEVEL_PUBLIC,
    )
    thread_id = p_thread.thread.id

    # TODO: fix this uggly logic, what link that we should pass to usecase
    link_code = get_link_code_from_link(p_thread.thread.link)

    thread_uscs.join_thread.join_by_link(TEST_U2, link_code)
    thread_uscs.join_thread.join_by_link(TEST_U3, link_code)

    with pytest.raises(ThreadNotFoundByLink):
        thread_uscs.join_thread.join_by_link(TEST_U4, "invalid_code")

    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3


def test_set_thread_score_when_create_new_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    conns = app.conns
    mysql = conns.mysql
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    pt_uscs = container.participant().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "test-group"
    )
    thread_id = p_thread.thread.id

    folder = folder_uscs.create(TEST_U1, "test_folder", "", [thread_id], [])
    folder_id = folder["alias"]

    # increase thread message counter because
    # zero-messages thread is considered as deleted
    with mysql.get_session() as session:
        repos.thread.incr_thread_msg_counter(session, thread_id)
        session.commit()

    # TEST_U1
    threads = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [FOLDER_DEFAULT]
    )
    assert len(threads) == 0
    threads = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U1, [folder_id]
    )
    assert len(threads) == 1

    # TEST_U2
    threads = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U2, [FOLDER_DEFAULT]
    )
    assert len(threads) == 1
    threads = pt_uscs.get_participant.get_all_threads_in_folders(
        TEST_U2, [folder_id]
    )
    # empty folder
    assert len(threads) == 0


def test_create_direct_chat_with_system(container: AppContainer):
    app = container.app()
    repos = app.repos
    conns = app.conns
    mysql = conns.mysql
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()

    p_thread = thread_uscs.create_thread.create_system_direct(
        TEST_U1, "system", "system"
    )

    p_thread2 = thread_uscs.create_thread.create_system_direct(
        TEST_U1, "system", "system"
    )

    assert p_thread.thread.id == p_thread2.thread.id


def test_create_org_group_by_department(container: AppContainer):
    app = container.app()
    conns = app.conns
    mysql = conns.mysql
    thread_uscs = container.thread().usecases()
    pt_repo = container.app().repos.pt

    p_thread = thread_uscs.create_thread.create_group_orgc(
        {TEST_WORKSPACE: [TEST_DEPARTMENT]},
        ORGC_GROUP_BY_DEPARTMENT,
        TEST_U1,
        "Test orgc",
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
    )
    thread_id = p_thread.thread.id
    with mysql.get_session() as session:
        members = pt_repo.get_members_in_thread(session, thread_id, 0, 10)
        assert len(members) == 1
        assert members[0].user_id == TEST_U2
        owners = pt_repo.get_owners(session, thread_id)
        assert len(owners) == 1
        assert owners[0].user_id == TEST_U1


def test_create_orgc_group_by_role(container: AppContainer):
    app = container.app()
    conns = app.conns
    mysql = conns.mysql
    thread_uscs = container.thread().usecases()
    pt_repo = container.app().repos.pt

    p_thread = thread_uscs.create_thread.create_group_orgc(
        {TEST_WORKSPACE: [TEST_ROLE]},
        ORGC_GROUP_BY_ROLE,
        TEST_U1,
        "Test orgc",
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
    )
    thread_id = p_thread.thread.id
    with mysql.get_session() as session:
        members = pt_repo.get_members_in_thread(session, thread_id, 0, 10)
        assert len(members) == 1
        assert members[0].user_id == TEST_U2
        owners = pt_repo.get_owners(session, thread_id)
        assert len(owners) == 1
        assert owners[0].user_id == TEST_U1


def test_restriction_for_droppi(container: AppContainer):
    app = container.app()
    conf = app.conf
    conns = app.conns
    mysql = conns.mysql
    thread_uscs = container.thread().usecases()

    with pytest.raises(RestrictedMembersInChatCreation):
        thread_uscs.create_thread.create_group(
            [str(i + 2) for i in range(11)],
            TEST_U1,
            "Test group",
            workspace_id=app.conf.miscs.droppi_workspace_id,
            call_source=constant.CALL_SOURCE_API,
        )


def test_create_thread_from_imported_excel_file(container: AppContainer):
    app = container.app()
    conns = app.conns
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    # add members from file TEST_FILE_ID, which contains TEST_U2, TEST_U3
    p_thread = thread_uscs.create_thread.create_group(
        [], TEST_U1, "Test group", member_file_id=TEST_MEMBER_FILE_ID
    )
    thread_id = p_thread.thread.id
    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3
