from typing import cast

import pytest

from chat import constant
from chat.connections.gapo_client.membership import MembershipMock
from chat.container import AppContainer
from chat.exception import UserNotInThread
from tests.conftest import (
    TEST_COLLAB_ID,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    TEST_WORKSPACE,
)
from tests.helpers.thread import create_sub_thread_of_group


def is_thread_deleted(thread):
    return thread["delete_to"] == thread["message_count"]


def test_deactivate_member(container: AppContainer):
    # create test group
    thread_repo = container.repos().thread
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    session_m = container.app().conns.mysql
    repos = container.repos()
    pt_repo = container.repos().pt
    thread_broker = container.app().brokers.thread_rd

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3],
        TEST_U1,
        "test-group",
        workspace_id=TEST_WORKSPACE,
        collab_id=TEST_COLLAB_ID,
    )
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    # increase message count, so it can mark as active thread
    with session_m.get_session() as session:
        thread_repo.incr_thread_msg_counter(session, thread_id)
        session.commit()

    thread_uscs.member_activation.deactivate(TEST_U1, TEST_WORKSPACE)

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    with pytest.raises(UserNotInThread):
        thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)

    # make sure thread score is clear from redis
    assert not thread_broker.get_thread_score(
        TEST_U1, thread_id, constant.FOLDER_DEFAULT
    )

    # reactivate user
    thread_uscs.member_activation.reactivate(TEST_U1, TEST_WORKSPACE)

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    # member = repos.part_cache.get(thread_id, TEST_U1)
    # assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    # user will rejoin collab
    assert cast(MembershipMock, repos.gapo.membership).join_collab_calls == 1

    # now user TEST_U2 is in thread
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.id == thread_id
    assert (
        p_thread.participant.role == "member"
    )  # user will be normal member rather owner if they were reactivated

    # thread score is set again
    assert thread_broker.get_thread_score(
        TEST_U2, thread_id, constant.FOLDER_DEFAULT
    )


def test_deactivate_user_from_parent_group(container: AppContainer):
    # create test group
    thread_repo = container.repos().thread
    repos = container.repos()
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    session_m = container.app().conns.mysql
    pt_repo = container.repos().pt
    thread_broker = container.app().brokers.thread_rd

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container,
        "Test group",
        TEST_U1,
        [TEST_U2, TEST_U3],
        workspace_id=TEST_WORKSPACE,
        sub_thread_member_ids=[TEST_U2],
    )

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    # increase message count, so it can mark as active thread
    with session_m.get_session() as session:
        thread_repo.incr_thread_msg_counter(session, thread_id)
        session.commit()

    thread_uscs.member_activation.deactivate(TEST_U2, TEST_WORKSPACE)

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    # user is removed from both thread and sub-thread
    for th_id in (thread_id, sub_thread_id):
        with pytest.raises(UserNotInThread):
            thread = thread_uscs.get_thread.get_by_id(TEST_U2, th_id)
            member = repos.part_cache.get(th_id, TEST_U2)
            assert member is None

    assert not thread_broker.get_thread_score(
        TEST_U2, thread_id, constant.FOLDER_DEFAULT
    )
    assert not thread_broker.get_thread_score(
        TEST_U2, sub_thread_id, constant.FOLDER_SUB_THREAD
    )

    # reactivate user
    thread_uscs.member_activation.reactivate(TEST_U2, TEST_WORKSPACE)

    for th_id in (thread_id, sub_thread_id):
        thread = thread_uscs.get_thread.get_by_id(TEST_U2, th_id)
        assert thread
        # member = repos.part_cache.get(th_id, TEST_U2)
        # assert member is not None

    # make sure thread score is set
    assert thread_broker.get_thread_score(
        TEST_U2, thread_id, constant.FOLDER_DEFAULT
    )
    assert thread_broker.get_thread_score(
        TEST_U2, sub_thread_id, constant.FOLDER_SUB_THREAD
    )
    assert not thread_broker.get_thread_score(
        TEST_U2, sub_thread_id, constant.FOLDER_DEFAULT
    )
