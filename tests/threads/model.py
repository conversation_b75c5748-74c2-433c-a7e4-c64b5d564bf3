from chat.threads.model import ThreadSettings


def test_thread_settings_public():
    group_level = 0b0001

    setting = ThreadSettings.decode(group_level)
    assert setting.is_public is True
    assert setting.encode() == group_level


def test_thread_settings_private():
    group_level = 0b0000

    setting = ThreadSettings.decode(group_level)
    assert setting.is_public is False
    assert setting.encode() == group_level


def test_thread_settings_disable_send_msg():
    group_level = 0b0010

    setting = ThreadSettings.decode(group_level)
    assert setting.is_public is False
    assert setting.disable_member_send_message is True
    assert setting.encode() == group_level


def test_thread_settings_disable_send_sub_msg():
    group_level = 0b1_0000_0000

    setting = ThreadSettings.decode(group_level)
    assert setting.disable_member_send_sub_message is True
    assert setting.encode() == group_level


def test_thread_settings_only_admin_can_add_member():
    group_level = 0b0000_0100

    setting = ThreadSettings.decode(group_level)
    assert setting.only_admin_can_add_member is True
    assert setting.encode() == group_level


def test_thread_settings_only_admin_can_update_info():
    group_level = 0b0000_1000

    setting = ThreadSettings.decode(group_level)
    assert setting.only_admin_can_update_info is True
    assert setting.encode() == group_level


def test_thread_settings_set_delete_1():
    group_level = 0b0

    setting = ThreadSettings.decode(group_level)
    assert setting.delete_msg_after_days == 0

    setting.set_delete_msg_day(1)
    assert setting.delete_msg_after_days == 1
    assert setting.encode() == 0b0001_0000


def test_thread_settings_set_delete_3():
    group_level = 0b0

    setting = ThreadSettings.decode(group_level)
    assert setting.delete_msg_after_days == 0

    setting.set_delete_msg_day(3)
    assert setting.delete_msg_after_days == 3
    assert setting.encode() == 0b0010_0000


def test_thread_settings_set_delete_7():
    group_level = 0b0

    setting = ThreadSettings.decode(group_level)
    assert setting.delete_msg_after_days == 0

    setting.set_delete_msg_day(7)
    assert setting.delete_msg_after_days == 7
    assert setting.encode() == 0b0100_0000


def test_thread_settings_set_delete_30():
    group_level = 0b0

    setting = ThreadSettings.decode(group_level)
    assert setting.delete_msg_after_days == 0

    setting.set_delete_msg_day(30)
    assert setting.delete_msg_after_days == 30
    assert setting.encode() == 0b1000_0000


def test_thread_settings_random():
    group_level = 0b1_0010_1010

    setting = ThreadSettings.decode(group_level)
    assert setting.delete_msg_after_days == 3
    assert setting.is_public is False
    assert setting.disable_member_send_message is True
    assert setting.only_admin_can_add_member is False
    assert setting.only_admin_can_update_info is True
    assert setting.disable_member_send_sub_message is True

    assert setting.encode() == 0b1_0010_1010


def test_thread_setting_toggle():
    group_level = 0b0

    setting = ThreadSettings.decode(group_level)

    assert setting.is_public is False
    setting.toggle_privacy()
    assert setting.is_public is True
    setting.toggle_privacy()
    assert setting.is_public is False

    assert setting.disable_member_send_message is False
    assert setting.disable_member_send_sub_message is False

    setting.toggle_send_msg()
    assert setting.disable_member_send_message() is True

    setting.toggle_send_sub_message()
    assert setting.disable_member_send_sub_message is True

    setting.toggle_send_msg()
    assert setting.disable_member_send_message is False
    assert setting.disable_member_send_sub_message is False

    assert setting.only_admin_can_add_member is False
    setting.toggle_add_mem()
    assert setting.only_admin_can_add_member is True
    setting.toggle_add_mem()
    assert setting.only_admin_can_add_member is False

    assert setting.only_admin_can_update_info is False
    setting.toggle_update_info()
    assert setting.only_admin_can_update_info is True
    setting.toggle_update_info()
    assert setting.only_admin_can_update_info is False
