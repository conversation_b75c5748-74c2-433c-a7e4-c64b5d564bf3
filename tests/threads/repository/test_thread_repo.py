from chat.container import AppContainer
from tests.conftest import TEST_U1


def test_update_media_count_with_invalid_count(container: AppContainer):
    member_repo = container.app().repos.pt
    thread_repo = container.app().repos.thread
    session_m = container.app().conns.mysql

    with session_m.get_session() as session:
        thread_id = thread_repo.create_group(
            session, "Test", member_count=5, workspace_id="1"
        )
        session.commit()
        member_repo.create_owner(session, thread_id, TEST_U1, folder="test")
        session.commit()

        # don't have any problem if we pass
        # invalid value
        thread_repo.update_thread_media_count(
            session, thread_id, [("file", 3)], decrease=True
        )
