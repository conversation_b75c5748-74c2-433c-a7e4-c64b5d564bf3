from chat.container import AppContainer
from tests.conftest import TEST_U1, TEST_U2


def test_repo(container: AppContainer):
    repo = container.repos().thread_read
    thread_id = 12
    repo.update_read(thread_id, TEST_U1, 12)
    repo.update_read(thread_id, TEST_U2, 13)

    active_mems = repo.get_most_recent_thread_viewers(thread_id, 1)
    assert len(active_mems) == 1
    assert active_mems[0] == TEST_U2

    active_mems = repo.get_most_recent_thread_viewers(thread_id, 2)
    assert set(active_mems) == set([TEST_U2, TEST_U1])
