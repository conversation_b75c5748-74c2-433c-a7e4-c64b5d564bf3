import pytest

from chat.container import AppContainer
from chat.exception import LockTimeout<PERSON>rror


def test_acquire(container: AppContainer):
    lock_manager = container.app().lock_manager
    lock_key = "test-lock"
    lock = lock_manager.get_lock(lock_key)
    lock.acquire()

    with pytest.raises(LockTimeoutError):
        with lock_manager.get_lock(lock_key, timeout=1):
            pass

    lock.release()

    other_lock = lock_manager.get_lock(lock_key)
    assert other_lock.acquire()
