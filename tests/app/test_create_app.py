from chat import constant
from chat.app.create_app import create_app
from chat.container import AppContainer
from chat.backgrounds import setup


def test_create_app(container: AppContainer):
    """Make sure we can create app"""
    app = create_app(container)
    assert app is not None


def test_create_background(container: AppContainer):
    b_app = setup(container)
    assert len(b_app) == 2

    handler = b_app[0]

    tasks = [
        constant.TASK_CREATE_MESSAGE,
        constant.TASK_ADD_MEMBER,
        constant.TASK_CREATE_MEMBER,
        constant.TASK_FORWARD_MESSEAGE,
        constant.TASK_DISBAND_GROUP,
        constant.TASK_FORWARD_MULTIPLE_MESSAGES,
        constant.TASK_DELETE_MESSAGE,
        constant.TASK_EDIT_MESSAGE,
        constant.TASK_MOVE_FOLDER,
        constant.TASK_SORT_FOLDER,
        constant.TASK_LEAVE_GROUP,
        constant.TASK_DEACTIVATE_MEMBER_IN_GROUP,
        constant.TASK_REACTIVATE_MEMBER_IN_GROUP,
        constant.TASK_REMOVE_MEMBER,
        constant.TASK_FIX_THREAD_SCORE,
        constant.TASK_CREATE_MASS_DIRECT_MESSAGES,
        constant.TASK_BAN_MEMBER,
        constant.TASK_TOGGLE_NOTI,
        constant.TASK_JOIN_COLLAB,
        constant.TASK_ADD_MEMBER_COLLAB,
    ]

    for task in tasks:
        handler({"event_type": task, "uuid": "123123", "body": {}})
