from chat.container import AppContainer
from tests.conftest import TEST_U1


def test_create(container: AppContainer):
    usecases = container.user().usecases()
    pass_code = "passcode"
    usecases.create(TEST_U1, pass_code)
    passcode = usecases.get_pass_code(TEST_U1)
    assert passcode is not None
    assert usecases.verify(TEST_U1, pass_code)


def test_update(container: AppContainer):
    usecases = container.user().usecases()
    usecases.create(TEST_U1, "passcode")

    new_passcode = "new passcode"
    usecases.update(TEST_U1, "passcode", new_passcode)
    assert usecases.verify(TEST_U1, new_passcode)
