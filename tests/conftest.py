"""
All test fixtures will be here.
See: https://www.tutorialspoint.com/pytest/pytest_conftest_py.htm
"""

import logging
import sys
from typing import cast

import pytest
from falcon import testing

from chat.app.create_app import create_app
from chat.backgrounds import setup as setupTask
from chat.connections.gapo_client.base import (
    SchemaBotProfile,
    SchemaUserProfile,
)
from chat.connections.gapo_client.membership import (
    CollabGroup,
    CollabGroupRole,
    CollabSettings,
    Member,
)
from chat.connections.gapo_client.mock import (
    Contact,
    GapoClientMock,
    OrgcMember,
    SchemaFeatureWorkspace,
)
from chat.constant import SAVE_MESSAGE_BOT_ID, USER_STATUS_DEACTIVATED
from chat.container.app import AppContainer
from chat.publishers.mqtt import MqttPublisherMock
from chat.publishers.rabbitmq import AmqpProducerMock
from mysql_models.models import Base
from tests.utils import get_test_config

# constants to recall easier
# test users
TEST_U1 = "1"
TEST_U2 = "2"
TEST_U3 = "3"
TEST_U4 = "4"

TEST_USER_ADMIN = TEST_U3
TEST_DEACTIVATED_USER = "5"

# test bots
TEST_B1 = "5827717749675246510"
TEST_B2 = "5828186785584378077"
TEST_B3 = "5828186786370932181"

TEST_WORKSPACE = "5"
TEST_WORKSPACE_DISABLE = "6"

TEST_ORGC = "test_orgc"

TEST_DEPARTMENT = "test_department"
"""Test department. This department has TEST_U1, TEST_U2."""

TEST_ROLE = "test_role"
"""Test role. TEST_U1, TEST_U2 have this role"""


TEST_API_KEY = "test_api_key"

TEST_COLLAB_ID = "6982333108996094976"
TEST_COLLAB_REQUIRES_ADMIN_APPROVAL_ID = "6982333108996094977"

# test data for test import user from file
TEST_MEMBER_FILE_ID = "test-file"
TEST_FILE_DATA = {TEST_MEMBER_FILE_ID: [TEST_U2, TEST_U3]}


@pytest.fixture(scope="session")
def container_mod():
    """App container for all tests.
    We init container only once for all testcases.
    """
    # setup
    config = get_test_config()

    c = AppContainer()
    c.conf.override(config)

    # mock mqtt publisher
    mqtt_publisher = MqttPublisherMock()

    c.check_dependencies()
    user_data = [
        SchemaUserProfile(id=TEST_U1, display_name="u1"),  # type: ignore
        SchemaUserProfile(id=TEST_U2, display_name="u2"),  # type: ignore
        SchemaUserProfile(id=TEST_U3, display_name="u3"),  # type: ignore
        SchemaUserProfile(id=TEST_U4, display_name="u4"),  # type: ignore
        SchemaUserProfile(id=TEST_DEACTIVATED_USER, display_name="u5", status=USER_STATUS_DEACTIVATED),  # type: ignore
    ]

    for i in range(6, 10010):
        user_data.append(SchemaUserProfile(id=i, display_name="u" + str(i)))

    c.repo_container.gapo.override(
        GapoClientMock(
            user_data=user_data,
            bot_data=[
                SchemaBotProfile(id=TEST_B1, name="b1", workspace_id="A"),
                SchemaBotProfile(id=TEST_B2, name="b2", workspace_id="B"),
                SchemaBotProfile(id=TEST_B3, name="b3", workspace_id="C"),
                SchemaBotProfile(
                    id=SAVE_MESSAGE_BOT_ID, name="Lưu trữ"
                ),  # save message bot
            ],
            contact_data=[Contact(u1=TEST_U1, u2=TEST_U2, code=1)],
            orgc_data=[
                OrgcMember(
                    user_id=TEST_U1,
                    department=TEST_DEPARTMENT,
                    role=TEST_ROLE,
                    workspace_id=TEST_WORKSPACE,
                ),
                OrgcMember(
                    user_id=TEST_U2,
                    department=TEST_DEPARTMENT,
                    role=TEST_ROLE,
                    workspace_id=TEST_WORKSPACE,
                ),
            ],
            membership_collabs=[
                CollabGroup(
                    collab_group_id=TEST_COLLAB_ID,
                    name="Collab 1",
                    avatar="",
                    description="",
                    workspace_id=TEST_WORKSPACE,
                    settings=CollabSettings(
                        discovery=False, public=False, need_approve=False
                    ),
                ),
                CollabGroup(
                    collab_group_id=TEST_COLLAB_REQUIRES_ADMIN_APPROVAL_ID,
                    name="Collab 2",
                    avatar="",
                    description="",
                    workspace_id=TEST_WORKSPACE,
                    settings=CollabSettings(
                        discovery=False,
                        public=False,
                        need_approve=True,  # need approval
                    ),
                ),
            ],
            membership_member_data={
                TEST_COLLAB_ID: [
                    Member(
                        membership_id=int(TEST_U1),
                        user_id=int(TEST_U1),
                        roles=[CollabGroupRole.Owner],
                    ),
                    Member(
                        membership_id=int(TEST_U2),
                        user_id=int(TEST_U2),
                        roles=[CollabGroupRole.Admin],
                    ),
                    Member(
                        membership_id=int(TEST_U3),
                        user_id=int(TEST_U3),
                        roles=[CollabGroupRole.Member],
                    ),
                    Member(
                        membership_id=int(TEST_U4),
                        user_id=int(TEST_U4),
                        roles=[CollabGroupRole.Member],
                    ),
                ]
            },
            relation_data=[(TEST_U1, TEST_U2)],
            company_data={"1": "A", "2": "A", "3": "B"},
            role_data={TEST_USER_ADMIN: True},
            file_data=TEST_FILE_DATA,
            iam_data={TEST_API_KEY: "all"},
            feature_data=[
                SchemaFeatureWorkspace(data={TEST_WORKSPACE_DISABLE: False})
            ],
        )
    )
    c.init_resources()
    c.wire(packages=[sys.modules["chat"]])

    # overwrite gapo client in info cache with mock client
    c.app().repos.info.gapo_client = c.app().repos.gapo

    # overwrite current broker with a Mock broker
    # that will handle all background tasks
    log = c.log()
    log.setLevel(logging.ERROR)
    task_handler, _ = setupTask(container=c)

    # FIXME: this way doesn't work
    # so we try to overwrite manually
    # c.conn_container.rabbitmq_pub.override(AmqpProducerMock(task_handler))
    amqp_producer = AmqpProducerMock(task_handler)
    app_broker = c.brokers()
    app_broker.message_rb.engine = amqp_producer
    app_broker.pt_rb.engine = amqp_producer
    app_broker.thread_rb.engine = amqp_producer

    c.conns().mqtt_pub = mqtt_publisher
    app_broker.message_mqtt.engine = mqtt_publisher
    app_broker.thread_mqtt.engine = mqtt_publisher
    app_broker.user_mqtt.engine = mqtt_publisher
    app_broker.pt_mqtt.engine = mqtt_publisher

    c.log().setLevel(logging.ERROR)

    mysql_c = c.app().conns.mysql

    # init database schema before all tests
    Base.metadata.drop_all(mysql_c.engine)
    Base.metadata.create_all(mysql_c.engine)

    # run tests here
    try:
        yield c
    except Exception:
        raise

    c.conns().close_all()


def truncate_db(engine):
    # Adapted from https://gist.github.com/absent1706/3ccc1722ea3ca23a5cf54821dbc813fb

    # delete all table data (but keep tables)
    # we do cleanup before test 'cause if previous test errored,
    # DB can contain dust
    meta = Base.metadata
    con = engine.connect()
    trans = con.begin()
    con.execute("SET FOREIGN_KEY_CHECKS = 0;")
    for table in meta.sorted_tables:
        con.execute(table.delete())
    con.execute("SET FOREIGN_KEY_CHECKS = 1;")
    trans.commit()


@pytest.fixture()
@pytest.mark.asyncio
def container(container_mod: AppContainer):
    """Fixture that we can use in testcase."""
    c = container_mod
    mysql_db = c.app().conns.mysql
    cas_db = c.app().conns.cas
    cast(GapoClientMock, c.app().repos.gapo).reset()
    broker = cast(AmqpProducerMock, c.app().brokers.message_rb.engine)
    broker.messages = []
    broker.heavy_messages = []

    # truncate database before each test
    cas_db.execute("truncate messages")

    # truncate database
    # NOTE: it's faster to init database schema once (in container_mod) and truncate table
    # before running each test
    truncate_db(mysql_db.engine)
    c.conns().redis_msg_c.client.flushall()
    c.conns().redis_cache_c.client.flushall()
    c.conns().redis_thread_c.client.flushall()
    c.conns().redis_persist_c.client.flushall()

    return c


@pytest.fixture()
def client(container: AppContainer):
    app = create_app(container)
    return testing.TestClient(app)


def user_header(user_id, workspace_id=None):
    headers = {
        "x-gapo-user-id": user_id,
        "x-gapo-role": "user",
        "x-gapo-lang": "vi",
    }
    if workspace_id:
        headers["x-gapo-workspace-id"] = workspace_id

    return headers


def service_header(api_key: str):
    return {
        "x-gapo-role": "service",
        "x-gapo-lang": "vi",
        "x-gapo-api-key": api_key,
    }


def task_broker(container: AppContainer):
    """A task broker.

    It will work like current worker. When we send a task to worker,
    it will trigger corresponding task defined in `core.background_app.setup`

    """
    log = container.log()
    thread_usc = container.thread().usecases()
    participant_usc = container.participant().usecases()
    message_usc = container.message().usecases()
    folder_usc = container.folder().usecases()
    task_handler, _ = setupTask(container=container)
    return AmqpProducerMock(task_handler)


def mqtt(container: AppContainer):
    return container.conns().mqtt_pub
