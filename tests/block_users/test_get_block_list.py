from chat.container import AppContainer
from tests.conftest import TEST_U1


# NOTE: This API is deprecated
def test_get_user_block_list(container: AppContainer):
    usecases = container.block_users().usecases()
    app = container.app()
    # conns = app.conns

    block_list = usecases.get_by_id(TEST_U1)
    assert len(block_list) == 0

    block_id = "1"
    usecases.add(TEST_U1, block_id, "user")

    block_list = usecases.get_by_id(TEST_U1)
    assert len(block_list) == 1
