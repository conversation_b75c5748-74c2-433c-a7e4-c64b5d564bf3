from typing import cast

import pytest

from chat import constant
from chat.connections.gapo_client.membership import MembershipMock
from chat.constant import (
    GROUP_LEVEL_PRIVATE,
    GROUP_LEVEL_PUBLIC,
    ORGC_GROUP_BY_DEPARTMENT,
)

from chat.threads.model import ThreadSettings
from chat.container import AppContainer
from chat.exception import (
    AddingMembersDisabled,
    InvalidParameters,
    UserNotInThread,
    OnlyAdminCanCreate,
)
from chat.participants import exception as PartError
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.utils.common import get_link_code_from_link
from tests.conftest import (
    TEST_COLLAB_REQUIRES_ADMIN_APPROVAL_ID,
    TEST_DEPARTMENT,
    TEST_MEMBER_FILE_ID,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    TEST_U4,
    TEST_USER_ADMIN,
    TEST_WORKSPACE,
)
from tests.helpers.thread import create_sub_thread_of_group


def test_add_members_to_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    with pytest.raises(UserNotInThread):
        usecases.add_participant.add_members(TEST_U4, thread_id, [TEST_U3])

    usecases.add_participant.add_members(TEST_U1, thread_id, [TEST_U3])

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    # check action note
    last_message = [
        msg for msg in broker.messages if msg["event_type"] == "create_message"
    ][-1]
    assert last_message["skip_message"] is False


def test_add_members_to_super_large_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_USER_ADMIN, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_USER_ADMIN)
    assert member is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    with pytest.raises(UserNotInThread):
        usecases.add_participant.add_members(TEST_U4, thread_id, [TEST_U3])

    usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U4])

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    # check action note
    last_message = [
        msg for msg in broker.messages if msg["event_type"] == "create_message"
    ][-1]
    assert last_message["skip_message"] is False
    members = list(range(1, 10005))

    with pytest.raises(OnlyAdminCanCreate):
        usecases.add_participant.add_members(TEST_U2, thread_id, members)
    usecases.add_participant.add_members(TEST_USER_ADMIN, thread_id, members)

    assert len(broker.heavy_messages) == 3


def test_add_members_to_large_group(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_USER_ADMIN, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_USER_ADMIN)
    assert member is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    with pytest.raises(UserNotInThread):
        usecases.add_participant.add_members(TEST_U4, thread_id, [TEST_U3])

    usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U4])

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    thread = repos.thread_cache.get(thread_id)
    assert thread is not None

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    # check action note
    last_message = [
        msg for msg in broker.messages if msg["event_type"] == "create_message"
    ][-1]
    assert last_message["skip_message"] is False
    members = list(range(1, 1005))

    with pytest.raises(OnlyAdminCanCreate):
        usecases.add_participant.add_members(TEST_U2, thread_id, members)
    usecases.add_participant.add_members(TEST_USER_ADMIN, thread_id, members)
    assert len(broker.heavy_messages) == 1


def test_add_members_without_action_note(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    with pytest.raises(UserNotInThread):
        usecases.add_participant.add_members(
            TEST_U4, thread_id, [TEST_U3], skip_action_notes=True
        )

    usecases.add_participant.add_members(
        TEST_U1, thread_id, [TEST_U3], skip_action_notes=True
    )

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    # no action note
    p_thread = thread_uscs.get_thread.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.message_count == 0

    # make sure rabbitmq event is stilled published
    # this is REQUIRED for search service to work correctly.
    last_message = [
        msg for msg in broker.messages if msg["event_type"] == "create_message"
    ][-1]
    assert last_message["skip_message"] == True
    assert len(broker.heavy_messages) == 0


def test_add_members_with_rejoin(container: AppContainer):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    leave_thread_usc = container.thread().usecases().leave_thread
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    usecases.add_participant.add_members(
        TEST_U1, thread_id, [TEST_U3], skip_action_notes=True
    )

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    leave_thread_usc.leave_group(thread_id, TEST_U3, clear_msg=True)

    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    usecases.add_participant.add_members(
        TEST_U1, thread_id, [TEST_U3], skip_action_notes=True
    )

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3


def test_add_members_from_source_thread_to_other_thread(
    container: AppContainer,
):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    settings = ThreadSettings.decode(0b0)
    settings.only_admin_can_add_member = True

    p_thread1 = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=settings.encode()
    )
    p_thread2 = thread_uscs.create_thread.create_group(
        [TEST_U4], TEST_U1, "Test group", group_level=settings.encode()
    )

    with pytest.raises(InvalidParameters):
        usecases.add_participant.add_members(
            TEST_U1,
            thread_id=p_thread1.thread.id,
            invite_ids=[],
            source_thread_ids=[p_thread1.thread.id],
        )

    # can't add from thread that you are not in
    with pytest.raises(PartError.AddMemberPermissionError):
        usecases.add_participant.add_members(
            TEST_U2,
            thread_id=p_thread1.thread.id,
            invite_ids=[],
            source_thread_ids=[p_thread2.thread.id],
        )

    usecases.add_participant.add_members(
        TEST_U1,
        thread_id=p_thread2.thread.id,
        invite_ids=[TEST_U3],
        source_thread_ids=[p_thread1.thread.id],
    )

    members = usecases.get_participant.get_all_members([p_thread2.thread.id])
    assert len(members) == 4


def test_add_member_in_strict_moderation_group(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()
    settings = ThreadSettings.decode(0b0)
    settings.only_admin_can_add_member = True

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=settings.encode()
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    # normal member cannot add other member to thread
    with pytest.raises(PartError.AddMemberPermissionError):
        usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U4])

    # admin/owner can add new member
    usecases.add_participant.add_members(TEST_U1, thread_id, [TEST_U4])
    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3


def test_member_add_member_in_private_group(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=GROUP_LEVEL_PRIVATE
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2

    with pytest.raises(UserNotInThread):
        usecases.add_participant.add_members(TEST_U4, thread_id, [TEST_U3])

    usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U3])

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3


def test_member_add_member_to_associate_group(container: AppContainer):
    create_thread_usc = container.thread().create_thread()
    participant_uscs = container.participant().usecases()
    associate_link = "http://link"
    p_thread = create_thread_usc.create_associate_group(
        TEST_U1,
        "Test associate group",
        associate_link,
        group_level=GROUP_LEVEL_PUBLIC,
    )
    thread_id = p_thread.thread.id

    participant_uscs.add_participant.add_members(
        TEST_U1, thread_id, [TEST_U3, TEST_U2]
    )

    # don't create message when add new member
    # check message count to verify this
    p_thread = create_thread_usc.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.message_count == 0


def test_member_add_member_to_chat_associated_to_department(
    container: AppContainer,
):
    create_thread_usc = container.thread().create_thread()
    participant_uscs = container.participant().usecases()
    p_thread = create_thread_usc.create_group_orgc(
        {TEST_WORKSPACE: [TEST_DEPARTMENT]},
        ORGC_GROUP_BY_DEPARTMENT,
        TEST_U1,
        "Test orgc",
        group_level=GROUP_LEVEL_PRIVATE,
        group_type="group",
    )
    thread_id = p_thread.thread.id

    participant_uscs.add_participant.add_members(
        TEST_U1, thread_id, [TEST_U3, TEST_U2]
    )

    # don't create message when add new member
    # check message count to verify this
    p_thread = create_thread_usc.get_by_id(TEST_U1, thread_id)
    assert p_thread.thread.message_count == 0


# def test_add_members_to_collab_requires_admin_approval(container: AppContainer):
#     app = container.app()
#     repos = app.repos
#     usecases = container.participant().usecases()
#     thread_uscs = container.thread().usecases()

#     thread = thread_uscs.create_thread.create_group(
#         [TEST_U2],
#         TEST_U1,
#         "Test group",
#         group_level=1,
#         workspace_id=1,
#         collab_id=TEST_COLLAB_REQUIRES_ADMIN_APPROVAL_ID,
#     )
#     thread_id = thread["id"]

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 2

#     with pytest.raises(UserNotInThread):
#         usecases.add_participant.add_members(TEST_U4, thread_id, [TEST_U3])

#     # normal user need review
#     usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U3])
#     assert cast(MembershipMock, repos.gapo.membership).add_member == TEST_U3
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 2

#     # admin user doesn't need to review
#     usecases.add_participant.add_members(TEST_U1, thread_id, [TEST_U3])
#     # don't call add pending members
#     assert cast(MembershipMock, repos.gapo.membership).add_member == TEST_U3
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 3


# def test_add_banned_members_to_collab_requires_admin_approval(container: AppContainer):
#     app = container.app()
#     repos = app.repos
#     usecases = container.participant().usecases()
#     thread_uscs = container.thread().usecases()

#     thread = thread_uscs.create_thread.create_group(
#         [TEST_U2, TEST_U3],
#         TEST_U1,
#         "Test group",
#         group_level=1,
#         workspace_id=1,
#         collab_id=TEST_COLLAB_REQUIRES_ADMIN_APPROVAL_ID,
#     )
#     thread_id = thread["id"]

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 3

#     usecases.ban_participant.ban_member(TEST_U1, thread_id, TEST_U3)

#     # normal user need review
#     usecases.add_participant.add_members(TEST_U2, thread_id, [TEST_U3])
#     assert (
#         cast(MembershipMock, repos.gapo.membership).request_join_calls == 0
#     )  # no request join calls to membership service
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 2


def test_restriction_for_droppi(container: AppContainer):
    app = container.app()
    conf = app.conf
    conns = app.conns
    mysql = conns.mysql
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U1, TEST_U2],
        TEST_U1,
        "Test group",
        workspace_id=conf.miscs.droppi_workspace_id,
        call_source=constant.CALL_SOURCE_API,
    )
    thread_id = p_thread.thread.id

    with pytest.raises(AddingMembersDisabled):
        usecases.add_participant.add_members(
            TEST_U1, thread_id, [TEST_U3], call_source=constant.CALL_SOURCE_API
        )


def test_add_members_to_thread_from_imported_excel_file(
    container: AppContainer,
):
    app = container.app()
    conns = app.conns
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    # add members from file TEST_FILE_ID, which contains TEST_U2, TEST_U3
    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U3], TEST_U1, "Test group"
    )
    thread_id = p_thread.thread.id

    participant_uscs.add_participant.add_members(
        TEST_U1, thread_id, [], member_file_id=TEST_MEMBER_FILE_ID
    )

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3


def test_cannot_add_members_to_sub_thread(container: AppContainer):
    create_thread_usc = container.thread().create_thread()
    participant_uscs = container.participant().usecases()
    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    with pytest.raises(UserNotInThread):
        participant_uscs.add_participant.add_members(
            TEST_U1, sub_thread_id, [TEST_U2]
        )
