import pytest

from chat import action_type
from chat.container import AppContainer
from chat.exception import UserNotInThread
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_add_and_get_thread_participants(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    participant_uscs = container.participant().usecases()
    session_m = create_thread_usc.session_m

    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    # repos.pt.create_group_member(session, thread_id, TEST_U3)
    # should we call this?
    usecases.add_participant.add_members_bg(
        TEST_U1, thread_id, [TEST_U3], action_type.INVITE_MEM, {}
    )

    with session_m.session() as session:
        member = repos.pt.get_member(session, thread_id, TEST_U3)
        assert member is not None

        members = participant_uscs.get_participant.get_all_members(
            [thread_id], ["owner", "admin", "member"]
        )
        assert len(members) == 3


def test_get_thread_participants(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    participant_uscs = container.participant().usecases()
    session_m = create_thread_usc.session_m

    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2


def test_get_thread_participants_without_permission(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    create_thread_usc = container.thread().usecases().create_thread
    session_m = create_thread_usc.session_m

    p_thread = create_thread_usc.create_group([TEST_U2], TEST_U1, "Test group")
    thread_id = p_thread.thread.id
