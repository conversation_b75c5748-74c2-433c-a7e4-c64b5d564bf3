from typing import cast

import pytest

from chat import constant
from chat.container import AppContainer
from chat.exception import UserNotInThread
from chat.publishers.rabbitmq import AmqpProducerMock
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


def test_update_unread(container: AppContainer):
    app = container.app()
    mysql = app.conns.mysql
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # increase message count
    with mysql.get_session() as session:
        repos.thread.update_msg_count(session, thread_id, 10)
        session.commit()

    # try to increase read
    with pytest.raises(UserNotInThread):
        usecases.participant_action.mark_unread(TEST_U3, thread_id, 2)

    # check read
    usecases.participant_action.mark_unread(TEST_U1, thread_id, 3)
    assert (
        thread_uscs.get_thread.get_by_id(
            TEST_U1, thread_id
        ).participant.read_count
        == 2
    )
    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.read_count == 2

    usecases.participant_action.mark_unread(TEST_U1, thread_id, 1)
    assert (
        thread_uscs.get_thread.get_by_id(
            TEST_U1, thread_id
        ).participant.read_count
        == 0
    )
    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.read_count == 0

    # check rabitmq event

    # passing with smaller message_id
    usecases.participant_action.mark_unread(TEST_U1, thread_id, 2)
    assert (
        thread_uscs.get_thread.get_by_id(
            TEST_U1, thread_id
        ).participant.read_count
        == 1
    )
    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.read_count == 1

    # passing with larger current value
    usecases.participant_action.mark_unread(TEST_U1, thread_id, 4)
    assert (
        thread_uscs.get_thread.get_by_id(
            TEST_U1, thread_id
        ).participant.read_count
        == 3
    )
    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.read_count == 3

    # passing with larger value than current message_count
    usecases.participant_action.mark_unread(TEST_U1, thread_id, 11)
    assert (
        thread_uscs.get_thread.get_by_id(
            TEST_U1, thread_id
        ).participant.read_count
        == 10
    )
    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None
    assert member.read_count == 10
