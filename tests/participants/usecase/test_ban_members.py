import pytest

from chat.constant import FOLDER_DEFAULT
from chat.container import AppContainer
from chat.participants import exception as PartError
from tests.conftest import TEST_U1, TEST_U2, TEST_U3


# def test_ban_members(container: AppContainer):
#     app = container.app()
#     repos = app.repos
#     usecases = container.participant().usecases()
#     thread_uscs = container.thread().usecases()
#     mysql = app.conns.mysql

#     thread = thread_uscs.create_thread.create_group(
#         [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
#     )
#     thread_id = thread["id"]

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 3

#     with pytest.raises(PartError.RemoveMemberPermissionError):
#         usecases.ban_participant.ban_member(TEST_U2, thread_id, TEST_U3)

#     usecases.ban_participant.ban_member(TEST_U1, thread_id, TEST_U3)

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 2

#     # cannot add a banned member
#     # NOTE: bypass add member when banned
#     # usecases.add_participant.add_members(TEST_U1, thread_id, [TEST_U3])
#     # members = usecases.get_participant.get_all_members([thread_id])
#     # assert len(members) == 3

#     # # check thread list

#     # # make sure we don't remove anything from TEST_U1
#     # threads = thread_uscs.get_thread.get_all(TEST_U1, [FOLDER_DEFAULT])
#     # assert len(threads) == 1

#     # # test U3
#     # threads = thread_uscs.get_thread.get_all(TEST_U3, [FOLDER_DEFAULT])
#     # assert len(threads) == 1

#     # check ban status
#     # with mysql.get_session() as session:
#     #     thread = repos.pt.get_thread_for_user(session, thread_id, TEST_U3)
#     #     assert thread is None


# def test_ban_member_from_thread_in_folder(container: AppContainer):
#     app = container.app()
#     repos = app.repos
#     mysql = app.conns.mysql
#     usecases = container.participant().usecases()
#     thread_uscs = container.thread().usecases()
#     folder_uscs = container.folder().usecases()

#     thread = thread_uscs.create_thread.create_group(
#         [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
#     )
#     thread_id = thread["id"]

#     # this thread will in a folder of TEST_U3
#     folder = folder_uscs.create(TEST_U3, "test_folder", "", [thread_id], [])
#     folder_id = folder["alias"]
#     # increase thread message counter because
#     # zero-messages thread is considered as deleted
#     with mysql.get_session() as session:
#         repos.thread.incr_thread_msg_counter(session, thread_id)

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 3

#     usecases.ban_participant.ban_member(TEST_U1, thread_id, TEST_U3)

#     # get thread members
#     members = usecases.get_participant.get_all_members([thread_id])
#     assert len(members) == 2

#     # check thread list
#     threads = thread_uscs.get_thread.get_all(TEST_U3, [FOLDER_DEFAULT])
#     assert len(threads) == 0
#     threads = thread_uscs.get_thread.get_all(TEST_U3, [folder])
#     assert len(threads) == 0
