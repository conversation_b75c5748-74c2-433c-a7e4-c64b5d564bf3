from chat.container import AppContainer
from tests.conftest import TEST_U1, TEST_U2, TEST_U3, TEST_U4, TEST_COLLAB_ID
from chat.backgrounds import add_member_collab


def test_join_collab(container: AppContainer):
    usecases = container.thread().usecases()
    leave_thread_usc = usecases.leave_thread
    pt_uscs = container.participant().usecases()
    repos = container.app().repos

    # create private group
    p_thread = usecases.create_thread.create_group(
        [TEST_U1, TEST_U2], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    assert p_thread.thread.group_level == 0
    thread_id = p_thread.thread.id

    member = repos.part_cache.get(thread_id, TEST_U2)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U1)
    assert member is not None

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is None

    member = repos.part_cache.get(thread_id, TEST_U4)
    assert member is None

    usecases.join_thread.join_collab(
        TEST_U3, TEST_COLLAB_ID, "join_chat_by_link"
    )
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3

    member = repos.part_cache.get(thread_id, TEST_U3)
    assert member is not None

    usecases.join_thread.join_collab_bg(
        TEST_U4, TEST_COLLAB_ID, "join_chat_by_link"
    )
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 4

    member = repos.part_cache.get(thread_id, TEST_U4)
    assert member is not None

    leave_thread_usc.leave_group(thread_id, TEST_U4)

    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3

    member = repos.part_cache.get(thread_id, TEST_U4)
    assert member is None

    usecases.join_thread.join_collab_bg(
        TEST_U4, TEST_COLLAB_ID, "join_chat_by_link"
    )
    members = pt_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 4

    member = repos.part_cache.get(thread_id, TEST_U4)
    assert member is not None

    add_member_collab.handle(
        container.log(),
        pt_uscs.add_participant,
        {
            "user_id": TEST_U1,
            "thread_id": thread_id,
            "invite_ids": [TEST_U4],
            "type": "invite_mem",
        },
    )
