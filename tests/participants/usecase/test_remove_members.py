import pytest

from chat.constant import FOLDER_DEFAULT
from chat.container import AppContainer
from chat.exception import UserNotInThread
from chat.participants import exception as PartError

from chat.messages.model import MessageBody

from tests.conftest import TEST_U1, TEST_U2, TEST_U3
from tests.helpers.thread import create_sub_thread_of_group
from tests.helpers.message import create_message


def test_remove_members_in_group(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id
    body = MessageBody(**{"type": "text", "text": "Hello"})
    create_message(app, thread_id, 1, TEST_U1, body, 1)

    _ = thread_uscs.create_thread.create_sub_thread(
        thread_id, 1, TEST_U1, [TEST_U2, TEST_U3]
    )
    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    with pytest.raises(PartError.RemoveMemberPermissionError):
        usecases.ban_participant.remove_member(TEST_U2, thread_id, TEST_U3)

    usecases.ban_participant.remove_member(TEST_U1, thread_id, TEST_U3)

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 2


def test_remove_many_members_in_group(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    with pytest.raises(PartError.RemoveMemberPermissionError):
        usecases.ban_participant.remove_member(TEST_U2, thread_id, TEST_U3)

    usecases.ban_participant.remove_members(
        TEST_U1, thread_id, [TEST_U2, TEST_U3]
    )

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 1


def test_remove_many_members_in_group_2(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    with pytest.raises(PartError.RemoveMemberPermissionError):
        usecases.ban_participant.remove_member(TEST_U2, thread_id, TEST_U3)

    usecases.ban_participant.remove_members_bg(
        TEST_U1, thread_id, [TEST_U2, TEST_U3]
    )

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 1


def test_remove_members_internal(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    with pytest.raises(PartError.RemoveMemberPermissionError):
        usecases.ban_participant.remove_member(TEST_U2, thread_id, TEST_U3)

    usecases.ban_participant.remove_members_internal(
        thread_id, [TEST_U2, TEST_U1]
    )
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 1

    # remove last member
    usecases.ban_participant.remove_members_internal(thread_id, [TEST_U3])
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 0


def test_remove_member_from_thread_in_folder(container: AppContainer):
    app = container.app()
    repos = app.repos
    usecases = container.participant().usecases()
    thread_uscs = container.thread().usecases()
    folder_uscs = container.folder().usecases()
    mysql = app.conns.mysql

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U3], TEST_U1, "Test group", group_level=1
    )
    thread_id = p_thread.thread.id

    # this thread will in a folder of TEST_U3
    folder = folder_uscs.create(TEST_U3, "test_folder", "", [thread_id], [])
    folder_id = folder["alias"]
    # increase thread message counter because
    # zero-messages thread is considered as deleted
    with mysql.get_session() as session:
        repos.thread.incr_thread_msg_counter(session, thread_id)

    # get thread members
    members = usecases.get_participant.get_all_members([thread_id])
    assert len(members) == 3

    usecases.ban_participant.remove_member(TEST_U1, thread_id, TEST_U3)

    u1_threads = usecases.get_participant.get_all_threads_in_folders(
        TEST_U3, [folder_id]
    )
    assert len(u1_threads) == 0

    u1_threads = usecases.get_participant.get_all_threads_in_folders(
        TEST_U3, [FOLDER_DEFAULT]
    )
    assert len(u1_threads) == 0


def test_leaving_sub_thread_if_user_leave_parent_thread(
    container: AppContainer,
):
    create_thread_usc = container.thread().create_thread()
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    thread_uscs.leave_thread.leave_group(thread_id, TEST_U2)

    # now TEST_U2 will leave sub_thread_id
    with pytest.raises(UserNotInThread):
        thread = thread_uscs.get_thread.get_by_id(TEST_U2, sub_thread_id)


def test_removing_sub_threads_if_user_is_removed_from_parent_thread(
    container: AppContainer,
):
    create_thread_usc = container.thread().create_thread()
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    participant_uscs.ban_participant.remove_member(TEST_U1, thread_id, TEST_U2)

    # now TEST_U2 will leave sub_thread_id
    with pytest.raises(UserNotInThread):
        p_thread = thread_uscs.get_thread.get_by_id(TEST_U2, sub_thread_id)


def test_cannot_remove_member_from_sub_thread(container: AppContainer):
    create_thread_usc = container.thread().create_thread()
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    with pytest.raises(UserNotInThread):
        participant_uscs.ban_participant.remove_member(
            TEST_U1, sub_thread_id, TEST_U2
        )


def test_cannot_leave_sub_thread(container: AppContainer):
    create_thread_usc = container.thread().create_thread()
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()

    thread_id, sub_thread_id = create_sub_thread_of_group(
        container, "Test group", TEST_U1, [TEST_U1, TEST_U2]
    )

    with pytest.raises(UserNotInThread):
        thread_uscs.leave_thread.leave_group(sub_thread_id, TEST_U2)
