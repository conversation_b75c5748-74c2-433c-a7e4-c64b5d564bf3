from chat.container import AppContainer
from tests.conftest import TEST_U1


def test_get_thread_members(container: AppContainer):
    member_repo = container.app().repos.pt
    thread_repo = container.app().repos.thread
    session_m = container.app().conns.mysql

    with session_m.get_session() as session:
        thread_id = thread_repo.create_group(
            session, "Test", member_count=5, workspace_id="1"
        )
        session.commit()
        member = member_repo.get_group_for_member(session, thread_id, TEST_U1)
        assert member is None

        member_repo.create_member(session, thread_id, TEST_U1)
        session.commit()
        member = member_repo.get_group_for_member(session, thread_id, TEST_U1)
        assert member is not None

        members = member_repo.get_members_in_list_threads(session, [thread_id])
        assert len(members) == 1

        members = member_repo.get_members_in_list_threads(session, [])
        assert len(members) == 0


def test_get_threads_in_folder(container: AppContainer):
    member_repo = container.app().repos.pt
    thread_repo = container.app().repos.thread
    session_m = container.app().conns.mysql

    with session_m.get_session() as session:
        thread_id = thread_repo.create_group(
            session, "Test", member_count=5, workspace_id="1"
        )
        session.commit()
        member_repo.create_member(session, thread_id, TEST_U1, folder="test")
        session.commit()

        threads = member_repo.get_all_threads_in_folder(session, TEST_U1, "test")
        assert len(threads) == 1


def test_get_thread_owner(container: AppContainer):
    member_repo = container.app().repos.pt
    thread_repo = container.app().repos.thread
    session_m = container.app().conns.mysql

    with session_m.get_session() as session:
        thread_id = thread_repo.create_group(
            session, "Test", member_count=5, workspace_id="1"
        )
        session.commit()
        member_repo.create_owner(session, thread_id, TEST_U1, folder="test")
        session.commit()

        # different methods to check thread owner

        owners = member_repo.get_members_by_roles(session, thread_id, ["owner"])
        assert len(owners) == 1
        assert owners[0].user_id == TEST_U1

        owners = member_repo.get_owners(session, thread_id)
        assert len(owners) == 1
        assert owners[0].user_id == TEST_U1

        owners = member_repo.get_owners(session, 0)
        assert len(owners) == 0

        member = member_repo.get_member(session, thread_id, TEST_U1)
        assert member and member.is_owner()

        owner = member_repo.get_group_for_owner(session, thread_id, TEST_U1)
        assert owner is not None
        assert owner.user_id == TEST_U1
