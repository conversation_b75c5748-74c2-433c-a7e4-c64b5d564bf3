import time
from dataclasses import dataclass
import pytest
from falcon.testing import TestClient
from typing import Any, Optional

from chat.participants.model.api import SchemaAdd  # type: ignore
from chat.container import AppContainer
from chat.model import make_input
from tests.conftest import (
    TEST_MEMBER_FILE_ID,
    TEST_U1,
    TEST_U2,
    TEST_U3,
    user_header,
    service_header,
    TEST_API_KEY,
)


@dataclass
class TestSchemaInput:
    name: str = ""
    data: Any = None
    error: Optional[str] = None


def test_add_members_from_other_threads(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Group")
    src_thread_id = p_thread.thread.id

    p_thread2 = thread_usc.create_group([TEST_U3], TEST_U1, "Test group")
    thread_id = p_thread2.thread.id

    result = client.simulate_post(
        "/participants",
        headers=user_header(TEST_U1),
        json={
            "source_thread_ids": [src_thread_id],
            "type": "add",
            "thread_id": thread_id,
        },
    )
    assert result.status_code == 200


def test_add_members_internal(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    p_thread2 = thread_usc.create_group([TEST_U3], TEST_U1, "Test group")
    thread_id = p_thread2.thread.id

    result = client.simulate_post(
        "/internal/participants",
        headers=service_header(TEST_API_KEY),
        json={
            "participant_ids": [TEST_U2],
            "action": "add",
            "thread_id": thread_id,
            "actor_id": TEST_U1,
        },
    )
    assert result.status_code == 200


def test_remove_members_internal(container: AppContainer, client: TestClient):
    thread_usc = container.thread().usecases().create_thread

    p_thread2 = thread_usc.create_group([TEST_U3], TEST_U1, "Test group")
    thread_id = p_thread2.thread.id

    result = client.simulate_post(
        "/internal/participants",
        headers=service_header(TEST_API_KEY),
        json={
            "participant_ids": [TEST_U3],
            "action": "remove",
            "thread_id": thread_id,
            "actor_id": TEST_U1,
        },
    )
    assert result.status_code == 200


def test_add_members_from_imported_file(
    container: AppContainer, client: TestClient
):
    thread_usc = container.thread().usecases().create_thread

    p_thread = thread_usc.create_group([TEST_U2], TEST_U1, "Group")
    thread_id = p_thread.thread.id

    result = client.simulate_post(
        "/participants",
        headers=user_header(TEST_U1),
        json={
            "member_file_id": TEST_MEMBER_FILE_ID,
            "type": "add",
            "thread_id": thread_id,
        },
    )
    assert result.status_code == 200


@pytest.mark.parametrize(
    "testcase",
    [
        TestSchemaInput(
            name="OK - Group type",
            data={
                "thread_id": int(time.time() * 1000),
                "participant_ids": [2],
            },
        ),
        TestSchemaInput(
            "Add require at least from a source",
            data={
                "thread_id": int(time.time() * 1000),
            },
            error="Add require at least from a source",
        ),
    ],
    ids=lambda vals: vals.name,  # type: ignore
)
def test_api_input_schema(testcase: TestSchemaInput):
    _, errors = make_input(SchemaAdd, testcase.data)
    if testcase.error:
        assert errors is not None and errors[0]["msg"] == testcase.error
    else:
        assert errors is None
