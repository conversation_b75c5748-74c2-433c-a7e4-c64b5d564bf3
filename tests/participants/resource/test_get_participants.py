from falcon.testing import TestClient

from chat.container import AppContainer
from tests.conftest import TEST_API_KEY, TEST_U1, TEST_U2, TEST_U3, service_header


# def test_get_thread_members(container: AppContainer, client: TestClient):
#     thread_usc = container.thread().usecases().create_thread

#     thread = thread_usc.create_group([TEST_U2, TEST_U3], TEST_U1, "Group")
#     thread_id = thread["id"]

#     result = client.simulate_get(
#         f"/internal/participants?thread_ids={thread_id}",
#         headers=service_header(TEST_API_KEY),
#     )
#     assert result.status_code == 200
#     data = result.json["data"]
#     assert len(data) == 3

#     # get owner
#     result = client.simulate_get(
#         f"/internal/participants?thread_ids={thread_id}&roles=owner",
#         headers=service_header(TEST_API_KEY),
#     )
#     assert result.status_code == 200
#     data = result.json["data"]
#     assert len(data) == 1
#     assert data == [TEST_U1]

#     # get normal members
#     result = client.simulate_get(
#         f"/internal/participants?thread_ids={thread_id}&roles=member",
#         headers=service_header(TEST_API_KEY),
#     )
#     assert result.status_code == 200
#     data = result.json["data"]
#     assert len(data) == 2
#     assert set(data) == set([TEST_U2, TEST_U3])
