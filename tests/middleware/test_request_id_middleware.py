import falcon
import pytest
from falcon import testing

from chat.middlewares import RequestID<PERSON>iddleware


@pytest.fixture()
def app():
    middleware = RequestIDMiddleware()

    class PingResource:
        def on_get(self, req, resp):
            resp.media = {"ok": True}

    app = falcon.App(middleware=[middleware])
    app.add_route("/ping", PingResource())
    return app


@pytest.fixture()
def client(app: falcon.App):
    return testing.TestClient(app)


def test_return_request_id(client: testing.TestClient):
    resp = client.simulate_get("/ping")
    assert resp.headers["x-request-id"] != None


def test_pass_request_id(client: testing.TestClient):
    req_id = "test-id"
    resp = client.simulate_get("/ping", headers={"x-request-id": req_id})
    assert resp.headers["x-request-id"] == req_id
