import logging

import falcon
import pytest
from falcon import Request, Response, testing
from pytest import MonkeyPatch

from chat.app import UserAuthResource
from chat.app.resource import GuestResource, InternalServiceResource
from chat.connections.gapo_client.mock import GapoClientMock
from chat.exception import add_error_handlers
from chat.middlewares.authen import AuthorizationMiddleware
from tests.utils import get_test_config


class TestAuthResource(UserAuthResource):
    def on_get(self, req: Request, resp: Response):
        resp.media = {"user_id": self.user_id}
        resp.status = falcon.HTTP_200


class TestDefaultResource(object):
    def on_get(self, req: Request, resp: Response):
        resp.media = {"user_id": self.user_id}  # type: ignore
        resp.status = falcon.HTTP_200


class TestGuestResource(GuestResource):
    def on_get(self, req: Request, resp: Response):
        resp.media = {"message": "ok"}
        resp.status = falcon.HTTP_200


class TestInternalServiceResource(InternalServiceResource):
    def on_get(self, req: Request, resp: Response):
        resp.media = {"message": "ok"}


class TestCaseData(object):
    def __init__(
        self, http_code, user_id=None, role="user", api_key=None, body=None
    ) -> None:
        self.user_id = user_id
        self.role = role
        self.api_key = api_key
        self.http_code = http_code
        self.body = body

    def get_headers(self):
        headers = {}
        if self.user_id:
            headers["x-gapo-user-id"] = self.user_id
        if self.role:
            headers["x-gapo-role"] = self.role
        if self.api_key:
            headers["x-gapo-api-key"] = self.api_key
        return headers

    def check_response(self, res: testing.Result):
        assert self.http_code == res.status_code
        if self.body is not None:
            assert res.json == self.body


@pytest.fixture()
def app(monkeypatch: MonkeyPatch):
    test_conf = get_test_config()
    log = logging.getLogger("test")

    middleware = AuthorizationMiddleware(test_conf, log, GapoClientMock())  # type: ignore

    def get_api_key():
        middleware.key_pair = {"test-service-key": "true"}
        return middleware.key_pair

    middleware.get_api_key_infomation = get_api_key  # type: ignore
    app = falcon.App(middleware=[middleware])
    add_error_handlers(app)
    app.add_route("/guest", TestGuestResource())
    app.add_route("/auth", TestAuthResource())
    app.add_route("/internal", TestInternalServiceResource())
    app.add_route("/default", TestDefaultResource())

    return app


@pytest.fixture()
def client(app: falcon.App):
    return testing.TestClient(app)


@pytest.mark.parametrize(
    "testcase",
    [
        TestCaseData(401, user_id=None, role="user"),
        TestCaseData(401, user_id="1", role="service"),
        TestCaseData(200, user_id="1", role="user", body={"user_id": "1"}),
    ],
)
def test_user_auth_resource(client: testing.TestClient, testcase: TestCaseData):
    rs = client.simulate_get("/auth", headers=testcase.get_headers())
    testcase.check_response(rs)


@pytest.mark.parametrize(
    "testcase",
    [
        TestCaseData(401, user_id=None, role="user"),
        # TestCaseData(401, user_id="1", role="service"),
        # TestCaseData(200, user_id="1", role="user", body={"user_id": "1"}),
    ],
)
def test_default_resource(client: testing.TestClient, testcase: TestCaseData):
    rs = client.simulate_get("/default", headers=testcase.get_headers())
    testcase.check_response(rs)


@pytest.mark.parametrize(
    "testcase",
    [
        TestCaseData(401, user_id=None, role="user"),
        TestCaseData(401, role="service"),
        TestCaseData(200, user_id="1", role="guest"),
        TestCaseData(200, user_id=None, role="guest"),
    ],
)
def test_guess_resource(client: testing.TestClient, testcase: TestCaseData):
    rs = client.simulate_get("/guest", headers=testcase.get_headers())
    testcase.check_response(rs)


@pytest.mark.parametrize(
    "testcase",
    [
        TestCaseData(401, user_id=None, role="user"),
        TestCaseData(401, role="service"),
        TestCaseData(403, role="service", api_key="unknown-key"),
        TestCaseData(200, role="service", api_key="test-service-key"),
    ],
)
def test_internal_resource(client: testing.TestClient, testcase: TestCaseData):
    rs = client.simulate_get("/internal", headers=testcase.get_headers())
    testcase.check_response(rs)
