import pytest

from chat.i18n import T

TEST_CASES = [
    ("Invalid parameters", "vi", "Tham số truyền vào không hợp lệ"),
    ("Invalid parameters", "en", "Invalid parameters"),
    (
        "Invalid parameters",
        "unknown_locale",
        "Tham số truyền vào không hợp lệ",
    ),
    ("Not found string", "vi", "Not found string"),
]


@pytest.mark.parametrize("test_input,locale,expected", TEST_CASES)
def test_i18n(test_input: str, locale: str, expected: str):
    translation = T(test_input, locale=locale)
    assert translation == expected
