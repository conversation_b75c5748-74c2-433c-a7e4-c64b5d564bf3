from typing import cast
import pytest

from chat.backgrounds.kafka.membership import (
    EVENT_ENABLE_FEATURE,
    EVENT_MEMBER_ADDED,
    EVENT_MEMBER_REMOVED,
    EVENT_ROLE_ASSIGNED,
    CollabMember,
    FeatureEnabledEvent,
    FeatureEnabledEventBody,
    MemberAddedEvent,
    MemberAddedEventBody,
    MemberRemovedEvent,
    MemberRemovedEventBody,
    RoleAssignedEvent,
    RoleAssignedEventBody,
    create_event_handler,
)
from chat.connections.gapo_client.membership.membership_client import (
    CollabFeature,
    CollabGroupRole,
)
from chat.publishers.rabbitmq import AmqpProducerMock
from chat.container import AppContainer
from chat.utils.common import now
from tests.conftest import TEST_COLLAB_ID, TEST_U1, TEST_U2, TEST_U3, TEST_U4


@pytest.fixture()
def event_handler(container: AppContainer):
    return create_event_handler(container)


def test_on_feature_enabled(container: AppContainer, event_handler):
    event = FeatureEnabledEvent(
        topic="collaborator_membership",
        routing_key=EVENT_ENABLE_FEATURE,
        issue_at=now(),
        message=FeatureEnabledEventBody(
            data_source=0,
            caller_id=TEST_U1,
            collab_group_id=TEST_COLLAB_ID,
            workspace_id="1",
            feature=CollabFeature.Chat.value,
        ),
    ).dict()

    event_handler(event)

    # check if new group is created
    thread_uscs = container.thread().usecases()
    participant_uscs = container.participant().usecases()
    p_thread = thread_uscs.get_thread.get_thread_id_by_collab(TEST_COLLAB_ID, TEST_U1)
    assert p_thread is not None
    thread_id = p_thread.thread.id

    expected_member_roles = {
        TEST_U1: "owner",
        TEST_U2: "admin",
        TEST_U3: "member",
        TEST_U4: "member",
    }
    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 4  # TEST_U1, TEST_U2, TEST_U3, TEST_U4, TEST_U5
    for member in members:
        user_id = member.user_id
        assert member.role == expected_member_roles[user_id]


def test_on_user_join_collab(container: AppContainer, event_handler):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U1], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    thread_uscs.join_thread.join_by_service(
        [TEST_U3], thread_id, "", "join_chat_by_link"
    )
    thread_uscs.join_thread.join_collab_bg(TEST_U3, TEST_COLLAB_ID, "join_chat_by_link")
    # check if new group is created
    participant_uscs = container.participant().usecases()

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3  # TEST_U1, TEST_U2, TEST_U3
    expected_member_roles = {
        TEST_U1: "owner",
        TEST_U2: "member",
        TEST_U3: "member",
    }
    for member in members:
        user_id = member.user_id
        assert member.role == expected_member_roles[user_id]


def test_on_member_added(container: AppContainer, event_handler):
    broker = cast(AmqpProducerMock, container.app().brokers.message_rb.engine)
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U1], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    thread_uscs.join_thread.join_by_service(
        [TEST_U3], thread_id, "", "join_chat_by_link"
    )
    thread_uscs.join_thread.join_collab_bg(TEST_U3, TEST_COLLAB_ID, "join_chat_by_link")

    # check if new group is created
    participant_uscs = container.participant().usecases()

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 3  # TEST_U1, TEST_U2, TEST_U3
    expected_member_roles = {
        TEST_U1: "owner",
        TEST_U2: "member",
        TEST_U3: "member",
    }
    for member in members:
        user_id = member.user_id
        assert member.role == expected_member_roles[user_id]


def test_on_member_removed(container: AppContainer, event_handler):
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U1], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    event = MemberRemovedEvent(
        topic="collaborator_membership",
        routing_key=EVENT_MEMBER_REMOVED,
        issue_at=now(),
        message=MemberRemovedEventBody(
            data_source=0,
            caller_id=TEST_U1,
            workspace_id="1",
            member=CollabMember(
                user_id=TEST_U2,
                type="normal",
                roles=[CollabGroupRole.Member.value],
                added_by=TEST_U1,
                created_at=now(),
                updated_at=now(),
                collab_group_id=TEST_COLLAB_ID,
            ),
        ),
    ).dict()

    event_handler(event)

    # check if new group is created
    participant_uscs = container.participant().usecases()

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 1  # TEST_U1
    expected_member_roles = {
        TEST_U1: "owner",
    }
    for member in members:
        user_id = member.user_id
        assert member.role == expected_member_roles[user_id]


def test_on_user_role_changed(container: AppContainer, event_handler):
    thread_uscs = container.thread().usecases()

    p_thread = thread_uscs.create_thread.create_group(
        [TEST_U2, TEST_U1], TEST_U1, "Test group", collab_id=TEST_COLLAB_ID
    )
    thread_id = p_thread.thread.id

    event = RoleAssignedEvent(
        topic="collaborator_membership",
        routing_key=EVENT_ROLE_ASSIGNED,
        issue_at=now(),
        message=RoleAssignedEventBody(
            data_source=0,
            caller_id=TEST_U1,
            user_id=TEST_U2,
            workspace_id="1",
            collab_group_id=TEST_COLLAB_ID,
            roles=[CollabGroupRole.Admin.value],
        ),
    ).dict()

    event_handler(event)

    # check if new group is created
    participant_uscs = container.participant().usecases()

    members = participant_uscs.get_participant.get_all_members(
        [thread_id], ["owner", "admin", "member"]
    )
    assert len(members) == 2  # TEST_U1 , TEST_U2
    expected_member_roles = {
        TEST_U1: "owner",
        TEST_U2: "admin",
    }
    for member in members:
        user_id = member.user_id
        assert member.role == expected_member_roles[user_id]
