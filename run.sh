#!/bin/bash

python -m gunicorn \
	--chdir /app \
	--log-level error \
	--keep-alive 600 \
	-w 3 \
	--max-requests 1000000 \
	--max-requests-jitter 1000000 \
	-t 60 \
	--graceful-timeout 10 \
	--limit-request-line 4094 \
	--backlog 128 -b "0.0.0.0:5000" \
	app:api & \

python -m gunicorn \
	--chdir /app \
	--log-level error \
	--keep-alive 600 \
	-w 1 \
	--max-requests 1000000 \
	--max-requests-jitter 1000000 \
	-t 60 \
	--graceful-timeout 10 \
	--limit-request-line 4094 \
	--backlog 128 -b 0.0.0.0:5001 \
	health:api
