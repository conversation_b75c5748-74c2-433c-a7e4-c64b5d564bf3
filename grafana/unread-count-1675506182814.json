{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 16, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{job=\"chat-unread-count-prod\"}[1m]))", "hide": false, "legendFormat": "request_rate", "range": true, "refId": "A"}], "title": "API request rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 9}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{job=\"chat-unread-count-prod\", status_code!=\"200\"}[1m]) or vector(0))", "hide": false, "legendFormat": "request_rate", "range": true, "refId": "A"}], "title": "Error rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 18}, "id": 4, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{job=\"chat-unread-count-prod\"}[1m])) by (status_code)", "hide": false, "legendFormat": "{{status_code}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum(rate(http_requests_total{job=\"chat-unread-count-prod\", status_code!=\"200\"}[1m]))", "hide": false, "legendFormat": "Non2XX", "range": true, "refId": "B"}], "title": "Request rate by status code", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 26}, "id": 2, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{job=\"chat-unread-count-prod\"}[2m])) by (le)) * 1000", "legendFormat": "50th", "range": true, "refId": "50%"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.75, sum(rate(http_request_duration_seconds_bucket{job=\"chat-unread-count-prod\"}[2m])) by (le)) * 1000", "hide": false, "legendFormat": "75th", "range": true, "refId": "75th"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{job=\"chat-unread-count-prod\"}[2m])) by (le)) * 1000", "hide": false, "legendFormat": "99th", "range": true, "refId": "99th"}], "title": "Latency (ms)", "type": "timeseries"}], "refresh": "", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "unread-count", "uid": "4Wlv7_mVk", "version": 13, "weekStart": ""}