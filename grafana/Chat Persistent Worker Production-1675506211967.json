{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 20, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "sum(rate(chat_pw_message_events_total{job=\"chat-persistent-worker-prod\"}[2m]))", "legendFormat": "rps", "range": true, "refId": "A"}], "title": "Events", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(chat_pw_processing_time_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "legendFormat": "50th", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(chat_pw_processing_time_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "99th", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(chat_pw_processing_time_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "95th", "range": true, "refId": "C"}], "title": "Processing time (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(chat_pw_processing_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "legendFormat": "50th", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(chat_pw_processing_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "95th", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(chat_pw_processing_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "99th", "range": true, "refId": "C"}], "title": "Processing delay (ms)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(chat_pw_total_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "legendFormat": "50th", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(chat_pw_total_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "95th", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "HDfoGH54k"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(chat_pw_total_delay_seconds_bucket{job=\"chat-persistent-worker-prod\"}[5m])) by (le)) * 1000 or vector(0)", "hide": false, "legendFormat": "99th", "range": true, "refId": "B"}], "title": "Total delay (ms)", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Chat Persistent Worker Production", "uid": "ztXh4H54z", "version": 15, "weekStart": ""}