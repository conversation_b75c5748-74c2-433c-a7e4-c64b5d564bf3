FROM python:3.8.13-slim as builder
MAINTAINER <EMAIL>

EXPOSE 5000
WORKDIR /app
RUN apt-get update && apt-get install -y gunicorn git-core gettext-base default-libmysqlclient-dev gcc && pip install 'pipenv==2022.10.25' && rm -rf /var/lib/apt/lists/*

# set prometheus multiproc_dir
# see: https://shiriev.ru/prometheus-multiprocessing/
ENV PROMETHEUS_MULTIPROC_DIR=.

ADD Pipfile Pipfile.lock /app/

RUN --mount=type=cache,target=/root/.cache pip3 install cython==0.29.30 \
    && pip3 install -v --no-binary :all: falcon==3.1.1 \
    && pipenv install --system --deploy
# RUN pip3 install cython==0.29.30
# RUN pip3 install -v --no-binary :all: falcon==3.1.1



# RUN pipenv install --system --deploy

ADD . /app

RUN chmod +x /app/run.sh

ENTRYPOINT ["/app/entrypoint.sh"]

# meinheld worker
# Note that we are using custom version
# of meinheld to support later version of greenlet
# merge request: https://github.com/mopemope/meinheld/pull/123
# Issue: https://github.com/benoitc/gunicorn/issues/2541#issuecomment-800353993
# CMD ["gunicorn", "--chdir", "/app", "--worker-class", "meinheld.gmeinheld.MeinheldWorker", "--log-level", "error", "--keep-alive", "600", "-w", "3","--max-requests", "1000000","--max-requests-jitter", "1000000", "-t", "60", "--graceful-timeout", "10","--limit-request-line", "4094","--backlog", "128","-b", "0.0.0.0:5000", "main:api"]

# Rollback if you have trouble with meinheld worker
#CMD ["gunicorn", "--chdir", "/app", "--log-level", "error", "--keep-alive", "600", "-w", "3","--max-requests", "1000000","--max-requests-jitter", "1000000", "-t", "60", "--graceful-timeout", "10","--limit-request-line", "4094","--backlog", "128","-b", "0.0.0.0:5000", "main:api"]
CMD ["/app/run.sh"]
