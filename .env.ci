CASSANDRA_HOST="cassandra"
CASSANDRA_USERNAME=""
CASSANDRA_PASSWORD=""
CASSANDRA_KEYSPACE="chat"
BUCKET_MESSAGE_FACTOR=2

# RABBITMQ
RABBITMQ_HOST="rabbitmq"
RABBITMQ_PORT=5672
RABBITMQ_VHOST="rabbitmq"
RABBITMQ_URLS="amqp://admin:admin@rabbitmq:5672/chat?heartbeat=30"
RABBITMQ_USERNAME="admin"
RABBITMQ_PASSWORD="admin"
RABBITMQ_MESSAGE_QUEUE_NAME="message_queue"
RABBITMQ_MESSAGE_EXCHANGE_NAME="message_exchange"
RABBITMQ_MESSAGE_ROUTING_KEY="message_routing_key"


# gapo config
GAPO_IS_FRIEND_URL=""
GAPO_IS_FRIENDS_URL=""

GAPO_USER_INFO_URL=""
GAPO_USER_API_KEY=""

GAPO_PAGE_COLLECTION_URL=""
GAPO_PAGE_PERMISSION_URL=""
GAPO_PAGE_ITEM_URL=""

GAPO_INTERNAL_REGISTER_TOPIC_NOTIFY_URL=""
GAPO_INTERNAL_UNREGISTER_TOPIC_NOTIFY_URL=""

MOBILE_PUSH_API_KEY=""

GAPO_BLOCK_USER_URL=""
GAPO_UNBLOCK_USER_URL=""

GAPO_RELATION_V2_API_KEY=""
GAPO_RELATION_V2_URL=""

GAPO_CONTACT_API_KEY=""
GAPO_CONTACT_URL=""

GAPO_REACT_URL=""
GAPO_REACT_API_KEY=""

GAPO_FEATURE_URL = "https://staging-api.gapowork.vn/features-config/v1.0/"
GAPO_FEATURE_API_KEY = "gapo-workspace-MjNlY2E2ZD"

GAPO_WORKSPACE_URL=""
GAPO_WORKSPACE_API_KEY=""

MEMBERSHIP_URL=gapo-collab-membership.default.svc.cluster.local.:10000


BOT_BASE_URL=""
BOT_API_KEY=""

POLL_BASE_URL=""
POLL_API_KEY=""

ORGC_BASE_URL=""
ORGC_API_KEY=""

GROUP_BASE_URL=""
GROUP_API_KEY=""

ORGC_V3_BASE_URL=""
ORGC_V3_API_KEY=""

MYSQL_URL="mysql://admin:admin@mysql:3306/chat?use_unicode=1&charset=utf8mb4"

REDIS_HOST="redis"
REDIS_PASSWORD="redis"
REDIS_DB=8
REDIS_PORT=6379

REDIS__PERSISTENT_CHAT_HOST="redis"
REDIS__PERSISTENT_CHAT_PASSWORD="redis"
REDIS__PERSISTENT_CHAT_DB=8
REDIS__PERSISTENT_CHAT_PORT=6379

REDIS__THREAD_CHAT_HOST="redis"
REDIS__THREAD_CHAT_PASSWORD="redis"
REDIS__THREAD_CHAT_DB=7
REDIS__THREAD_CHAT_PORT=6379

REDIS__MESSAGE_CHAT_HOST="redis"
REDIS__MESSAGE_CHAT_PASSWORD="redis"
REDIS__MESSAGE_CHAT_DB=7
REDIS__MESSAGE_CHAT_PORT=6379

BAD_COMMENT_KEY="CHAT-BAD-COMMENT"
LIMIT_PIN_THREAD=3


REDIS_CACHE_HOST="redis"
REDIS_CACHE_PASSWORD="redis"
REDIS_CACHE_PORT=6379
REDIS_CACHE_DB=8


SENTRY_DSN=""


# MQTT
MQTT_HOST="mqtt"
MQTT_PORT="1883"
MQTT_ADMIN_USERNAME="admin"
MQTT_ADMIN_PASSWORD="admin"

CDN_MAPPING_FILEPATH="./cdn_mapping_example.json"

CHAT_DOMAIN="https://gapowork.vn"

USE_JSON_LOG=True

IAM_API_KEY=""
GAPO_IAM_URL=""

TTL_EDIT_MESSAGE = 1000
TTL_MESSAGE_DELETION = 1000

KAFKA_BOOTSTRAP_SERVERS = ""


# Feature flags
FEATURE__DISABLE_USER_BLOCKING=false


# Misc configs
MISC__DROPPI_WORKSPACE_ID=582734851238186
MISC__DROPPI_LIMIT_MEMBERS_CREATING_CHAT=10


# Attach error trace to responses to 5xx errors
# This help debug problem faster in Staging/UAT.
# However, this SHOULDN'T be enabled in PRODUCTION
# because it may LEAK some information about server.
INCLUDE_SERVER_ERROR_TRACE=false
GAPO_CALL_BOT_ID="5843186210444538880"
