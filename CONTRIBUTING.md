> Modified from https://github.com/portainer/portainer/blob/develop/CONTRIBUTING.md
> and https://karma-runner.github.io/6.3/dev/git-commit-msg.html

# Contributing Guidelines

Some basic conventions for contributing to this project.

## General

Please make sure that there aren't existing pull requests attempting to address the issue mentioned. Likewise, please check for issues related to update, as someone else may be working on the issue in a branch or fork.

- Please open a discussion in a new issue / existing issue to talk about the changes you'd like to bring
- <PERSON><PERSON><PERSON> in a topic branch, not master/develop

When creating a new branch, prefix it with the _type_ of the change (see section **Commit Message Format** below), the associated opened issue number, a dash and some text describing the issue (using dash as a separator).

For example, if you work on a bugfix for the issue #361, you could name the branch `fix361-template-selection`.


## Commit Message Format


Each commit message should include a **type**, a **scope** and a **subject**:

```
 <type>(<scope>): <subject>
```

Lines should not exceed 100 characters. This allows the message to be easier to read on github as well as in various git tools and produces a nice, neat commit log ie:

```
 #271 feat(containers): add exposed ports in the containers view
 #270 fix(templates): fix a display issue in the templates view
 #269 style(dashboard): update dashboard with new layout
```

Example from our repo:

![commit-log](./docs/asssets/../assets/commit-logs.png)

### Type

Must be one of the following

- **feat**: New feature for the user, not a new feature for build script. Such commit will trigger a release bumping a MINOR version. 
- **fix**: Bug fix for the user, not a fix to a build script. Such commit will trigger a release bumping a PATCH version.
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing
  semi-colons, etc)
- **refactor**: Refactoring production code, e.g. renaming a variable.
- **test**: Adding missing tests, refactoring tests; no production code change.
- **perf**: Performance improvements. Such commit will trigger a release bumping a PATCH version.
- **chore**: Changes to the build process or auxiliary tools and libraries such as documentation.

### Scope (optional)

The scope could be anything specifying place of the commit change.

### Subject

The subject contains succinct description of the change:

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Don't capitalize first letter
- No dot (.) at the end
