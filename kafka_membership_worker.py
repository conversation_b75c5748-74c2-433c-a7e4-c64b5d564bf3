"""
Kafka message worker

This worker will consumes all events from
membership service and update chat database.

"""
import sys

from chat.backgrounds.kafka.membership import setup_kafka_worker
from chat.container import AppContainer

if __name__ == "__main__":
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])

    container.check_dependencies()

    setup_kafka_worker(container)
