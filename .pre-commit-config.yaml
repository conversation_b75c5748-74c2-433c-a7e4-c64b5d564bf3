exclude: >
  (?x)(
      LICENSE$|
      ^migrations/mysql/|
      ^migrations/cql/|
      ^chat/connections/gapo_client/membership/proto/ 
  )
fail_fast: true
repos:
  - repo: local
    hooks:
      - id: autoflake
        name: Remove unused variables and imports
        entry: bash -c 'autoflake "$@"; git add -u' --
        language: python
        args:
          ["--in-place", "--remove-all-unused-imports", "--expand-star-imports"]
        types: [python]
        require_serial: true
        files: \.py$
      - id: isort
        name: Sorting import statements
        entry: bash -c 'isort "$@"; git add -u' --
        language: python
        args: ["--profile", "black"]
        types: [python]
        require_serial: true
        files: \.py$
      - id: black
        name: Black Python code formatting
        entry: bash -c 'black "$@"; git add -u' --
        language: python
        types: [python]
        require_serial: true
        files: \.py$
      # - id: pyright
      #   name: Check errors with pyright
      #   entry: bash -c 'pyright "$@"; git add -u' --
      #   language: python
      #   files: \.py$
      #   types: [python]
      #   require_serial: true
      #   args: ["--project", "pyrightconfig-commithook.json"]
      - id: mypy
        name: Check errors with mypy
        files: '.*\.py[i]?'
        entry: mypy 
        # entry: ./dev-scripts/mypy-check.sh
        language: python
        types: [python]
        require_serial: true
      - id: babel
        name: Check translation files
        files: \.po$
        entry: ./dev-scripts/check-translation.sh
        additional_dependencies:
          - polib
        language: python
        require_serial: true
