#!/bin/bash

display_message () {
    echo -e "\e[32m$1\e[0m"
}

# Generate membership client
display_message "Generating membership GRPC client ..."
output="./chat/connections/gapo_client/membership/proto"
python -m grpc_tools.protoc \
  -I ./chat/connections/gapo_client/membership/proto \
  --python_out=$output \
  --grpc_python_out=$output \
  --mypy_grpc_out=$output --mypy_out=$output \
  chat/connections/gapo_client/membership/proto/membership.proto

# Fix imports
sed -i "s/import membership_pb2/from . import membership_pb2/g" chat/connections/gapo_client/membership/proto/membership_pb2.pyi
sed -i "s/import membership_pb2/from . import membership_pb2/g" chat/connections/gapo_client/membership/proto/membership_pb2_grpc.pyi
sed -i "s/import membership_pb2 as membership__pb2/from . import membership_pb2 as membership__pb2/g" chat/connections/gapo_client/membership/proto/membership_pb2_grpc.py

display_message "Done!"