#!/bin/bash

# exit when any command fails
set -e

display_message () {
    echo -e "\e[32m$1\e[0m"
}

# setup for CI step
if [ "$ENV" == "ci" ]; then 
    display_message 'Preparing cassandra schema...'
    cqlsh -f cassandra-schema.cql cassandra > /dev/null 2>&1 

    # clear some variables
    # for unknown reason, some variables are set with wrong values
    # so we have to unset them before running tests
    # See example https://gitlab.gapo.com.vn/chat/chat-api/-/jobs/130430#L877
    # Maybe we forget to set variable and gitlab runner to set them with some value
    display_message 'Overwrite some environment variables...'
    unset RABBITMQ_PORT
    unset MQTT_PORT
    unset REDIS_PORT
    unset CASSANDRA_PORT
fi

display_message 'Checking translation file ...'
python ./dev-scripts/check_translation_files.py
display_message 'Translation files are ok!'

# display_message '[pyright] Checking syntax ...'
# pyright --project pyrightconfig-commithook.json
# display_message '[pyright] Syntax checked!'


display_message '[mypy] Checking errors with mypy ...'
mypy --install-types --non-interactive chat 
PYTHONPATH=. mypy chat 
display_message '[mypy] Checked errors with mypy!'

display_message 'Running tests ...'
pipenv run test-cov