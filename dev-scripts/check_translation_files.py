"""Check translation files.

Check if any message to be translated is missing.
"""

import polib

translation_text_files = [
    {"path": "./locales/en/LC_MESSAGES/base.po", "check": False},
    {"path": "./locales/vi/LC_MESSAGES/base.po", "check": True},
]

translation_binary_files = [
    {"path": "./locales/vi/LC_MESSAGES/base.mo", "check": True},
]


def check_text_file(p, check_empty_msg=True):
    po = polib.pofile(p)
    is_ok = True
    msg_ids = set()
    for entry in po:
        if check_empty_msg and entry.msgstr == "":
            print('- Missing trans: "{}"'.format(entry.msgid))
            is_ok = False
        msg_ids.add(entry.msgid)
    return msg_ids, is_ok


def check_binary_file(p, check_empty_msg=True):
    mo_file = polib.mofile(p)
    msg_ids = set()
    is_ok = True
    for entry in mo_file:
        msg_ids.add(entry.msgid)
        if check_empty_msg and entry.msgstr == "":
            is_ok = False

    return msg_ids, is_ok


if __name__ == "__main__":
    keys = []
    for text_file in translation_text_files:
        file_path = text_file["path"]
        check_empty_msg = text_file["check"]
        print("Checking file: {}".format(file_path))
        msg_ids, is_ok = check_text_file(file_path, check_empty_msg)
        print(
            "File: {}, Num keys: {}, is_ok: {}".format(
                file_path, len(msg_ids), is_ok
            )
        )
        if not is_ok:
            exit(1)
        keys.append(msg_ids)

    for binary_file in translation_binary_files:
        file_path = binary_file["path"]
        check_empty_msg = binary_file["check"]
        print("Checking file {}".format(file_path))
        msg_ids, is_ok = check_binary_file(file_path, check_empty_msg)
        print(
            "File: {}, Num keys: {}, is_ok: {}".format(
                file_path, len(msg_ids), is_ok
            )
        )
        if not is_ok:
            exit(1)
        keys.append(msg_ids)

    if len(keys) >= 2:
        for i in range(0, len(keys) - 1):
            if keys[i] != keys[i + 1]:
                print(keys[i])
                print(keys[i + 1])
                print(
                    'There are some difference between po and bo files. Don\'t forget to run "pipenv run update-i18n"'
                )
                exit(1)
