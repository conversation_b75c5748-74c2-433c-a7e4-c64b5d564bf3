"""Check db revision.

Check if the revision of database matches our revision.

This will query info from database and in migrations folder.
"""

import os
from glob import glob
from os import path

import requests


def get_repo_revision():
    root_folder = path.dirname(__file__)
    revision_folder = path.abspath(
        path.join(root_folder, "../migrations/mysql/versions")
    )

    migration_files = glob(revision_folder + "/*.py")
    cur_vesion = 0
    for f in migration_files:
        f = path.basename(f)
        version = int(f.split("_")[0])
        if cur_vesion < version:
            cur_vesion = version
    return cur_vesion


def get_current_revision():
    branch = os.environ.get("DEPLOY_BRANCH")
    if branch == "prod":
        url = "https://messenger.gapowork.vn/chat/v3.3/healthz"
    else:
        url = "https://staging-messenger.gapowork.vn/chat/v3.3/healthz"
    resp = requests.get(url)
    json = resp.json()
    if "revision" in json:
        return json["revision"]

    return None


def main():
    disable_check = os.environ.get("DISABLE_REVISION_CHECK")
    if disable_check == "true":
        print("Disable revision check because DISABLE_REVISION_CHECK is set to true")
        return

    revision = get_repo_revision()
    api_revision = get_current_revision()
    if api_revision is None:
        print("Cannot detect db revision from API, ignore check")
        return

    # if we rollback, our revision will be smaller
    # should we do strict checking here?
    if revision > api_revision:
        print(
            "Migration action needed, current db revision: {}, expected: {}".format(
                api_revision, revision
            )
        )
        exit(1)

    print("Db schema is up to date, current revision: {}".format(revision))


if __name__ == "__main__":
    main()
