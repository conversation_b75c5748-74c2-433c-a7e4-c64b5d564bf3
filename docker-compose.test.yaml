version: "2"
services:
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: 'chat-rb'
    ports:
      - 5672:5672
      - 15672:15672
    volumes:
      - ./data/rabbitmq/data/:/var/lib/rabbitmq/
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
      - RABBITMQ_DEFAULT_VHOST=chat
  mqtt:
    image: eclipse-mosquitto:2.0
    network_mode: host
    volumes:
      - ./data/mqtt/conf:/mosquitto/conf
      - ./data/mqtt/data:/mosquitto/data
      - ./data/mqtt/log:/mosquitto/log
  db:
    container_name: chat-db
    image: mysql:8
    command: ["mysqld", "--mysql-native-password=ON"]
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=chat
      - MYSQL_USER=admin
      - MYSQL_PASSWORD=admin
    volumes:
      - ./data/mysql:/var/lib/mysql
    ports:
      - 3306:3306

  redis:
    container_name: chat-redis
    image: 'bitnami/redis:latest'
    environment:
      - REDIS_PASSWORD=redis
    ports:
      - '6379:6379'


  cassandra:
    container_name: chat-cassandra
    image: 'cassandra:4'
    ports:
      - 9042:9042
    environment:
      - "MAX_HEAP_SIZE=256M"
      - "HEAP_NEWSIZE=128M"
    volumes:
      - ./data/cassandra:/var/lib/cassandra
      - ./cassandra-schema.cql:/sql.cql

