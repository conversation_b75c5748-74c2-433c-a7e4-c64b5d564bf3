"""
Fix inconsistency between redis
and database related to pin folder.


"""
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from time import time
from typing import Dict

import click
import structlog
from tqdm import tqdm

from chat.connections.mysql_c import MySQLConnection
from chat.connections.redis_c import RedisConnection

logger = structlog.get_logger()
BATCH_SIZE = 50

YODY_WORKSPACE_ID = "582669684739408"


def pin_count_key(user_id, folder):
    if folder == "default":
        return f"{user_id}_threads_pin"

    return f"{user_id}_threads_pin_{folder}"


def folder_score_key(user_id, folder):
    if folder == "default":
        return f"{user_id}_threads"

    return f"{user_id}_threads_{folder}"


def fix_thread_score_for_user(
    mysql_c: MySQLConnection,
    redis_c: RedisConnection,
    user_id: str,
    threads=8,
):
    # logger.info("Fixing thread score for user: {}".format(user_id))
    with mysql_c.get_session() as session:
        query = (
            "select p.user_id, p.thread_id, p.folder, p.pin_default_pos, p.pin_pos, p.is_removed, p._updated "
            "from participant_threads as p "
            "join threads as t on p.thread_id = t.id "
            "where t.message_count > p.delete_to and p.user_id=:user_id"
        )
        result = session.execute(query, {"user_id": user_id})
        user_pinned = defaultdict(defaultdict)  # type: ignore
        user_thread_scores = defaultdict(defaultdict)  # type: ignore
        # logger.info("Loading data from database ...")
        ti = time()

        total = 0

        while True:
            chunk = result.fetchmany(100)
            if not chunk:
                break

            # logger.info("Chunk len: {}".format(len(chunk)))
            total += len(chunk)
            for row in chunk:
                user_id = row["user_id"]
                thread_id = int(row["thread_id"])
                folder = row["folder"]
                pin_pos = row["pin_pos"]
                pin_default_pos = row["pin_default_pos"]
                is_removed = row["is_removed"]
                updated_at = row["_updated"]

                if user_id == "system" or is_removed:
                    continue

                if not folder in user_pinned[user_id]:
                    user_pinned[user_id][folder] = {}
                    user_thread_scores[user_id][folder] = {}

                if not "default" in user_pinned[user_id]:
                    user_pinned[user_id]["default"] = {}
                    user_thread_scores[user_id]["default"] = {}

                if folder != "default":
                    # set score to folder
                    thread_score = pin_pos * 1000 if pin_pos > 0 else updated_at
                    user_thread_scores[user_id][folder][thread_id] = thread_score

                # set score for thread in default folder
                thread_default_score = (
                    pin_default_pos * 1000 if pin_default_pos > 0 else updated_at
                )
                user_thread_scores[user_id]["default"][thread_id] = thread_default_score

                if pin_default_pos > 0:
                    user_pinned[user_id]["default"][thread_id] = pin_default_pos
                elif pin_pos > 0 and folder != "default":
                    user_pinned[user_id][folder][thread_id] = pin_pos

        ti = time() - ti
        # logger.info("Data loaded in {:.2f}s!".format(ti))

        def sync_user_pinned_threads_list(args):
            (user_id, pinned) = args
            total_deletions = 0
            total_insertions = 0
            batch_size = 0
            redis_pipe = redis_c.client.pipeline()

            for folder, thread_pinned_times in pinned.items():
                redis_pin_count_key = pin_count_key(user_id, folder)
                redis_folder_score_key = folder_score_key(user_id, folder)

                folder_thread_scores = user_thread_scores[user_id][folder]

                thread_ids = set(thread_pinned_times.keys())
                redis_thread_ids = set(
                    [
                        int(th)
                        for th in redis_c.client.zrange(redis_pin_count_key, 0, -1)
                    ]
                )
                to_delete = redis_thread_ids - thread_ids
                to_insert = thread_ids - redis_thread_ids
                to_insert = thread_ids  # try overwrite pinned values

                if len(to_delete) > 0:
                    # remove all scores related to thread in redis

                    # logger.info(
                    #     "Removing in redis: user_id: {} , folder: {} , threads: {}".format(
                    #         user_id, folder, to_delete
                    #     )
                    # )

                    # overwrite thread score if it exists in db but unpinned
                    to_update: Dict[int, int] = {}
                    for thread_id in to_delete:
                        if thread_id in folder_thread_scores:
                            thread_score = folder_thread_scores[thread_id]
                            to_update[thread_id] = thread_score

                    redis_pipe.zrem(redis_pin_count_key, *to_delete)
                    redis_pipe.zrem(redis_folder_score_key, *to_delete)
                    batch_size += 2

                    if len(to_update) > 0:
                        redis_pipe.zadd(redis_folder_score_key, to_update)  # type: ignore
                        batch_size += 1

                    total_deletions += len(to_delete)

                    if batch_size == BATCH_SIZE:
                        batch_size = 0
                        redis_pipe.execute()

                if len(to_insert) > 0:
                    thread_scores = {}
                    for th in to_insert:
                        thread_scores[th] = thread_pinned_times[th] * 1000

                    redis_pipe.zadd(redis_pin_count_key, thread_scores)
                    redis_pipe.zadd(redis_folder_score_key, thread_scores)
                    # logger.info(
                    #     "Inserting to redis: user_id: {} , folder: {} , threads: {}, thread_scores: {}".format(
                    #         user_id, folder, to_insert, thread_scores
                    #     )
                    # )

                    total_insertions += len(to_insert)
                    batch_size += 2

                    if batch_size == BATCH_SIZE:
                        batch_size = 0
                        redis_pipe.execute()

            if batch_size > 0:
                redis_pipe.execute()

            return (total_insertions, total_deletions)

        # logger.info("Processing data with {} threads".format(threads))
        with ThreadPoolExecutor(max_workers=threads) as executor:
            jobs = [(user_id, pinned) for (user_id, pinned) in user_pinned.items()]
            results = list(executor.map(sync_user_pinned_threads_list, jobs))
            total_deletions = 0
            total_insertions = 0
            for r in results:
                total_deletions += r[1]
                total_insertions += r[0]
            # logger.info(
            #     "Total insertion: {}, total deletions: {}".format(
            #         total_insertions, total_deletions
            #     )
            # )

    # logger.info("Fixed for workspace {} !".format(user_id))


@click.command()
@click.option("--threads", type=click.INT, default=8)
@click.option("--user_id", type=click.STRING, default="")
@click.argument(
    "redis_host",
    type=click.STRING,
)
@click.argument("redis_port", type=click.INT)
@click.argument("redis_db", type=click.STRING)
@click.argument("redis_password", type=click.STRING)
@click.argument("mysql_url", type=click.STRING)
def main(threads, user_id, redis_host, redis_port, redis_db, redis_password, mysql_url):
    redis_c = RedisConnection(
        "Test", redis_host, redis_port, redis_db, redis_password, logger
    )
    mysql_c = MySQLConnection("test", mysql_url, logger)

    logger.info("Connecting to db ...")
    redis_c.connect()
    mysql_c.connect()
    logger.info("Connected to db!")

    total = 0

    if user_id:
        fix_thread_score_for_user(mysql_c, redis_c, user_id, threads)
        logger.info("Fixed for {}".format(user_id))
        return

    # query all workspaces
    logger.info("Querying users that have pinned threads...")
    ti = time()
    user_ids = []
    with mysql_c.get_session() as session:
        query = "select DISTINCT user_id from participant_threads where pin_default_pos > 0 or pin_pos > 0"
        result = session.execute(query)
        while True:
            chunk = result.fetchmany(100)
            if not chunk:
                break

            # logger.info("Chunk len: {}".format(len(chunk)))
            total += len(chunk)
            for row in chunk:
                user_id = row["user_id"]
                user_ids.append(user_id)

    dt = time() - ti
    logger.info("Query runs in {:.2f}s, Total users: {}".format(dt, len(user_ids)))
    for user_id in tqdm(user_ids):
        fix_thread_score_for_user(mysql_c, redis_c, user_id, threads)

    logger.info("Done!")


if __name__ == "__main__":
    main()  # type: ignore
