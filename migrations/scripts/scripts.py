import os
from datetime import datetime
from math import ceil

import requests
from logzero import logger

# from core import create_session
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

from chat.config import from_pyfile

"""'
def migrate(config=None):
    if config is None:
        root_path = os.path.dirname(os.path.abspath(__file__))
        config, err = from_pyfile(root_path, "config.py")
        if err is False:
            raise Exception("Config can't import")
    sql_config = config["GAPO_SQLALCHEMY"]
    engine = create_engine(sql_config["URL"], pool_pre_ping=True)
    session_factory = sessionmaker(bind=engine)
    sql_session = scoped_session(session_factory)
    session = create_session(config)
    user_count = sql_session.execute("SELECT COUNT(*) FROM tbl_user").first()[0]
    parts = [i * 1000 for i in range(0, ceil(user_count / 1000))]
    insert_user = "INSERT INTO users (id, name) VALUES (%s, %s)"    # noqa
    for p in parts:
        rows = sql_session.execute(
            "SELECT id, display_name FROM tbl_user ORDER BY id LIMIT :offset, 1000",    # noqa
            {
                "offset": p
            }).fetchall()
        batch = BatchStatement()
        for r in rows:
            batch.add(SimpleStatement(insert_user),
                      (str(r["id"]), r["display_name"]))
        session.execute(batch)
"""


def migrate_workspace_id(config=None):
    logger.info("==========START MIGRATE WORKSPACE==========")
    start_time = datetime.now()
    if config is None:
        root_path = os.path.dirname(os.path.abspath(__file__))
        config, err = from_pyfile(root_path, "config.py")
        if err is False:
            raise Exception("Config can't import")

    gapo_config = config["GAPO"]
    workspace_url = f"{gapo_config['WORKSPACE_URL']}/internal/users/multi"
    header = {
        "x-gapo-role": "service",
        "x-gapo-api-key": gapo_config["WORKSPACE_API_KEY"],
    }
    sql_config = config["MYSQL"]
    engine = create_engine(sql_config["URL"], pool_pre_ping=True)
    session_factory = sessionmaker(bind=engine)
    sql_session = scoped_session(session_factory)
    request_session = requests.Session()
    logger.info(f"Config workspace url: {workspace_url}, {header} ")

    owner_threads = sql_session.execute(
        """
        SELECT pt.user_id, t.id FROM threads as t JOIN participant_threads as pt
        ON t.id=pt.thread_id WHERE t.type='group' AND pt.role='owner';
        """
    ).all()

    UPDATE_THREAD_Q = (
        "UPDATE threads SET workspace_id=:workspace_id WHERE id IN :thread_ids"
    )
    user_thread = {}
    user_ids = []
    for ti in owner_threads:
        user_id = ti[0]
        thread_id = ti[1]
        if user_id in ["system", "undefined"]:
            continue
        if user_id not in user_thread:
            user_thread[user_id] = [thread_id]
        else:
            user_thread[user_id].append(thread_id)

    user_ids = list(user_thread.keys())
    logger.debug(f"user_thread: {user_thread}")
    logger.info(f"user_ids: {user_ids} len: {len(user_ids)}")
    parts = [i * 100 for i in range(0, ceil(len(user_ids) / 100))]
    for p in parts:
        temp_uid = user_ids[p : (p + 100)]
        params = {"user_ids": ",".join(temp_uid)}
        logger.info(f"user_id: params")
        res = request_session.get(
            url=workspace_url,
            params=params,
            headers=header,
            timeout=0.5,
            verify=False,
        )

        if res.status_code == 200:
            result = res.json()
            data = result["data"]
            for d in data:
                logger.info(f"item: {d}")
                if not d:
                    continue
                if d.get("workspace_id"):
                    user_id = str(d["id"])
                    thread_ids = user_thread[user_id]
                    workspace_id = d["workspace_id"]
                    # logger.info(f"owner_id: {user_id}, thread_id: {thread_ids}, workspace_id: {workspace_id}")
                    sql_session.execute(
                        UPDATE_THREAD_Q,
                        {"workspace_id": workspace_id, "thread_ids": thread_ids},
                    )
        else:
            logger.error(res.text)

    sql_session.commit()
    total_time = datetime.now() - start_time
    logger.info("==========DONE MIGRATE WORKSPACE==========")
    logger.info(f"Total time: {total_time}")


if __name__ == "__main__":
    # migrate()
    migrate_workspace_id()
