"""
This script allows us to sync colab members between chat database
and membership service.

Usage:

To sync all collabs:

```
python migration_scripts/04_sync_collab_members.py --all_chats true
```

To sync some collabs:

```
python migration_scripts/04_sync_collab_members.py --collab_ids 123,2,4
```

or:

```
python migration_scripts/04_sync_collab_members.py --thread_ids 1234,1567
```

"""
import sys
from dataclasses import dataclass
from logging import Logger
from typing import Dict, List, TypedDict

import click
import structlog
from sqlalchemy.orm import Session

from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
    MemberAlreadyExist,
)
from chat.container.app import AppContainer

logger: Logger = structlog.get_logger()


class ChatThread(TypedDict):
    id: int
    name: str
    owner: str
    group_level: int
    workspace_id: str
    collab_id: str


@dataclass
class ChatMember:
    user_id: str
    thread_id: int
    role: str


ROLE_MAP: Dict[str, CollabGroupRole] = {
    "owner": CollabGroupRole.Owner,
    "admin": CollabGroupRole.Admin,
    "member": CollabGroupRole.Member,
}


def map_member_to_collab(members: List[dict]):
    return [
        (
            int(member["user_id"]),
            [ROLE_MAP[member["role"]]],
            member["is_removed"],
        )
        for member in members
    ]


def get_owner(members: List[dict]):
    for member in members:
        if member["role"] == "owner":
            return member


def load_collab_by_id(session: Session, collab_id: str):
    query = (
        "SELECT t.id, t.name, t.group_level, t.workspace_id, t.collab_id "
        "FROM threads as t WHERE t.collab_id=:collab_id"
    )
    result = session.execute(query, {"collab_id": collab_id})
    row = result.fetchone()
    if not row:
        return None
    return dict(row)


def load_thread_by_id(session: Session, thread_id: str):
    query = (
        "SELECT t.id, t.name, t.group_level, t.workspace_id, t.collab_id "
        "FROM threads as t WHERE t.id=:thread_id"
    )
    result = session.execute(query, {"thread_id": thread_id})
    row = result.fetchone()
    if not row:
        return None
    return dict(row)


def update_thread_collab(session: Session, thread_id: str, collab_id: str):
    query = (
        "UPDATE threads " " SET collab_id=:collab_id " "WHERE id= :thread_id "
    )
    session.execute(query, {"thread_id": thread_id, "collab_id": collab_id})


def resurrention_owner(session, thread_id: int, owner_id: str):
    query = (
        "UPDATE participant_threads "
        " SET is_removed=False, role=:role "
        "WHERE thread_id= :thread_id "
        "AND user_id=:user_id "
    )
    session.execute(
        query, {"role": "owner", "thread_id": thread_id, "user_id": owner_id}
    )


def get_members_in_thread(session, thread_id: int, with_removed=False):
    if not with_removed:
        query = (
            "SELECT user_id, role, is_removed FROM participant_threads "
            "WHERE thread_id= :thread_id "
            "AND is_removed=False "
            "ORDER BY user_id"
        )
    else:
        query = (
            "SELECT user_id, role, is_removed FROM participant_threads "
            "WHERE thread_id= :thread_id "
            "ORDER BY user_id"
        )

    members: List[Dict] = []
    result = session.execute(query, {"thread_id": thread_id})

    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        for row in chunk:
            members.append(dict(row))  # type: ignore

    return members


def revive_owner(session, thread, members):
    owner = get_owner(members)
    if not owner:
        members[0]["role"] = "owner"
        owner = get_owner(members)

    logger.info("Revive owner %s", owner["user_id"])
    resurrention_owner(session, thread["id"], owner["user_id"])
    session.commit()


def sync_to_collab(membership_client, thread, members):
    collab_members = map_member_to_collab(members)
    for member in collab_members:
        logger.info("Adding member to collab %s", member[0])
        try:
            membership_client.add_member_direct(
                member[0],
                thread["collab_id"],
                member[0],
                thread["workspace_id"],
                member[1],
            )
        except MemberAlreadyExist:
            continue
        except Exception as e:
            logger.error(e)
            continue


@click.command()
@click.option("--collab_id", type=click.STRING)
@click.option("--thread_id", type=click.STRING)
@click.option("--resur", type=click.BOOL, default=False)
def main(collab_id: str, thread_id: str, resur: bool):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()
    app = container.app()
    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")
    membership_client = app.repos.gapo.membership
    # pt_usc = app.participant.usecases

    with mysql_c.get_session() as session:
        # get thread info check exist
        # if recur -> get all records
        # else get all members
        # start sync
        if collab_id:
            thread = load_collab_by_id(session, collab_id)
        else:
            thread = load_thread_by_id(session, thread_id)
            if not thread["collab_id"]:
                members = get_members_in_thread(session, thread["id"], False)
                owner = get_owner(members)
                if not owner:
                    raise "Cant create collab when no owner"
                collab_id = membership_client.create_collab_group(
                    caller_id=int(owner["user_id"]),
                    # temporary fix. Collab service requires not empty name for
                    # group while chat-service doesn't have
                    # this constraint
                    name=thread["name"] or "Collab",
                    workspace_id=thread["workspace_id"],
                )
                thread["collab_id"] = collab_id
                update_thread_collab(session, thread_id, collab_id)

        if not thread:
            logger.info("Thread by collab_id %s not found", collab_id)
        else:
            members = get_members_in_thread(session, thread["id"], resur)
            if len(members) == 0:
                logger.info("Found 0 members")
            else:
                revive_owner(session, thread, members)
                sync_to_collab(membership_client, thread, members)

    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
