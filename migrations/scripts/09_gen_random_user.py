import sys
from logging import Logger

import click
import structlog
from chat.container.app import AppContainer

logger: Logger = structlog.get_logger()


@click.command()
@click.option("--collab_id", type=click.STRING)
@click.option("--thread_id", type=click.STRING)
@click.option("--resur", type=click.BOOL, default=False)
def main(collab_id: str, thread_id: str, resur: bool):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()
    app = container.app()
    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")

    with mysql_c.get_session() as session:
        pass
        # get thread info check exist
        # if recur -> get all records
        # else get all members
        # start sync

    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
