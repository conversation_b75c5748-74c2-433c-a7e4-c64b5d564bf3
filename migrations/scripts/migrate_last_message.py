# type: ignore
# pylint: skip-file

import json

import click
from cassandra.auth import PlainTextAuthProvider
from cassandra.cluster import Cluster
from cassandra.policies import ConstantReconnectionPolicy, WhiteListRoundRobinPolicy
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker


class MySQLConnection(object):
    def __init__(self, url):
        self.connect(url)

    def connect(self, url):
        engine = create_engine(url, pool_pre_ping=True)
        session_factory = sessionmaker(bind=engine)
        self.Session = scoped_session(session_factory)

    def session(self):
        return self.Session()


def validate_pair_ids(pair_ids):
    user_a, user_b = pair_ids.split("|")
    return "|".join(map(str, sorted([user_a, user_b]))) == pair_ids


def create_cluster(config):
    ap = None
    if config["CASSANDRA"].get("USERNAME"):
        ap = PlainTextAuthProvider(
            config["CASSANDRA"]["USERNAME"], config["CASSANDRA"]["PASSWORD"]
        )

    c = ConstantReconnectionPolicy(delay=2.5, max_attempts=None)
    hosts = config["CASSANDRA"]["HOST"].split(",")
    w = WhiteListRoundRobinPolicy(hosts)
    if ap:
        cluster = Cluster(
            hosts, load_balancing_policy=w, auth_provider=ap, reconnection_policy=c
        )
    else:
        cluster = Cluster(hosts, load_balancing_policy=w, reconnection_policy=c)

    return cluster


@click.command()
@click.argument("url", type=click.STRING)
@click.argument("host", type=click.STRING)
@click.argument("username", type=click.STRING)
@click.argument("password", type=click.STRING)
@click.argument("port", type=click.INT)
@click.argument("db", type=click.STRING)
def manager(url, host, username, password, port, db):
    config = {
        "CASSANDRA": {
            "USERNAME": username,
            "PASSWORD": password,
            "HOST": host,
            "PORT": port,
            "DB": db,
        }
    }
    cluster = create_cluster(config)
    UPDATE_PARTICIPANT_THREADS = (
        "UPDATE participant_threads "
        "SET last_message=:lm "
        "WHERE user_id=:user_id AND thread_id=:thread_id "
    )
    session = cluster.connect(config["CASSANDRA"]["DB"])
    mysql_c = MySQLConnection(url)
    mysql_s = mysql_c.session()
    threads = mysql_s.execute("SELECT id, pair_ids FROM threads").fetchall()
    start = 1
    for t in threads:
        result = session.execute(
            "SELECT last_message FROM threads WHERE id=%s", (t[0],)
        )
        last_message = result.one()
        if last_message.last_message:
            user_a, user_b = t[1].split("|")
            lmlm = last_message.last_message
            lm = {
                "id": lmlm.id,
                "body": lmlm.body,
                "user_id": lmlm.user_id,
                "created_at": lmlm.created_at,
            }
            mysql_s.execute(
                UPDATE_PARTICIPANT_THREADS,
                {"user_id": int(user_a), "thread_id": t[0], "lm": json.dumps(lm)},
            )
            mysql_s.execute(
                UPDATE_PARTICIPANT_THREADS,
                {"user_id": int(user_b), "thread_id": t[0], "lm": json.dumps(lm)},
            )
            mysql_s.commit()
        print("DONE {} {}/{}".format(t[0], start, len(threads)))
        start += 1


if __name__ == "__main__":
    manager()  # type: ignore
