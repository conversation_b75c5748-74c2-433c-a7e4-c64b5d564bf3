import datetime
import json
import sys
import time
import typing
from logging import Logger

import click
import pandas as pd
import pydantic
import pytz
import sqlalchemy
import structlog

from chat import exception, load_cdn_mapping
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.container.app import AppContainer
from chat.messages.model import MessageBody
from chat.messages.model.api import (
    SchemaBodyFile,
    SchemaBodyImage,
    SchemaBodyMultiImage,
    SchemaBodyText,
    SchemaBodyVideo,
    SchemaFileInformation,
    SchemaImageInformation,
    SchemaImageThumb,
    SchemaMetadataFile,
    SchemaMetadataImage,
    SchemaMetadataVideo,
    SchemaVideoInformation,
)

logger: Logger = structlog.get_logger()


def load_file_link(link):
    try:
        return pd.read_csv(
            link, quotechar="'", delimiter=";", keep_default_na=False
        )
    except pd.errors.EmptyDataError:
        return []


run_now = int(time.time())


class ThreadInfo(pydantic.BaseModel):
    associate_id: str
    name: str
    participants: typing.List[str]
    creator: str
    type: str
    is_read: bool

    def is_direct(self):
        return self.type == "direct"

    def associate_link(self):
        return f"workplace:{self.associate_id}:{run_now}"

    def partner(self):
        for user_id in self.participants:
            if user_id != self.creator:
                return user_id
        raise Exception("no partner found %s", self.associate_id)

    def get_participants(self):
        return [p for p in self.participants if p != self.creator]


class Message(pydantic.BaseModel):
    associate_id: str
    type: str
    creator: str
    is_markdown: bool
    text: str
    medias: typing.List[dict] = []
    created_at: int = 0

    def build_message(self):
        try:
            if self.type == "multi_image":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyMultiImage(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataImage(
                        image_thumb=[
                            SchemaImageThumb(
                                width=m["width"], height=m["height"]
                            )
                            for m in self.medias
                        ],
                        image_information=[
                            SchemaImageInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                            )
                            for m in self.medias
                        ],
                    ),
                ).dict()
            elif self.type == "image":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyImage(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataImage(
                        image_thumb=[
                            SchemaImageThumb(
                                width=m["width"], height=m["height"]
                            )
                            for m in self.medias
                        ],
                        image_information=[
                            SchemaImageInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                            )
                            for m in self.medias
                        ],
                    ),
                ).dict()
            elif self.type == "video":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyVideo(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataVideo(
                        video_information=[
                            SchemaVideoInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                                name=m["name"],
                                duration=m.get("duration", 0),
                            )
                            for m in self.medias
                        ]
                    ),
                ).dict()
            elif self.type == "file":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyFile(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataFile(
                        file_information=[
                            SchemaFileInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                id=m["id"],
                                name=m["name"],
                            )
                            for m in self.medias
                        ]
                    ),
                ).dict()
            elif self.type == "text":
                body = SchemaBodyText(
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                ).dict()
            else:
                raise Exception("not support type")

        except Exception as e:
            logger.error(e)
            logger.info(self)
            raise e
        return body

    def associate_link(self):
        return f"workplace:{self.associate_id}:{run_now}"


def get_thread_infos(link):
    thread_infos = []
    datas = load_file_link(link)
    if not len(datas):
        return thread_infos

    for idx, data in datas.iterrows():
        participants = data["participant_ids"]
        participants = participants.replace("\\", "", 2)
        participants = json.loads(participants)
        try:
            info = ThreadInfo(
                name=str(data["name"])[:100],
                associate_id=data["thread_id"],
                participants=[
                    str(p) for p in participants if str(p) != data["user_id"]
                ],
                type=data["type"],
                creator=data["user_id"] if data["user_id"] else "system",
                is_read=data.get("is_read", "0") == "1",
            )
        except Exception as e:
            logger.error(f"Error processing thread at index {idx}: {e}")
            raise e

        thread_infos.append(info)

    return thread_infos


def get_messages(link):
    messages = []
    datas = load_file_link(link)
    if not len(datas):
        return messages

    for idx, data in datas.iterrows():
        try:
            medias = data["media"]
            if medias == "":
                medias = []
            else:
                medias = medias.replace("\\", "")
                medias = json.loads(medias)

            created_at = datetime.datetime.strptime(
                data["created_at"], "%Y-%m-%dT%H:%M:%S"
            )
            created_at = created_at.replace(tzinfo=pytz.UTC)
            created_at = int(created_at.timestamp() * 1000)

            message = Message(
                associate_id=data["thread_id"],
                type=data["type"],
                creator=data["from"] if data["from"] else "system",
                is_markdown=data["is_markdown_text"],
                text=data["text"],
                medias=medias,
                created_at=created_at,
            )
            message.build_message()
        except Exception as e:
            logger.error(f"Error processing message at index {idx}: {e}")
            raise e

        messages.append(message)
    return messages


@click.command()
@click.argument("workspace_id", type=click.STRING)
@click.argument("cdn_mapping_filepath", type=click.STRING)
@click.argument("thread_link", type=click.STRING)
@click.argument("message_link", type=click.STRING)
def main(workspace_id: str, cdn_mapping_filepath, thread_link: str, message_link: str):
    logger.info("Init container ...")
    load_cdn_mapping.load(cdn_mapping_filepath)

    messages = get_messages(message_link)
    thread_infos = get_thread_infos(thread_link)

    logger.info("Validate done")



if __name__ == "__main__":
    main()  # type: ignore