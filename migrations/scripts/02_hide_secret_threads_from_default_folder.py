"""
Fix inconsistency between redis
and database related to pin folder.


"""
from collections import defaultdict
from time import time
from typing import List, <PERSON>ple

import click
import structlog
from tqdm import tqdm

from chat import constant
from chat.connections.mysql_c import MySQLConnection
from chat.connections.redis_c import RedisConnection

logger = structlog.get_logger()
BATCH_SIZE = 50


def pin_count_key(user_id, folder):
    if folder == "default":
        return f"{user_id}_threads_pin"

    return f"{user_id}_threads_pin_{folder}"


def folder_score_key(user_id, folder):
    if folder == "default":
        return f"{user_id}_threads"

    return f"{user_id}_threads_{folder}"


@click.command()
@click.option("--threads", type=click.INT, default=8)
@click.option("--workspace", type=click.STRING, default=None)
@click.argument(
    "redis_host",
    type=click.STRING,
)
@click.argument("redis_port", type=click.INT)
@click.argument("redis_db", type=click.STRING)
@click.argument("redis_password", type=click.STRING)
@click.argument("mysql_url", type=click.STRING)
def main(
    threads, workspace, redis_host, redis_port, redis_db, redis_password, mysql_url
):
    redis_c = RedisConnection(
        "Test", redis_host, redis_port, redis_db, redis_password, logger
    )
    mysql_c = MySQLConnection("test", mysql_url, logger)

    logger.info("Connecting to db ...")
    redis_c.connect()
    mysql_c.connect()
    logger.info("Connected to db!")

    total_deletions = 0
    total_insertions = 0
    total = 0

    secret_threads: List[Tuple[str, int]] = []

    with mysql_c.get_session() as session:
        if workspace:
            query = (
                "select p.user_id, p.thread_id, p.is_removed"
                "from participant_threads as p "
                "join threads as t on p.thread_id = t.id "
                "where t.message_count > p.delete_to and p.folder='secret' and workspace_id=:workspace_id"
            )
            result = session.execute(query, {"workspace_id": workspace})
        else:
            query = (
                "select p.user_id, p.thread_id "
                "from participant_threads as p "
                "join threads as t on p.thread_id = t.id "
                "where t.message_count > p.delete_to and p.folder='secret'"
            )
            result = session.execute(query)
        user_pinned = defaultdict(defaultdict)  # type: ignore
        logger.info("Loading data from database ...")
        ti = time()

        while True:
            chunk = result.fetchmany(100)
            if not chunk:
                break

            # logger.info("Chunk len: {}".format(len(chunk)))
            total += len(chunk)
            for row in chunk:
                user_id = row["user_id"]
                thread_id = int(row["thread_id"])
                is_removed = row["is_removed"]
                if is_removed or user_id == "system":
                    continue

                secret_threads.append((user_id, thread_id))

        ti = time() - ti
        logger.info("Data loaded in {:.2f}s!".format(ti))

    pipe = redis_c.client.pipeline()
    batch_size = 0
    logger.info("Hiding all secret chats in default folder")
    for (user_id, thread_id) in tqdm(secret_threads):
        default_folder_score_key = folder_score_key(user_id, constant.FOLDER_DEFAULT)
        pipe.zrem(default_folder_score_key, thread_id)
        batch_size += 1
        if batch_size == BATCH_SIZE:
            pipe.execute()
            batch_size = 0

    if batch_size > 0:
        pipe.execute()

    logger.info("Done!")


if __name__ == "__main__":
    main()  # type: ignore
