"""
This script allows us to sync colab members between chat database
and membership service.

Usage:

To sync all collabs:

```
python migration_scripts/04_sync_collab_members.py --all_chats true
```

To sync some collabs:

```
python migration_scripts/04_sync_collab_members.py --collab_ids 123,2,4
```

or:

```
python migration_scripts/04_sync_collab_members.py --thread_ids 1234,1567
```

"""
import sys
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from logging import Logger
from time import time
from typing import Dict, List, Set, Tuple, TypedDict

import click
import structlog
from sqlalchemy.orm import Session
from tqdm import tqdm

from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
    MemberAlreadyExist,
)
from chat.container import AppContainer
from chat.container.app import AppContainer
from chat.threads.repository.interface import ThreadRepository

logger: Logger = structlog.get_logger()


class ChatThread(TypedDict):
    id: int
    name: str
    owner: str
    group_level: int
    workspace_id: str
    collab_id: str


@dataclass
class ChatMember:
    user_id: str
    thread_id: int
    role: str


ROLE_MAP: Dict[str, CollabGroupRole] = {
    "owner": CollabGroupRole.Owner,
    "admin": CollabGroupRole.Admin,
    "member": CollabGroupRole.Member,
}


def load_all_collabs(session: Session):
    threads: List[ChatThread] = []
    query = (
        "select t.id, t.name, t.group_level, t.workspace_id, t.collab_id, p.user_id as owner "
        "from threads as t "
        "INNER JOIN participant_threads as p on t.id = p.thread_id "
        "where collab_id is not null and p.role='owner'"
    )
    result = session.execute(query)

    logger.info("Loading collabs from database ...")
    ti = time()

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            threads.append(dict(row))  # type: ignore

    ti = time() - ti
    logger.info("Loaded all collabs in {:.2f}s!".format(ti))

    return threads


def load_collab_by_thread_ids(session: Session, thread_ids: List[str]):
    threads: List[ChatThread] = []

    # handle empty list case
    if len(thread_ids) == 0:
        return threads

    query = (
        "SELECT t.id, t.name, t.group_level, t.workspace_id, t.collab_id, p.user_id as owner "
        "FROM threads as t "
        "INNER JOIN participant_threads as p on t.id = p.thread_id "
        "WHERE collab_id is not null AND p.role='owner' AND id in :ids"
    )
    result = session.execute(query, {"ids": thread_ids})

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            threads.append(dict(row))  # type: ignore

    return threads


def load_collab_by_ids(session: Session, collab_ids: List[str]):
    threads: List[ChatThread] = []

    # handle empty list case
    if len(collab_ids) == 0:
        return threads

    query = (
        "SELECT t.id, t.name, t.group_level, t.workspace_id, t.collab_id, p.user_id as owner "
        "FROM threads as t "
        "INNER JOIN participant_threads as p on t.id = p.thread_id "
        "WHERE p.role='owner' AND collab_id in :collab_ids"
    )
    result = session.execute(query, {"collab_ids": collab_ids})

    ti = time()

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            threads.append(dict(row))  # type: ignore

    ti = time() - ti

    return threads


def get_members_in_thread(session, thread_id: int):
    query = (
        f"SELECT user_id, role FROM participant_threads "
        "WHERE thread_id= :thread_id "
        "AND is_removed=False "
        "ORDER BY user_id"
    )
    members: List[Dict] = []
    result = session.execute(query, {"thread_id": thread_id})

    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        for row in chunk:
            members.append(dict(row))  # type: ignore

    return members


@click.command()
@click.option("--num_threads", type=click.INT, default=10)
@click.option("--all_chats", type=click.BOOL, default=False)
@click.option("--collab_ids", type=click.STRING, default="")
@click.option("--thread_ids", type=click.STRING, default="")
def main(num_threads: int, all_chats: bool, collab_ids: str, thread_ids: str):
    if not all_chats and not collab_ids and not thread_ids:
        logger.warning("Either --all_chats or --collab_ids or --thread_ids must be set")
        return
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()

    app = container.app()
    mysql_c = app.conns.mysql

    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")

    membership_client = app.repos.gapo.membership
    thread_repo: ThreadRepository = app.repos.thread
    pt_repo = app.repos.pt

    threads: List[ChatThread] = []
    with mysql_c.get_session() as session:
        # conn = conn.execution_options(stream_results=True, max_row_buffer=100)
        if all_chats:
            threads = load_all_collabs(session)
        elif thread_ids:
            thread_id_list: List[str] = [
                th.strip() for th in thread_ids.split(",") if th.strip() != ""
            ]
            threads = load_collab_by_thread_ids(session, thread_id_list)
        elif collab_ids:
            collab_ids_list = [
                collab_id.strip()
                for collab_id in collab_ids.split(",")
                if collab_id.strip() != ""
            ]
            threads = load_collab_by_ids(session, collab_ids_list)

        if not threads:
            logger.warning("No threads found, exit")
            return

    logger.info("Num threads: {}".format(len(threads)))

    def sync_collab_members(thread: ChatThread):
        try:
            with mysql_c.get_session() as session:
                thread_id = thread["id"]
                owner_id_str = thread.get("owner")
                workspace_id = thread["workspace_id"]
                thread_name = thread["name"]

                if not owner_id_str or not workspace_id:
                    logger.info("Owner: {}".format(owner_id_str))
                    return None

                owner_id = int(owner_id_str)
                workspace_id = int(workspace_id)  # type: ignore

                collab_id = thread["collab_id"]

                # update name if needed
                try:
                    collab = membership_client.get_collab_group(
                        owner_id, collab_id, workspace_id
                    )
                except PermissionError:
                    logger.warning(
                        "User {owner_id} is not the owner of collab {collab_id}, thread: {thread_id}, need to update manual!"
                    )
                    # owner not found
                    # need to create owner
                    return

                collab_name = collab.name
                updated_name = 0
                if collab_name != thread_name:
                    # logger.info(f" Update name, thread name: {thread_name}, collab name: {collab_name}")
                    try:
                        membership_client.update_collab_info(
                            collab_id, owner_id, workspace_id, thread_name
                        )
                        updated_name = 1
                    except Exception as e:
                        logger.warning(
                            f"Failed to update collab {collab_id} because error {e}"
                        )

                # members = pt_repo.get_members_in_list_threads(session, [thread_id])
                members = get_members_in_thread(session, thread_id)
                db_members: Set[Tuple[int, CollabGroupRole]] = set(
                    [
                        (int(member["user_id"]), ROLE_MAP[member["role"]])
                        for member in members
                    ]
                )
                collab_members: Set[Tuple[int, CollabGroupRole]] = set(
                    [
                        (member.user_id, member.roles[0])
                        for member in membership_client.list_members(
                            owner_id, collab_id
                        )
                    ]
                )
                db_member_ids = set(m[0] for m in db_members)
                collab_member_ids = set(m[0] for m in collab_members)

                add_or_update = db_members - collab_members

                to_update = [m for m in add_or_update if m[0] in collab_member_ids]
                to_add = [m for m in add_or_update if m[0] not in collab_member_ids]
                to_remove = collab_member_ids - db_member_ids

                for user_id in to_remove:
                    try:
                        # self leave
                        membership_client.remove_member(
                            user_id, collab_id, user_id, workspace_id
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to leave collab {collab_id} by {user_id} due to {e}"
                        )

                for (member_id, role) in to_add:
                    try:
                        # TODO: what if the member is the owner of collab?
                        membership_client.add_member(
                            owner_id, collab_id, member_id, workspace_id, [role]
                        )
                    except MemberAlreadyExist:
                        logger.warning(
                            "Member already exists. Try to update role user_id {member_id} new role {role} , collab {collab_id} "
                        )
                        # update role
                        membership_client.set_member_role(
                            owner_id, collab_id, member_id, [role], workspace_id
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to add user {member_id} by {owner_id} to collab {collab_id} due to: {e}"
                        )

                for (member_id, role) in to_update:
                    try:
                        # update role
                        membership_client.set_member_role(
                            owner_id, collab_id, member_id, [role], workspace_id
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to update role user {member_id} by {owner_id} to collab {collab_id} due to: {e}"
                        )

                session.commit()

                return (len(to_add), len(to_remove), len(to_update), updated_name)
        except Exception as e:
            logger.warning(
                f"Got error while syncing collab members, collab_id: {thread['collab_id']}: error: {e}",
                exc_info=True,
            )
            return None

    num_added_members = 0
    num_removed_members = 0
    num_updated_names = 0
    num_updated_roles = 0
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        results = list(
            tqdm(executor.map(sync_collab_members, threads), total=len(threads))
        )
        # build thread to collab map
        for rs in results:
            if rs is None:
                continue
            (added, removed, updated_roles, updated_name) = rs
            num_added_members += added
            num_removed_members += removed
            num_updated_names += updated_name
            num_updated_roles += updated_roles

    logger.info(
        f"Added {num_added_members} members, removed {num_removed_members} members, updated roles: {num_updated_roles}, updated name: {num_updated_names}"
    )
    if num_added_members == 0 and num_removed_members == 0:
        logger.info("DONE, no extra retry is needed!!!")
    else:
        logger.info("MAYBE you should retry again!")

    # closing connection
    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
