# type: ignore
# pylint: skip-file

import csv

import click
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker


class MySQLConnection(object):
    def __init__(self, url):
        self.connect(url)

    def connect(self, url):
        engine = create_engine(url, pool_pre_ping=True)
        session_factory = sessionmaker(bind=engine)
        self.Session = scoped_session(session_factory)

    def session(self):
        return self.Session()


def validate_pair_ids(pair_ids):
    user_a, user_b = pair_ids.split("|")
    return "|".join(map(str, sorted([int(user_a), int(user_b)]))) == pair_ids


def make_pair_ids(pair_ids):
    user_a, user_b = pair_ids.split("|")
    return "|".join(map(str, sorted([int(user_a), int(user_b)])))


@click.command()
@click.argument("url", type=click.STRING)
@click.argument("csv_dir", type=click.STRING)
def manager(url, csv_dir):
    mysql_c = MySQLConnection(url)
    thread_file = csv_dir + "/gapo_chat.threads.csv"
    direct_thread_file = csv_dir + "/gapo_chat.direct_threads.csv"
    participant_file = csv_dir + "/gapo_chat.participants.csv"
    # participant_t_file = csv_dir + "/gapo_chat.participant_threads.csv"
    message_counter_file = csv_dir + "/gapo_chat.message_counter.csv"

    direct_threads = {}
    with open(direct_thread_file, "r") as open_file:
        csv_reader = csv.reader(open_file, delimiter=",")
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                line_count += 1
            else:
                direct_threads[row[1]] = row[0]
                line_count += 1

    message_counters = {}
    with open(message_counter_file, "r") as open_file:
        csv_reader = csv.reader(open_file, delimiter=",")
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                line_count += 1
            else:
                message_counters[row[0]] = row[1]
                line_count += 1
    threads = []
    last_messages = {}
    with open(thread_file, "r") as open_file:
        csv_reader = csv.reader(open_file, delimiter=",")
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                line_count += 1
            else:
                pair_ids = direct_threads.get(row[0], None)
                if not pair_ids:
                    continue
                if not validate_pair_ids(pair_ids):
                    old_pair_ids = pair_ids
                    pair_ids = make_pair_ids(pair_ids)
                    print("{} -> {}".format(old_pair_ids, pair_ids))

                thread = {
                    "id": row[0],
                    "pair_ids": pair_ids,
                    "message_count": message_counters[row[0]],
                }
                last_messages[row[0]] = row[7]
                line_count += 1
                threads.append(thread)

    participants = []
    participant_threads = []
    with open(participant_file, "r") as open_file:
        csv_reader = csv.reader(open_file, delimiter=",")
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                line_count += 1
            else:
                pair_ids = direct_threads.get(row[0], None)
                if not pair_ids:
                    continue
                participant = {
                    "thread_id": row[0],
                    "user_id": row[1],
                    "delete_to": row[2],
                    "read_count": row[6],
                    "role": "owner",
                }
                participants.append(participant)
                partners = pair_ids.split("|")
                partner_id = partners[0] if partners[0] != str(row[1]) else partners[1]

                participant_thread = {
                    "thread_id": row[0],
                    "user_id": row[1],
                    "read_count": row[6],
                    "last_message": None,
                    "partner_id": int(partner_id),
                }
                participant_threads.append(participant_thread)
                line_count += 1

    INSERT_THREAD = (
        "INSERT INTO threads(id, message_count, pair_ids"
        ") VALUES(:id, :message_count, :pair_ids)"
    )
    INSERT_PARTICIPANTS = (
        "INSERT INTO participants(thread_id, "
        "user_id, delete_to, read_count,role) VALUES("
        ":thread_id, :user_id, :delete_to, :read_count, :role)"
    )
    INSERT_PARTICIPANT_THREADS = (
        "INSERT INTO participant_threads(thread_id, "
        "user_id, read_count, "
        "last_message, partner_id) VALUES("
        ":thread_id, :user_id, :read_count, "
        ":last_message, :partner_id)"
    )

    session = mysql_c.session()
    insert_batch = 500
    for i in range(0, len(threads), insert_batch):
        session.execute(INSERT_THREAD, threads[i : i + insert_batch])
        session.commit()

    for i in range(0, len(participants), insert_batch):
        session.execute(INSERT_PARTICIPANTS, participants[i : i + insert_batch])
        session.commit()

    for i in range(0, len(participant_threads), insert_batch):
        session.execute(
            INSERT_PARTICIPANT_THREADS, participant_threads[i : i + insert_batch]
        )
        session.commit()


if __name__ == "__main__":
    manager()  # type: ignore
