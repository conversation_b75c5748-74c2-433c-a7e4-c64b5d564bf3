import datetime
import json
import sys
import time
import typing
from logging import Logger

import click
import pandas as pd
import pydantic
import pytz
import sqlalchemy
import structlog

from chat import exception, load_cdn_mapping
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.container.app import AppContainer
from chat.messages.model import MessageBody
from chat.messages.model.api import (
    SchemaBodyFile,
    SchemaBodyImage,
    SchemaBodyMultiImage,
    SchemaBodyText,
    SchemaBodyVideo,
    SchemaFileInformation,
    SchemaImageInformation,
    SchemaImageThumb,
    SchemaMetadataFile,
    SchemaMetadataImage,
    SchemaMetadataVideo,
    SchemaVideoInformation,
)

logger: Logger = structlog.get_logger()


def load_file_link(link):
    try:
        return pd.read_csv(
            link, quotechar="'", delimiter=";", keep_default_na=False
        )
    except pd.errors.EmptyDataError:
        return []


run_now = int(time.time())


class ThreadInfo(pydantic.BaseModel):
    associate_id: str
    name: str
    participants: typing.List[str]
    creator: str
    type: str
    is_read: bool

    def is_direct(self):
        return self.type == "direct"

    def associate_link(self):
        return f"workplace:{self.associate_id}:{run_now}"

    def partner(self):
        for user_id in self.participants:
            if user_id != self.creator:
                return user_id
        raise Exception("no partner found %s", self.associate_id)

    def get_participants(self):
        return [p for p in self.participants if p != self.creator]


class Message(pydantic.BaseModel):
    associate_id: str
    type: str
    creator: str
    is_markdown: bool
    text: str
    medias: typing.List[dict] = []
    created_at: int = 0

    def build_message(self):
        try:
            if self.type == "multi_image":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyMultiImage(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataImage(
                        image_thumb=[
                            SchemaImageThumb(
                                width=m["width"], height=m["height"]
                            )
                            for m in self.medias
                        ],
                        image_information=[
                            SchemaImageInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                            )
                            for m in self.medias
                        ],
                    ),
                ).dict()
            elif self.type == "image":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyImage(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataImage(
                        image_thumb=[
                            SchemaImageThumb(
                                width=m["width"], height=m["height"]
                            )
                            for m in self.medias
                        ],
                        image_information=[
                            SchemaImageInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                            )
                            for m in self.medias
                        ],
                    ),
                ).dict()
            elif self.type == "video":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyVideo(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataVideo(
                        video_information=[
                            SchemaVideoInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                quality=m["quality"],
                                id=m["id"],
                                name=m["name"],
                                duration=m.get("duration", 0),
                            )
                            for m in self.medias
                        ]
                    ),
                ).dict()
            elif self.type == "file":
                medias = []
                for m in self.medias:
                    if m.get("src"):
                        medias.append(m["src"])
                    else:
                        medias.append(m["file_link"])
                body = SchemaBodyFile(
                    media=medias,
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                    metadata=SchemaMetadataFile(
                        file_information=[
                            SchemaFileInformation(
                                type=m["file_type"],
                                size=int(m["size"]),
                                id=m["id"],
                                name=m["name"],
                            )
                            for m in self.medias
                        ]
                    ),
                ).dict()
            elif self.type == "text":
                body = SchemaBodyText(
                    type=self.type,
                    is_markdown_text=self.is_markdown,
                    text=self.text,
                ).dict()
            else:
                raise Exception("not support type")

        except Exception as e:
            logger.error(e)
            logger.info(self)
            raise e
        return body

    def associate_link(self):
        return f"workplace:{self.associate_id}:{run_now}"


def get_thread_infos(link):
    thread_infos = []
    datas = load_file_link(link)
    if not len(datas):
        return thread_infos

    for _, data in datas.iterrows():
        participants = data["participant_ids"]
        participants = participants.replace("\\", "", 2)
        participants = json.loads(participants)
        try:
            info = ThreadInfo(
                name=str(data["name"])[:100],
                associate_id=data["thread_id"],
                participants=[
                    str(p) for p in participants if str(p) != data["user_id"]
                ],
                type=data["type"],
                creator=data["user_id"] if data["user_id"] else "system",
                is_read=data.get("is_read", "0") == "1",
            )
        except Exception as e:
            logger.error(e)
        thread_infos.append(info)

    return thread_infos


def get_messages(link):
    messages = []
    datas = load_file_link(link)
    if not len(datas):
        return messages

    for _, data in datas.iterrows():
        try:
            medias = data["media"]
            if medias == "":
                medias = []
            else:
                medias = medias.replace("\\", "")
                medias = json.loads(medias)

            created_at = datetime.datetime.strptime(
                data["created_at"], "%Y-%m-%dT%H:%M:%S"
            )
            created_at = created_at.replace(tzinfo=pytz.UTC)
            created_at = int(created_at.timestamp() * 1000)

            message = Message(
                associate_id=data["thread_id"],
                type=data["type"],
                creator=data["from"] if data["from"] else "system",
                is_markdown=data["is_markdown_text"],
                text=data["text"],
                medias=medias,
                created_at=created_at,
            )
            message.build_message()
        except Exception as e:
            logger.error(e)
            raise e

        messages.append(message)
    return messages


@click.command()
@click.argument("workspace_id", type=click.STRING)
@click.argument("thread_link", type=click.STRING)
@click.argument("message_link", type=click.STRING)
def main(workspace_id: str, thread_link: str, message_link: str):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()
    config = container.conf()
    load_cdn_mapping.load(config.cdn_mapping_filepath)
    app = container.app()

    messages = get_messages(message_link)
    thread_infos = get_thread_infos(thread_link)

    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")
    # pt_usc = app.participant.usecases
    create_thread_usc = container.thread().create_thread()
    add_member_usc = container.participant().usecases().add_participant
    action_member_usc = container.participant().usecases().participant_action
    create_msg_usc = container.message().usecases().create_message
    thread_repo = app.repos.thread

    for thread_info in thread_infos:
        retry = 0
        with mysql_c.get_session() as session:
            while True:
                thread = thread_repo.get_by_associate(
                    session, thread_info.associate_link()
                )
                print("EXIST", thread)

                if thread:
                    break
                session.commit()
                try:
                    thread = create_thread_usc.create_associate_group(
                        user_id=thread_info.creator,
                        name=thread_info.name,
                        associate_link=thread_info.associate_link(),
                        workspace_id=workspace_id,
                        should_create_owner=False,
                    )
                except Exception as e:
                    if retry < 10:
                        logger.error("ERROR: %s retry %s" % (e, retry))
                        time.sleep(1)
                        retry += 1
                        continue
                    raise e
                print("CREATE", thread)

    for message in messages:
        with mysql_c.get_session() as session:
            thread = thread_repo.get_by_associate(
                session, message.associate_link()
            )

            if not thread:
                logger.info("NOT FOUND THREAD %s", message.associate_link())
                continue
            session.commit()

        body = message.build_message()
        api_version = "3"
        create_msg_usc._create(
            message.creator,
            thread.id,
            MessageBody(**body),
            api_version,
            bypass_all=True,
            workspace_id=workspace_id,
            event_at=message.created_at,
        )

    created_thread_id = set()
    for thread_info in thread_infos:
        with mysql_c.get_session() as session:
            thread = thread_repo.get_by_associate(
                session, thread_info.associate_link()
            )
            thread_id = thread.id
            if thread_id in created_thread_id:
                continue

            created_thread_id.add(thread_id)
            print("Create owner", thread_id, thread_info.creator)
            try:
                create_thread_usc._create_owner(
                    session,
                    thread_id,
                    thread_info.creator,
                    None,
                    init_read_count=(
                        thread.message_count if thread_info.is_read else 0
                    ),
                )
                session.commit()
                create_thread_usc.gapo_client.membership.set_member_role(
                    caller_id=int(thread_info.creator),
                    collab_id=thread.collab_id,
                    user_id=int(thread_info.creator),
                    workspace_id=workspace_id,
                    roles=[CollabGroupRole.Owner],
                )
            except Exception as e:
                if not isinstance(e, sqlalchemy.exc.IntegrityError):
                    logger.error("Failed: %s", thread_info)
                    raise e
                print("Already create owner", thread_id, thread_info.creator)
            retry = 0
            while retry <= 3:
                try:
                    add_member_usc.add_members(
                        thread_info.creator,
                        thread_id,
                        invite_ids=thread_info.get_participants(),
                    )

                    action_member_usc.mark_as_read(
                        thread_info.creator, thread_id
                    )

                    session.commit()
                    break
                except Exception as e:
                    if isinstance(e, exception.UserNotInThread):
                        time.sleep(3)
                        retry += 1
                        if retry == 3:
                            raise e
                        continue

    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
