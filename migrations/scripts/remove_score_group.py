# type: ignore
# pylint: skip-file

from typing import List

import click
import redis
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker


class MySQLConnection(object):
    def __init__(self, url):
        self.connect(url)

    def connect(self, url):
        engine = create_engine(url, pool_pre_ping=True)
        session_factory = sessionmaker(bind=engine)
        self.Session = scoped_session(session_factory)

    def session(self):
        return self.Session()


class RedisConnection(object):
    def __init__(self, host, passwd, port, db):
        self.connect(host, passwd, port, db)

    def connect(self, host, passwd, port, db):
        params = {"host": host, "port": port, "db": db}
        if passwd:
            params["password"] = passwd
        self.client = redis.Redis(**params)
        self.client.ping()


def list_score(user_ids: List[str], redis_c, thread_id, should_delete=False):
    key_format = "{user_id}_threads"
    f_key_format = key_format + "_default"

    for user_id in user_ids:
        data = redis_c.zscore(key_format.format(user_id=user_id), thread_id)
        f_data = redis_c.zscore(f_key_format.format(user_id=user_id), thread_id)
        print(f"{user_id} has {data}, {f_data}")
        if data:
            redis_c.zrem(key_format.format(user_id=user_id), thread_id)
        if f_data:
            redis_c.zrem(f_key_format.format(user_id=user_id), thread_id)


@click.command()
@click.argument("url", type=click.STRING)
@click.argument("redis_host", type=click.STRING)
@click.argument("redis_password", type=click.STRING)
@click.argument("redis_port", type=click.STRING)
@click.argument("redis_db", type=click.INT)
@click.argument("thread_id", type=click.INT)
def manager(url, redis_host, redis_password, redis_db, redis_port, thread_id):
    mysql_c = MySQLConnection(url)
    session = mysql_c.session()
    redis_c = RedisConnection(redis_host, redis_password, redis_port, redis_db)
    print("Thread_id", thread_id)

    user_ids = [
        u[0]
        for u in session.execute(
            "SELECT user_id FROM participant_threads WHERE thread_id=:thread_id",
            {"thread_id": thread_id},
        ).fetchall()
    ]
    list_score(user_ids, redis_c.client, thread_id)


if __name__ == "__main__":
    manager()  # type: ignore
