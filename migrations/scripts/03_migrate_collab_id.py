import csv
import sys
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
from dataclasses import dataclass
from logging import Logger
from time import time
from typing import Dict, List, Set, TypedDict

import click
import structlog
from sqlalchemy.orm import Session
from tqdm import tqdm

from chat import constant
from chat.connections.gapo_client.membership.membership_client import Privacy
from chat.container import AppContainer
from chat.container.app import AppContainer
from chat.threads.repository.interface import ThreadRepository
from chat.utils.common import now
from chat.utils.group_level_helper import is_public

logger: Logger = structlog.get_logger()


class ChatThread(TypedDict):
    id: int
    name: str
    owner: str
    group_level: int
    workspace_id: str


@dataclass
class ChatMember:
    user_id: str
    thread_id: int
    role: str


ROLE_MAP: Dict[str, int] = {
    "owner": 1,
    "admin": 2,
    "member": 3,
}


def load_participant_thread_db(session: Session, filtered_threads: Set[int]):
    logger.info("Loading participant_threads database ...")
    query = "select thread_id, user_id, role from participant_threads"
    ti = time()
    result = session.execute(query)
    thread_owners: Dict[int, str] = {}
    members: List[ChatMember] = []
    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            thread_id = int(row["thread_id"])
            user_id = row["user_id"]
            role = row["role"]

            # ignore all threads
            if not thread_id in filtered_threads:
                continue

            if role == constant.OWNER_ROLE:
                thread_owners[thread_id] = user_id
            else:
                # normal members
                # need to import into membership database
                members.append(
                    ChatMember(user_id=user_id, thread_id=thread_id, role=role)
                )
    ti = time() - ti
    logger.info("Loaded participant_threads table in {:.2f}s!".format(ti))

    return thread_owners, members


def load_thread_db(session: Session, all_chats: bool):
    threads: List[ChatThread] = []
    thread_ids: Set[int] = set([])
    if all_chats:
        query = "select id, name, group_level, workspace_id from threads where type='group' and workspace_id is not null"
    else:
        query = "select id, name, group_level, workspace_id from threads where type='group' and collab_id is null and workspace_id is not null"
    result = session.execute(query)

    logger.info("Loading threads table from database ...")
    ti = time()

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            thread_id = int(row["id"])
            thread_ids.add(thread_id)
            threads.append(dict(row))  # type: ignore

    ti = time() - ti
    logger.info("Loaded thread data in {:.2f}s!".format(ti))

    return threads, thread_ids


@click.command()
@click.option("--num_threads", type=click.INT, default=10)
@click.option("--all_chats", type=click.BOOL, default=False)
def main(num_threads: int, all_chats):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()

    app = container.app()
    mysql_c = app.conns.mysql

    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")

    membership_client = app.repos.gapo.membership
    thread_repo: ThreadRepository = app.repos.thread
    missing_threads = False

    threads: List[ChatThread] = []
    thread_ids: Set[int] = set([])
    thread_owners: Dict[int, str] = {}
    members: List[ChatMember] = []
    thread_collabs: Dict[int, str] = {}
    with mysql_c.engine.connect() as conn:
        conn = conn.execution_options(stream_results=True, max_row_buffer=100)
        threads, thread_ids = load_thread_db(conn, all_chats)
        logger.info("Loaded threads db, size: {}".format(len(threads)))
        thread_owners, members = load_participant_thread_db(conn, thread_ids)
        logger.info("Loaded participant_threads")

        # fill owner id
        for thread in threads:
            thread_id = thread["id"]
            owner = thread_owners.get(thread_id)
            if owner:
                thread["owner"] = owner

    logger.info(
        "Total threads: {}, total members: {}".format(len(threads), len(members))
    )

    def create_collab_group(thread: ChatThread):
        try:
            with mysql_c.get_session() as session:
                is_public_group = is_public(thread["group_level"])
                thread_id = thread["id"]
                owner_id = thread.get("owner")
                thread_name = thread["name"]
                workspace_id = thread["workspace_id"]

                if not owner_id:
                    logger.warning(f"Cannot find the owner of thread: {thread_id}")
                    return None

                if not workspace_id:
                    logger.warning(f"Thread {thread_id} has null workspace value")
                    return None

                privacy = Privacy.Public if is_public_group else Privacy.Close

                collab_id = membership_client.create_collab_group(
                    int(owner_id), thread_name, workspace_id
                )
                thread_repo.set_collab_id(session, thread_id, collab_id)
                session.commit()

                return (thread_id, collab_id)
        except Exception as e:
            logger.warning(f"Got error while creating collab: {e}")
            return None

    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        logger.info("Creating collab ...")
        results = tqdm(list(executor.map(create_collab_group, threads)))
        # build thread to collab map
        for rs in results:
            if rs is None:
                continue
            (thread_id, collab_id) = rs
            thread_collabs[thread_id] = collab_id

        for thread in threads:
            if thread["id"] not in thread_collabs:
                logger.warning(f"Cannot create collab for thread {thread} ")
                missing_threads = True

    logger.info("Exporting membership data to csv...")
    output_file_name = "membership_{}.csv".format(now())
    with open(output_file_name, "w") as f:
        writer = csv.writer(f, quoting=csv.QUOTE_ALL)
        for member in tqdm(members):
            collab_id = thread_collabs.get(member.thread_id)
            if not collab_id:
                continue

            collab_role = ROLE_MAP[member.role]

            writer.writerow([collab_id, member.thread_id, member.user_id, collab_role])
    logger.info(f"Exported data to {output_file_name} !")
    if missing_threads:
        logger.info(
            "Cannot create collab groups for some threads, please try to re-run again!"
        )

    # closing connection
    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
