import os
from functools import partial
from multiprocessing import Pool

import click
import redis
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker


class MySQLConnection(object):
    def __init__(self, url):
        self.connect(url)

    def connect(self, url):
        engine = create_engine(url, pool_pre_ping=True)
        session_factory = sessionmaker(bind=engine)
        self.Session = scoped_session(session_factory)

    def session(self):
        return self.Session()


class RedisConnection(object):
    def __init__(self, host, passwd, port, db):
        self.connect(host, passwd, port, db)

    def connect(self, host, passwd, port, db):
        params = {"host": host, "port": port, "db": db}
        if passwd:
            params["password"] = passwd
        self.client = redis.Redis(**params)
        self.client.ping()


def validate_pair_ids(pair_ids):
    user_a, user_b = pair_ids.split("|")
    return "|".join(map(str, sorted([user_a, user_b]))) == pair_ids


def worker(url, redis_host, redis_password, redis_port, redis_db, user_ids):
    mysql_c = MySQLConnection(url)
    session = mysql_c.session()
    tsf = "{user_id}_threads"
    redis_c = RedisConnection(redis_host, redis_password, redis_port, redis_db)
    max_user = len(user_ids)
    start = 1
    for u in user_ids:
        u = u[0]
        thread_ids = session.execute(
            "SELECT thread_id FROM participant_threads WHERE user_id=:user_id",
            {"user_id": u},
        ).fetchall()
        redis_c.client.zadd(tsf.format(user_id=u), {t[0]: t[0] for t in thread_ids})
        print("DONE {} {}/{}".format(os.getpid(), start, max_user))
        start += 1


def worker_2(redis_host, redis_password, redis_port, redis_db, threads):
    tsf = "{user_id}_threads"
    redis_c = RedisConnection(redis_host, redis_password, redis_port, redis_db)
    max_user = len(threads)
    start = 1
    for t in threads:
        user_a, user_b = t["pair_ids"].split("|")
        redis_c.client.zrem(tsf.format(user_id=user_a), t["id"])
        redis_c.client.zrem(tsf.format(user_id=user_b), t["id"])
        print(
            "DONE REMOVE {} {} {} {}/{}".format(
                os.getpid(), tsf.format(user_id=user_a), t["id"], start, max_user
            )
        )
        print(
            "DONE REMOVE {} {} {} {}/{}".format(
                os.getpid(), tsf.format(user_id=user_b), t["id"], start, max_user
            )
        )
        start += 1


@click.command()
@click.argument("url", type=click.STRING)
@click.argument("redis_host", type=click.STRING)
@click.argument("redis_password", type=click.STRING)
@click.argument("redis_port", type=click.STRING)
@click.argument("redis_db", type=click.INT)
@click.argument("max_pool", type=click.INT)
def manager(url, redis_host, redis_password, redis_db, redis_port, max_pool):
    mysql_c = MySQLConnection(url)
    session = mysql_c.session()
    user_ids = session.execute(
        "SELECT DISTINCT user_id FROM participants WHERE delete_to < read_count"
    ).fetchall()
    pool = Pool(processes=max_pool)
    func = partial(worker, url, redis_host, redis_password, redis_port, redis_db)
    max_size = len(user_ids) // max_pool + 1
    for y in pool.imap_unordered(
        func, [user_ids[i : i + max_size] for i in range(0, len(user_ids), max_size)]
    ):
        print(y)

    threads = session.execute(
        "SELECT * FROM threads WHERE message_count = 0"
    ).fetchall()
    func = partial(worker_2, redis_host, redis_password, redis_port, redis_db)
    max_size = len(threads) // max_pool + 1
    for y in pool.imap_unordered(
        func, [threads[i : i + max_size] for i in range(0, len(threads), max_size)]
    ):
        print(y)


if __name__ == "__main__":
    manager()  # type: ignore
