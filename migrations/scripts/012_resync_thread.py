import json
import sys
import time
import typing
from logging import Logger

import click
import pandas as pd
import pydantic
import sqlalchemy
import structlog

from chat import exception, load_cdn_mapping
from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.container.app import AppContainer

logger: Logger = structlog.get_logger()


def load_file_link(link):
    try:
        return pd.read_csv(
            link, quotechar="'", delimiter=";", keep_default_na=False
        )
    except pd.errors.EmptyDataError:
        return []


run_now = int(time.time())


class ThreadInfo(pydantic.BaseModel):
    associate_id: str
    name: str
    participants: typing.List[str]
    creator: str
    type: str
    is_read: bool

    def is_direct(self):
        return self.type == "direct"

    def associate_link(self):
        return f"workplace:{self.associate_id}:{run_now}"

    def partner(self):
        for user_id in self.participants:
            if user_id != self.creator:
                return user_id
        raise Exception("no partner found %s", self.associate_id)

    def get_participants(self):
        return [p for p in self.participants if p != self.creator]


def get_thread_infos(link):
    thread_infos = []
    datas = load_file_link(link)
    if not len(datas):
        return thread_infos

    for _, data in datas.iterrows():
        participants = data["participant_ids"]
        participants = participants.replace("\\", "", 2)
        participants = json.loads(participants)
        try:
            info = ThreadInfo(
                name=str(data["name"])[:100],
                associate_id=data["thread_id"],
                participants=[
                    str(p) for p in participants if str(p) != data["user_id"]
                ],
                type=data["type"],
                creator=data["user_id"] if data["user_id"] else "system",
                is_read=data.get("is_read", "0") == "1",
            )
        except Exception as e:
            logger.error(e)
        thread_infos.append(info)

    return thread_infos


@click.command()
@click.argument("workspace_id", type=click.STRING)
@click.argument("thread_link", type=click.STRING)
@click.argument("start_time", type=click.INT)
def main(workspace_id: str, thread_link: str, start_time: int):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()
    config = container.conf()
    load_cdn_mapping.load(config.cdn_mapping_filepath)
    app = container.app()

    if start_time:
        global run_now
        run_now = start_time

    thread_infos = get_thread_infos(thread_link)

    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")
    create_thread_usc = container.thread().create_thread()
    add_member_usc = container.participant().usecases().add_participant
    action_member_usc = container.participant().usecases().participant_action
    thread_repo = app.repos.thread

    for thread_info in thread_infos:
        retry = 0
        with mysql_c.get_session() as session:
            while True:
                thread = thread_repo.get_by_associate(
                    session, thread_info.associate_link()
                )
                print("EXIST", thread)

                if thread:
                    break
                session.commit()
                try:
                    thread = create_thread_usc.create_associate_group(
                        user_id=thread_info.creator,
                        name=thread_info.name,
                        associate_link=thread_info.associate_link(),
                        workspace_id=workspace_id,
                        should_create_owner=False,
                    )
                except Exception as e:
                    if retry < 10:
                        logger.error("ERROR: %s retry %s" % (e, retry))
                        time.sleep(1)
                        retry += 1
                        continue
                    raise e
                print("CREATE", thread)

    created_thread_id = set()
    for thread_info in thread_infos:
        with mysql_c.get_session() as session:
            thread = thread_repo.get_by_associate(
                session, thread_info.associate_link()
            )
            thread_id = thread.id
            if thread_id in created_thread_id:
                continue

            created_thread_id.add(thread_id)
            print("Create owner", thread_id, thread_info.creator)
            try:
                create_thread_usc._create_owner(
                    session,
                    thread_id,
                    thread_info.creator,
                    None,
                    init_read_count=(
                        thread.message_count if thread_info.is_read else 0
                    ),
                )
                session.commit()
                create_thread_usc.gapo_client.membership.set_member_role(
                    caller_id=int(thread_info.creator),
                    collab_id=thread.collab_id,
                    user_id=int(thread_info.creator),
                    workspace_id=workspace_id,
                    roles=[CollabGroupRole.Owner],
                )
            except Exception as e:
                if not isinstance(e, sqlalchemy.exc.IntegrityError):
                    logger.error("Failed: %s", thread_info)
                    raise e
                print("Already create owner", thread_id, thread_info.creator)
            retry = 0
            while retry <= 3:
                try:
                    add_member_usc.add_members(
                        thread_info.creator,
                        thread_id,
                        invite_ids=thread_info.get_participants(),
                    )

                    action_member_usc.mark_as_read(
                        thread_info.creator, thread_id
                    )

                    session.commit()
                    break
                except Exception as e:
                    if isinstance(e, exception.UserNotInThread):
                        time.sleep(3)
                        retry += 1
                        if retry == 3:
                            raise e
                        continue

    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
