import sys

from typing import List, TypedDict
from logging import Logger
from sqlalchemy.orm import Session
from time import time

import click
import structlog

from chat.container.app import AppContainer

logger: Logger = structlog.get_logger()


class PartThread(TypedDict):
    thread_id: int
    user_id: str
    folder: str


def load_all_secret(session: Session):
    p_threads: List[PartThread] = []
    query = (
        "select pt.user_id, pt.thread_id, pt.folder "
        "from participant_threads as pt "
        "where pt.folder = 'secret' AND pt.is_removed=0"
    )
    result = session.execute(query)

    logger.info("Loading collabs from database ...")
    ti = time()

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            p_threads.append(dict(row))  # type: ignore

    ti = time() - ti
    logger.info("Loaded all p_threads in {:.2f}s!".format(ti))

    return p_threads


@click.command()
def main():
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()

    app = container.app()
    mysql_c = app.conns.mysql

    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")

    p_threads: List[PartThread] = []
    with mysql_c.get_session() as session:
        p_threads = load_all_secret(session)
        if not p_threads:
            logger.warning("No threads found, exit")
            return

    logger.info("Num p_threads: {}".format(len(p_threads)))
    updated = 0

    container.folder.init_resources()

    for p_thread in p_threads:
        container.folder().usecases().move_to_folder(
            user_id=p_thread["user_id"],
            new_folder="default",
            thread_ids=[p_thread["thread_id"]],
            partner_ids=[],
        )
        updated += 1
    logger.info(f"Done! Updated {updated} threads!")

    # closing connection
    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
