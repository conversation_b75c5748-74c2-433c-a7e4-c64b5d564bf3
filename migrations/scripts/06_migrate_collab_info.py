"""
This script synces collab avatar/description to membership service.

Before this, membership didn't store avatar/description information.
"""
import sys
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from logging import Logger
from time import time
from typing import Dict, List, TypedDict

import click
import structlog
from sqlalchemy.orm import Session
from tqdm import tqdm

from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
)
from chat.threads.repository.interface import ThreadRepository

logger: Logger = structlog.get_logger()


class ChatThread(TypedDict):
    id: int
    name: str
    description: str
    avatar: str
    owner: str
    group_level: int
    workspace_id: str
    collab_id: str


@dataclass
class ChatMember:
    user_id: str
    thread_id: int
    role: str


ROLE_MAP: Dict[str, CollabGroupRole] = {
    "owner": CollabGroupRole.Owner,
    "admin": CollabGroupRole.Admin,
    "member": CollabGroupRole.Member,
}


def load_all_collabs(session: Session):
    threads: List[ChatThread] = []
    query = (
        "select t.id, t.name, t.description, t.avatar, t.group_level, t.workspace_id, t.collab_id, p.user_id as owner "
        "from threads as t "
        "INNER JOIN participant_threads as p on t.id = p.thread_id "
        "where collab_id is not null and p.role='owner'"
    )
    result = session.execute(query)

    logger.info("Loading collabs from database ...")
    ti = time()

    total = 0
    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        total += len(chunk)
        for row in chunk:
            threads.append(dict(row))  # type: ignore

    ti = time() - ti
    logger.info("Loaded all collabs in {:.2f}s!".format(ti))

    return threads


def get_members_in_thread(session, thread_id: int):
    query = (
        f"SELECT user_id, role FROM participant_threads "
        "WHERE thread_id= :thread_id "
        "AND is_removed=False "
        "ORDER BY user_id"
    )
    members: List[Dict] = []
    result = session.execute(query, {"thread_id": thread_id})

    while True:
        chunk = result.fetchmany(100)
        if not chunk:
            break

        for row in chunk:
            members.append(dict(row))  # type: ignore

    return members


@click.command()
@click.option("--num_threads", type=click.INT, default=10)
def main(num_threads: int):
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()

    app = container.app()
    mysql_c = app.conns.mysql

    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")

    membership_client = app.repos.gapo.membership
    thread_repo: ThreadRepository = app.repos.thread
    pt_repo = app.repos.pt

    threads: List[ChatThread] = []
    with mysql_c.get_session() as session:
        # conn = conn.execution_options(stream_results=True, max_row_buffer=100)
        threads = load_all_collabs(session)
        if not threads:
            logger.warning("No threads found, exit")
            return

    logger.info("Num threads: {}".format(len(threads)))

    def update_collab_info(thread: ChatThread):
        try:
            with mysql_c.get_session() as session:
                thread_id = thread["id"]
                owner_id_str = thread.get("owner")
                workspace_id = thread["workspace_id"]
                thread_name = thread["name"] or ""
                thread_description = thread["description"] or ""
                thread_avatar = thread["avatar"] or ""

                if not owner_id_str or not workspace_id:
                    logger.info("Owner: {}".format(owner_id_str))
                    return None

                owner_id = int(owner_id_str)
                workspace_id = int(workspace_id)  # type: ignore

                collab_id = thread["collab_id"]

                # update name if needed
                try:
                    collab = membership_client.get_collab_group(
                        owner_id, collab_id, workspace_id
                    )
                except PermissionError:
                    logger.warning(
                        "User {owner_id} is not the owner of collab {collab_id}, thread: {thread_id}, need to update manual!"
                    )
                    # owner not found
                    # need to create owner
                    return

                updated = False
                if (
                    collab.name != thread_name
                    or collab.description != thread_description
                    or collab.avatar != thread_avatar
                ):
                    try:
                        membership_client.update_collab_info(
                            collab_id,
                            owner_id,
                            workspace_id,
                            name=thread_name,
                            avatar=thread_avatar,
                            description=thread_description,
                        )
                        updated = True
                    except Exception as e:
                        logger.warning(
                            f"Failed to update collab {collab_id} because error {e}"
                        )

                return updated

        except Exception as e:
            logger.warning(
                f"Got error while syncing collab members, collab_id: {thread['collab_id']}: error: {e}",
                exc_info=True,
            )
            return None

    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        results = list(
            tqdm(executor.map(update_collab_info, threads), total=len(threads))
        )
        # build thread to collab map
        updated = 0
        for rs in results:
            if rs:
                updated += 1

    logger.info(f"Done! Updated {updated} threads!")

    # closing connection
    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
