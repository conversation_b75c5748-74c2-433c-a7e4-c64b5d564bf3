"""
This script allows us to sync colab members between chat database
and membership service.

Usage:

To sync all collabs:

```
python migration_scripts/04_sync_collab_members.py --all_chats true
```

To sync some collabs:

```
python migration_scripts/04_sync_collab_members.py --collab_ids 123,2,4
```

or:

```
python migration_scripts/04_sync_collab_members.py --thread_ids 1234,1567
```

"""
import sys
import time
from logging import Logger
from typing import Dict, List, Union
from pydantic import BaseModel

import click
import structlog
from sqlalchemy.orm import Session

from chat.connections.gapo_client.membership.membership_client import (
    CollabGroupRole,
    MemberAlreadyExist,
)
from chat.container.app import AppContainer

logger: Logger = structlog.get_logger()


ROLE_MAP: Dict[str, CollabGroupRole] = {
    "owner": CollabGroupRole.Owner,
    "admin": CollabGroupRole.Admin,
    "member": CollabGroupRole.Member,
}


class Thread(BaseModel):
    id: int
    name: str
    collab_id: Union[str, None]
    workspace_id: Union[str, None]


class ParticipantThread(BaseModel):
    thread_id: int
    user_id: str
    is_removed: bool
    role: str
    type: str

    def is_owner(self):
        return self.role == "owner"

    def is_member(self):
        return self.role == "member"

    def is_bot(self):
        return self.type == "bot"


class Membership(BaseModel):
    collab_id: str
    user_id: int
    role: int
    status: str

    def is_owner(self):
        return self.role == 1

    def is_admin(self):
        return self.role == 2

    def is_member(self):
        return self.role == 3

    def is_active(self):
        return self.status == "active"


def get_owner(members: List[ParticipantThread]):
    for member in members:
        if member.is_owner():
            return member


def get_collab_members_by_id(session: Session, collab_id: str):
    query = """
    SELECT * FROM memberships WHERE collab_id=:collab_id
        AND status ='active'
    """
    result = session.execute(query, {"collab_id": collab_id})
    rows = result.fetchall()
    if not rows:
        return []
    return [Membership(**dict(row)) for row in rows]


def get_part_user_thread_by_id(session: Session, thread_id: str):
    query = """
    SELECT * FROM participant_threads WHERE thread_id=:thread_id 
        AND is_removed=False
        AND type='user'
    """
    result = session.execute(query, {"thread_id": thread_id})
    rows = result.fetchall()
    if not rows:
        return []
    return [ParticipantThread(**dict(row)) for row in rows]


def get_part_thread_by_id(session: Session, thread_id: str):
    query = """
    SELECT * FROM participant_threads WHERE thread_id=:thread_id 
        AND is_removed=False
    """
    result = session.execute(query, {"thread_id": thread_id})
    rows = result.fetchall()
    if not rows:
        return []
    return [ParticipantThread(**dict(row)) for row in rows]


def get_chat_thread_no_collab(session: Session):
    query = """
    SELECT t.id, t.name, t.group_level, t.workspace_id, t.collab_id 
        FROM threads as t WHERE (t.collab_id is null or t.collab_id = '') 
        AND t.type='group' 
        AND t.member_count > 0
        """
    result = session.execute(query, {})
    rows = result.fetchall()
    if not rows:
        return []
    return [Thread(**dict(row)) for row in rows]


def get_chat_thread_no_owner(session: Session):
    query = """
    SELECT * FROM threads WHERE threads.type='group' 
        AND threads.member_count > 0
    """


def update_thread_collab(session: Session, thread_id: str, collab_id: str):
    query = "UPDATE threads SET collab_id=:collab_id WHERE id= :thread_id "
    session.execute(query, {"thread_id": thread_id, "collab_id": collab_id})
    session.commit()


def update_owner_thread(session, thread_id: int, owner_id: str):
    query = (
        "UPDATE participant_threads "
        " SET is_removed=False, role=:role "
        "WHERE thread_id= :thread_id "
        "AND user_id=:user_id "
    )
    session.execute(
        query, {"role": "owner", "thread_id": thread_id, "user_id": owner_id}
    )


def update_owner_collab(session, collab_id: str, user_id: int):
    query = """
    UPDATE memberships SET role=3
    """


def revive_owner(session, thread, members):
    owner = get_owner(members)
    if not owner:
        members[0].role = "owner"
        owner = get_owner(members)

    logger.info("Revive owner %s", owner.user_id)
    update_owner_thread(session, thread.id, owner.user_id)
    session.commit()


def map_member_to_collab(members: List[ParticipantThread]):
    return [
        (
            int(member.user_id),
            [ROLE_MAP[member.role]],
        )
        for member in members
        if member.user_id.isnumeric()
    ]


def sync_to_collab(membership_client, thread, members):
    collab_members = map_member_to_collab(members)
    for member in collab_members:
        logger.info("Adding member to collab %s", member[0])
        try:
            membership_client.add_member_direct(
                member[0],
                thread.collab_id,
                member[0],
                thread.workspace_id,
                member[1],
            )
        except MemberAlreadyExist:
            continue
        except Exception as e:
            logger.error(e)
            continue


def update_threads_no_collab(chat_sql, collab_cli):
    with chat_sql.get_session() as session:
        threads = get_chat_thread_no_collab(session)
        logger.info("Found %s threads", len(threads))
        logger.info("Sleep before real run in 10s")
        time.sleep(10)
        for thread in threads:
            logger.info("### Get members from threads %s", thread.id)
            user_members = get_part_user_thread_by_id(session, thread.id)
            if len(user_members) == 0:
                logger.info(
                    "###### Thread %s has 0 user members, IGNORE", thread.id
                )
                continue
            else:
                logger.info(
                    "###### Thread %s has %s user members",
                    thread.id,
                    len(user_members),
                )
            owner = get_owner(user_members)
            if not owner:
                logger.info("###### Thread %s has no owner", thread.id)
                revive_owner(session, thread, user_members)
            owner = get_owner(user_members)
            if not owner.user_id.isnumeric():
                logger.info(
                    "###### Ignore thread cause owner %s", owner.user_id
                )
                continue
            collab_id = collab_cli.create_collab_group(
                caller_id=int(owner.user_id),
                name=thread.name or "Collab",
                workspace_id=thread.workspace_id,
            )
            logger.info(
                "###### Update thread %s by collab %s", thread.id, collab_id
            )
            thread.collab_id = collab_id
            update_thread_collab(session, thread.id, collab_id)
            members = get_part_thread_by_id(session, thread.id)
            sync_to_collab(collab_cli, thread, members)


@click.command()
# @click.argument("collab_mysql_url", type=click.STRING)
def main():
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    container.check_dependencies()
    app = container.app()
    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")
    membership_client = app.repos.gapo.membership
    # pt_usc = app.participant.usecases

    update_threads_no_collab(mysql_c, membership_client)
    app.conns.close_all()


if __name__ == "__main__":
    main()  # type: ignore
