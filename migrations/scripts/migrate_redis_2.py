# type: ignore
# pylint: skip-file

import os
from functools import partial
from multiprocessing import Pool

import click
import redis
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker


class MySQLConnection(object):
    def __init__(self, url):
        self.connect(url)

    def connect(self, url):
        engine = create_engine(url, pool_pre_ping=True)
        session_factory = sessionmaker(bind=engine)
        self.Session = scoped_session(session_factory)

    def session(self):
        return self.Session()


class RedisConnection(object):
    def __init__(self, host, passwd, port, db):
        self.connect(host, passwd, port, db)

    def connect(self, host, passwd, port, db):
        params = {"host": host, "port": port, "db": db}
        if passwd:
            params["password"] = passwd
        self.client = redis.Redis(**params)
        self.client.ping()


def worker(url, redis_host, redis_password, redis_port, redis_db, user_ids):
    mysql_c = MySQLConnection(url)
    session = mysql_c.session()
    tsf = "{user_id}_threads"
    tfsf = tsf + "_{folder}"
    redis_c = RedisConnection(redis_host, redis_password, redis_port, redis_db)
    max_user = len(user_ids)
    start = 1
    for u in user_ids:
        u = u[0]
        threadInfos = session.execute(
            "SELECT thread_id, folder FROM participant_threads WHERE user_id=:user_id",
            {"user_id": u},
        ).fetchall()
        print("Start {} {}/{}".format(os.getpid(), start, max_user))
        for thread_id, folder in threadInfos:
            infoTsf = tsf.format(user_id=u)
            infoTfsf = tfsf.format(user_id=u, folder=folder)
            score = redis_c.client.zscore(infoTsf, thread_id)
            print(f"    GET SCORE {infoTsf} Thread {thread_id} Score {score}")
            if score:
                print(f"        UPDATE {infoTfsf} Thread {thread_id} Score {score}")
                redis_c.client.zadd(infoTfsf, {thread_id: score})

        print("DONE {} {}/{}".format(os.getpid(), start, max_user))
        start += 1


@click.command()
@click.argument("url", type=click.STRING)
@click.argument("redis_host", type=click.STRING)
@click.argument("redis_password", type=click.STRING)
@click.argument("redis_port", type=click.STRING)
@click.argument("redis_db", type=click.INT)
@click.argument("max_pool", type=click.INT)
def manager(url, redis_host, redis_password, redis_db, redis_port, max_pool):
    mysql_c = MySQLConnection(url)
    session = mysql_c.session()
    print("GETTING DISTINCE users")
    user_ids = session.execute(
        "SELECT DISTINCT user_id FROM participant_threads"
    ).fetchall()
    print(f"GOT DISTINCE {len(user_ids)}")
    pool = Pool(processes=max_pool)
    func = partial(worker, url, redis_host, redis_password, redis_port, redis_db)
    max_size = len(user_ids) // max_pool + 1
    for y in pool.imap_unordered(
        func,
        [user_ids[i : i + max_size] for i in range(0, len(user_ids), max_size)],
    ):
        pass
    print("FINISHED")


if __name__ == "__main__":
    manager()  # type: ignore
