import sys

import click
import structlog
import json
import time

from chat import load_cdn_mapping
from chat.container.app import AppContainer
from chat.utils import common

logger = structlog.get_logger()


@click.command()
@click.argument("user_id", type=click.STRING)
@click.argument("export", type=click.BOOL, default=False)
def main(user_id: str, export: bool):
    container = load_container()
    app = container.app()

    mysql_c = app.conns.mysql
    logger.info("Connecting to db ...")
    mysql_c.connect()
    logger.info("Connected to db!")
    # pt_usc = app.participant.usecases

    get_thread_usc = container.thread().usecases().get_thread
    leave_thread_usc = container.thread().usecases().leave_thread
    message_usc = container.message().usecases().create_message
    logger.info("Disable deactivation for user ...")
    leave_thread_usc.disable_thread_reactivate(user_id)

    pthreads = get_thread_usc.get_all_thread_by_user(user_id)
    logger.info(f"Found {len(pthreads)} threads for user: {user_id}")
    if export:
        export_only(container, pthreads)
    else:
        for pthread in pthreads:
            logger.info(
                f"""Thread ID: {pthread.thread.id} User ID: {pthread.participant.user_id} IsRemoved: {pthread.participant.is_removed} IsDeactivated: {pthread.participant.is_deactivated}"""  # noqa
            )
            if not pthread.participant.is_removed:
                if pthread.thread.is_group():
                    logger.info(f"Leave thread {pthread.thread.id}")
                    leave_thread_usc.leave_group(
                        pthread.thread.id, pthread.participant.user_id
                    )

            message_count = app.repos.counter.get_counter(pthread.thread.id)
            logger.info(
                f"""
                Thread ID: {pthread.thread.id},
                User ID: {pthread.participant.user_id},
                    MessageCount: {message_count}
                        """
            )
            while message_count > 0:
                message_to = message_count
                message_from = max(0, message_to - 100)
                message_count = message_from
                logger.info(
                    f"""Thread ID: {pthread.thread.id}, User ID: {pthread.participant.user_id} Begin message_from: {message_from} End message_to: {message_to}"""  # noqa
                )

                messages = app.repos.message.get_between(
                    pthread.thread.id, message_to, message_from
                )
                for idx, m in enumerate(messages):
                    logger.info(f"Checking thread {pthread.thread.id} message {idx}/{len(messages)}") # noqa
                    if m.user_id == pthread.participant.user_id:
                        logger.info(f"Trigger thread {pthread.thread.id} delete message {m.id}") # noqa
                        m.will_deleted_at = common.now()
                        message_usc.rb_broker.publish_delete(m)
                        time.sleep(0.01)


def export_only(container, pthreads):
    app = container.app()
    mysql_c = app.conns.mysql

    mcr = app.repos.media_collection
    filename = f"export_links_{time.time()}.txt"
    logger.info(f"Get ready for output: {filename}")

    with open(filename, "w") as f:
        with mysql_c.get_session() as session:
            for pidx, pthread in enumerate(pthreads):
                logger.info(
                    f"Thread {pthread.thread.id} At index {pidx}/{len(pthreads)}" # noqa
                )
                media_collections = mcr.get_all_by_user(
                    session, pthread.thread.id, pthread.participant.user_id
                )
                for index, m in enumerate(media_collections):
                    logger.info(
                        f" == Running {index}/{len(media_collections)}"
                    )
                    detail = json.loads(m["detail"])
                    for media in detail["media"]:
                        if any(
                            ext in media for ext in ["image", "file", "video"]
                        ):
                            logger.info(f"  == Found+Write link {media}")
                            f.write(media + "\n")
    logger.info(f"Write done ==> {filename}")


def write_file(filename):
    pass


def load_container():
    logger.info("Init container ...")
    container = AppContainer()
    container.init_resources()
    container.wire(packages=[sys.modules["chat"]])
    # make sure everything is ok
    container.check_dependencies()
    config = container.conf()
    load_cdn_mapping.load(config.cdn_mapping_filepath)
    return container


if __name__ == "__main__":
    main()  # This will be the entry point for the CLI
