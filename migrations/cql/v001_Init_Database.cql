/* Cassandra migration for keyspace chat_server.
Version 1 - 2019-07-19T07:05:57.698463+00:00

Init: Database */

CREATE TYPE policies (
	id INT,
	body TEXT,
	user_id TEXT,
);

CREATE TYPE last_message (
	id BIGINT,
	body TEXT,
	user_id TEXT,
	created_at BIGINT,
);

CREATE TABLE threads (
	name TEXT,
	avatar TEXT,
	policies LIST<FROZEN <policies>>,
	id BIGINT,
	group_level INT,
	is_direct BOOLEAN,
	deleted_at BIGINT,
	is_deleted BOOLEAN,
	is_lock BOOLEAN,
	last_message FROZEN <last_message>,
	PRIMARY KEY(id),
);
CREATE INDEX ON threads (is_deleted);
