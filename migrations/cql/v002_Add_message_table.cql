/* Cassandra migration for keyspace chat_server.
Version 2 - 2019-07-19T17:41:26.167153+00:00

Add: message table */

CREATE  TYPE react (
	user_id TEXT,
	code TEXT,
);

CREATE TYPE reply_message (
	id BIGINT,
	body TEXT,
	user_id TEXT,
	delete_level INT,
	deleted_by set<TEXT>,
	type TEXT,
);

CREATE TABLE message_counter (
	thread_id BIGINT,
	count COUNTER,
	PRIMARY KEY(thread_id),
);

CREATE TABLE messages (
	user_id TEXT,
	thread_id BIGINT,
	bucket BIGINT,
	uuid UUID,
	react LIST<FROZEN <react>>,
	reply_to FROZ<PERSON> <reply_message>,
	delete_level INT,
	deliver_status INT,
	body TEXT,
	id BIGINT,
	created_at BIGINT,
	PRIMARY KEY((thread_id, bucket), id),
) WITH CLUSTERING ORDER BY (id DESC);
