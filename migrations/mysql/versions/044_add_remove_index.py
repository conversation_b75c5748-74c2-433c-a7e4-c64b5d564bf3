"""Add: remove index

Revision ID: 044
Revises: 043
Create Date: 2020-06-09 15:32:35.138360

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '044'
down_revision = '043'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_participant_threads_is_removed', table_name='participant_threads')
    op.drop_index('ix_participant_threads_partner_id', table_name='participant_threads')
    op.drop_index('user_pin', table_name='participant_threads')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('user_pin', 'participant_threads', ['user_id', 'pin_pos'], unique=False)
    op.create_index('ix_participant_threads_partner_id', 'participant_threads', ['partner_id'], unique=False)
    op.create_index('ix_participant_threads_is_removed', 'participant_threads', ['is_removed'], unique=False)
    # ### end Alembic commands ###
