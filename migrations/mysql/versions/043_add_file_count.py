"""Add: file count

Revision ID: 043
Revises: 042
Create Date: 2020-06-04 16:19:23.972247

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '043'
down_revision = '042'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('file_count', mysql.BIGINT(display_width=4), server_default='0', nullable=True))
    op.add_column('threads', sa.Column('file_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'file_count')
    op.drop_column('participant_threads', 'file_count')
    # ### end Alembic commands ###
