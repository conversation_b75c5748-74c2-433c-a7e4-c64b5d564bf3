"""Add: change size

Revision ID: 037
Revises: 036
Create Date: 2020-05-19 08:42:16.161328

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '037'
down_revision = '036'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('media_collection_delete_logs', 'created_at')
    op.add_column('media_collections', sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), server_default='0', nullable=True))
    op.alter_column('threads', 'group_level',
               existing_type=mysql.TINYINT(display_width=3, unsigned=True),
               nullable=False,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('threads', 'group_level',
               existing_type=mysql.TINYINT(display_width=3, unsigned=True),
               nullable=True,
               existing_server_default=sa.text("'0'"))
    op.drop_column('media_collections', 'created_at')
    op.add_column('media_collection_delete_logs', sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
