"""Update: quick message to json

Revision ID: 078
Revises: 077
Create Date: 2025-04-17 17:48:06.550482

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '078'
down_revision = '077'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('quick_messages', 'message',
               existing_type=mysql.VARCHAR(length=4096),
               type_=sa.JSON(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('quick_messages', 'message',
               existing_type=sa.JSON(),
               type_=mysql.VARCHAR(length=4096),
               nullable=False)
    # ### end Alembic commands ###
