"""Add: index threads and participants

Revision ID: 076
Revises: 075
Create Date: 2024-07-23 16:36:27.392861

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '076'
down_revision = '075'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx__participant_threads__thread_id_removed', 'participant_threads', ['thread_id', 'is_removed'], unique=False)
    op.alter_column('threads', 'group_level',
               existing_type=mysql.BIGINT(unsigned=True),
               nullable=False,
               existing_server_default=sa.text("'0'"))
    op.create_index('idx__threads__parent_id_root_message_id', 'threads', ['parent_id', 'root_message_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx__threads__parent_id_root_message_id', table_name='threads')
    op.alter_column('threads', 'group_level',
               existing_type=mysql.BIGINT(unsigned=True),
               nullable=True,
               existing_server_default=sa.text("'0'"))
    op.drop_index('idx__participant_threads__thread_id_removed', table_name='participant_threads')
    # ### end Alembic commands ###
