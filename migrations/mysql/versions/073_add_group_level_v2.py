"""Add: group_level v2

Revision ID: 073
Revises: 072
Create Date: 2024-02-05 10:30:47.019640

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "073"
down_revision = "072"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "threads",
        sa.Column(
            "group_level_V2",
            mysql.BIGINT(display_width=20, unsigned=True),
            server_default="0",
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("threads", "group_level_V2")
    # ### end Alembic commands ###
