"""Add: change size

Revision ID: 036
Revises: 035
Create Date: 2020-05-19 00:13:31.306398

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '036'
down_revision = '035'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('flirt_samples', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               existing_nullable=False,
               type_=mysql.SMALLINT(unsigned=True),
               autoincrement=True)

    op.alter_column('threads', 'member_count',
               existing_type=mysql.INTEGER(display_width=10, unsigned=True),
               type_=mysql.SMALLINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('threads', 'group_level',
               existing_type=mysql.INTEGER(display_width=11),
               type_=mysql.TINYINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('threads', 'message_count',
               existing_type=mysql.INTEGER(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=4,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('threads', 'id',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('participants', 'thread_id',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('participants', 'read_count',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participants', 'delete_to',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participants', 'pin_pos',
               existing_type=mysql.INTEGER(display_width=10, unsigned=True),
               type_=mysql.TINYINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'thread_id',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('participant_threads', '_updated',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'delete_to',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'pin_pos',
               existing_type=mysql.INTEGER(display_width=10, unsigned=True),
               type_=mysql.TINYINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'read_count',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('users', 'seen_at',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('block_users', 'thread_id',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               )
    op.alter_column('policies', 'thread_id',
               existing_type=mysql.BIGINT(display_width=20, unsigned=True),
               type_=mysql.BIGINT(display_width=8,unsigned=True),
               existing_nullable=False,
               )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('flirt_samples', 'id',
               existing_type=mysql.SMALLINT(unsigned=True),
               existing_nullable=False,
               type_=mysql.INTEGER(display_width=11),
               autoincrement=True)

    op.alter_column('threads', 'member_count',
               existing_type=mysql.SMALLINT(unsigned=True),
               type_=mysql.INTEGER(display_width=10, unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('threads', 'message_count',
               existing_type=mysql.INTEGER(display_width=4, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('threads', 'id',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('threads', 'group_level',
               existing_type=mysql.TINYINT(unsigned=True),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('participants', 'thread_id',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('participants', 'read_count',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participants', 'delete_to',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participants', 'pin_pos',
               existing_type=mysql.TINYINT(unsigned=True),
               type_=mysql.INTEGER(display_width=10, unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'thread_id',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_nullable=False,
               )

    op.alter_column('participant_threads', '_updated',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'delete_to',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'pin_pos',
               existing_type=mysql.TINYINT(unsigned=True),
               type_=mysql.INTEGER(display_width=10, unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))

    op.alter_column('participant_threads', 'read_count',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('users', 'seen_at',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_server_default=sa.text("'0'"))

    op.alter_column('block_users', 'thread_id',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               )
    op.alter_column('policies', 'thread_id',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.BIGINT(display_width=20,unsigned=True),
               existing_nullable=False,
               )
    # ### end Alembic commands ###
