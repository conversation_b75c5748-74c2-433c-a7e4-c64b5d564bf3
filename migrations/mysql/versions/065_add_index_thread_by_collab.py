"""add index thread by collab

Revision ID: 065
Revises: 064
Create Date: 2022-11-04 09:25:06.451420

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '065'
down_revision = '064'
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "idx__threads__collab_id",
        table_name="threads",
        columns=["collab_id"],
    )


def downgrade():
    op.drop_index("idx__threads__collab_id", table_name="threads")
