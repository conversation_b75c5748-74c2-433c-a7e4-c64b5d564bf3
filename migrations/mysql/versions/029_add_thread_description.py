"""Add: thread description

Revision ID: 029
Revises: 028
Create Date: 2020-04-20 14:35:07.709000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '029'
down_revision = '028'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('description', sa.String(length=256), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'description')
    # ### end Alembic commands ###
