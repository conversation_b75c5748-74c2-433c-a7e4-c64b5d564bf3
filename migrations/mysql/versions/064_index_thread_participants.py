"""index thread participants

Revision ID: 064
Revises: 063
Create Date: 2022-10-11 09:48:52.407787

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '064'
down_revision = '063'
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "idx__participant_threads__thread_id",
        table_name="participant_threads",
        columns=["thread_id"],
    )
    pass


def downgrade():
    op.drop_index("idx__participant_threads__thread_id", table_name="participant_threads")
