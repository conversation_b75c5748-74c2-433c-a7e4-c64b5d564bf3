"""Update: change size column

Revision ID: 032
Revises: 031
Create Date: 2020-05-15 15:42:05.903187

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '032'
down_revision = '031'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('block_users', 'blocked_id',
               existing_type=mysql.VARCHAR(length=255),
               existing_nullable=False,
               type_=sa.String(length=24))
    op.alter_column('block_users', 'is_removed',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('block_users', 'pair_ids',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.String(length=49),
               existing_nullable=True)
    op.alter_column('block_users', 'type',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=16),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('block_users', 'user_id',
               existing_type=mysql.VARCHAR(length=255),
               existing_nullable=False,
               type_=sa.String(length=24))
    op.alter_column('participant_threads', 'enable_notify',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_nullable=True,
               existing_server_default=sa.text("'1'"))
    op.alter_column('participant_threads', 'is_removed',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('participant_threads', 'partner_id',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=24),
               existing_nullable=True)
    op.alter_column('participant_threads', 'role',
               existing_type=mysql.VARCHAR(length=1024),
               type_=sa.String(length=16),
               existing_nullable=False)
    op.alter_column('participant_threads', 'type',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=16),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('participant_threads', 'user_id',
               existing_type=mysql.VARCHAR(length=255),
               existing_nullable=False,
               type_=sa.String(length=24))
    op.alter_column('participants', 'is_removed',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('participants', 'role',
               existing_type=mysql.VARCHAR(length=1024),
               type_=sa.String(length=16),
               existing_nullable=False)
    op.alter_column('participants', 'type',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=16),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('participants', 'user_id',
               existing_type=mysql.VARCHAR(length=255),
               existing_nullable=False,
               type_=sa.String(length=24))
    op.alter_column('policies', 'is_removed',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_server_default=sa.text("'0'"))
    op.alter_column('policies', 'user_id',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=24),
               existing_nullable=False)
    op.alter_column('threads', 'blocked_by',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=24),
               existing_nullable=True)
    op.alter_column('threads', 'creator',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=24),
               existing_nullable=True)
    op.alter_column('threads', 'pair_ids',
               existing_type=mysql.VARCHAR(length=100),
               type_=sa.String(length=49),
               existing_nullable=True)
    op.alter_column('users', 'enable_notify',
               existing_type=mysql.TINYINT(display_width=1),
               type_=sa.Boolean(),
               existing_nullable=True,
               existing_server_default=sa.text("'1'"))
    op.alter_column('users', 'id',
               existing_type=mysql.VARCHAR(length=255),
               existing_nullable=False,
               type_=sa.String(length=24))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'id',
               existing_type=sa.String(length=24),
               existing_nullable=False,
               type_=mysql.VARCHAR(length=255))
    op.alter_column('users', 'enable_notify',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'1'"))
    op.alter_column('threads', 'pair_ids',
               existing_type=sa.String(length=49),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('threads', 'creator',
               existing_type=sa.String(length=24),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('threads', 'blocked_by',
               existing_type=sa.String(length=24),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('policies', 'user_id',
               existing_type=sa.String(length=24),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('policies', 'is_removed',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('participants', 'user_id',
               existing_type=sa.String(length=24),
               existing_nullable=False,
               type_=mysql.VARCHAR(length=255))
    op.alter_column('participants', 'type',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('participants', 'role',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=1024),
               existing_nullable=False)
    op.alter_column('participants', 'is_removed',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('participant_threads', 'user_id',
               existing_type=sa.String(length=24),
                existing_nullable=False,
               type_=mysql.VARCHAR(length=255))
    op.alter_column('participant_threads', 'type',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('participant_threads', 'role',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=1024),
               existing_nullable=False)
    op.alter_column('participant_threads', 'partner_id',
               existing_type=sa.String(length=24),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True)
    op.alter_column('participant_threads', 'is_removed',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('participant_threads', 'enable_notify',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'1'"))
    op.alter_column('block_users', 'user_id',
               existing_type=sa.String(length=24),
                existing_nullable=False,
               type_=mysql.VARCHAR(length=255))
    op.alter_column('block_users', 'type',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=255),
               existing_nullable=True,
               existing_server_default=sa.text("'user'"))
    op.alter_column('block_users', 'pair_ids',
               existing_type=sa.String(length=49),
               type_=mysql.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('block_users', 'is_removed',
               existing_type=sa.Boolean(),
               type_=mysql.TINYINT(display_width=1),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('block_users', 'blocked_id',
               existing_type=sa.String(length=24),
               existing_nullable=False,
               type_=mysql.VARCHAR(length=255))
    # ### end Alembic commands ###
