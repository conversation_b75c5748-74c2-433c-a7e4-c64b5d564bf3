"""change folder size

Revision ID: 058
Revises: 057
Create Date: 2022-04-15 16:21:29.315137

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '058'
down_revision = '057'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column('participant_threads', 'folder',
        existing_type=sa.String(length=16),
        type_=sa.String(length=32),
        existing_nullable=True
    )


def downgrade():
    op.alter_column('participant_threads', 'folder',
        existing_type=sa.String(length=32),
        type_=sa.String(length=16),
        existing_nullable=True
    )
