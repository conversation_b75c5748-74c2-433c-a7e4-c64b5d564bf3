"""add workspace_id

Revision ID: 056
Revises: 055
Create Date: 2021-11-02 10:12:26.616106

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision = '056'
down_revision = '055'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('threads', sa.Column('workspace_id', sa.String(length=24), nullable=True))


def downgrade():
    op.drop_column('threads', 'workspace_id')

