"""Add: thread disable notify

Revision ID: 030
Revises: 029
Create Date: 2020-04-27 09:22:07.545258

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '030'
down_revision = '029'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.<PERSON>n('enable_notify', sa.<PERSON>(), server_default='1', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'enable_notify')
    # ### end Alembic commands ###
