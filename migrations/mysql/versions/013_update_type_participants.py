"""Update: type participants

Revision ID: 013
Revises: 012
Create Date: 2019-12-10 15:32:06.857054

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '013'
down_revision = '012'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('type', sa.String(length=255), server_default='user', nullable=True))
    op.add_column('participants', sa.Column('type', sa.String(length=255), server_default='user', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participants', 'type')
    op.drop_column('participant_threads', 'type')
    # ### end Alembic commands ###
