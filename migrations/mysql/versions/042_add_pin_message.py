"""Add: pin message

Revision ID: 042
Revises: 041
Create Date: 2020-06-01 16:03:07.216733

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '042'
down_revision = '041'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('pinned_message_id', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'pinned_message_id')
    # ### end Alembic commands ###
