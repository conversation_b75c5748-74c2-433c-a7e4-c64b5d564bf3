"""Add: pin thread participants

Revision ID: 020
Revises: 019
Create Date: 2020-02-28 11:01:20.331194

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '020'
down_revision = '019'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participants', sa.Column('pin_pos', mysql.INTEGER(unsigned=True), server_default='0', nullable=True))
    op.create_index(op.f('ix_participants_pin_pos'), 'participants', ['pin_pos'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_participants_pin_pos'), table_name='participants')
    op.drop_column('participants', 'pin_pos')
    # ### end Alembic commands ###
