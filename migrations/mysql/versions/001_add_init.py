"""Add: init

Revision ID: 001
Revises: 
Create Date: 2019-10-23 09:47:09.803229

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('block_users',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('user_id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('block_id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('is_removed', sa.<PERSON>(), server_default='0', nullable=True),
    sa.<PERSON>eyConstraint('user_id', 'block_id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_block_users_is_removed'), 'block_users', ['is_removed'], unique=False)
    op.create_table('participant_threads',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('user_id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('thread_id', sa.BigInteger(), nullable=False),
    sa.Column('_updated', sa.BigInteger(), server_default='0', nullable=True),
    sa.Column('read_count', sa.BigInteger(), server_default='0', nullable=True),
    sa.Column('is_removed', sa.Boolean(), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('user_id', 'thread_id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_participant_threads_is_removed'), 'participant_threads', ['is_removed'], unique=False)
    op.create_table('participants',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('thread_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('delete_to', sa.BigInteger(), server_default='0', nullable=True),
    sa.Column('read_count', sa.BigInteger(), server_default='0', nullable=True),
    sa.Column('role', sa.String(length=1024), nullable=False),
    sa.Column('is_removed', sa.Boolean(), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'user_id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_participants_is_removed'), 'participants', ['is_removed'], unique=False)
    op.create_table('policies',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('thread_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('policy_id', sa.BigInteger(), nullable=False),
    sa.Column('body', sa.String(length=1024), nullable=True),
    sa.Column('is_removed', sa.Boolean(), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'policy_id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_policies_is_removed'), 'policies', ['is_removed'], unique=False)
    op.create_table('threads',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('avatar', sa.String(length=1024), nullable=True),
    sa.Column('name', sa.String(length=1024, collation="utf8_general_ci"),
        nullable=True),
    sa.Column('sender_id', mysql.INTEGER(unsigned=True), nullable=True),
    sa.Column('receiver_id', mysql.INTEGER(unsigned=True), nullable=True),
    sa.Column('is_lock', sa.Boolean(), server_default='0', nullable=True),
    sa.Column('group_level', sa.Integer(), server_default='0', nullable=False),
    sa.Column('last_message_id', sa.BigInteger(), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_threads_receiver_id'), 'threads', ['receiver_id'], unique=False)
    op.create_index(op.f('ix_threads_sender_id'), 'threads', ['sender_id'], unique=False)
    op.create_index('sender_receiver_index', 'threads', ['sender_id', 'receiver_id'], unique=False)
    op.create_table('users',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('id', mysql.INTEGER(unsigned=True), nullable=False),
    sa.Column('name', sa.String(length=1024, collation="utf8_general_ci"),
        nullable=True),
    sa.Column('seen_at', sa.BigInteger(), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('users')
    op.drop_index('sender_receiver_index', table_name='threads')
    op.drop_index(op.f('ix_threads_sender_id'), table_name='threads')
    op.drop_index(op.f('ix_threads_receiver_id'), table_name='threads')
    op.drop_table('threads')
    op.drop_index(op.f('ix_policies_is_removed'), table_name='policies')
    op.drop_table('policies')
    op.drop_index(op.f('ix_participants_is_removed'), table_name='participants')
    op.drop_table('participants')
    op.drop_index(op.f('ix_participant_threads_is_removed'), table_name='participant_threads')
    op.drop_table('participant_threads')
    op.drop_index(op.f('ix_block_users_is_removed'), table_name='block_users')
    op.drop_table('block_users')
    # ### end Alembic commands ###
