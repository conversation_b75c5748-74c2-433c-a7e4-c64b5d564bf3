"""Update: media collection with text and multiple url

Revision ID: 038
Revises: 037
Create Date: 2020-05-19 18:17:40.460042

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '038'
down_revision = '037'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_collections', sa.Column('detail', sa.JSON(), nullable=True))
    op.add_column('media_collections', sa.Column('length', mysql.TINYINT(unsigned=True), server_default='1', nullable=True))
    op.drop_column('media_collections', 'thumb')
    op.drop_column('media_collections', 'url')
    op.alter_column('threads', 'type',
               existing_type=mysql.VARCHAR(length=50),
               type_=sa.String(length=16),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('threads', 'type',
               existing_type=sa.String(length=16),
               type_=mysql.VARCHAR(length=50),
               existing_nullable=True)
    op.add_column('media_collections', sa.Column('url', mysql.VARCHAR(length=1024), nullable=True))
    op.add_column('media_collections', sa.Column('thumb', mysql.JSON(), nullable=True))
    op.drop_column('media_collections', 'length')
    op.drop_column('media_collections', 'detail')
    # ### end Alembic commands ###
