"""Add: type thread

Revision ID: 011
Revises: 010
Create Date: 2019-12-04 10:27:37.506644

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '011'
down_revision = '010'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('type', sa.String(length=16), nullable=True))
    op.create_index(op.f('ix_threads_type'), 'threads', ['type'], unique=False)
    op.execute('ALTER TABLE threads ALTER type SET default "direct"')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_threads_type'), table_name='threads')
    op.drop_column('threads', 'type')
    # ### end Alembic commands ###
