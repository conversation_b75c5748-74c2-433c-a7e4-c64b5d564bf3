"""Update: index

Revision ID: 041
Revises: 040
Create Date: 2020-05-26 09:45:28.550239

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '041'
down_revision = '040'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("UPDATE participant_threads JOIN participants ON participant_threads.thread_id =participants.thread_id AND participant_threads.user_id = participants.user_id SET participant_threads.type=participants.type")

    op.drop_index('ix_participants_is_removed', table_name='participants')
    op.drop_index('ix_participants_pin_pos', table_name='participants')
    op.drop_table('participants')
    op.drop_index('ix_participant_threads_pin_pos', table_name='participant_threads')
    op.drop_index('ix_participant_threads_thread_id', table_name='participant_threads')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_participant_threads_thread_id', 'participant_threads', ['thread_id'], unique=False)
    op.create_index('ix_participant_threads_pin_pos', 'participant_threads', ['pin_pos'], unique=False)
    op.create_table('participants',
    sa.Column('_created', mysql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', mysql.TIMESTAMP(), nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), autoincrement=False, nullable=False),
    sa.Column('user_id', mysql.VARCHAR(length=24), nullable=False),
    sa.Column('delete_to', mysql.BIGINT(display_width=8, unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('read_count', mysql.BIGINT(display_width=8, unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('role', mysql.VARCHAR(length=16), nullable=False),
    sa.Column('is_removed', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('type', mysql.VARCHAR(length=16), server_default=sa.text("'user'"), nullable=True),
    sa.Column('pin_pos', mysql.TINYINT(display_width=3, unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('alias', mysql.VARCHAR(charset='utf8mb4', length=1024), nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'user_id'),
    mysql_default_charset='latin1',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_participants_pin_pos', 'participants', ['pin_pos'], unique=False)
    op.create_index('ix_participants_is_removed', 'participants', ['is_removed'], unique=False)
    # ### end Alembic commands ###
