"""Update: resize pin_pos

Revision ID: 051
Revises: 050
Create Date: 2021-02-25 09:32:21.722496

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '051'
down_revision = '050'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('participant_threads', 'pin_pos',
               existing_type=mysql.TINYINT(unsigned=True),
               type_=mysql.BIGINT(display_width=8, unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('participant_threads', 'pin_pos',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.TINYINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###
