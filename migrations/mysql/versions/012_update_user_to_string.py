"""Update: user to string

Revision ID: 012
Revises: 011
Create Date: 2019-12-06 18:12:07.534140

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '012'
down_revision = '011'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('ALTER TABLE users MODIFY id VARCHAR(255) NOT NULL')
    op.execute('ALTER TABLE policies MODIFY user_id VARCHAR(255) NOT NULL')
    op.execute('ALTER TABLE participants MODIFY user_id VARCHAR(255) NOT NULL')
    op.execute('ALTER TABLE participant_threads MODIFY user_id VARCHAR(255) NOT NULL')
    op.execute('ALTER TABLE participant_threads MODIFY partner_id VARCHAR(255)')
    op.execute('ALTER TABLE block_users MODIFY user_id VARCHAR(255) NOT NULL')
    op.execute('ALTER TABLE block_users MODIFY blocked_id VARCHAR(255) NOT NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE users MODIFY id int(10) UNSIGNED NOT NULL')
    op.execute('ALTER TABLE policies MODIFY user_id int(10) UNSIGNED NOT NULL')
    op.execute('ALTER TABLE participants MODIFY user_id int(10) UNSIGNED NOT NULL')
    op.execute('ALTER TABLE participant_threads MODIFY user_id int(10) UNSIGNED NOT NULL')
    op.execute('ALTER TABLE participant_threads MODIFY partner_id int(10) UNSIGNED')
    op.execute('ALTER TABLE block_users MODIFY user_id int(10) UNSIGNED NOT NULL')
    op.execute('ALTER TABLE block_users MODIFY blocked_id int(10) UNSIGNED NOT NULL')
    # ### end Alembic commands ###
