"""add collab_id

Revision ID: 061
Revises: 060
Create Date: 2022-08-19 10:15:47.101361

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "061"
down_revision = "060"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "threads", sa.Column("collab_id", sa.String(length=24), nullable=True)
    )


def downgrade():
    op.drop_column("threads", "collab_id")
