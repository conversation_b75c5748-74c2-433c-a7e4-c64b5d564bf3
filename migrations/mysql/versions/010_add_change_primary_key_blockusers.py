"""Add: change primary key blockUsers

Revision ID: 010
Revises: 009
Create Date: 2019-11-29 16:01:45.023706

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE block_users DROP PRIMARY KEY, ADD PRIMARY KEY(user_id, blocked_id)')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
