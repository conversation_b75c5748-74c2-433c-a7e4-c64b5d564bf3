"""Update: block with pair_ids

Revision ID: 014
Revises: 013
Create Date: 2019-12-16 16:23:40.405693

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '014'
down_revision = '013'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('block_users', sa.Column('pair_ids', sa.String(length=49), nullable=True))
    op.create_index(op.f('ix_block_users_pair_ids'), 'block_users', ['pair_ids'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_block_users_pair_ids'), table_name='block_users')
    op.drop_column('block_users', 'pair_ids')
    # ### end Alembic commands ###
