"""Remove: group_level

Revision ID: 074
Revises: 073
Create Date: 2024-02-05 10:39:24.790996

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '074'
down_revision = '073'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'group_level')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('group_level', mysql.TINYINT(unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
