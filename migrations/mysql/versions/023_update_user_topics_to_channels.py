"""Update: user topics to channels

Revision ID: 023
Revises: 022
Create Date: 2020-03-19 10:33:46.648423

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '023'
down_revision = '022'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('channels', sa.String(length=1024), nullable=True))
    op.drop_column('users', 'topics')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('topics', mysql.VARCHAR(length=1024), nullable=True))
    op.drop_column('users', 'channels')
    # ### end Alembic commands ###
