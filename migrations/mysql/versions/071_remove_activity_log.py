"""Remove: activity log

Revision ID: 071
Revises: 070
Create Date: 2023-11-24 01:49:44.514716

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '071'
down_revision = '070'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('_ix_thread_type', table_name='activity_logs')
    op.drop_index('_ix_thread_user_message', table_name='activity_logs')
    op.drop_index('_ix_thread_user_type', table_name='activity_logs')
    op.drop_table('activity_logs')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_logs',
    sa.Column('_created', mysql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', mysql.TIMESTAMP(), nullable=True),
    sa.Column('created_at', mysql.BIGINT(unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.Column('thread_id', mysql.BIGINT(unsigned=True), autoincrement=False, nullable=False),
    sa.Column('user_id', mysql.VARCHAR(length=24), nullable=False),
    sa.Column('message_id', mysql.BIGINT(unsigned=True), autoincrement=False, nullable=False),
    sa.Column('detail', mysql.JSON(), nullable=True),
    sa.Column('type', mysql.TINYINT(unsigned=True), server_default=sa.text("'0'"), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'message_id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('_ix_thread_user_type', 'activity_logs', ['thread_id', 'user_id', 'type', 'message_id'], unique=False)
    op.create_index('_ix_thread_user_message', 'activity_logs', ['thread_id', 'user_id', 'message_id'], unique=False)
    op.create_index('_ix_thread_type', 'activity_logs', ['thread_id', 'type', 'message_id'], unique=False)
    # ### end Alembic commands ###
