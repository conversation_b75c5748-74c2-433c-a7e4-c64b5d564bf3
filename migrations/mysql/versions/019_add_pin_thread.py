"""Add: pin thread

Revision ID: 019
Revises: 018
Create Date: 2020-02-27 17:02:09.863102

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '019'
down_revision = '018'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('pin_pos', mysql.INTEGER(unsigned=True), server_default='0', nullable=True))
    op.create_index(op.f('ix_participant_threads_pin_pos'), 'participant_threads', ['pin_pos'], unique=False)
    op.create_index('user_pin', 'participant_threads', ['user_id', 'pin_pos'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('user_pin', table_name='participant_threads')
    op.drop_index(op.f('ix_participant_threads_pin_pos'), table_name='participant_threads')
    op.drop_column('participant_threads', 'pin_pos')
    # ### end Alembic commands ###
