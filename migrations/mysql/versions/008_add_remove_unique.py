"""Add: remove unique

Revision ID: 008
Revises: 007
Create Date: 2019-10-29 14:12:41.470199

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '008'
down_revision = '007'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_participant_threads_partner_id', table_name='participant_threads')
    op.create_index(op.f('ix_participant_threads_partner_id'), 'participant_threads', ['partner_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_participant_threads_partner_id'), table_name='participant_threads')
    op.create_index('ix_participant_threads_partner_id', 'participant_threads', ['partner_id'], unique=True)
    # ### end Alembic commands ###
