"""Update: last_message_id_to_json

Revision ID: 002
Revises: 001
Create Date: 2019-10-27 13:59:48.912933

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('last_message', sa.JSON(), nullable=True))
    op.drop_column('threads', 'last_message_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('last_message_id', mysql.BIGINT(display_width=20), server_default=sa.text("'0'"), autoincrement=False, nullable=True))
    op.drop_column('threads', 'last_message')
    # ### end Alembic commands ###
