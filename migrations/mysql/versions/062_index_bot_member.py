"""empty message

Revision ID: 062
Revises: 061
Create Date: 2022-10-11 09:24:56.934573

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "062"
down_revision = "061"
branch_labels = None
depends_on = None


def upgrade():
    # TODO: partial index is better
    # because we only want to filter bot
    op.create_index(
        "idx__participant_threads__bot_type",
        table_name="participant_threads",
        columns=["type"],
    )


def downgrade():
    op.drop_index("idx__participant_threads__bot_type", table_name="participant_threads")
