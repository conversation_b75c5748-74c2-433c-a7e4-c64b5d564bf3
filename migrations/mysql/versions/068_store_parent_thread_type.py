"""store parent_thread_type

Revision ID: 068
Revises: 067
Create Date: 2023-02-03 15:09:18.409889

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "068"
down_revision = "067"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "threads",
        sa.Column("parent_thread_type", sa.String(16), nullable=True),
    )


def downgrade():
    op.drop_column("threads", "parent_thread_type")
