"""Add: activity log

Revision ID: 070
Revises: 069
Create Date: 2023-11-15 16:11:53.342015

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '070'
down_revision = '069'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_logs',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), server_default='0', nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('user_id', sa.String(length=24), nullable=False),
    sa.Column('message_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('detail', sa.JSON(), nullable=True),
    sa.Column('type', mysql.TINYINT(unsigned=True), server_default='0', nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'message_id'),
    mysql_engine='InnoDB'
    )
    op.create_index('_ix_thread_type', 'activity_logs', ['thread_id', 'type', 'message_id'], unique=False)
    op.create_index('_ix_thread_user_message', 'activity_logs', ['thread_id', 'user_id', 'message_id'], unique=False)
    op.create_index('_ix_thread_user_type', 'activity_logs', ['thread_id', 'user_id', 'type', 'message_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('_ix_thread_user_type', table_name='activity_logs')
    op.drop_index('_ix_thread_user_message', table_name='activity_logs')
    op.drop_index('_ix_thread_type', table_name='activity_logs')
    op.drop_table('activity_logs')
    # ### end Alembic commands ###
