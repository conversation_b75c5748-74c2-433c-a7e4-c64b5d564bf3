"""Add: quick_messages

Revision ID: 077
Revises: 076
Create Date: 2025-04-16 11:43:55.373419

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '077'
down_revision = '076'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('quick_messages',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('user_id', sa.String(length=24), nullable=False),
    sa.Column('command', sa.String(length=24), nullable=False),
    sa.Column('message', sa.String(length=4096), nullable=False),
    sa.PrimaryKeyConstraint('user_id', 'command'),
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('quick_messages')
    # ### end Alembic commands ###
