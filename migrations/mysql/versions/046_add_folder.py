"""Add: folder

Revision ID: 046
Revises: 045
Create Date: 2020-07-02 16:36:28.688049

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '046'
down_revision = '045'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('folder', sa.String(length=16), server_default='default', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'folder')
    # ### end Alembic commands ###
