"""Update: add ban and index

Revision ID: 040
Revises: 039
Create Date: 2020-05-25 10:01:58.970371

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '040'
down_revision = '039'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('thread_logs',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('deleted_at', mysql.BIGINT(display_width=8, unsigned=True), nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('maker_id', sa.String(length=24), nullable=False),
    sa.Column('detail', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('thread_id', 'created_at'),
    mysql_engine='InnoDB'
    )
    op.add_column('participant_threads', sa.Column('banned_by', sa.String(length=24), nullable=True))
    op.add_column('participant_threads', sa.Column('banned_from', mysql.BIGINT(display_width=8, unsigned=True), nullable=True))
    op.add_column('participant_threads', sa.Column('banned_level', mysql.SMALLINT(unsigned=True), server_default='0', nullable=True))
    op.create_index('_ix_user_thread', 'participant_threads', ['thread_id', 'user_id'], unique=False)
    op.add_column('threads', sa.Column('ban_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'ban_count')
    op.drop_index('_ix_user_thread', table_name='participant_threads')
    op.drop_column('participant_threads', 'banned_level')
    op.drop_column('participant_threads', 'banned_from')
    op.drop_column('participant_threads', 'banned_by')
    op.drop_table('thread_logs')
    # ### end Alembic commands ###
