"""filter by associate link

Revision ID: 063
Revises: 062
Create Date: 2022-10-11 09:40:01.893276

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '063'
down_revision = '062'
branch_labels = None
depends_on = None


def upgrade():
    op.create_index(
        "idx__threads__associate_link",
        table_name="threads",
        columns=["associate_link"],
    )


def downgrade():
    op.drop_index("idx__threads__associate_link", table_name="threads")
