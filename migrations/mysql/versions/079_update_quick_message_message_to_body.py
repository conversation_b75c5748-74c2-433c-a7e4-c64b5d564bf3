"""Update: quick message message to body

Revision ID: 079
Revises: 078
Create Date: 2025-04-18 10:14:27.537384

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '079'
down_revision = '078'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('quick_messages', sa.Column('body', sa.JSON(), nullable=True))
    op.drop_column('quick_messages', 'message')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('quick_messages', sa.Column('message', mysql.JSON(), nullable=True))
    op.drop_column('quick_messages', 'body')
    # ### end Alembic commands ###
