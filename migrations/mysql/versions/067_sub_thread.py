"""sub thread

Revision ID: 067
Revises: 066
Create Date: 2023-02-01 17:37:20.000874

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, SMALLINT, TINYINT



# revision identifiers, used by Alembic.
revision = "067"
down_revision = "066"
branch_labels = None
depends_on = None

BIGINT_SIZE = 8
COUNT_SIZE = 4


def upgrade():
    op.add_column("threads", sa.Column("commenters", sa.JSON))
    op.add_column(
        "threads",
        sa.Column("parent_id", BIGINT(BIGINT_SIZE, unsigned=True), nullable=True),
    )
    op.add_column(
        "threads",
        sa.Column(
            "root_message_id", BIGINT(COUNT_SIZE, unsigned=True), nullable=True
        ),
    )

    # to query sub-threads of a thread
    op.create_index(
        "idx__threads__parent_id",
        table_name="threads",
        columns=["parent_id"],
    )


def downgrade():
    op.drop_column("threads", "commenters")
    op.drop_column("threads", "parent_id")
    op.drop_column("threads", "root_message_id")

    op.drop_index("idx__threads__parent_id", table_name="threads")
