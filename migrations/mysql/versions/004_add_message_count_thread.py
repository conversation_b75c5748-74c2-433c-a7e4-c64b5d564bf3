"""Add: message_count_thread

Revision ID: 004
Revises: 003
Create Date: 2019-10-27 14:40:26.931504

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('message_count', sa.BigInteger(), server_default='0', nullable=True))
    op.drop_column('threads', 'last_message')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('last_message', mysql.JSON(), nullable=True))
    op.drop_column('threads', 'message_count')
    # ### end Alembic commands ###
