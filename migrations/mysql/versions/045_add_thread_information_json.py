"""Add: thread information json

Revision ID: 045
Revises: 044
Create Date: 2020-06-19 14:43:09.241725

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '045'
down_revision = '044'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('information', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'information')
    # ### end Alembic commands ###
