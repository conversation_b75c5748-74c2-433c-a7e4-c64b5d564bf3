"""Add: pair_ids on threads

Revision ID: 006
Revises: 005
Create Date: 2019-10-29 03:07:53.798596

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('pair_ids', sa.String(length=24), nullable=True))
    op.create_index(op.f('ix_threads_pair_ids'), 'threads', ['pair_ids'], unique=False)
    op.drop_column('threads', 'is_direct')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('is_direct', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_threads_pair_ids'), table_name='threads')
    op.drop_column('threads', 'pair_ids')
    # ### end Alembic commands ###
