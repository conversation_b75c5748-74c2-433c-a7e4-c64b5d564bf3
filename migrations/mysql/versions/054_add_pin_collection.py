"""Add: pin collection

Revision ID: 054
Revises: 053
Create Date: 2021-09-09 18:21:53.752058

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '054'
down_revision = '053'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pin_collections',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), server_default='0', nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('message_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Primary<PERSON>eyConstraint('thread_id', 'message_id'),
    mysql_engine='InnoDB'
    )
    op.create_index('_ix_thread_message_pin', 'pin_collections', ['thread_id', 'message_id'], unique=True)
    op.create_index(op.f('ix_pin_collections_created_at'), 'pin_collections', ['created_at'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_pin_collections_created_at'), table_name='pin_collections')
    op.drop_index('_ix_thread_message_pin', table_name='pin_collections')
    op.drop_table('pin_collections')
    # ### end Alembic commands ###
