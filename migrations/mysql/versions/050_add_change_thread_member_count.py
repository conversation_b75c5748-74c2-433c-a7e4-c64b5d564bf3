"""Add: change thread member_count

Revision ID: 050
Revises: 049
Create Date: 2020-10-22 14:20:41.797812

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '050'
down_revision = '049'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('threads', 'member_count',
               existing_type=mysql.SMALLINT(unsigned=True),
               type_=mysql.BIGINT(display_width=8, unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('threads', 'member_count',
               existing_type=mysql.BIGINT(display_width=8, unsigned=True),
               type_=mysql.SMALLINT(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###
