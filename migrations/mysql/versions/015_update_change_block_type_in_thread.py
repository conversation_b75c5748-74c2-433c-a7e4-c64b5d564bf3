"""Update: change block type in thread

Revision ID: 015
Revises: 014
Create Date: 2020-01-13 18:19:19.174579

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '015'
down_revision = '014'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('blocked_by', sa.String(length=255), nullable=True))
    op.drop_column('threads', 'is_blocked')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('is_blocked', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True))
    op.drop_column('threads', 'blocked_by')
    # ### end Alembic commands ###
