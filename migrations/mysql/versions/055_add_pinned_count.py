"""Add: pinned count

Revision ID: 055
Revises: 054
Create Date: 2021-09-10 16:06:27.897247

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '055'
down_revision = '054'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('pinned_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'pinned_count')
    # ### end Alembic commands ###
