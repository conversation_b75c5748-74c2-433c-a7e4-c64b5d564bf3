"""Add: user passcode

Revision ID: 049
Revises: 048
Create Date: 2020-09-11 17:03:34.320052

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '049'
down_revision = '048'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('pass_code', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'pass_code')
    # ### end Alembic commands ###
