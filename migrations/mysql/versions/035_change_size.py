"""Add: change size

Revision ID: 035
Revises: 034
Create Date: 2020-05-18 23:48:34.395556

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '035'
down_revision = '034'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('policies', 'policy_id',
               existing_type=mysql.BIGINT(display_width=20),
               existing_nullable=False,
               type_=mysql.INTEGER(display_width=4, unsigned=True))
    op.alter_column('threads', 'member_count',
               existing_type=mysql.BIGINT(display_width=20),
               type_=mysql.INTEGER(unsigned=True),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('threads', 'member_count',
               existing_type=mysql.INTEGER(unsigned=True),
               type_=mysql.BIGINT(display_width=20),
               existing_nullable=True,
               existing_server_default=sa.text("'0'"))
    op.alter_column('policies', 'policy_id',
               existing_type=mysql.INTEGER(display_width=4, unsigned=True),
               existing_nullable=False,
               type_=mysql.BIGINT(display_width=20))
    # ### end Alembic commands ###
