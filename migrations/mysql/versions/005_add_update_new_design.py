"""Add: update new design

Revision ID: 005
Revises: 004
Create Date: 2019-10-28 18:12:47.122149

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('block_users', sa.Column('blocked_id', mysql.INTEGER(unsigned=True), nullable=False))
    op.add_column('block_users', sa.Column('thread_id', sa.BigInteger(), nullable=True))
    op.drop_column('block_users', 'block_id')
    op.add_column('participant_threads', sa.Column('partner_id', mysql.INTEGER(unsigned=True), nullable=True))
    op.create_index(op.f('ix_participant_threads_partner_id'), 'participant_threads', ['partner_id'], unique=False)
    op.add_column('threads', sa.Column('is_blocked', sa.<PERSON>(), server_default='0', nullable=True))
    op.add_column('threads', sa.Column('is_direct', sa.<PERSON>olean(), server_default='0', nullable=True))
    op.drop_index('ix_threads_receiver_id', table_name='threads')
    op.drop_index('ix_threads_sender_id', table_name='threads')
    op.drop_index('sender_receiver_index', table_name='threads')
    op.drop_column('threads', 'receiver_id')
    op.drop_column('threads', 'sender_id')
    op.drop_column('threads', 'is_lock')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('is_lock', mysql.TINYINT(display_width=1), server_default=sa.text("'0'"), autoincrement=False, nullable=True))
    op.add_column('threads', sa.Column('sender_id', mysql.INTEGER(display_width=10, unsigned=True), autoincrement=False, nullable=True))
    op.add_column('threads', sa.Column('receiver_id', mysql.INTEGER(display_width=10, unsigned=True), autoincrement=False, nullable=True))
    op.create_index('sender_receiver_index', 'threads', ['sender_id', 'receiver_id'], unique=False)
    op.create_index('ix_threads_sender_id', 'threads', ['sender_id'], unique=False)
    op.create_index('ix_threads_receiver_id', 'threads', ['receiver_id'], unique=False)
    op.drop_column('threads', 'is_direct')
    op.drop_column('threads', 'is_blocked')
    op.drop_index(op.f('ix_participant_threads_partner_id'), table_name='participant_threads')
    op.drop_column('participant_threads', 'partner_id')
    op.add_column('block_users', sa.Column('block_id', mysql.INTEGER(display_width=10, unsigned=True), autoincrement=False, nullable=False))
    op.drop_column('block_users', 'thread_id')
    op.drop_column('block_users', 'blocked_id')
    # ### end Alembic commands ###
