"""Add: participant alias

Revision ID: 031
Revises: 030
Create Date: 2020-04-27 16:10:05.734141

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '031'
down_revision = '030'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('alias', sa.String(length=1024), nullable=True))
    op.add_column('participants', sa.Column('alias', sa.String(length=1024), nullable=True))
    op.execute("ALTER TABLE participant_threads MODIFY alias varchar(1024) CHARACTER SET utf8mb4")
    op.execute("ALTER TABLE participants MODIFY alias varchar(1024) CHARACTER SET utf8mb4")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participants', 'alias')
    op.drop_column('participant_threads', 'alias')
    # ### end Alembic commands ###
