"""create table participant_folders

Revision ID: 057
Revises: 056
Create Date: 2022-04-13 14:38:57.244479

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '057'
down_revision = '056'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('participant_folders',
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('deleted_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('user_id', mysql.INTEGER(unsigned=True), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('alias', sa.String(length=50), nullable=False),
        sa.Column('avatar', sa.String(length=200), nullable=True),
        sa.Column('position',  mysql.INTEGER(unsigned=True), nullable=False),
        sa.PrimaryKeyConstraint('user_id', 'position'),
        mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_participant_folders_user_id'), 'participant_folders', ['user_id'], unique=False)

def downgrade():
    op.drop_table('participant_folders')
    op.drop_index(op.f('ix_participant_folders_user_id'), table_name='participant_folders')


