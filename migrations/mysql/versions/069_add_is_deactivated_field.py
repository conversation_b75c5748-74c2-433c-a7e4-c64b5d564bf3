"""add_is_deactivated_field

Revision ID: 069
Revises: 068
Create Date: 2023-02-22 14:50:13.585251

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "069"
down_revision = "068"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "participant_threads",
        sa.Column("is_deactivated", sa.<PERSON>, server_default="0"),
    )


def downgrade():
    op.drop_column("participant_threads", "is_deactivated")
