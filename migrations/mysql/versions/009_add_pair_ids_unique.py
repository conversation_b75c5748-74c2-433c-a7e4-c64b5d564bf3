"""Add: pair_ids unique

Revision ID: 009
Revises: 008
Create Date: 2019-11-02 17:24:05.545071

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_threads_pair_ids', table_name='threads')
    op.create_index(op.f('ix_threads_pair_ids'), 'threads', ['pair_ids'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_threads_pair_ids'), table_name='threads')
    op.create_index('ix_threads_pair_ids', 'threads', ['pair_ids'], unique=False)
    # ### end Alembic commands ###
