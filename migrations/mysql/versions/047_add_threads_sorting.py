"""Add: threads sorting

Revision ID: 047
Revises: 046
Create Date: 2020-07-17 11:21:14.159393

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "047"
down_revision = "046"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "threads",
        sa.Column(
            "_updated",
            mysql.BIGINT(display_width=8, unsigned=True),
            server_default="0",
            nullable=True,
        ),
    )
    op.create_index(
        op.f("ix_threads__updated"), "threads", ["_updated"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_threads__updated"), table_name="threads")
    op.drop_column("threads", "_updated")
    # ### end Alembic commands ###
