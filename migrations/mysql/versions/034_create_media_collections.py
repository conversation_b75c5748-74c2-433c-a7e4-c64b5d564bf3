"""Add: media collections

Revision ID: 034
Revises: 033
Create Date: 2020-05-18 23:48:34.395556

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '034'
down_revision = '033'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('media_collection_delete_logs',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('created_at', mysql.BIGINT(display_width=8, unsigned=True), server_default='0', nullable=False),
    sa.Column('user_id', sa.String(length=24), nullable=False),
    sa.Column('message_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.PrimaryKeyConstraint('thread_id', 'message_id', 'user_id'),
    mysql_engine='InnoDB'
    )
    op.create_table('media_collections',
    sa.Column('_created', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('_deleted', sa.TIMESTAMP(), nullable=True),
    sa.Column('thread_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('user_id', sa.String(length=24), nullable=False),
    sa.Column('message_id', mysql.BIGINT(display_width=8, unsigned=True), nullable=False),
    sa.Column('url', sa.String(length=1024), nullable=True),
    sa.Column('thumb', sa.JSON(), nullable=True),
    sa.Column('type', mysql.TINYINT(display_width=3, unsigned=True), nullable=False, server_default='0'),
    sa.PrimaryKeyConstraint('thread_id', 'type', 'message_id'),
    mysql_engine='InnoDB'
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('media_collections')
    op.drop_table('media_collection_delete_logs')
    # ### end Alembic commands ###
