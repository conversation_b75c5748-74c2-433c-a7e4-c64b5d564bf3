"""Add: participant threads role

Revision ID: 027
Revises: 026
Create Date: 2020-04-10 00:14:35.894623

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '027'
down_revision = '026'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('role', sa.String(length=1024), nullable=False))
    # ### end Alembic commands ###
    op.execute("UPDATE participant_threads JOIN participants ON participant_threads.thread_id =participants.thread_id AND participant_threads.user_id = participants.user_id SET participant_threads.role=participants.role")


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'role')
    # ### end Alembic commands ###
