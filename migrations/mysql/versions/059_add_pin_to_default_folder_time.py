"""Add pin to default folder time

Revision ID: 059 
Revises: 058
Create Date: 2022-07-11 08:38:08.314578

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.mysql import BIGINT, INTEGER

from mysql_models.models import BIGINT_SIZE


# revision identifiers, used by Alembic.
revision = "059"
down_revision = "058"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "participant_threads",
        sa.Column(
            "pin_default_pos",
            BIGINT(BIGINT_SIZE, unsigned=True),
            default=0,
            server_default="0",
        ),
    )


def downgrade():
    op.drop_column("participant_threads", "pin_default_pos")
