"""Add: delete_to participant_thread

Revision ID: 018
Revises: 017
Create Date: 2020-02-18 15:17:13.267966

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '018'
down_revision = '017'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('delete_to', sa.BigInteger(), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'delete_to')
    # ### end Alembic commands ###
