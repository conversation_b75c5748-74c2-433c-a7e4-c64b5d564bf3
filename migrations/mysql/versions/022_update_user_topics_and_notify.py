"""Update: user topics and notify

Revision ID: 022
Revises: 021
Create Date: 2020-03-19 09:09:08.790057

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '022'
down_revision = '021'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_participant_threads_thread_id'), 'participant_threads', ['thread_id'], unique=False)
    op.add_column('users', sa.Column('enable_notify', sa.<PERSON>(), server_default='1', nullable=True))
    op.add_column('users', sa.Column('topics', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'topics')
    op.drop_column('users', 'enable_notify')
    op.drop_index(op.f('ix_participant_threads_thread_id'), table_name='participant_threads')
    # ### end Alembic commands ###
