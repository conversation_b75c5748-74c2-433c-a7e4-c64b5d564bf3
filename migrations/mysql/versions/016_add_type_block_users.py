"""Add: type block users

Revision ID: 016
Revises: 015
Create Date: 2020-01-14 11:07:48.119562

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '016'
down_revision = '015'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('block_users', sa.Column('type', sa.String(length=255), server_default='user', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('block_users', 'type')
    # ### end Alembic commands ###
