"""Add: associate link

Revision ID: 053
Revises: 052
Create Date: 2021-08-23 15:13:04.970725

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '053'
down_revision = '052'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('threads', sa.Column('associate_link', sa.String(length=256), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'associate_link')
    # ### end Alembic commands ###
