"""Rename: group_level

Revision ID: 075
Revises: 074
Create Date: 2024-02-05 10:40:39.764043

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = "075"
down_revision = "074"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "threads",
        "group_level_V2",
        existing_type=mysql.BIGINT(display_width=4, unsigned=True),
        existing_nullable=True,
        existing_server_default="0",
        existing_default="0",
        new_column_name="group_level",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "threads",
        "group_level",
        existing_type=mysql.BIGINT(display_width=4, unsigned=True),
        existing_nullable=True,
        existing_server_default="0",
        existing_default="0",
        new_column_name="group_level_V2",
    )
    # ### end Alembic commands ###
