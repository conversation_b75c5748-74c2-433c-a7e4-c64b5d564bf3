"""Update: tags per user

Revision ID: 052
Revises: 051
Create Date: 2021-03-22 11:27:55.773483

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '052'
down_revision = '051'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('tags', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'tags')
    # ### end Alembic commands ###
