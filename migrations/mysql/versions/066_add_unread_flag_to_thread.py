"""add unread flag to thread

Revision ID: 066
Revises: 065
Create Date: 2022-11-16 15:33:31.103990

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "066"
down_revision = "065"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "participant_threads", sa.Column("mark_unread", sa.<PERSON>, server_default="0")
    )


def downgrade():
    op.drop_column("participant_threads", "mark_unread")
