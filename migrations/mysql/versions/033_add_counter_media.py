"""Add: counter

Revision ID: 033
Revises: 032
Create Date: 2020-05-18 23:48:34.395556

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '033'
down_revision = '032'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.Column('image_count', mysql.BIGINT(display_width=4), server_default='0', nullable=True))
    op.add_column('participant_threads', sa.Column('url_count', mysql.BIGINT(display_width=4), server_default='0', nullable=True))
    op.add_column('participant_threads', sa.Column('video_count', mysql.BIGINT(display_width=4), server_default='0', nullable=True))
    op.add_column('threads', sa.Column('image_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    op.add_column('threads', sa.Column('url_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    op.add_column('threads', sa.Column('video_count', mysql.BIGINT(display_width=4, unsigned=True), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('threads', 'video_count')
    op.drop_column('threads', 'url_count')
    op.drop_column('threads', 'image_count')
    op.drop_column('participant_threads', 'video_count')
    op.drop_column('participant_threads', 'url_count')
    op.drop_column('participant_threads', 'image_count')
    # ### end Alembic commands ###
