"""Update: change size column

Revision ID: 028
Revises: 027
Create Date: 2020-04-15 17:51:37.785848

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '028'
down_revision = '027'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE threads MODIFY pair_ids varchar(100)")
    op.execute("ALTER TABLE block_users MODIFY pair_ids varchar(100)")
    op.execute("ALTER TABLE threads MODIFY type varchar(50)")
    op.execute("ALTER TABLE threads CONVERT TO CHARACTER SET utf8mb4")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE threads MODIFY pair_ids varchar(1024)")
    op.execute("ALTER TABLE block_users MODIFY pair_ids varchar(1024)")
    op.execute("ALTER TABLE threads MODIFY type varchar(1024)")
    # ### end Alembic commands ###
