"""Add: link index

Revision ID: 026
Revises: 025
Create Date: 2020-04-09 22:52:11.990095

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '026'
down_revision = '025'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('threads', sa.Column('link', sa.String(length=128), nullable=True, server_default=sa.text("''")))
    op.create_index(op.f('ix_threads_link'), 'threads', ['link'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_threads_link'), table_name='threads')
    op.drop_column('threads', 'link')
    # ### end Alembic commands ###
