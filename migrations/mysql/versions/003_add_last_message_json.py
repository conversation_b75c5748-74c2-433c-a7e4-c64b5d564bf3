"""Add: last_message_json

Revision ID: 003
Revises: 002
Create Date: 2019-10-27 14:17:15.104133

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('participant_threads', sa.<PERSON>umn('last_message', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('participant_threads', 'last_message')
    # ### end Alembic commands ###
