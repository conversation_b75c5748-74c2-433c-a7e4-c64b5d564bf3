{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "internalConsole",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "."
            }
        },
        {
            "name": "Python: App",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/main.py",
            "console": "internalConsole",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": ".",
                "ENV_FILE": "env.staging"
            }
        },
        {
            "name": "Python: Debug Tests",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "purpose": ["debug-test"],
            "console": "internalConsole",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "."
            }
        }
    ]
}
