{"yaml.customTags": ["!upload scalar", "!remove scalar", "!keep scalar", "!erase scalar", "!jwt scalar"], "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "files.exclude": {"**/__pycache__": true}, "python.formatting.provider": "black", "python.linting.mypyEnabled": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "spellright.language": ["en-US-10-1.", "en-US-9-0."], "spellright.documentTypes": ["markdown", "latex", "plaintext"]}