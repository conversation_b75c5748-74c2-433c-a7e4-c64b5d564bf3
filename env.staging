CASSANDRA_HOST="**************,**************,**************"
CASSANDRA_USERNAME="gapo_chat"
CASSANDRA_PASSWORD="F7UvkcA2vP4yzNMx"
CASSANDRA_KEYSPACE="gapo_chat"
BUCKET_MESSAGE_FACTOR=10000

# RABBITMQ
RABBITMQ_HOST="**************"
# RABBITMQ_HOST="127.0.0.1"
RABBITMQ_PORT=5672
RABBITMQ_VHOST="gapo_chat"
RABBITMQ_URLS="*******************************************************************************?heartbeat=5"
RABBITMQ_USERNAME="gapo_chat"
RABBITMQ_PASSWORD="R9L6Trbrdr6475gtxVc5FwEr9xQlzdbF"
RABBITMQ_MESSAGE_QUEUE_NAME="message_queue"
RABBITMQ_MESSAGE_EXCHANGE_NAME="message_exchange"
RABBITMQ_MESSAGE_ROUTING_KEY="message_routing_key"


# local rabbitmq
# RABBITMQ_HOST="127.0.0.1"
# RABBITMQ_PORT=5672
# RABBITMQ_VHOST="127.0.0.1"
# RABBITMQ_URLS="amqp://admin:admin@127.0.0.1:5672/chat?heartbeat=30"
# RABBITMQ_USERNAME="admin"
# RABBITMQ_PASSWORD="admin"

# gapo config
GAPO_IS_FRIEND_URL=""
GAPO_IS_FRIENDS_URL=""

GAPO_USER_INFO_URL="https://staging-api.gapowork.vn/user-core/v2.0/users"
GAPO_USER_API_KEY="gapo-chat-uoptjwl3"

GAPO_FEATURE_URL = "https://staging-api.gapowork.vn/features-config/v1.0/"
GAPO_FEATURE_API_KEY = "gapo-workspace-MjNlY2E2ZD"

GAPO_PAGE_COLLECTION_URL=""
GAPO_PAGE_PERMISSION_URL=""
GAPO_PAGE_ITEM_URL=""

GAPO_INTERNAL_REGISTER_TOPIC_NOTIFY_URL=""
GAPO_INTERNAL_UNREGISTER_TOPIC_NOTIFY_URL=""

MOBILE_PUSH_API_KEY=""

GAPO_BLOCK_USER_URL=""
GAPO_UNBLOCK_USER_URL=""

GAPO_RELATION_V2_API_KEY=""
GAPO_RELATION_V2_URL=""

GAPO_CONTACT_API_KEY=""
GAPO_CONTACT_URL=""

GAPO_REACT_URL=""
GAPO_REACT_API_KEY=""

GAPO_WORKSPACE_URL=""
GAPO_WORKSPACE_API_KEY=""

# MEMBERSHIP_URL=gapo-collab-membership.default.svc.cluster.local.:10000
MEMBERSHIP_URL=**************:31811


BOT_BASE_URL=""
BOT_API_KEY=""

POLL_BASE_URL=""
POLL_API_KEY=""

ORGC_BASE_URL="https://staging-api.gapowork.vn/organization-chart/v2.0/"
ORGC_API_KEY="gapo-chat-NWE0Y2E5MD"

GROUP_BASE_URL=""
GROUP_API_KEY=""

ORGC_V3_BASE_URL="https://staging-api.gapowork.vn/organization-chart/v3.0/"
ORGC_V3_API_KEY="gapo-chat-ZWVlZDJjZD"

MYSQL_URL="mysql://gapo_chat:qOh6ctUyVNopnpRJqPri4QxMq0EUubAU@**************:3306/gapo_chat?use_unicode=1&charset=utf8mb4"

REDIS__PERSISTENT_CHAT_HOST="**************"
REDIS__PERSISTENT_CHAT_PASSWORD="ycQonkMJhn9X2agF6ppdicrIbss2CH6avy9UYJi8Ru0wEkg11F"
REDIS__PERSISTENT_CHAT_DB=8
REDIS__PERSISTENT_CHAT_PORT=6379

REDIS__THREAD_CHAT_HOST="**************"
REDIS__THREAD_CHAT_PASSWORD="ycQonkMJhn9X2agF6ppdicrIbss2CH6avy9UYJi8Ru0wEkg11F"
REDIS__THREAD_CHAT_DB=19
REDIS__THREAD_CHAT_PORT=6379

REDIS__MESSAGE_CHAT_HOST="**************"
REDIS__MESSAGE_CHAT_PASSWORD="ycQonkMJhn9X2agF6ppdicrIbss2CH6avy9UYJi8Ru0wEkg11F"
REDIS__MESSAGE_CHAT_DB=17
REDIS__MESSAGE_CHAT_PORT=6379

BAD_COMMENT_KEY="CHAT-BAD-COMMENT"
LIMIT_PIN_THREAD=10


REDIS_CACHE_HOST="**************"
REDIS_CACHE_PASSWORD="ycQonkMJhn9X2agF6ppdicrIbss2CH6avy9UYJi8Ru0wEkg11F"
REDIS_CACHE_PORT=6379
REDIS_CACHE_DB=7

# SENTRY_DSN="https://<EMAIL>/55"
SENTRY_DSN=""


# MQTT
MQTT_HOST="staging-mqtt-work.gapo.vn"
MQTT_PORT="1883"
MQTT_ADMIN_USERNAME="gapo_chat"
MQTT_ADMIN_PASSWORD="CBuRxlvmav8f9jeavuu1Yv4zSaBF77iq"

CDN_MAPPING_FILEPATH="./cdn_mapping_example.json"

CHAT_DOMAIN="https://gapowork.vn"

USE_JSON_LOG=False

IAM_API_KEY=""
GAPO_IAM_URL=""

# Orgchart v3 config
USE_ORGC_CHART_V3 = False


# KAFKA_BOOTSTRAP_SERVERS = "localhost:19092"
KAFKA_BOOTSTRAP_SERVERS = "**************:9092"
KAFKA_USERNAME=membership
KAFKA_PASSWORD="uLRwuYMMesxg4eYsPx55"


# Feature flags
FEATURE__DISABLE_USER_BLOCKING=false

# Misc configs
MISC__DROPPI_WORKSPACE_ID=
MISC__DROPPI_LIMIT_MEMBERS_CREATING_CHAT=10


# Attach error trace to responses to 5xx errors
# This help debug problem faster in Staging/UAT.
# However, this SHOULDN'T be enabled in PRODUCTION
# because it may LEAK some information about server.
INCLUDE_SERVER_ERROR_TRACE=true
GAPO_CALL_BOT_ID="5843186210444538880"
