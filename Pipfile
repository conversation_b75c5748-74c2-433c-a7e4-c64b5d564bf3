[[source]]
name = "pypi"
url = "https://pypi.org/simple"
verify_ssl = true

[dev-packages]
mypy = "==1.10.0"
black = "==22.10.0"
pyright = "*"
isort = "*"
autoflake = "*"
pre-commit = "*"
pytest = "*"
pytest-cov = "*"
pytest-instafail = "*"
pytest-timeout = "*"
babel = "*"
pylint = "*"
# types for mypy
types-Markdown = "==3.3.29"
types-redis = "==4.3.3"
types-requests = "==2.28.0"
types-ujson = "==5.4.0"
types-urllib3= "==1.26.16"
types-polib = "*"
pytest-testmon = "*"
tqdm = "*"
types-protobuf = "*"
mypy-protobuf = "*"
types-pygments = "*"
types-colorama = "*"
types-commonmark = "*"
types-mysqlclient = "*"
types-beautifulsoup4 = "*"
radon = "*"

[packages]
polib = "*"
falcon = "*"
paho-mqtt = "==1.6.1"
cassandra-driver = "*"
cassandra-migrate = "==0.3.3"
sentry-sdk = {version = "==1.17.0", extras = ["falcon"]}
werkzeug = "*"
cerberus = "*"
gunicorn = "*"
pika = "*"
requests = "*"
pyjwt = "*"
sqlalchemy = "~=1.4"
mysqlclient = "==1.4.6"
gevent = "*"
alembic = "*"
logzero = "*"
cryptography = "*"
bson = "*"
nose = "*"
pydantic = "==1.10.15"
cython = "==0.29.30"
orjson = "*"
bitstring = "*"
pyyaml = "*"
ua-parser = "*"
user-agents = "*"
ujson = "*"
markdown = "*"
bs4 = "*"
pymdown-extensions = "*"
raven = "*"
dependency-injector = "*"
urlextract = "*"
nanoid = "*"
structlog = "*"
rich = ">=13.7.0"
prometheus-client = "*"
python-redis-lock = "*"
python-dotenv = "*"
grpcio-tools = "*"
protobuf = "*"
grpc-stubs = "*"
grpcio = "*"
confluent-kafka = "*"
kafka-python = "*"
redis = {extras = ["hiredis"], version = "*"}
lz4 = "*"
urlquote = "*"
click = "==8.1.3"
structlog-sentry = "*"
phonenumbers = "*"
wheel = "*"
pandas = "*"

[requires]
python_version = "3.8"

[scripts]
app = "gunicorn -w 1 --log-level INFO -t 999999 --max-requests 1000 -b 0.0.0.0:5000 app:api --reload"
background = "python background_app.py"
background-heavy = "python background_heavy_app.py"
test = "pytest --testmon tests"
test-cov = "pytest --cov=chat --cov-report=term --cov-report=xml tests"
setup-db = "./dev-scripts/setup-db.sh"
check-trans = "python ./dev-scripts/check_translation_files.py"
check-migration = "./dev-scripts/check-migration.sh"
extract-i18n = "./dev-scripts/extract_messages.sh"
update-i18n = "./dev-scripts/update_translation.sh"
prepare-cas-schema = "docker compose exec cassandra cqlsh -f /sql.cql -k chat"
gen-grpc-clients = "./dev-scripts/gen_grpc_clients.sh"
