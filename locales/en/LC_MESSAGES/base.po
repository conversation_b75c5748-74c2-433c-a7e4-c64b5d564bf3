# English translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-04-21 16:59+0700\n"
"PO-Revision-Date: 2022-06-21 09:43+0700\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en\n"
"Language-Team: en <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: chat/response_messages/__init__.py:3
msgid "Unable to find the user"
msgstr ""

#: chat/response_messages/__init__.py:4
msgid "Unable to find data, please try again!"
msgstr ""

#: chat/response_messages/__init__.py:5
msgid "Unable to find this thread chat, please try again!"
msgstr ""

#: chat/response_messages/__init__.py:6
msgid "Unable to find page please try again!"
msgstr ""

#: chat/response_messages/__init__.py:7 chat/response_messages/__init__.py:58
msgid "Unable to find data. Please try again!"
msgstr ""

#: chat/response_messages/__init__.py:8
msgid "You can not join the group chat via this link."
msgstr ""

#: chat/response_messages/__init__.py:10
msgid "Request denied, unable to take this action!"
msgstr ""

#: chat/response_messages/__init__.py:12
msgid "Your organization has been locked. Please contact Administrator to unlock."
msgstr ""

#: chat/response_messages/__init__.py:16
msgid "Unable to add members"
msgstr ""

#: chat/response_messages/__init__.py:17
msgid "Unable to remove members"
msgstr ""

#: chat/response_messages/__init__.py:18
msgid "Unable to unban a member"
msgstr ""

#: chat/response_messages/__init__.py:19
msgid "Unable to ban a member"
msgstr ""

#: chat/response_messages/__init__.py:20
msgid "Unable to remove permission of this user"
msgstr ""

#: chat/response_messages/__init__.py:21 chat/response_messages/__init__.py:22
msgid "Unable to grant permission this user"
msgstr ""

#: chat/response_messages/__init__.py:24
msgid "Unable to delete message"
msgstr ""

#: chat/response_messages/__init__.py:25
msgid "Unable to leave the thread chat"
msgstr ""

#: chat/response_messages/__init__.py:26
msgid "Unable to leave conversation"
msgstr ""

#: chat/response_messages/__init__.py:27
msgid "Unable to delete the message after 60 minutes!"
msgstr ""

#: chat/response_messages/__init__.py:28
msgid "Unable to send message. Please try again"
msgstr ""

#: chat/response_messages/__init__.py:29
msgid "You can only pin up to 10 thread chats"
msgstr ""

#: chat/response_messages/__init__.py:30
msgid "Reach limit number of members"
msgstr ""

#: chat/response_messages/__init__.py:31
msgid "You have exceeded limit numbers of contact to unknown user"
msgstr ""

#: chat/response_messages/__init__.py:35
msgid "Wrong passcode"
msgstr ""

#: chat/response_messages/__init__.py:36
msgid "Please fill passcode"
msgstr ""

#: chat/response_messages/__init__.py:38
msgid "Directory name already exists"
msgstr ""

#: chat/response_messages/__init__.py:40
msgid "Unable to find user in this thread chat. Please try again"
msgstr ""

#: chat/response_messages/__init__.py:43
msgid "Unable to create a new thread chat, please try again!"
msgstr ""

#: chat/response_messages/__init__.py:44
msgid "This user has been blocked or you two are not friends"
msgstr ""

#: chat/response_messages/__init__.py:45
msgid ""
"No members have been selected to be added to the chat group. Please try "
"again"
msgstr ""

#: chat/response_messages/__init__.py:49
msgid "All invitees are in this thread chat"
msgstr ""

#: chat/response_messages/__init__.py:50
msgid "All excluded members are not in this thread chat"
msgstr ""

#: chat/response_messages/__init__.py:52
msgid "Please try again"
msgstr ""

#: chat/response_messages/__init__.py:53
msgid "Send message not allowed"
msgstr ""

#: chat/response_messages/__init__.py:56
msgid "An internal server error has occured, please try again!"
msgstr ""

#: chat/response_messages/__init__.py:57
msgid "Invalid parameters"
msgstr ""

#: chat/response_messages/__init__.py:60
msgid "Unable to pin in this folder!"
msgstr ""

#: chat/response_messages/__init__.py:62
msgid "Unable to process this request now, please try again!"
msgstr ""

#: chat/response_messages/__init__.py:63
msgid "Thread name too long"
msgstr "Group name must not exceed 100 character"

#: chat/response_messages/__init__.py:65
msgid "Unable to add bot"
msgstr ""

#: chat/response_messages/__init__.py:66
msgid "Only one bot allow in the group"
msgstr ""

#: chat/response_messages/__init__.py:68
msgid "Cannot delete a sent message after 1 day"
msgstr ""

#: chat/response_messages/__init__.py:69
msgid "Cannot edit message after 1 hour since its last edit"
msgstr ""

#: chat/response_messages/__init__.py:74
msgid "Blocking users feature is disabled, you can't perform this action!"
msgstr ""

#: chat/response_messages/__init__.py:78
msgid "Secret feature is disabled, you can't perform this action!"
msgstr ""

#: chat/response_messages/__init__.py:82
msgid "Some of selected messages can not be deleted. Please try again!"
msgstr ""

#: chat/response_messages/__init__.py:86
msgid ""
"You have exceeded the limit of adding people when creating a chat group "
"(maximum 10 people)"
msgstr ""

#: chat/response_messages/__init__.py:91
msgid ""
"Adding members to chat is currently disabled. You can only send link to "
"invite others to group chat!"
msgstr ""

#: chat/response_messages/__init__.py:95
msgid "Only Admin can create group large member."
msgstr ""

#: chat/response_messages/__init__.py:97
msgid "Too many requests."
msgstr ""

#: chat/response_messages/__init__.py:99
msgid "Quick message already exists"
msgstr ""

