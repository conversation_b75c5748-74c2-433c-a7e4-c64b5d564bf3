# Monitoring và Logging

## Monitor

Chat API sử dụng prometheus để tracking metrics. Các metrics của API sẽ được hiển thị tại endpoint `/metrics`.
App có thể tạo thêm các metrics để tracking bằng cách tự định nghĩa các metric mới với registry mặc định `DEFAULT_REGISTRY`.

Tùy vào mục đích, dev có thể dùng Alert Manager để tracking, cảnh báo các metric liên quan. Xem thêm tại : https://prometheus.io/docs/practices/alerting/

Xem dashboard production mẫu tại thư mục [grafana](../grafana)


Sysadmin Gapo hiện tại đều đã có dashboard này.


Demo monitor:

![demo-grafana](assets/demo-grafana.png)

## Logging

Logging service sẽ được parse và lưu trữ qua graylog:
Link để xem logging: [graylog](https://log-work.contentcms.online)

Log trên chat service là structured log (json log có các  trường dữ liệu). Client có thể dùng
để lọc log cho phù hợp. Một số trường cơ bản:

- `kubernetes_labels_app`: tên service, lấy từ argo. 

Một số trường khác có trong log chat service (sinh ra do middleware request_id trong chat-service):
- `status`: Status của http request (do chat service đẩy ra)
- `duration`: Thời gian xử lý request trên từng API
- `request_id`: ID của request 
- `ip`: IP của request 
- `auth_type`: Kiểu auth (user, service)
- `user_id`: ID của người dùng 
- `workspace_id`: ID của workspace
- `ua`: User agent của request
- `exception`: Exception trace 

Ví dụ một số truy vấn:
- Request không thành công của `chat-api`:
```
kubernetes_labels_app:"gapo-chat-api" AND status:>400
```
- Truy vấn request xử lý chậm của `chat-api`:

```
kubernetes_labels_app:"gapo-chat-api" AND duration:>200
```
- Truy vấn thông tin của một request cụ thể tới `chat-api`:

```
kubernetes_labels_app:"gapo-chat-api" AND request_id:"4a82ad31-4c67-449b-9c58-762dfd01c254"
```
- Lấy lịch sử gọi API của một người dùng cụ thể: 

```
kubernetes_labels_app:"gapo-chat-api" AND user_id:218269533
```

Trong trường hợp cần kiểm tra lỗi server (5xx) dùng request_id để xem exception trace liên quan qua trường `request_id`. Exception trace sẽ được thể hiện như sau:

![exception-trace](assets/exception-trace.png)

Có thể dùng gralog để tạo thêm một số thống kê cần thiết. Ví dụ:

![graylog-metrics](assets/graylog-metrics.png)

Khi muốn kiểm tra lịch sử gọi API của một user, dùng trường `user_id`. 

## Deploy

Deploy được tiến hành thông qua Argo.
Argo để quản lý staging tại [địa chỉ](https://staging-deployment.gapowork.vn/applications?proj=&sync=&health=&namespace=&cluster=&labels=). 