# Definition


```
Badge:
Th<PERSON> hiện con số notification client nhận mà user chưa đọc
```


# flow cho phần nhận noti

```mermaid
graph LR
    A[FCM] -->|Send Noti| B[Client Update badge Background/Foreground]
    B --> C{Check}
    C -->|Noti has thread_id| D{Check}
    C -->|Noti does not have thread_id| E[Badge = Badage + 1]
    E --> F(Show Badge)
    D -->|client stored noti_thread_id| F
    D -->|client doesnt have noti_thread_id| G[store noti_thread_id]
    G --> E
```

# flow cho phần khi mở app sau khi nhận noti


```mermaid
graph TD
    A[Client] -->|Open| B[App]
    B --> C{Check Model}
    C -->|Android| D[Read 1 unread thread]
    D --> E[Clean thread noti on screen, updated badge]
    E --> End[Send Server Read]
    C -->|IOS| F[Update badge count]
    F --> D
    End --> MP[Mobile Push]
    MP --> AD[Android data updated badge/clean noti]
    MP --> Ios[Ios silent push update badge/clean noti]
```
