# Hỗ trợ khác hàng

## Kiểm tra các vấn đề lỗi liên quan đến khách hàng 

Kiểm tra log trên graylog qua trường `user_id`. Xem [monitor](./monitoring.md)

## C<PERSON>c vấn đề thường gặp

1. Thay đổi chủ sở hưũ của nhóm chat

- L<PERSON>y thông tin group, người cần set quyền owner 
- Nếu người dùng chưa trong nhóm, cần nhờ người khác thêm người dùng vào nhóm 
- Nếu hiện tại nhóm đã có owner -> nhờ người dùng đó set 
- Nếu owner không active -> gọi API thay đổi role trong gateway với `x-gapo-user-id` là ID của owner.
- N<PERSON>ú không có owner -> Cập nhật database:
```sql
update participant_threads set role='owner' where thread_id={thread_id} and user_id={user_id} 
```
- <PERSON>ểm tra lại để đồng bộ với collab group.

2. Lỗi không up đ<PERSON><PERSON><PERSON> ảnh, video 

Kiểm tra lại với CDN.

3. Lỗi không tạo được poll-vote 

Kiểm tra với service poll-vote

4. Các vấn đề với search 

Kiểm tra lại với bên search

5. Thiếu hoặc không đủ thành viên, thành viên không có quyền. 

Kiểm tra lại với service collab xem người đó đã có trong cả chat và collab hay chưa. 

6. Đồng bộ lại thành viên với collab

Đôi khi, do vấn đề xử lý không đồng bộ hoặc một lý do nào đó, thành viên nhóm chat không giống dữ liệu ở [membership service](./features/collab_group.md).
Khi đó, cần đồng bộ lại thành viên với collab. Cần chạy script trên server để đồng bộ lại:

```bash
pipenv install tqdm
PYTHONPATH=. USE_JSON_LOG=false python migrations/scripts/04_sync_collab_members.py --collab_ids ${collab_id}
```
