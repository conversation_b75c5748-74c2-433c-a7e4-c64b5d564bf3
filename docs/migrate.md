# Migration

### Kiểm tra migration mysql

Khi đẩy lên production, có thể bạn quên chạy migration database.
Đ<PERSON> kiểm tra xem có cần migration, chạy lệnh:

```bash
$ export DEPLOY_BRANCH=staging # or prod
$ pipenv run check-migration
```
### Migration mysql database

```bash
# xuat mysql url
export MYSQL_URL=...

# chay migration
envsubst < alembic.ini.example > alembic.ini
export PYTHONPATH=$(pwd)
alembic upgrade head
```

### Migration cassandra 

Thay đổi schema trên cassandra cần tạo issue lệnh thay đổi tới Sys-admin.

Trên local, cần cập nhật lại file [cassandra-schema](../cassandra-schema.cql) và update schema trên 
cassandra local qua lệnh:

```bash
pipenv run setup-db
```

### Tạo migration script 

```bash
alembic revision --rev-id $REVISION_ID -m "Mô tả migration"
```

`REVISION_ID` phải là số nguyên . Xem ví dụ tại thư mục [migration](../migrations/mysql/versions/)


Tuy nhiên với sys-admin, sẽ tiện hơn nếu viết câu lệnh sql. [Ví dụ](https://app.clickup.com/t/865bn1vk2)


### Python migration scripts

Các script python dùng để migration dùng để chạy được đặt trong thư mục [scripts](../migrations/scripts).

Để chạy cần set `ENV_FILE`, tương tự như khi chạy app. Ví dụ:

```bash
ENV_FILE=env.staging USE_JSON_LOG=false python migrations/scripts/01_fix_thread_pin_db_score_sync.py
````