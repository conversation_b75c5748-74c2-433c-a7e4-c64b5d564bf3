# Cách tạo một tính năng mới 


## Định nghiã database model 

Thêm các model db vào [models.py](../chat/../mysql_models/models.py). 

Test sử dụng định nghĩa này để thiết lập schema cho test tự động. Xem thêm trong file [conftest.py](../tests/conftest.py).


Với những thay đổi liên quan đến <PERSON>, cần cập nhật lại trong [cassandra-schema.cql](../cassandra-schema.cql) và update lại schema db trên local qua lệnh sau:

```bash
pipenv run setup-db
```

## Tạo feature folder

Giả sử ta cần tạo feature tên `FeatureA`. 

Tạo một folder feature trong thư mục `chat/FeatureA` với các file chính:

- `deliver`: định nghĩa API. Các Resource nên kế thừa `UserAuthResource`, `GuestResource` hoặc `InternalServiceResource`. Xem thêm [resource.py](../chat/app/resource.py). Dựa trên kiểu resource, middleware authen có thể hoạt động đúng đắn.
- `repository`: Định nghĩa repository. Thông thường để linh hoạt, định nghĩa interface  trong file `interface.py` và định nghĩa cài đặt trong `mysql.py`
- `model`: Định nghĩa các model 
- `container`: Định nghĩa DI liên quan đến module.  Thông thường trong module sẽ có 2 lớp: `FeatureAContainer` -> Để định nghĩa container và `FeatureAResourcesContainer` để định nghĩa các handle cho API. 
- `exception`: Chứa các exception riêng cho module đó. Với các exception chung, nên để trong thư mục `chat/exceptions` thay vì để ở đây. 
- `routing`: Chưá hàm `routing` để thiết lập routes liên quan đến tính năng.  Hàm này sẽ có 2 tham số truyền vào là `router` và `FeatureAContainer`. 

Để kiểm tra quyền truy cập thread, module mới dùng usecase từ trong module `participants`.

## Thêm routing vào API

Cập nhật filed [create_app.py](../chat/app/create_app.py#L131).

Ví dụ:

```python
from chat.featureA.routing import routing as featureA_routing

featureA_routing(api, container.featureA())
```

## Cập nhật background tasks

Nhiều task được xử lý trong worker [background](../background_app.py). Để định nghĩa background task mới, tạo task mới định nghĩa trong thư mục [backgrounds](../chat/backgrounds)

và cập nhật task vào file [cấu hình](../chat/backgrounds/__init__.py).

Mỗi task là một tin nhắn Rabitmq. Client tạo thông qua phương thức `self.rb_broker.publish_background_task` trong usecase. 

Các background task handle nên có dạng `{method_name}_bg` để phân biện với các phương thức khác của usecase.

## RabbitMQ

Có một số routing key được sử dụng cho những mục đích nhất định (routing các loại tin nhắn khác nhau).

Xem [rabbitmq.py](../chat/broker/rabbitmq.py).

- `background_task`: Dùng để tạo các task xử lý ngầm. Các worker cần lắng nghe sự kiện liên quan đến routing key này nếu muốn làm một worker. Client sử dụng phương thức `publish_background_task` để tạo task.
- `message`: Để gửi tin nhắn đến worker `chat-persistent-worker`.  Client dùng phương thức `publish_message` để gửi event. [Search](./features/search.md) lắng nghe sự kiện để index tin nhắn. `chat-notify-worker-golang` và `chat-delivery-worker-golang` lắng nghe sự kiện để tạo thông báo tin nhắn và bắn MQTT message.
- `status`: Dùng để tạo các sự kiện thay đổi liên quan đến chat. Ví dụ đọc, xoá tin nhắn,... Một ví dụ là worker `chat-notify-worker-golang` lắng nghe sự kiện ở đây để tạo thông báo ẩn để xoá thông báo.  Client dùng phương thức `publish_status` để gửi event.

## MQTT

MQTT được dùng để cập nhật thông tin đến client, với web là qua websocket, với client khác qua tcp. 

Client sử dụng thông tin MQTT để cập nhật trạng thông tin mà không cần phải gọi lại API. Ví dụ, khi có tin nhắn mới đến, đổi tên thread,...

MQTT về tin nhắn gọi là flash message.

Để tạo tin nhắn MQTT, dùng phương thức `publish_status` trong [mqtt.py](../chat/publishers/mqtt.py)

Client có thể định nghĩa broker để wrap lại kiểu tin nhắn. Ví dụ [mqtt_broker.py](../chat/messages/broker/mqtt_broker.py)

## Middleware 

Hiện tại có một số middleware:

- [authen](../chat/middlewares/authen.py): Dùng để kiểm tra quyền dựa trên header. Khi đi qua gateway, client sẽ chuyển tiếp một số header `x-gapo-user-id` (id của user đã authen qua token), `x-gapo-role` (kiểu user, service), `x-gapo-api-key` (cho internal service). Authen dựa vào định nghĩa Falcon Resource để kiểm tra quyền cho phù hợp.
- [request_id](../chat/middlewares/request_id.py): Dùng để inject request_id. Mỗi một request HTTP sẽ được gắn với một id cụ thể. Client dùng ID này để trace lại lỗi liên quan đến request cụ thể. Middleware này cũng thực hiện logging request.
- [metrics](../chat/metrics/middleware.py): Dùng để trích xuất các metrics, dùng cho grafana [monitor](./monitoring.md).

## Metrics

Metrics giúp hiểu tình trạng của hệ thống, hoạt động nghiệp vụ tốt hơn, giúp 
hiểu cách người dùng sử dụng tính năng. chat service hỗ trợ thêm metric prometheus.

Để tạo thêm metrics, cần cập nhật trong lớp [AppMetrics](../chat/metrics/model.py#L97).

Metric endpoint sẽ ở địa chỉ `/metrics`.


## Thêm API GET 

Lưu ý rằng các API GET của chat hiện tại ở bên [chat-fastapi](https://gitlab.gapo.com.vn/chat/chat-fastapi). Do vậy cần thêm API ở repo `chat-fastapi`. Xem thêm [cấu trúc chat service](./c4_model/c4.md).

Hạn chế  của việc tách service "quá mức" khiến việc test ở `chat-fastapi` phức tạp hơn khi phải thêm logic từ chat-api vào đây. 

## Tạo tests

Test sẽ được đặt trong thư mục `tests/FeatureA`. 

- `tests/FeatureA/resource`: Test API end-to-end. Test này đầy đủ nhất nhưng mất công thiết lập nhất. Ưu điểm so với test usecase là các test này kiểm tra dược định dạng đầu vào, đầu ra của API. 
- `tests/FeatureA/usecase`: Test liên quan các usecase. Nên viết nhiều test ở đây. 
- `tests/FeatureA/repository`: Test liên quan đến repository.

Các lớp helper:
- `tests/conftest.py`: Định nghĩa các fixture của pytest. Chủ yếu các testcase dùng fixture `container`, là object `AppContainer` chứa toàn bộ các thông tin cần thiết để thực hiện test. 
- `tests/helpers`: Chứa các hàm trợ giúp, hàm chung cho các test.

Lưu ý rằng, các background task được inject vào task broker, nên khi viết test không cần lo lắng là làm sao để gọi hàm background để xử lý task. [Xem thêm](../tests/conftest.py#L176).

## Cập nhật tài liệu

Tạo file `FeatureA.md` trong thư mục `docs/features` và link đến file `docs/README.md`. 


## Thêm migration script

Xem thêm mục [migration](./migrate.md).

## Cập nhật API document

Link API document repo: https://gitlab.gapo.com.vn/docs/gapo-api-docs

## Coding style

- Tham khảo:
  - https://github.com/zhanymkanov/fastapi-best-practices
  - https://github.com/zedr/clean-code-python


## Tạo các issue liên quan BE & devops 

Ví dụ: https://app.clickup.com/t/865bmt0km

## Khác

- Refactor issue: https://app.clickup.com/t/2c1w55y