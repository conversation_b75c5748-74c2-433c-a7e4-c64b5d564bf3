# Config

Config được định nghĩa qua file environment.
Mẫu file config có thể xem file [env.staging](../env.staging).

Cấu hình này được định nghĩa trong lớp [Settings](../chat/config.py).

Đ<PERSON> chạy server, cần thiết lập biến môi trường hoặc  file `.env.prod` .

Để test, cần copy file `env.test` qua `.env.test`.
Cấu hình test cho CI được đặt trong `.env.ci`.

Bạn cũng có thể chạy server với file environment xác định thông qua biến `ENV_FILE`:

```bash
ENV_FILE=env.staging pipenv run app
```

Bạn cũng có thể ghi đè toàn bộ biến môi trường thông qua ghi đè giá trị trong file `.env`. L<PERSON>u
ý rằng  không nên dùng file này để định nghĩa toàn bộ giá trị biến môi trường.

Bạn cũng có thể ghi đè biến môi trường trực tiếp từ dòng lệnh. Ví dụ:

```bash
ENV_FILE=env.staging USE_JSON_LOG=true pipenv run app 
```


Để load config từ file qua Python code, dùng hàm:

```python
from chat.config import load_config 
load_config('env.staging')
```