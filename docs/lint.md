# Linting

Để tránh lỗi, bạn nên bật linter với [mymy](https://mypy.readthedocs.io/en/stable/index.html).
Điều này rất quan trọng bởi kiểu trong python mặc định không bắt buộc. Mypy giúp check kiểu dựa
trên định nghĩa hàm và biến của bạn, hạn chế lỗi khi triển khai chat trên production do truyền sai kiểu.

Việc linting cũng được thực hiện trong [CI](./ci.md).

Linting cũng được thiết lập trong [pre-commit hooks](./pre-commit-hook.md).

## Setup với VSCode

## Lệnh hữu ích

-   Cài đặt các thư viện type còn thiếu (nếu có)

```bash
mypy --install-types --non-interactive core
```

-   Test lỗi package

```bash
mypy package_name
# for example
mypy chat
# Success: no issues found in 305 source files
```

## Bỏ lỗi

Đô<PERSON> khi, tạm thời bạn chưa thể xác định được chính xác lỗi và bạn muốn tạm thời bỏ qua lỗi đó.
Khi đó, bạn chỉ cần thêm `# type: ignore` vào cuối mỗi dòng nếu bạn muốn bỏ qua check lỗi trên một dòng, hoặc
đầu file nếu bạn muốn bỏ qua toàn bộ file.

Đôi khi bạn gặp lỗi như `Module "chat.container.app" does not explicitly export attribute "AppConnContainer"; implicit reexport disabled`
Điều này có nghĩa bạn đang sử một import từ một package được import từ một package khác. Implicit reexport mặc định được bỏ để tránh lỗi import.
Để tránh lỗi bạn cần thực hiện reimport. Ví dụ trong các file `__init__.py`:

```bash
from .api import MySchema as MySchema
```

Khi làm việc với pydantic, bạn thường sẽ gặp lỗi với constr, consint. Hãy thêm `# type: ignore` để bỏ lỗi check. Hoặc có thể định nghĩa dùng `Field`. Bạn có thể đặt ` #type: ignore` nên đầu file
để bỏ toàn bộ lỗi. Tuy nhiên lưu ý rằng Toàn bộ type sẽ có kiểu any nên bạn sẽ gặp lỗi mỗi khi import. Do vậy tốt nhất hãy cố gắng chỉ lỗi cụ thể.
