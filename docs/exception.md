# Exception


Các lỗi API được quản lý tập trung tại middleware. Điều này giúp đơn giản hơn với người lập trình.
Người lập trình không cần phải `try/catch` lỗi cụ thể và trả về JSON phù hợp cho từng lỗi.
Thay vì vậy, lập trình viên sẽ raise lỗi và để server tự xử lý. 

Các lớp lỗi nên kế thừa lớp [ChatError](../chat/exception.py#L18). Lớp này
cho phép định nghĩa http lỗi trả về cho client và hỗ trợ đa ngôn ngữ. 

Ví dụ:
```python
class LockTimeoutError(ChatError):
    http_code = 500
    error = "lock_error"
    i18n_message = response_messages.LOCK_ERROR
```

Json tương ứng trả về sẽ có dạng như sau:

```json
{
    "error": "lock_error", 
    "error_details": [], 
    "message": "Unable to process this request now, please try again!"
}
```