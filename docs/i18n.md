# i18n

Thư viện sử dụng `gettext` và pybabel để quản lý dịch đa ngôn ngữ.
Đ<PERSON> đánh dấu một đoạn text cần phải dịch, sử dụng hàm `_` trong `chat.i18n` để đánh dấu.

V<PERSON> dụ:

```python
from chat.i18n import _

MY_MESSAGE = _("my error message")
```

Hiện tại các text cần dịch đều nằm trong [tại đây](../chat/response_messages/__init__.py).

<PERSON>u đ<PERSON>, cần chạy lệnh để sinh trích xuất các file "*.po":

```bash
pipenv run extract-i18n
```

Sau khi cập nhật dịch từ trong các file `locales/{vi,en}/LC_MESSAGES/base.po`, cần chạy
lại file để  sinh ra các file `*.mo` mà client sẽ sử dụng để dịch:


```bash
pipenv run update-i18n
```

<PERSON>oài ra để kiểm tra xem có thiếu nội dung nào không được dịch, có thể chạy lệnh sau 
để kiểm tra các file:

```bash
python ./dev-scripts/check_translation_files.py
```


Lưu ý rằng, nếu cụm từ không được dịch, thì i18n sẽ trả về cụm từ gốc. Do đó thông thường để nội dung 
text tiếng anh, chỉ cần cập nhật lại trong file [tiếng việt](../locales/vi/LC_MESSAGES/base.po).