# Chat thread

Mỗi cuộc hôị thoaị có thể  giữa 2 hoặc nhiều người. 
Chat thread model (bảng `threads`):
```python 
class ChatThread(TypedDict):
    id: int
    avatar: NotRequired[str]
    name: str
    blocked_by: NotRequired[str]
    type: str
    group_level: str
    message_count: int
    creator: str
    centralize_last_message: NotRequired[Any]
    member_count: int
    link: str
    associate_link: NotRequired[str]
    description: NotRequired[str]
    video_count: int
    url_count: int
    image_count: int
    file_count: int
    ban_count: int
    pinned_count: int
    pinned_message_id: str
    information: NotRequired[Any]
    banner: NotRequired[Any]
    workspace_id: str

    _updated: int
    _created: int
    _deleted: int
```
[source](../../chat/threads/model/__init__.py)
Giải thích một số trường 
- `message_count`: Tổng số message trong thư mục đó. 
- `type`: <PERSON><PERSON>u group. <PERSON><PERSON><PERSON> trị hợp lệ là `direct` (thread trực tiếp), `group` (thread nhiều người ) hoặc `supergroup` (group lớn) hoặc `subthread` ([sub thread](./sub_thread.md))
- `associate_link`: Link liên kết tới thread. 
- `link`: Link code. Link code để sinh ra link chia sẻ mà người khác có thể join vào group.

Môí quan hệ giữa thành viên và thread được định nghĩa trong bảng `participant_threads`:

```python
class ChatThreadMember(ThreadMemberWithFolder):
    partner_id: NotRequired[str]
    thread_id: int
    type: str
    last_message: Any
    delete_to: str
    is_removed: int
    alias: str
    tags: str
    read_count: int
    video_count: int
    image_count: int
    file_count: int
    role: str

    banned_level: int
    banned_by: NotRequired[str]

    pin_default_pos: int
    pin_pos: int
```
[source](../../chat/participants/model/__init__.py)
Ý nghĩa cuả một số trường 
- `read_count`: số message mà user đã đọc tới. Nếu `thread.message_count == participant_threads.read_count` user đã đọc hết hôị thoaị.
- `role`: Quyền cuả user trong thread, có thể là `member`, `admin`, `owner`.
- `pin_pos`: Thời điểm thread pin trong thư mucj `folder`. 0 nghĩa là chưa được pin.
- `pin_default_pos`: thời điểm thread được pin trong thư mục tất cả. 
- `folder`: Folder mà thư mục chưá thread đó. Folder giúp gom các thread laị với nhau.

## Chat 1-1

Chat 1-1 là chat trực tiếp giữa 2 người. 

## Group chat

Group chat là thread chat cho một nhóm người. Thành viên nhóm chat có thể đến từ nhiều nguồn

- Danh sách thành viên được chọn 
- Nhập từ nhóm chat khác 
- Thành viên từ phòng ban
- Thành viên theo chức vụ cho trước. 
- Nhập từ file excel

![create-thread](../assets/create-thread.png)


Một số issue liên quan:
- Thêm thành viên từ nhóm chat: https://app.clickup.com/t/3kc8j6w
- Thêm thành viên từ file: https://app.clickup.com/t/865bbqjhq


## Phân quyền trong nhóm chat

Thành viên trong nhóm chat có thể có quyền `member`, `admin` hoặc `owner`. Một nhóm chat chỉ có 1 owner. Mặc định, quyền của các thành viên trong nhóm chat như sau.

- Chuyển owner nhóm chat: chỉ chủ nhóm chat
- Xóa thành viên và block khỏi nhóm: owner và admin
- Xem danh sách thành viên bị ban: admin và owner 
- Thêm thành viên: Với collab group mà bật phê duyệt thêm thành viên, chỉ admin mới thêm và duyệt thành viên. Nếu không, tất cả thành viên thông thường đều có quyền.
- Rời nhóm: Tất cả mọi người 
- Sưả thông tin nhóm: Các nhóm public thì chỉ owner và admin mới có quyền, còn member không. Với nhóm private, tất cả mọi người đều có quyền.
- Tắt bật chia sẻ link: Chỉ admin và owner 
- Giải tán nhóm chat: Chỉ owner của group 
- Pin tin nhắn: Chỉ owner và admin với nhóm public, tất cả với nhóm private.
- Xoá tin nhắn:
- Tắt không cho thành viên nhắn tin: admin hoặc owner 

Để kiểm tra quyền, dùng hàm `check_permission` trong module [acl](../../chat/acl.py). Một
số code vẫn dùng phương thức cũ `get_permission` nên chuyển qua phương thức mới.

## Strict Moderation (chưa được triển khai)

Mặc định, các thành viên có thể sưả thông tin group và thêm các thành viên khác.
Ở chế độ quản trị chặt, chỉ admin/owner mới có thể sưả hoặc thêm các thành viên.

Issue: https://app.clickup.com/t/2pt7dxt

## Chat folder

![chat-folder](../assets/chat-folder.png)

Các thread có thể được gom nhóm laị vói nhau thành các thư mục giúp việc
quản lý dễ dàng hơn. Ví dụ group `marketing`, `it`,...

Hiện taị, một thread chỉ có thể nằm trong 1 thư mục.
Ngòai ra có một số thư mục đặc biệt 
- `default`: Chưá tất cả các thread người dùng trừ thread với người lạ.
- `stranger`: Thread với người lạ, những người không cùng trong tổ chức hoặc là bạn. Khi chat với người lạ, thread sẽ chuyển sang thư mục `default`.
- `secret`: Chưá các hôị thoaị bí mật.
- `subthread`: Chứa toàn bộ sub-thread mà người đó tham gia.



## Thread score

Mỗi một thread được gán với một điểm số nhất định tương ứng trong thư mục (mặc định và thư mục mà thread ở trong đó). Dựa trên điểm số này mà thread được sắp xếp theo thứ tự. Thread có điểm cao hơn sẽ hiển thị trước trong danh sách. 

Điểm này được tính như sau:
- Timestamp của tin nhắn mới nhất. Như vậy khi có tin nhắn mới nhất thread sẽ được đưa lên đầu danh sách.
- Các thread được pin trong thư mục có điểm số cố định = `timestamp_pin * 1000`. Điều này giúp thread luôn ở trên các thread không được pin khác. 

Các điểm số này được lưu ở:
- Trong database:
  - `pin_pos`: timestamp_pin trong thư mục 
  - `pin_default_pos`: timestamp_pin trong thư mục mặc định. 
  - `_updated`: timestamp của tin nhắn cuối cùng
- Trong redis:
  - [redis sorted sets](https://redis.io/docs/data-types/sorted-sets/) key: `{user_id}_threads` với thư mục mặc định hoặc `{user_id}_threads_{folder_id}` với thread trong folder khác.

Việc duy trì danh sách thread trên cả redis và database dẫn đến hạn chế cần cập nhật đúng, tránh các vấn đề đồng bộ. Rất nhiều vấn đề bug trước đây liên quan đến vấn đề đồng bộ logic này.

Để lấy danh sách thread theo thứ tự, ta thường làm như sau:
- Truy vấn vào redis để lấy danh sách thread_id theo thứ tự điểm giảm dần
- Truy vấn vào database để lọc các thread mà có id trong danh sách thread đó.

Do đó để đảm bảo thread được trả về trong danh sách thread cần phải **set thread score trên redis**.


Thread score được cập nhật khi:
- Người dùng pin thread
- Thread có tin nhắn mới nhất. Nếu thread không được pin, thread score sẽ được cập nhật là timestamp hiện tại.

## Chat thread liên kết

Một phòng ban hoặc một nhóm trong một workspace có thể có một chat thread liên kết. 

Liên kết nhóm chat:
![group](../assets/group-lien-ket-nhom.png)
Liên kết với phòng ban:
![phong ban](../assets/group-lien-ket-phong-ban.png)

Group có liên kết này được nhận biết qua trường `associate_link` trong bảng `participant_threads`. 
Với group liên kết với phòng ban sẽ có kiểu dạng : `department:<department_id>`.
Với group liên kết với nhóm sẽ có kiểu dạng: `group:<group_id>`.

Khi huỷ liên kết với phòng ban cần xoá trường thông tin này. 


Link to issue: ?

## Thread bí mật

Thread bí mật là thread được đặt trong thư mục `secret`. Thread này sẽ không xuất hiện trong các thư mục bình 
thường. Để lấy danh sách thread bí mật thì user cần truyền thêm pass_code vào mục tìm kiếm để hiện lên.

![secret-chat-1](../assets/secret-chat-1.png)

![secret-chat-2](../assets/secret-chat-2.png)

Link to issue: ?

## Giải tán nhóm chat

Khi một nhóm chat bị giải tán

- Các thành viên trong nhóm chat sẽ rời nhóm chat 
- Nếu thread liên kết với một nhóm, phòng ban, thread sẽ được unlink bằng cách gọi qua API group/workspace.
- Thread chuyển sang bí mật, link join sẽ bị disable. 

Link to issue: https://app.clickup.com/t/2dv2z94


## Thoát khỏi nhóm chat nếu user bị deactivate/rời workspac 

Khi người dùng bị deactivate hoặc rời workspac , người dùng sẽ bị xoá khỏi toàn bộ các nhóm chat mà người dùng đang ở
trong đó. Khi người dùng được mời trở lại các workspace, người dùng sẽ join trở lại các nhóm cũ.
Trong database có trường `is_deactivated` để đánh dấu các group chat mà bị deactivate.

Issue: https://app.clickup.com/t/865bthzmx