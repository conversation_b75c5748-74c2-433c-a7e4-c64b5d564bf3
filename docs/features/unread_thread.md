## Đ<PERSON><PERSON> dấu cuộc hội thoại chưa đọ<PERSON>, để kiểm tra hội thoại đã đọc hay chưa, ta cần so sánh 2 trường `message_count` trong bảng `threads` và `read_count` trong bảng `participant_threads`. Nếu `message_count` > `read_count` thì thread có tin nhắn chưa đọc.

<PERSON><PERSON>, client có thê đánh dấu hội thoại đã đọ<PERSON>, chưa đọc. M<PERSON><PERSON> đích là sau họ có thể quay lại sau với hội thoại này sau đó. <PERSON><PERSON> làm điều này, trong db lưu trữ thêm trường `mark_read` để đánh dấu điều đó.

Issue: https://app.clickup.com/t/2hdq95v