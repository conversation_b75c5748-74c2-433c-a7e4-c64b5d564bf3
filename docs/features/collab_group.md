# Nhóm cộng tác (Collab group)

## <PERSON><PERSON> tả

![collab](../assets/collab_group.png)

Một nhóm cộng tác là tính năng mà giúp gộp các tính năng, chat, công việc, cuộc họp
tương ứng với một group liên quan.

Trong trường thread sẽ có thêm trường `collab_id` chưá ID của collab. Việc quản lý thành viên,
cập nhật thông tin collab, setting thông qua service membership. Chat cần đồng bộ thông tin
này với membership service.

## Membership API

Membership service được triển khai với Grpc. Các API liên quan đến membership được định nghĩa trong file [memberhsip.proto](../../chat/connections/gapo_client/membership/proto/membership.proto).

<PERSON><PERSON><PERSON> cập nhật file này để đảm bảo API là mới nhất.

<PERSON><PERSON> gọ<PERSON> sang API service, Grpc client cần được sinh ra trước khi sử dụng. Grpc client được sinh ra từ file proto thông qua lệnh :

```bash
pipenv run gen-grpc-clients
```

Sau khi sinh client xong, cần cập nhật `membership_client.py` và `membership_mock.py` (mock) cho phù hợp.

## Membership worker 

Chat cần đồng bộ dữ liệu từ membership service. Để làm điêù này, chat lắng nghe sự kiện từ membership service (Kafka) và thực hiện cập nhật.

Để chạy worker thực hiện lệnh:

```bash
ENV_FILE=env.staging python kafka_membership_worker.py
```

Có thể thay `env.staging` với file môi trường phù hợp. 

Lưu ý rằng, không thể xử lý lại event kafka cũ, do đó cần phải xử lý để đảm bảo tính đúng đắn, đẩy qua worker nếu có thể để tránh vẫn đề delay cao khi xử lý sự kiện.

Chính vì vậy, trong worker cài đặt hook để chỉ thoát khi đã xử lý xong event đã nhận. 
Xem hàm [on_exit](../../chat/backgrounds/kafka/membership.py#L258)


## Issue

Issue: https://app.clickup.com/t/2r73we4