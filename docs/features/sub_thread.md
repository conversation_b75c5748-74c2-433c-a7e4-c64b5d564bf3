# Sub-thread


- Link mô tả & kĩ thuật: https://app.clickup.com/t/865bmt0km

## Quản lý quyền trong sub-thread

Đây là phần quan trọng liên và hiện tại có nhiều hạn chế. 

Hiện tại:
- Bất kì ai trong thread đều có bình luận, tương tác trong sub-thread của thread đó. 
- Chỉ khi người dùng được mention hoặc bình luận trong sub-thread, thread mới hiển thị trong danh sách sub-thread.

Với các thread bình thường membership của thread được quản lý trong bảng `participant_threads`. <PERSON>y nhiên việc thêm toàn bộ thành viên vào sub-thread khi một sub-thread được tạo là một thao tác cơ sở dữ liệu tốn kém, trong khi người đó chưa thực sự là thành viên sub-thread (chư<PERSON> nhận thông báo, chưa hiển thị trong danh sách sub-thread). Do vậy giải pháp hiện tại là giải pháp hybrid. Cụ thể thứ tự kiểm tra quyền như sau:
- Lấy thông tin public của sub-thread (trong bảng `threads`)
- Nếu thread là thread bình thường, dùng logic cũ. Tức join 2 bảng `participant_threads` và `threads` để trả về đầy đủ thông tin.
- Nếu thread là sub-thread:
  - Nếu join 2 bảng và có bản ghi trả về, trả về thông tin đó.
  - Nếu không có, điền các trường thông tin dummy trong bảng `participant_threads` vào thông tin public-thread hiện tại (read_count = 0, folder=subthread, ...)

Các logic này được thể hiện trong hàm [get_thread_for_user](../../chat/threads/usecase/get_thread_usecase.py#L393). Để đúng đắn, tất cả các hàm liên quan đến quyền sub-thread cần được thực hiện thông qua hàm này. 
Tuy nhiên cách làm này cũng có nhược điểm là tất cả các API hiện tại đều bị ảnh hưởng. Do đó cần tối ưu thêm về phần này trong tương lai. Ví dụ, tách riêng API cho sub-thread ?

Khi thành viên bình luận hoặc được mention, thành viên bình luận và người được mention thực sự được thêm vào bảng `participant_threads`. Xem hàm `_add_sender_and_mentioned_users_to_sub_thread`. 

## Tiêu đề thông báo notification

Để phân biệt thông báo của sub-thread và thread thông thường, Tiêu đề  thông báo sub-thread
hiện tại bổ xung thêm nội dung tin nhắn gốc của sub-thread. Điều này được thực lấy từ trường `referenced_message_preview` của thread được sinh ra trong message usecase.


## Tạo sub-thread

Khi tạo sub-thread, sub-thread không được thêm vào redis. Điều này để thành viên sub-thread chỉ thực sự thấy sub-thread khi sub-thread có tin nhắn.