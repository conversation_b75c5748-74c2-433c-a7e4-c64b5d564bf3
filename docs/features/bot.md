# Bot 

Bot có thể tạo tin nhắn trong group chat/thread chat mà nó tham gia.

Bot hệ thống cũng có thể gửi tin nhắn vào nhóm mà bot không ở trong đó thông
qua việc truyền tham số `bypass_all` vào trong usecase message.

Các logic chính vẫn nằm trong service chat. Bot service chỉ gọi sang. Do đó khi
cần hỗ trợ kiểu tin nhắn mới, thêm schema cho kiểu tin nhắn mới và cập nhật danh sách schema hỗ trợ cho bot trong object `SCHEMA_BOT_MESSAGES`.

Xem thêm [Message](./message.md).

Xem thêm về [Bot](https://gitlab.gapo.com.vn/bots/backend/-/blob/staging-work-v1.0/docs/README.md).