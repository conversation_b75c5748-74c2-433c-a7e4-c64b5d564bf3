# Search


Chat service đồng bộ với search service qua các sự kiện rabbitmq. V<PERSON> vậy cần chú ý các sự kiện
này để đảm bảo đồng bộ dữ liệu đúng đắn với bên search. <PERSON><PERSON><PERSON> sự kiện chính:
- <PERSON><PERSON><PERSON>, xo<PERSON>, s<PERSON><PERSON> thông tin thread.
- <PERSON><PERSON><PERSON>, xo<PERSON>, sửa tin nhắn. 
- Sự kiện thay đổi thêm, xo<PERSON> thành viên. (Qua action notes tin nhắn liên quan đến tạo, thêm, xoá thành viên)
- Sự kiện media file.


Việc chỉ cập nhật database nhưng bỏ qua các event, sẽ dẫn đến việc thiếu đồng bộ.
Nên khi cập nhật, cần kiểm tra lại thông tin này, bổ sung task liên quan.

V<PERSON> dụ: https://app.clickup.com/t/865bqmqjb