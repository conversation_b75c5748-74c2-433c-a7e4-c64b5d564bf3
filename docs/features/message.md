# Message
## Model
Mỗi tin nhắn trong chat-api có kiểu nhất định: `text`, `meeting`, `video`, `file`,...
Model chung cuả tin nhắn:

```python
class _MessageBody(BaseModel):
    media: list = []
    reply_to_msg: typing.Union[None, int] = None
    reply_to_msg_object: typing.Union[None, dict] = None
    forward: typing.Optional[Dict] = None
    text: typing.Union[None, str]
    is_markdown_text: bool = False
    tmp_text: typing.Union[None, str]

    metadata: typing.Union[None, dict] = None
    type: str
    edited_at: typing.Union[None, int] = 0
    workspace_id: typing.Union[None, int] = 0

    def forward_body(self):
        return _MessageBody(
            media=self.media,
            text=self.text,
            tmp_text=self.tmp_text,
            metadata=self.metadata,
            type=self.type,
        )

class MessageModel(BaseModel):
    thread_id: int
    bucket: int
    id: int
    body: _MessageBody
    created_at: int
    delete_level: int
    deleted_by: set = ()  # type: ignore
    deleted: bool = False
    react_binary: int
    type: typing.Union[None, str]
    user_id: str
    uuid: str
    version: typing.Union[None, str]
    pinned_at: int = 0

    user: typing.Optional[Dict] = None
    react: typing.Optional[Dict] = None
    read: list = []

    edited_at: typing.Union[None, int] = 0

    @pydantic.root_validator(pre=True)
    def fill_default_payload(cls, values):
        values["uuid"] = str(values["uuid"])

        react_binary = values["react_binary"]
        values["react_binary"] = react_binary or 0
        values["deleted_by"] = list(values["deleted_by"] or [])

        _body_versioning(values)

        _body_delete(values)
        return values

    def fill_reply_message_object(self, obj):
        self.body.reply_to_msg_object = {
            "body": obj.body.dict(),
            "sender": obj.user,
        }

    def is_deleted_2_side(self):
        return self.delete_level == _TWO_WAY_LEVEL

    def is_deleted_1_side(self, user_id):
        return user_id in self.deleted_by

    def is_owner(self, user_id) -> bool:
        return self.user_id == user_id

    def is_forward(self):
        return self.body.forward

    def delete(self):
        self.body.text = _MESSAGE_DELETE_DEFAULT
        self.deleted = True
        self.body.media = []
        self.body.type = "text"

    def fill_user(self, user_info):
        self.user = user_info

    def fill_react(self, react_info):
        self.react = react_info

    def fill_read(self, read_info):
        self.read = read_info

    def build_last_message(self):
        return orjson.dumps(
            {
                "id": self.id,
                "body": self.body.text,
                "raw_body": self.body.dict(),
                "user_id": self.user_id,
                "created_at": self.created_at,
            }
        )

    def edit(self, new_body):
        self.body.edited_at = common.now()
        self.body.text = new_body.text
        self.body.is_markdown_text = new_body.is_markdown_text
        # print('new_body.metadata :', new_body.metadata)
        if new_body.metadata is not None:
            # print(f'new body media data {self.body.metadata}')
            self.body.metadata = (
                new_body.metadata.dict()
                if self.body.metadata is None
                else self.body.metadata
            )
            self.body.metadata["preview_link"] = new_body.metadata.preview_link
            self.body.metadata["mentions"] = new_body.metadata.mentions
        else:
            self.body.metadata = {"preview_link": {}, "mentions": []}

    def is_expire(self, ttl, now=None):
        if not now:
            now = common.now()
        compare_time = ttl * 60 * 1000
        last_edit = (
            self.created_at if (self.body.edited_at == 0) else self.body.edited_at
        )
        return (now - last_edit) < compare_time

    def is_poll_type(self):
        return self.body.type == "poll"

    def get_poll_id(self):
        if not self.body.metadata:
            return None
        poll_i = self.body.metadata["poll_information"]
        if isinstance(poll_i, dict):
            return poll_i["id"]
        elif isinstance(poll_i, str):
            return poll_i
        else:
            return poll_i.id

    def update_poll(self, poll):
        self.body.metadata["poll_information"] = poll  # type: ignore

    def build_media_link(self):
        temp_body = self.body.dict()
        metadata = temp_body.get("metadata", None)

        urls = set()
        if metadata:
            preview_link = metadata.get("preview_link", None)
            if preview_link:
                urls.add(preview_link["source_url"])

        match_urls = extractor.find_urls(self.body.text)
        for url in match_urls:
            urls.add(url)

        return {
            "body": list(urls),
            "media": self.body.media,
            "metadata": metadata,
            "created_at": self.created_at,
        }
```

Xem thêm taị [file](../../chat/messsages/model/__init__.py)


Tin nhắn hiện tại chỉ xoá, sưả trong vòng một giờ. Xem [issue](https://app.clickup.com/t/2fu79n7). Tuy nhiên
hội thoại **Lưu trữ** (bot) có thể xoá.

Tin nhắn trong bot **GapoSystem** (bot login hệ thống) không thể xoá để đảm bảo tính bảo mật.

## Lưu trữ tin nhắn trong cassandra


Tin nhắn được lưu trữ trong Cassandra tương tự cách [Discord lưu trữ.](https://discord.com/blog/how-discord-stores-billions-of-messages).  Các tin nhắn sẽ được lưu trữ trong một bucket cụ thể theo id cuả tin nhắn. Do đó cần phải truyền đúng `BUCKET_MESSAGE_FACTOR` giữa các API để đảm bảo việc đọc/ghi đúng.

```python
BUCKET_MESSAGE_FACTOR = 10000
bucket = floor(msg_id/ BUCKET_MESSAGE_FACTOR)
query = "select * from messages where thread_id=%s and bucket=%s and id=%s"
result = session.execute(query, (thread_id, bucket_id, message_id))
```

message_id được sinh ra cho từng tin nhắn, dựa trên trường `message_count` trong database `threads`. Do đó cần đảm bảo rằng luôn chỉ tăng `message_count`
khi tạo message. Một số action note trong hệ thống có tạo tin nhắn, trong khi một số không. Xem thêm trong hàm `_skip_creating_message_from_action_note` .

Xem thêm code repository: [repo](../../chat/messages/repository/cassandra.py)

## Rich text format 

Tin nhắn hỗ trợ markdown. Client thiết lập trường `is_markdown_text` true để báo hiệu tin nhắn
mardown (rich text format).


## MQTT & Notification push

Client nhận được thông báo cập nhật về tin nhắn mới thông qua MQTT (nếu client đang ở trong web/app) hoặc notification push (cho mobile) hoặc firebase (với web đã bật thông báo nhận tin nhắn).

- Firebase message

![firebase-message](../assets/firebase-message.png)

- Flash message 
  
![flash-message](../assets/flash-message.png)

Xem thêm [Tạo tính năng](../feature)

## Chat bot

Bot service gọi đến API chat để tạo tin nhắn. Service bên bot chỉ quản lý thông tin liên quan đến bên bot. Toàn bộ logic tin nhắn nằm ở bên chat service.

## Một số loại tin nhắn 

- Tin nhắn carousel: https://app.clickup.com/t/865bbx0c0
- Tin nhắn quick reply: https://app.clickup.com/t/3ehufex

## Action note

![action note](../assets/action-note.png)

Action note là một loại tin nhắn do hệ thống tạo (từ user `system`) để cập nhật các thông 
báo liên quan đến thay đổi liên quan nhóm chat.

Hiện tại action note không hỗ trợ đa ngôn ngữ, mà client phải tự dịch nên nhiều hạn chế.

Để tạo action note, ta gọi phương thức `self.rb_broker.publish_action_note` trong usecase.


## Gửi tin nhắn 1-1 hàng loạt

Issue: https://app.clickup.com/t/3jv0ypc

Mục đích API để các service bên ngoài (bot) có thể gửi tin nhắn 1-1 hàng loạt đến nhiều người.

Để  tránh ảnh hưởng đến các tin nhắn khác, task tạo tin nhắn được đẩy vào với độ ưu tiên thấp hơn.

Một ví dụ của API này là bot thông báo bài viết (bên post team). Bot sẽ lấy ra danh sách những người chưa đọc
và gửi đến người dùng. 

![post-unread-notification](../assets/post-unread-notification.png)