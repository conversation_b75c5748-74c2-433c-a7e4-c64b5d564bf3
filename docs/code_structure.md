# Code structure

Tổng quan, codebase `chat-api` bao gồm một số thư mục chính:

- `chat`: <PERSON><PERSON><PERSON> toàn bộ code logic 
- `tests`: <PERSON><PERSON><PERSON> toàn bộ test liên quan đến app
- `docs`: Tài liệu liên quan đến chat-api.
- `dev-scripts`: Chứa các script hỗ trợ trong quá trình dev như lint, sinh grpc client, check typing, i18n,...
- `migrations`: Chư<PERSON> toàn bộ các script liên quan đến migration. 
  - `migrations/cql`: Liên quan đến migrate cassandra
  - `migrations/mysql`: Liên quan đến migrate mysql.
  - `migrations/scripts`: Chưá các script liên quan đến migration cụ thể .

## Module 

Mỗi module tính năng được tổ chức trong một thư mục tính năng cụ thể trong thư mục `chat`. Ví dụ `chat/folders`, `chat/threads`,... Mỗi thư mục tính năng thường bao gồm một số module khác nữa.
- `container.py`: <PERSON><PERSON><PERSON> nghĩa DI 
- `deliver`: Định nghĩa các API handlers 
- `model`: Định nghĩa các model liên quan đến module.
- `repository`: Định nghĩa các phương thức truy cập db liên quan đến tính năng. 
- `usecase`: Các usecase liên quan đến tính năng. 
- `exception`: Các lỗi dành riêng cho module.

Việc tổ chức module theo tính năng (folder, ..) thay vì theo chức năng (model, repository,...) giúp code base dễ mở rộng hơn. Dev không cần quá quan tâm đến các module khác
khi lập trình. Tên module nên là [từ tiếng anh số nhiều](https://softwareengineering.stackexchange.com/a/75929) với ý nghĩa chưá các các module con khác loại (model, repository,...). 


Các phương thức public của lớp nên được để trên cùng của lớp để giúp nhanh chóng
hiểu các phương thức mà lớp hỗ trợ.


Các test cho module cụ thể nằm trong thư mục `tests/{module_name}`. Nên bắt đầu với các test cụ thể để hiểu mục
đích của module.

## Hướng dẫn khác

Code nên "sạch" nhất có thể. Giả sử bạn viết cho người maintain kế tiếp, làm sao để họ có thể tiếp cận nhanh nhất có thể.

- [Git commit style](../CONTRIBUTING.md)
- https://github.com/zedr/clean-code-python
- https://github.com/zhanymkanov/fastapi-best-practices