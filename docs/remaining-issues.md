# Vẫn đề tồn đọng 

1. Unread count chưa hoàn toàn ch<PERSON>h xác

Unread count trả về số lượng thread chưa đọc. <PERSON><PERSON> nhiên để tính chính xác thread chưa đọc cần join 2 bảng 
`participant_threads` và `threads` để tìm (`message_count` > `read_count`). Việc này không hiệu quả nếu phải scan
qua toàn bộ bản ghi. <PERSON><PERSON> hiệu qu<PERSON> h<PERSON>, hiện tại làm như sau:
  - Lấy top 50 thread trong thư mục tất cả và các top 50 thread trong từng thư mục `stranger`, `subthread`
  - Quét database với filter thread trong danh sách trên. 

Việc này giúp ổn định hệ thống (p99th latency, tránh gây tải cao cho database),

2. Vấn đề unread count 

Đô<PERSON> khi người dùng gặp vấn đề là thư mục chưa đọc báo có thread chưa đọc, nh<PERSON><PERSON> khi click vào danh sách thread trống.

Ch<PERSON><PERSON> tìm được nguyên nhân cụ thể. Hiện tại mơí thêm tracking cho web cảnh báo trên Telegram để theo dõi thêm. Một ngày có khoảng 30 trường hợp bị.

Issue: https://app.clickup.com/t/3y3gaa8
