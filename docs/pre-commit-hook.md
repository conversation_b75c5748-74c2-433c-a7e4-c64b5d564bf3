# Pre-commit hook

[Pre-commit](https://pre-commit.com/) hook là hook sẽ chạy trước khi đẩy một commit. Việc sử dụng pre-commit hooks giúp
nâng cao chất lượng code, hạn chế lỗi. Nội dung pre-commit hook được định nghĩa qua file 
[.pre-commit-config.yaml](../.pre-commit-config.yaml).


Để  cài đặt pre-commit hook chạy lệnh sau:

```bash
pipenv install --dev
pre-commit install
```

Các pre-commit hook  hiện tại:
- Loại bỏ các import và biến không sử dụng
- Sắp xếp import qua isort 
- Format code với Black 
- Kiểm tra lỗi với mypy 
- Kiểm tra lỗi với các file dịch

Khi chạy git commit, kết quả hiển thị sẽ tương tự như sau:

```bash
git commit -a -m 'docs: add test and debug docs'
Remove unused variables and imports..................(no files to check)Skipped
Sorting import statements............................(no files to check)Skipped
Black Python code formatting.........................(no files to check)Skipped
Check errors with mypy...............................(no files to check)Skipped
Check translation files..............................(no files to check)Skipped
```