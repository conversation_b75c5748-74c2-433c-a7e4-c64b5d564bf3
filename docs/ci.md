# CI


Nội dung toàn bộ CI được định nghĩ trên đường dẫn: https://ci-template.s3-ap-southeast-1.amazonaws.com/python3.8.2-with-lint.yml

Hiện tại các project python CI có các bước sau:
- `test`: Th<PERSON><PERSON> hiện `lint-and-test.sh` Nội dung các task trong CI được định nghĩa trong file [lint-and-test.sh](../dev-scripts/lint-and-test.sh).
- `build`: Build docker image. Trong project cần có file `Dockerfile`. 
- `check_migration`: Kiểm tra revision. Kiểm tra xem schema db hiện tại có lớn hơn db revision trong db hiện tại. Xem thêm [check_revision.py](../dev-scripts/check_revision.py). <PERSON><PERSON><PERSON><PERSON> nà<PERSON> có thể được bỏ qua nếu biến môi trường CI `DISABLE_REVISION_CHECK` đư<PERSON><PERSON> bật.
- `deploy`: Triển khai lên production.

Xem setting: https://gitlab.gapo.com.vn/chat/chat-api/-/settings/ci_cd

Nếu cần cập nhật setting, cần báo Sysadmin. Lưu ý rằng các project python đang dùng cùng một setting. Để tuỳ chỉnh nội dung task test cụ thể, client cập nhật file `lint-and-test.sh` cho project cụ thể thay vì nhờ admin chỉnh file này.

Ví dụ: https://gitlab.gapo.com.vn/chat/chat-api/-/jobs/158981


Trạng thái của project (pipeline, code coverage) có thể xem trực tiếp tại project.

![status](assets/project-status.png)

