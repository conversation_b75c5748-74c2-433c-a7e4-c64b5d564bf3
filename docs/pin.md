# Pin 

## <PERSON><PERSON> đồ (c<PERSON>)

```mermaid
graph LR
    A[Actor] -->|Post| B[Thread /threads/<thread_id>/pin]
    B --> C{Check}
    C -->|folder_secret| D[403 Permission Denied]
    C -->|folder_stranger| D
    C -->|folder_default| F[Mark <PERSON>n]
    F -->|sync device| G[mqtt publish /status]
    G -->|Response HTTP| H[200]
```

## Logic

Danh sách thread được pin được lưu trữ trong key trên redis `{user_id}_threads_pin` (với thư mục mặc định) hoặc `{user_id}_threads_pin_{folder}` với folder khác. Mỗi người dùng
không được pin quá 10 lần. Đ<PERSON> đảm bảo logic, [distributed lock](./dist_lock.md) đư<PERSON>c sử dụng. 

Ngoài ra, trong db có 2 trường `pin_pos` và `pin_default_pos` là timestamp thread được pin, c<PERSON> thể dùng để kiểm tra xem thread đã dược pin hay chưa. 

Xem thêm tại [code](../chat/threads/broker/redis_broker.py).

## Issue

Issue: https://app.clickup.com/t/2hkbqkx