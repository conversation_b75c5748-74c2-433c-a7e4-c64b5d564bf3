# Logging

Có 2 chế độ logging lập trình viên có thể sử dụng. Dạng text thông thường và structured log (json).

-   Text thông thường: <PERSON><PERSON><PERSON> g<PERSON>, <PERSON><PERSON> hiể<PERSON>, <PERSON>hù hợp cho việc phát triển local.
-   Dạng structured log: Dùng để log trên production, dùng để filter trên gray log. Structured log giúp debug vấn đề trên production dễ dàng hơn. Xem [debug vấn đề khách hàng](./monitoring.md).

Logging được bổ xung thêm một số trường để  tiện cho việc debug sau:
- `ua`: user agent
- `ip`: IP adddress 
- `request_id`: ID của request
- `auth_type`: service, user, guess
- `status`: http status 
- `user_id`: ID của user
- `workspace_id`: workspace ID

Để chuyển đổi giữa 2 định dạng logging này, ta dùng biến môi trường `USE_JSON_LOG`. Khi biến này đượ<PERSON> bật, structured log sẽ được dùng. Ví dụ:

```bash
USE_JSON_LOG=false ENV_FILE=env.staging pipenv run appp
```


- Log thông thường:

![normal-logging](./assets/normal-logging.png)

- Structured log:

![structured-logging](./assets/structured-logging.png)