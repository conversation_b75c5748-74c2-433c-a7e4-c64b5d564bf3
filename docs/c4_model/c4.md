# Chat Service C4 Context

## Current

![Drag Racing](./c4_context.png)

**For editable version Please manually copy and paste the link** (must signed in using gapo account)

https://viewer.diagrams.net/?tags=%7B%7D&highlight=0000ff&edit=_blank&layers=1&nav=1#G1cN1qwpn0z8bWXHMO9nFkYADSMyOqLZJJ

[DrawIO File](./system.drawio)

## Sơ đồ các module chat

![module](./chat-modules.png)


Các module chính:

- [chat-api](https://gitlab.gapo.com.vn/chat/chat-api): <PERSON><PERSON> ch<PERSON>h chưá các API ghi, cập nh<PERSON>t dữ liệu, worker chính.
- [chat-fastapi](https://gitlab.gapo.com.vn/chat/chat-fastapi): <PERSON><PERSON> chư<PERSON> các API đọc dữ liệu (thread, tin nhắn,...).
- [**********************](https://gitlab.gapo.com.vn/chat/**********************): Worker tạo tin nhắn trong database, xử lý lưu trữ media file đ<PERSON>h kèm trong tin nhắn.
- [chat-delivery-worker-golang](https://gitlab.gapo.com.vn/chat/chat-delivery-worker-golang): Worker chuyển MQTT về tin nhắn cho người dùng liên quan.
- [chat-********************](https://gitlab.gapo.com.vn/chat/chat-********************): Worker tạo thông báo tin nhắn.
- [unread_count](https://gitlab.gapo.com.vn/chat/unread_count): API trả về số lượng tin nhắn chưa đọc theo từng folder.
- [chat-refresh-cache-worker](https://gitlab.gapo.com.vn/chat/chat-refresh-cache-worker): Worker refresh thông tin user/bot trên redis.
- [chat_client](https://gitlab.gapo.com.vn/chat/chat-client): Dùng để test end-to-end tới các API chính.
- [chat-listen-gapo-event-worker-golang](https://gitlab.gapo.com.vn/chat/chat-listen-gapo-event-worker-golang): Worker lắng nghe các sự kiện bên workspace, call để cập nhật. 