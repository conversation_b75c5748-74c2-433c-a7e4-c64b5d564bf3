# Test và debug

## Cài đặt plugin

Khuyến nghị sử dụng vscode để phát triển project này sẽ dễ dàng hơn.

Các plugin được khuyến nghị cũng được để vào trong [extensions.json](../.vscode/extensions.json)

## Test 

Trước khi chạy test cần chạy setup database:

```bash
cp docker-compose.test.yaml docker-compose.yaml
pipenv run setup-db
```

<PERSON><PERSON> thể test trên giao diện hoặc qua câu lệnh. Giao diện test trên vscode sẽ như sau:

![vscode-test](assets/test.png)

Để chạy test qua dòng lệnh, chạy :

```
pytest path_to_file_test.py
```

Đ<PERSON> chạy toàn bộ test:

```
pytest 
```

Để tránh việc chạy lại toàn bộ test khi thêm test, project này dùng pytest-testmon để cache 
test. Khi chạy lệnh sau, những test không thay đổi sẽ không cần chạy lại:

```
pipenv run test
```


Ngoài ra có thể xem code coverage thông qua lệnh sau:

```
pipenv run test-cov
```

Sau khi chạy xong, pytest sẽ sinh ra file `coverage.xml`, dùng để hiển thị code coverage
trên Vs-code. Giao diện sẽ tương tự như sau:

![code-coverage](./assets/code-coverage.png)


Trong repo có các test end-to-end đến membership service. Tuy nhiên các test này cần 
kết nối thật đến server membership, nên mặc định các test này bị tắt (kể cả ở CI).

Để bật test này cần set biến `MEMBERSHIP_URL` đến địa chỉ đúng và bật cờ `ENABLE_MEMBERSHIP_SERVICE_TESTS`. Ví dụ

```bash
MEMBERSHIP_URL=************:30080 ENABLE_MEMBERSHIP_SERVICE_TESTS=true pytest
```

Khi fix bug cho một module, **nên** bổ xung thêm test để tránh việc lỗi đó phát sinh trong tương lai.

## Debug 

![vscode-debug](assets/debug.png)

Trên vscode có sẵn một số chế độ debug:

- `Python: App`: Dùng để chạy server trên staging và test 
- `Python: Current File`: Để chạy với file hiện tại


Khi cần cập nhật biến môi trường staging để test, cần cập nhật file [env.staging](../env.staging).